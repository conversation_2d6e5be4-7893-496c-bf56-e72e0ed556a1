## 小熊美术 C 端小程序合集

#### 开发时代码及组件请遵循 Taro 规范

#### 项目初始化：

```shell
npm install

```

### 项目打包注意事项！！！

- 不要使用`Taro`自带的`npm`命令打包！！！

### 项目构建

#### weapp 构建

```shell
# 下拉选择构建的小程序
yarn dev:weapp || npm run dev:weapp
# 或者根据src/scripts/version.js中配置的env_number的value执行
yarn dev:weapp v1 test || npm run dev:weapp v1 test

#打包执行(开发者工具上传 开发者工具上传需手动修改下release_number)
yarn build:weapp || || npm run build:weapp
# 或者根据src/scripts/version.js中配置的env_number的value执行
yarn build:weapp v1 test || npm run build:weapp v1 test

# 命令行上传(上传版本号需要对应src/scripts/version.js对应的release_number线上版本)
yarn upload:weapp || npm run upload:weapp
# 或者根据src/scripts/version.js中配置的env_number的value执行
yarn upload:weapp v1 test || npm run upload:weapp v1 test
```

### 项目目录使用规范：

- 所有页面都不需要在`src/app.tsx`中`config.pages`字段下配置路由
- 所有小程序页面，都需要在`routers`目录下根据小程序项目名新建自己的 `router` 文件，每个路由文件只允许配置自己小程序用到的 `pages`，`appid`等所有涉及`project.config.json`
  的配置都在对应的`router`文件`project`字段下配置
- 所有模板文件（用于构建时代替 `app.tsx` 文件）配置都在 `entry` 目录，文件名需要与`routers`目录中文件配置的 `entry` 字段对应对应，`build` 的时候不会使用 `app.tsx`
  文件构建，而是使用 `entry` 目录中与`routers`目录下的路由文件对应的 `tsx` 文件（不配置默认使用 `entry/default.tsx` 模板文件）构建!!!

### 支付流程

- 1.进入首页： （1）调用微信 login 接口获取 code --> 拿 code 调接口拿到 微信小程序用户信息（包含 openid ，userid）
- 2.选择级别后支付框弹出 （1）如果有 userid，点击支付按钮 -->发起支付 （2）如果没有 userid，点击支付按钮 -->走授权 -->发起支付

## appid 及对应小程序

- 小程序名称 : 小熊艺术乐园 AppID : wx34831c7cbd4406e5 原始 ID : gh_66caa4fba934 project.config.json

- 小程序名称 : 学美术赢好礼 AppID : wx326319ed9257c109 原始 ID : gh_c6c1f0ca5385 project.weappV1.json

- 小程序名称 : 学美术享好礼 AppID : wx817df83e561f7921 原始 ID : gh_1658b42e7dc9 project.weappV2.json

- 小程序名称 : 艺术学习乐园 AppID : wx81af891de9460f57 原始 ID : gh_2550f68e9827 project.weappV3.json

- 小程序名称 : 硬笔书法天天练 AppID : wx9861c14361892f82 原始 ID : gh_5b6973859328 project.weappV4.json

- 小程序名称 : 硬笔书法体验 AppID : wxebd60a8ba0f15e64 原始 ID : gh_6ef819e143ca project.weappV5.json 枚举：WRITE_APPLET_3

- 小程序名称 : 硬笔书法每天练 AppID : wxd47a9f80e4886f96 原始 ID : gh_360bf8d96e77 project.weappV6.json 枚举： WRITE_APPLET_2

- 小程序名称 : 每天写好字 AppID : wx58ded43dc2c098cc 原始 ID : gh_57b7fc45a2e5 project.weappV7.json 枚举：WRITE_APPLET_5

- 小程序名称 : 学书法打好基本功 AppID : wx13434df9aa2d6248 原始 ID : gh_51908f61cd15 project.weappV8.json 枚举：WRITE_APPLET_6

- 小程序名称 : 小伴熊艺术 AppID : wxf0543f82c4836de6 原始 ID : gh_b484786ef320 project.weappV9.json 枚举：ART_APPLET_1

- 小程序名称 : 学美术画世界 AppID : wx866e1fec122c0fec 原始 ID : gh_2dcd0703f164 project.weappV10.json 枚举：ART_DRAW_WORLD
  已切换至 ai-mp-parents-center 项目 用于添加老师

- 小程序名称 : 学美术激发创意 AppID : wx701876346aa13859 原始 ID : gh_71168d13eb95 project.weappV11.json 枚举：ART_IDEA

- 小程序名称 : 书法学习乐园 AppID : wx538cb2df4b3ecbb4 原始 ID : gh_1fa433984195 project.weappV12.json 枚举：WRITE_APPLET_01

- 小程序名称 : 小伴熊美术 AppID : wx804cb7ac551ec97b 原始 ID : gh_2e9e1f407b10 project.weappV13.json 枚举：ART_REDEEM

- 小程序名称 : 天天领大礼 AppID : wx9b8dcac074fb3151 原始 ID : gh_bea9cdedbb06 project.weappV14.json 枚举：ART_GIFT
  tapd: https://www.tapd.cn/59397856/prong/stories/view/1159397856001025784

- 小程序名称 : 周周有礼 AppID : wxbd18318a1f9a7b03 原始 ID : gh_0554b0426b18 project.weappV15.json 枚举：WEEK_APPLET
  tapd: https://www.tapd.cn/59397856/prong/stories/view/1159397856001026088

- 小程序名称 : 小伴熊学画画 AppID : wxc7c14f4b2f39252b 原始 ID : gh_24de96adfa62 project.weappV17.json 枚举：ART_XIAOBANXIONG_HUAHUA
