{"name": "ai-mp-groupbuy", "version": "1.0.0", "private": true, "description": "小熊美术C端小程序", "templateInfo": {"name": "default", "typescript": true, "css": "sass"}, "scripts": {"upload:weapp": "node src/scripts/upload.js", "open:weapp": "node src/scripts/open.js", "dev:weapp": "node src/scripts/watch.js", "build:weapp": "node src/scripts/watch.js --build build", "dev:alipay": "node src/scripts/alipay-watch.js", "build:alipay": "node src/scripts/alipay-watch.js --build build", "upload:alipay": "node src/scripts/upload-alipay.js", "dev:tt": "taro build --type tt --watch", "build:ttdev": "NODE_ENV=dev taro build --type tt && rm -rf dist/assets", "build:tttest": "NODE_ENV=test taro build --type tt && rm -rf dist/assets", "build:ttgray": "NODE_ENV=gray taro build --type tt && rm -rf dist/assets", "build:ttonline": "NODE_ENV=online taro build --type tt && rm -rf dist/assets && npm run mr", "mr": "bash ./.bin/mr.sh"}, "lint-staged": {"*.{js,jsx,scss,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "author": "", "license": "MIT", "dependencies": {"@babel/runtime": "^7.7.7", "@tarojs/components": "3.3.9", "@tarojs/react": "3.3.9", "@tarojs/runtime": "3.3.9", "@tarojs/taro": "3.3.9", "ali-oss": "^6.7.0", "child_process": "^1.0.2", "cross-env": "^7.0.2", "js-md5": "^0.7.3", "qs": "6.9.6", "react": "^17.0.0", "react-dom": "^17.0.0", "react-redux": "^7.2.4", "redux": "^4.1.0", "redux-logger": "^3.0.6", "redux-thunk": "^2.3.0", "sa-sdk-miniprogram": "1.17.7", "taro-ui": "3.0.0-alpha.10"}, "devDependencies": {"@babel/core": "^7.8.0", "@commitlint/cli": "^9.1.1", "@commitlint/config-conventional": "^9.1.1", "@tarojs/cli": "3.3.9", "@tarojs/mini-runner": "3.3.9", "@tarojs/service": "3.3.9", "@tarojs/webpack-runner": "3.3.9", "@types/react": "^17.0.2", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "babel-preset-taro": "3.3.9", "colors": "^1.4.0", "eslint": "^6.8.0", "eslint-config-taro": "3.3.9", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "husky": "^4.2.5", "lint-staged": "^10.2.11", "minimist": "^1.2.8", "stylelint": "9.3.0", "typescript": "^4.1.0", "webpack-aliyun-oss": "^0.3.13"}}