/** 
 *  css常量 
 **/
// 白色
$white: #ffffff;
// 烟白
$smoke-white: #f5f5f5;
// 黑色
$black: #000000;
// 暗淡灰
$dim-gray: #333333;
// 银白色
$silver: #666666;
// 灰色
$grey: #999999;
// 浅灰色
$light-grey: #b4b4b4;
// 橘红色
$red: #ff6a00;
// 橘红色2号
$red-two: #ff5100;
// 橘黄色
$orange: #ff9c00;
// 绿色
$green: #7fd316;
// 橘红色渐变至橘黄色
$red-to-orange: linear-gradient(270deg, #ff731e, #ff3a30);
// 橘黄色渐变至黄色
$orange-to-yellow: linear-gradient(270deg, #ffb61e, #fd880d);
// 橘红色渐变至黄色
$red-to-yellow: linear-gradient(270deg, #ffa300, #ff6a00);
// 淡黄色
$light-yellow: #fff9e1;
// 深橘色
$de_orange: #ffcf00;
// 字红色
$c_red: #fe252e;
// 浅灰色
$linght-grey: #f7f7f7;

// 字体大小
$font-20: 20rpx;
$font-22: 22rpx;
$font-24: 24rpx;
$font-26: 26rpx;
$font-28: 28rpx;
$font-32: 32rpx;
$font-30: 30rpx;
$font-34: 34rpx;
$font-36: 36rpx;
$font-38: 38rpx;
$font-40: 40rpx;
$font-44: 44rpx;
$font-48: 48rpx;
$font-50: 50rpx;
$font-68: 68rpx;
$font-96: 96rpx;
