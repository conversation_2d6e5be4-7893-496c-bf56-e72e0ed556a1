import { View } from '@tarojs/components';

export default function CodeSnippet() {
  return (
    <View style='font-weight:blod; text-align:center;padding: 50rpx;min-height:100vh;line-height: 3;'>
      <View style='font-size: 22rpx;color:#000;'>黑色 22rpx $black</View>
      <View style='font-size: 24rpx;color:#000;'>黑色 24rpx $black</View>
      <View style='font-size: 26rpx;color:#333333;'>
        暗淡灰 26rpx $dim-gray
      </View>
      <View style='font-size: 28rpx;color:#666666;'>银白色 28rpx $silver</View>
      <View style='font-size: 32rpx;color:#b4b4b4;'>
        浅灰色 32rpx $light-grey
      </View>
      <View style='font-size: 34rpx;color:#ff6a00;'>橘红色 34rpx $red</View>
      <View style='font-size: 36rpx;color:#ff9c00;'>橘黄色 36rpx $orange</View>
      <View style='font-size: 38rpx;color:#7fd316;'>绿色 38rpx $green</View>
      <View style='font-size: 40rpx;color:#000;'>黑色 40rpx $black</View>
      <View style='font-size: 50rpx;color:#000;'>黑色 50rpx $black</View>
      <View style='background:linear-gradient(270deg,#ff731e,#ff3a30);color:#fff;font-size: 36rpx;'>
        橘红色渐变至橘黄色 $red-to-orange
      </View>
      <View style='background:linear-gradient(270deg,#ffb61e,#fd880d);color:#fff;font-size: 36rpx;'>
        橘黄色渐变至黄色 $orange-to-yellow
      </View>
      <View style='background:linear-gradient(270deg,#ffa300,#ff6a00);color:#fff;font-size: 36rpx;'>
        橘红色渐变至黄色 $red-to-yellow
      </View>
    </View>
  );
}
