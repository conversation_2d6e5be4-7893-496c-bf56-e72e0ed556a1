@import '../groupbuy/common.scss';
/** 公共类名 **/
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
  left: 0;
  right: 0;
}

.cl:after {
  content: '.';
  display: block;
  height: 0;
  visibility: hidden;
  clear: both;
}

.cl {
  zoom: 1;
}

.fl {
  float: left;
}

.fr {
  float: right;
}

.flex {
  display: flex;
  justify-content: center;
  /* 水平居中 */
  align-items: center;
  /* 垂直居中 */
}

.flex_only {
  display: flex;
}

.flex_align {
  display: flex;
  align-items: center;
  /* 垂直居中 */
}

.flexitem {
  flex: 1;
}

.bg_white {
  background-color: $white !important;
  -webkit-overflow-scrolling: touch;
}

.hide {
  display: none;
}

.margin {
  margin: 0 auto;
}

.co_white {
  color: $white;
}

.t_align_l {
  text-align: left;
}

.text_al {
  text-align: center;
}

.t_align_r {
  text-align: right;
}

.border_gr {
  border: 2rpx solid $grey;
  border-radius: 10rpx;
}

.w100 {
  width: 100%;
  box-sizing: border-box;
}

.font_blod {
  font-weight: bold;
}
/** 公共类名结束 **/

.redeem-modal {
  .at-modal__overlay {
    background-color: rgba(0, 0, 0, 0.7);
  }
  .at-modal__container {
    padding-top: 90px;
    box-sizing: border-box;
    width: 580px;
    border-radius: 30px;
    overflow: auto;
    background: transparent;
    .close-img {
      width: 60px;
      height: 60px;
      position: absolute;
      top: 0;
      right: 0;
    }
    .at-modal__content {
      padding: 0;
      overflow: auto;
      background: transparent;
      position: relative;
      .redeem-img {
        width: 100%;
        // height: 519px;
        vertical-align: top;
      }
      .redeem-btn {
        width: 380px;
        height: 90px;
        position: absolute;
        left: 50%;
        bottom: 120px;
        transform: translateX(-50%);
      }
      .footer {
        height: 168px;
        background: $white;
        display: flex;
        align-items: center;
        justify-content: center;
        .btn {
          width: 500px;
          height: 88px;
          background: $orange;
          border-radius: 44px;
          font-size: 36px;
          font-weight: bold;
          color: $white;
          line-height: 88px;
          text-align: center;
          margin: 0;
        }
      }
    }
  }
}
