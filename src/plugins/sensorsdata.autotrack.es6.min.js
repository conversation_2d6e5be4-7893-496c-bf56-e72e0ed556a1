var sa = {},
  _ = {};
(sa.para = {
  name: 'sensors',
  server_url: '',
  max_string_length: 500,
  datasend_timeout: 25e3,
  autoTrack: {
    appLaunch: !0,
    appShow: !0,
    appHide: !0,
    pageShow: !0,
    mpClick: !0,
  },
  show_log: !1,
  preset_properties: {},
  framework: {},
  batch_send: !0,
}),
  (sa.setPara = function(e) {
    (sa.para = _.extend2Lev(sa.para, e)),
      _.isObject(sa.para.register) &&
        _.extend(_.info.properties, sa.para.register),
      sa.para.name || (sa.para.name = 'sensors');
    var t = sa.para.server_url;
    if (t) {
      -1 !== t.indexOf('/sa.gif') &&
        (sa.para.server_url = t.replace('/sa.gif', '/sa')),
        (sa.para.preset_properties = _.isObject(sa.para.preset_properties)
          ? sa.para.preset_properties
          : {});
      var a = { send_timeout: 6e3, max_length: 6 };
      (e && e.datasend_timeout) ||
        (sa.para.batch_send && (sa.para.datasend_timeout = 1e4)),
        !0 === sa.para.batch_send
          ? (sa.para.batch_send = _.extend({}, a))
          : _.isObject(sa.para.batch_send)
          ? (sa.para.batch_send = _.extend({}, a, sa.para.batch_send))
          : (sa.para.batch_send = !1);
    } else
      console.log(
        '\u8bf7\u4f7f\u7528 setPara() \u65b9\u6cd5\u8bbe\u7f6e server_url \u6570\u636e\u63a5\u6536\u5730\u5740,\u8be6\u60c5\u53ef\u67e5\u770bhttps://www.sensorsdata.cn/manual/mp_sdk_new.html#112-%E5%BC%95%E5%85%A5%E5%B9%B6%E9%85%8D%E7%BD%AE%E5%8F%82%E6%95%B0',
      );
  }),
  (sa.getServerUrl = function() {
    return sa.para.server_url;
  }),
  (sa._queue = []),
  (sa.getSystemInfoComplete = !1);
var ArrayProto = Array.prototype,
  FuncProto = Function.prototype,
  ObjProto = Object.prototype,
  slice = ArrayProto.slice,
  toString = ObjProto.toString,
  hasOwnProperty = ObjProto.hasOwnProperty,
  LIB_VERSION = '1.1.6',
  LIB_NAME = 'AlipayMini',
  source_channel_standard =
    'utm_source utm_medium utm_campaign utm_content utm_term',
  latest_source_channel = [
    '$latest_utm_source',
    '$latest_utm_medium',
    '$latest_utm_campaign',
    '$latest_utm_content',
    '$latest_utm_term',
    '$latest_sa_utm',
  ],
  sa_referrer = '\u76f4\u63a5\u6253\u5f00';
sa.lib_version = LIB_VERSION;
var is_first_launch = !1,
  mpshow_time = null,
  first_show_page = !1,
  logger = 'object' == typeof logger ? logger : {};
(logger.info = function() {
  if (sa.para.show_log && 'object' == typeof console && console.log)
    try {
      return console.log.apply(console, arguments);
    } catch (e) {
      console.log(arguments[0]);
    }
}),
  (function() {
    FuncProto.bind;
    var e = ArrayProto.forEach,
      t = ArrayProto.indexOf,
      a = Array.isArray,
      r = {},
      n = (_.each = function(t, a, n) {
        if (null == t) return !1;
        if (e && t.forEach === e) t.forEach(a, n);
        else if (t.length === +t.length) {
          for (var s = 0, i = t.length; s < i; s++)
            if (s in t && a.call(n, t[s], s, t) === r) return !1;
        } else
          for (var o in t)
            if (hasOwnProperty.call(t, o) && a.call(n, t[o], o, t) === r)
              return !1;
      });
    (_.logger = logger),
      (_.extend = function(e) {
        return (
          n(slice.call(arguments, 1), function(t) {
            for (var a in t) void 0 !== t[a] && (e[a] = t[a]);
          }),
          e
        );
      }),
      (_.extend2Lev = function(e) {
        return (
          n(slice.call(arguments, 1), function(t) {
            for (var a in t)
              void 0 !== t[a] &&
                null !== t[a] &&
                (_.isObject(t[a]) && _.isObject(e[a])
                  ? _.extend(e[a], t[a])
                  : (e[a] = t[a]));
          }),
          e
        );
      }),
      (_.coverExtend = function(e) {
        return (
          n(slice.call(arguments, 1), function(t) {
            for (var a in t)
              void 0 !== t[a] && void 0 === e[a] && (e[a] = t[a]);
          }),
          e
        );
      }),
      (_.isArray =
        a ||
        function(e) {
          return '[object Array]' === toString.call(e);
        }),
      (_.isFunction = function(e) {
        try {
          return /^\s*\bfunction\b/.test(e);
        } catch (e) {
          return !1;
        }
      }),
      (_.isArguments = function(e) {
        return !(!e || !hasOwnProperty.call(e, 'callee'));
      }),
      (_.toArray = function(e) {
        return e
          ? e.toArray
            ? e.toArray()
            : _.isArray(e)
            ? slice.call(e)
            : _.isArguments(e)
            ? slice.call(e)
            : _.values(e)
          : [];
      }),
      (_.values = function(e) {
        var t = [];
        return null == e
          ? t
          : (n(e, function(e) {
              t[t.length] = e;
            }),
            t);
      }),
      (_.include = function(e, a) {
        var s = !1;
        return null == e
          ? s
          : t && e.indexOf === t
          ? -1 != e.indexOf(a)
          : (n(e, function(e) {
              if (s || (s = e === a)) return r;
            }),
            s);
      });
  })(),
  (_.trim = function(e) {
    return e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, '');
  }),
  (_.isObject = function(e) {
    return '[object Object]' == toString.call(e) && null != e;
  }),
  (_.isEmptyObject = function(e) {
    if (_.isObject(e)) {
      for (var t in e) if (hasOwnProperty.call(e, t)) return !1;
      return !0;
    }
    return !1;
  }),
  (_.isUndefined = function(e) {
    return void 0 === e;
  }),
  (_.isString = function(e) {
    return '[object String]' == toString.call(e);
  }),
  (_.isDate = function(e) {
    return '[object Date]' == toString.call(e);
  }),
  (_.isBoolean = function(e) {
    return '[object Boolean]' == toString.call(e);
  }),
  (_.isNumber = function(e) {
    return '[object Number]' == toString.call(e) && /[\d\.]+/.test(String(e));
  }),
  (_.isJSONString = function(e) {
    try {
      JSON.parse(e);
    } catch (e) {
      return !1;
    }
    return !0;
  }),
  (_.decodeURIComponent = function(e) {
    var t = '';
    try {
      t = decodeURIComponent(e);
    } catch (a) {
      t = e;
    }
    return t;
  }),
  (_.encodeDates = function(e) {
    return (
      _.each(e, function(t, a) {
        _.isDate(t)
          ? (e[a] = _.formatDate(t))
          : _.isObject(t) && (e[a] = _.encodeDates(t));
      }),
      e
    );
  }),
  (_.formatDate = function(e) {
    function t(e) {
      return e < 10 ? '0' + e : e;
    }
    return (
      e.getFullYear() +
      '-' +
      t(e.getMonth() + 1) +
      '-' +
      t(e.getDate()) +
      ' ' +
      t(e.getHours()) +
      ':' +
      t(e.getMinutes()) +
      ':' +
      t(e.getSeconds()) +
      '.' +
      t(e.getMilliseconds())
    );
  }),
  (_.searchObjDate = function(e) {
    _.isObject(e) &&
      _.each(e, function(t, a) {
        _.isObject(t)
          ? _.searchObjDate(e[a])
          : _.isDate(t) && (e[a] = _.formatDate(t));
      });
  }),
  (_.formatString = function(e) {
    return e.length > sa.para.max_string_length
      ? (logger.info(
          '\u5b57\u7b26\u4e32\u957f\u5ea6\u8d85\u8fc7\u9650\u5236\uff0c\u5df2\u7ecf\u505a\u622a\u53d6--' +
            e,
        ),
        e.slice(0, sa.para.max_string_length))
      : e;
  }),
  (_.searchObjString = function(e) {
    _.isObject(e) &&
      _.each(e, function(t, a) {
        _.isObject(t)
          ? _.searchObjString(e[a])
          : _.isString(t) && (e[a] = _.formatString(t));
      });
  }),
  (_.parseSuperProperties = function(e) {
    _.isObject(e) &&
      (_.each(e, function(t, a) {
        if (_.isFunction(t))
          try {
            (e[a] = t()),
              _.isFunction(e[a]) &&
                (logger.info(
                  '\u60a8\u7684\u5c5e\u6027- ' +
                    a +
                    ' \u683c\u5f0f\u4e0d\u6ee1\u8db3\u8981\u6c42\uff0c\u6211\u4eec\u5df2\u7ecf\u5c06\u5176\u5220\u9664',
                ),
                delete e[a]);
          } catch (t) {
            delete e[a],
              logger.info(
                '\u60a8\u7684\u5c5e\u6027- ' +
                  a +
                  ' \u629b\u51fa\u4e86\u5f02\u5e38\uff0c\u6211\u4eec\u5df2\u7ecf\u5c06\u5176\u5220\u9664',
              );
          }
      }),
      _.strip_sa_properties(e));
  }),
  (_.unique = function(e) {
    for (var t, a = [], r = {}, n = 0; n < e.length; n++)
      (t = e[n]) in r || ((r[t] = !0), a.push(t));
    return a;
  }),
  (_.strip_sa_properties = function(e) {
    return _.isObject(e)
      ? (_.each(e, function(t, a) {
          if (_.isArray(t)) {
            var r = [];
            _.each(t, function(e) {
              _.isString(e)
                ? r.push(e)
                : logger.info(
                    '\u60a8\u7684\u6570\u636e-',
                    t,
                    '\u7684\u6570\u7ec4\u91cc\u7684\u503c\u5fc5\u987b\u662f\u5b57\u7b26\u4e32,\u5df2\u7ecf\u5c06\u5176\u5220\u9664',
                  );
            }),
              0 !== r.length
                ? (e[a] = r)
                : (delete e[a],
                  logger.info(
                    '\u5df2\u7ecf\u5220\u9664\u7a7a\u7684\u6570\u7ec4',
                  ));
          }
          _.isString(t) ||
            _.isNumber(t) ||
            _.isDate(t) ||
            _.isBoolean(t) ||
            _.isArray(t) ||
            (logger.info(
              '\u60a8\u7684\u6570\u636e-',
              t,
              '-\u683c\u5f0f\u4e0d\u6ee1\u8db3\u8981\u6c42\uff0c\u6211\u4eec\u5df2\u7ecf\u5c06\u5176\u5220\u9664',
            ),
            delete e[a]);
        }),
        e)
      : e;
  }),
  (_.strip_empty_properties = function(e) {
    var t = {};
    return (
      _.each(e, function(e, a) {
        null != e && (t[a] = e);
      }),
      t
    );
  }),
  (_.utf8Encode = function(e) {
    var t,
      a,
      r,
      n,
      s = '';
    for (
      t = a = 0,
        r = (e = (e + '').replace(/\r\n/g, '\n').replace(/\r/g, '\n')).length,
        n = 0;
      n < r;
      n++
    ) {
      var i = e.charCodeAt(n),
        o = null;
      i < 128
        ? a++
        : (o =
            i > 127 && i < 2048
              ? String.fromCharCode((i >> 6) | 192, (63 & i) | 128)
              : String.fromCharCode(
                  (i >> 12) | 224,
                  ((i >> 6) & 63) | 128,
                  (63 & i) | 128,
                )),
        null !== o &&
          (a > t && (s += e.substring(t, a)), (s += o), (t = a = n + 1));
    }
    return a > t && (s += e.substring(t, e.length)), s;
  }),
  (_.base64Encode = function(e) {
    var t,
      a,
      r,
      n,
      s,
      i = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',
      o = 0,
      c = 0,
      u = '',
      p = [];
    if (!e) return e;
    e = _.utf8Encode(e);
    do {
      (t =
        ((s =
          (e.charCodeAt(o++) << 16) |
          (e.charCodeAt(o++) << 8) |
          e.charCodeAt(o++)) >>
          18) &
        63),
        (a = (s >> 12) & 63),
        (r = (s >> 6) & 63),
        (n = 63 & s),
        (p[c++] = i.charAt(t) + i.charAt(a) + i.charAt(r) + i.charAt(n));
    } while (o < e.length);
    switch (((u = p.join('')), e.length % 3)) {
      case 1:
        u = u.slice(0, -2) + '==';
        break;
      case 2:
        u = u.slice(0, -1) + '=';
    }
    return u;
  }),
  (_.info = {
    currentProps: {},
    properties: { $lib: LIB_NAME, $lib_version: String(LIB_VERSION) },
    getSystem: function() {
      var e = this.properties,
        t = !0;
      function a() {
        t &&
          ((t = !0),
          my.getSystemInfo({
            success: function(t) {
              var a, r;
              (e.$model = t.model),
                (e.$screen_width = Number(t.screenWidth)),
                (e.$screen_height = Number(t.screenHeight)),
                (e.$os =
                  ((a = t.platform),
                  'ios' === (r = a.toLowerCase())
                    ? 'iOS'
                    : 'android' === r
                    ? 'Android'
                    : a)),
                (e.$os_version =
                  t.system.indexOf(' ') > -1
                    ? t.system.split(' ')[1]
                    : t.system),
                (e.$manufacturer = t.brand);
            },
            complete: function() {
              var t,
                a = new Date().getTimezoneOffset();
              my.getAppIdSync && (t = my.getAppIdSync().appId),
                t && (e.$app_id = t),
                _.isNumber(a) && (e.$timezone_offset = a),
                (sa.getSystemInfoComplete = !0),
                sa.checkIsComplete();
            },
          }));
      }
      my.getNetworkType({
        success: function(t) {
          (e.$network_type = t.networkType), a();
        },
        complete: function() {
          a();
        },
      });
    },
    setStatusComplete: function() {
      if (sa.getSystemInfoComplete) return !1;
      (sa.getSystemInfoComplete = !0),
        sa._queue.length > 0 &&
          (_.each(sa._queue, function(e) {
            sa.prepareData.apply(sa, slice.call(e));
          }),
          (sa._queue = []));
    },
  }),
  (_.getIsFirstDay = function() {
    return (
      'object' == typeof sa.store._state &&
      'number' == typeof sa.store._state.first_visit_day_time &&
      sa.store._state.first_visit_day_time > new Date().getTime()
    );
  }),
  (sa._ = _),
  (sa.prepareData = function(e, t) {
    if (!sa.isComplete) return sa._queue.push(arguments), !1;
    var a = {
      distinct_id: this.store.getDistinctId(),
      lib: {
        $lib: LIB_NAME,
        $lib_method: 'code',
        $lib_version: String(LIB_VERSION),
      },
      properties: {},
    };
    _.extend(a, e),
      _.isObject(e.properties) &&
        !_.isEmptyObject(e.properties) &&
        _.extend(a.properties, e.properties),
      (e.type && 'profile' === e.type.slice(0, 7)) ||
        ((a._track_id = Number(
          String(Math.random()).slice(2, 5) +
            String(Math.random()).slice(2, 4) +
            String(Date.now()).slice(-4),
        )),
        (a.properties = _.extend(
          {},
          _.info.properties,
          sa.store.getProps(),
          _.info.currentProps,
          a.properties,
        )),
        'track' === e.type && (a.properties.$is_first_day = _.getIsFirstDay())),
      a.properties.$time && _.isDate(a.properties.$time)
        ? ((a.time = 1 * a.properties.$time), delete a.properties.$time)
        : (a.time = 1 * new Date()),
      _.parseSuperProperties(a.properties),
      _.searchObjDate(a),
      _.searchObjString(a),
      sa.para.batch_send ? sa.sendStrategy.send(a) : sa.send(a);
  }),
  (sa.checkIsComplete = function() {
    (this.isComplete = !1),
      this.getSystemInfoComplete &&
        this.hasInit &&
        ((this.isComplete = !0),
        sa._queue.length > 0 &&
          (_.each(sa._queue, function(e) {
            sa.prepareData.apply(sa, slice.call(e));
          }),
          (sa._queue = [])));
  }),
  (sa.store = {
    getUUID: function() {
      return (
        Date.now() +
        '-' +
        Math.floor(1e7 * Math.random()) +
        '-' +
        Math.random()
          .toString(16)
          .replace('.', '') +
        '-' +
        String(31242 * Math.random())
          .replace('.', '')
          .slice(0, 8)
      );
    },
    setStorage: function() {},
    getStorage: function() {
      return my.getStorageSync({ key: 'sensorsdata2015_zfb' }) || {};
    },
    _state: {},
    mem: {
      mdata: [],
      getLength: function() {
        return this.mdata.length;
      },
      add: function(e) {
        this.mdata.push(e);
      },
      clear: function(e) {
        this.mdata.splice(0, e);
      },
    },
    toState: function(e) {
      'object' == typeof e && e.distinct_id
        ? (this._state = e)
        : this.set('distinct_id', this.getUUID());
    },
    getFirstId: function() {
      return this._state.first_id;
    },
    getDistinctId: function() {
      return this._state.distinct_id;
    },
    getProps: function() {
      return this._state.props || {};
    },
    setProps: function(e, t) {
      var a = this._state.props || {};
      t ? this.set('props', e) : (_.extend(a, e), this.set('props', a));
    },
    set: function(e, t) {
      var a = {};
      for (var r in ('string' == typeof e
        ? (a[e] = t)
        : 'object' == typeof e && (a = e),
      (this._state = this._state || {}),
      a))
        this._state[r] = a[r];
      this.save();
    },
    change: function(e, t) {
      this._state[e] = t;
    },
    save: function() {
      my.setStorageSync({ key: 'sensorsdata2015_zfb', data: this._state });
    },
    init: function() {
      var e = this.getStorage().data;
      if (e) this.toState(e);
      else {
        is_first_launch = !0;
        var t = new Date(),
          a = t.getTime();
        t.setHours(23),
          t.setMinutes(59),
          t.setSeconds(60),
          sa.setOnceProfile({ $first_visit_time: new Date() }),
          this.set({
            distinct_id: this.getUUID(),
            first_visit_time: a,
            first_visit_day_time: t.getTime(),
          });
      }
    },
  }),
  (sa.setProfile = function(e, t) {
    sa.prepareData({ type: 'profile_set', properties: e }, t);
  }),
  (sa.setOnceProfile = function(e, t) {
    sa.prepareData({ type: 'profile_set_once', properties: e }, t);
  }),
  (sa.track = function(e, t, a) {
    this.prepareData({ type: 'track', event: e, properties: t }, a);
  }),
  (sa.identify = function(e, t) {
    if ('number' == typeof e) e = String(e);
    else if ('string' != typeof e) return !1;
    var a = sa.store.getFirstId();
    !0 === t
      ? a
        ? sa.store.set('first_id', e)
        : sa.store.set('distinct_id', e)
      : a
      ? sa.store.change('first_id', e)
      : sa.store.change('distinct_id', e);
  }),
  (sa.trackSignup = function(e, t, a, r) {
    var n = sa.store.getFirstId() || sa.store.getDistinctId();
    sa.store.set('distinct_id', e),
      sa.prepareData(
        {
          original_id: n,
          distinct_id: e,
          type: 'track_signup',
          event: t,
          properties: a,
        },
        r,
      );
  }),
  (sa.registerApp = function(e) {
    _.isObject(e) &&
      !_.isEmptyObject(e) &&
      (_.info.currentProps = _.extend(_.info.currentProps, e));
  }),
  (sa.clearAppRegister = function(e) {
    _.isArray(e) &&
      _.each(_.info.currentProps, function(t, a) {
        _.include(e, a) && delete _.info.currentProps[a];
      });
  }),
  (sa.clearAllRegister = function() {
    sa.store.setProps({}, !0);
  }),
  (sa.login = function(e) {
    var t = sa.store.getFirstId(),
      a = sa.store.getDistinctId();
    e !== a &&
      (t
        ? sa.trackSignup(e, '$SignUp')
        : (sa.store.set('first_id', a), sa.trackSignup(e, '$SignUp')));
  }),
  (sa.logout = function(e) {
    var t = sa.store.getFirstId();
    t
      ? (sa.store.set('first_id', ''),
        !0 === e
          ? sa.store.set('distinct_id', sa.store.getUUID())
          : sa.store.set('distinct_id', t))
      : logger.info('\u6ca1\u6709first_id\uff0clogout\u5931\u8d25');
  }),
  (sa.getAnonymousID = function() {
    if (!_.isEmptyObject(sa.store._state))
      return sa.store._state.first_id || sa.store._state.distinct_id;
    logger.info('\u8bf7\u5148\u521d\u59cb\u5316SDK');
  }),
  (sa.getLocation = function() {
    my.getSetting({
      success: function(e) {
        if (!e.authSetting.location) return !1;
        my.getLocation({
          success: function(e) {
            sa.registerApp({
              $latitude: e.latitude * Math.pow(10, 6),
              $longitude: e.longitude * Math.pow(10, 6),
            });
          },
          fail: function(e) {
            console.log('\u83b7\u53d6\u4f4d\u7f6e\u5931\u8d25\uff1a', e);
          },
        });
      },
    });
  }),
  (sa.initial = function() {
    this._.info.getSystem(),
      this.store.init(),
      _.isObject(this.para.register) &&
        (_.info.properties = _.extend(_.info.properties, this.para.register));
  }),
  (sa.init = function() {
    if (!0 === this.hasInit) return !1;
    (this.hasInit = !0),
      sa.para.batch_send && sa.sendStrategy.init(),
      sa.checkIsComplete();
  }),
  (sa.getPresetProperties = function() {
    if (_.info && _.info.properties && _.info.properties.$lib) {
      var e = {};
      _.each(_.info.currentProps, function(t, a) {
        0 === a.indexOf('$') && (e[a] = t);
      });
      var t = _.extend(
        e,
        { $url_path: _.getCurrentPath(), $is_first_day: _.getIsFirstDay() },
        _.info.properties,
      );
      return delete t.$lib, t;
    }
    return {};
  }),
  (sa.send = function(e) {
    var t = '';
    (e._nocache = (
      String(Math.random()) +
      String(Math.random()) +
      String(Math.random())
    ).slice(2, 15)),
      logger.info(e),
      (e._flush_time = Date.now()),
      (e = JSON.stringify(e)),
      (t =
        -1 !== sa.para.server_url.indexOf('?')
          ? sa.para.server_url +
            '&data=' +
            encodeURIComponent(_.base64Encode(e))
          : sa.para.server_url +
            '?data=' +
            encodeURIComponent(_.base64Encode(e)));
    !(function() {
      if (my.canIUse('request'))
        var e = my.request({
            url: t,
            dataType: 'text',
            method: 'GET',
            headers: { 'content-type': 'application/x-www-form-urlencoded' },
            complete: function() {
              a && clearTimeout(a);
            },
          }),
          a = setTimeout(function() {
            _.isObject(e) && _.isFunction(e.abort) && e.abort();
          }, sa.para.datasend_timeout);
      else
        var r = my.httpRequest({
            url: t,
            dataType: 'text',
            method: 'GET',
            complete: function() {
              a && clearTimeout(n);
            },
          }),
          n = setTimeout(function() {
            _.isObject(r) && _.isFunction(r.abort) && r.abort();
          }, sa.para.datasend_timeout);
    })();
  }),
  (sa.sendStrategy = {
    dataHasSend: !0,
    syncStorage: !1,
    is_first_batch_write: !0,
    failTime: 0,
    init: function() {
      my.getStorage({
        key: 'sensors_prepare_data',
        complete: function(e) {
          var t = e.data && _.isArray(e.data) ? e.data : [];
          (sa.store.mem.mdata = t.concat(sa.store.mem.mdata)),
            (sa.sendStrategy.syncStorage = !0);
        },
      }),
        this.batchInterval();
    },
    onAppHide: function() {
      sa.para.batch_send && this.batchSend();
    },
    send: function(e) {
      if (!sa.para.server_url) return !1;
      (this.dataHasChange = !0),
        sa.store.mem.getLength() >= 500 &&
          (logger.info(
            '\u6570\u636e\u91cf\u5b58\u50a8\u8fc7\u5927\uff0c\u6709\u5f02\u5e38',
          ),
          sa.store.mem.mdata.shift()),
        logger.info(e),
        sa.store.mem.add(e),
        sa.store.mem.getLength() >= sa.para.batch_send.max_length &&
          this.batchSend();
    },
    batchWrite: function() {
      var e = this;
      this.dataHasChange &&
        (this.is_first_batch_write &&
          ((this.is_first_batch_write = !1),
          setTimeout(function() {
            e.batchSend();
          }, 1e3)),
        this.syncStorage &&
          (my.setStorageSync({
            key: 'sensors_prepare_data',
            data: sa.store.mem.mdata,
          }),
          (this.dataHasChange = !1)));
    },
    batchInterval() {
      var e = this;
      !(function t() {
        setTimeout(function() {
          e.batchSend(), t();
        }, sa.para.batch_send.send_timeout * Math.pow(2, e.failTime));
      })(),
        (function t() {
          setTimeout(function() {
            e.batchWrite(), t();
          }, 500);
        })();
    },
    batchSend() {
      if (this.dataHasSend) {
        var e,
          t,
          a = this,
          r = sa.store.mem.mdata;
        if (
          ((e = r.length >= 100 ? r.slice(0, 100) : r),
          (t = e.length),
          _.isArray(e) && e.length > 0)
        ) {
          this.dataHasSend = !1;
          var n = Date.now();
          e.forEach(function(e) {
            e._flush_time = n;
          }),
            (e = JSON.stringify(e)),
            (e = 'data_list=' + encodeURIComponent(_.base64Encode(e))),
            my.request({
              url: sa.para.server_url,
              method: 'POST',
              data: e,
              dataType: 'text',
              success: function(e) {
                a.batchRemove(t);
              },
              fail: function(e) {
                a.sendFail();
              },
            });
        }
      }
    },
    batchRemove(e) {
      (this.dataHasSend = !0),
        (this.dataHasChange = !0),
        sa.store.mem.clear(e),
        this.batchWrite(),
        (this.failTime = 0);
    },
    sendFail() {
      (this.dataHasSend = !0), this.failTime++;
    },
  }),
  (_.getPath = function(e) {
    return (e =
      'string' == typeof e ? e.replace(/^\//, '') : '\u53d6\u503c\u5f02\u5e38');
  }),
  (_.getQueryParam = function(e, t) {
    var a = new RegExp('[\\?&]' + t + '=([^&#]*)').exec(e);
    return null === a || (a && 'string' != typeof a[1] && a[1].length)
      ? ''
      : _.decodeURIComponent(a[1]);
  }),
  (_.getMPScene = function(e) {
    return 'number' == typeof e || ('string' == typeof e && '' !== e)
      ? (e = 'ali-' + String(e))
      : '\u672a\u53d6\u5230\u503c';
  }),
  (_.getQuery = function(e) {
    var t = {};
    if (
      (e &&
        _.isObject(e.query) &&
        ((t = _.extend({}, e.query)),
        e.query.qrCode &&
          _.extend(t, _.getObjFromQuery(_.decodeURIComponent(e.query.qrCode)))),
      e && _.isObject(e.referrerInfo) && e.referrerInfo.extraData)
    ) {
      var a = {};
      _.isObject(e.referrerInfo.extraData) &&
      !_.isEmptyObject(e.referrerInfo.extraData)
        ? (a = e.referrerInfo.extraData)
        : _.isJSONString(e.referrerInfo.extraData) &&
          (a = JSON.parse(e.referrerInfo.extraData)),
        _.extend(t, a);
    }
    return t;
  }),
  (_.setUtm = function(e, t) {
    var a = _.getQuery(e),
      r = {},
      n = _.getCustomUtmFromQuery(a, '$', '_', '$'),
      s = _.getCustomUtmFromQuery(a, '$latest_', '_latest_', '$latest_');
    return (r.pre1 = n), (r.pre2 = s), _.extend(t, r.pre1), r;
  }),
  (_.getObjFromQuery = function(e) {
    var t = e.split('?'),
      a = {};
    return t && t[1]
      ? (_.each(t[1].split('&'), function(e) {
          var t = e.split('=');
          t[0] && t[1] && (a[t[0]] = t[1]);
        }),
        a)
      : {};
  }),
  (_.getCustomUtmFromQuery = function(e, t, a, r) {
    if (!_.isObject(e)) return {};
    var n = {};
    if (e.sa_utm)
      for (var s in e)
        'sa_utm' !== s
          ? _.include(sa.para.source_channel, s) && (n[a + s] = e[s])
          : (n[r + s] = e[s]);
    else
      for (var s in e)
        -1 === (' ' + source_channel_standard + ' ').indexOf(' ' + s + ' ')
          ? _.include(sa.para.source_channel, s) && (n[a + s] = e[s])
          : (n[t + s] = e[s]);
    return n;
  }),
  (_.existLatestUtm = function() {
    var e = !1;
    return (
      _.each(latest_source_channel, function(t, a) {
        _.info.currentProps[t] && (e = !0);
      }),
      e
    );
  }),
  (_.setQuery = function(e, t) {
    if (e && _.isObject(e) && !_.isEmptyObject(e)) {
      var a = [];
      return (
        _.each(e, function(e, r) {
          ('q' === r && _.isString(e) && 0 === e.indexOf('http')) ||
            (t
              ? a.push(r + '=' + e)
              : a.push(r + '=' + _.decodeURIComponent(e)));
        }),
        a.join('&')
      );
    }
    return '';
  }),
  (_.setLatestChannel = function(e) {
    _.isEmptyObject(e) ||
      ((function(e, t) {
        var a = !1;
        for (var r in t) e[t[r]] && (a = !0);
        return a;
      })(e, latest_source_channel) &&
        sa.clearAppRegister(latest_source_channel),
      sa.registerApp(e));
  }),
  (_.getCurrentPath = function() {
    var e = '\u672a\u53d6\u5230';
    try {
      var t = getCurrentPages();
      e = t[t.length - 1].route;
    } catch (e) {
      logger.info(e);
    }
    return e;
  }),
  (_.getIsFirstDay = function() {
    return (
      'object' == typeof sa.store._state &&
      'number' == typeof sa.store._state.first_visit_day_time &&
      sa.store._state.first_visit_day_time > new Date().getTime()
    );
  }),
  (sa.appLaunch = function(e, t, a) {
    (t && _.isObject(t)) || (t = {});
    var r = {};
    e && e.path && (r.$url_path = _.getPath(e.path));
    var n = _.setUtm(e, r);
    is_first_launch
      ? ((r.$is_first_time = !0),
        _.isEmptyObject(n.pre1) || sa.setOnceProfile(n.pre1))
      : (r.$is_first_time = !1),
      _.isEmptyObject(n.pre2) || _.setLatestChannel(n.pre2);
    var s = _.getMPScene(e.scene);
    s && ((r.$scene = s), sa.registerApp({ $latest_scene: r.$scene })),
      (r.$url_query = _.setQuery(e.query)),
      _.extend(r, t),
      (!a || (sa.para.autoTrack && sa.para.autoTrack.appLaunch)) &&
        sa.track('$MPLaunch', r);
  }),
  (sa.appShow = function(e, t, a) {
    (t && _.isObject(t)) || (t = {});
    var r = {};
    (mpshow_time = new Date().getTime()),
      (first_show_page = !0),
      e && e.path && (r.$url_path = _.getPath(e.path));
    var n = _.setUtm(e, r);
    _.isEmptyObject(n.pre2) || _.setLatestChannel(n.pre2),
      !0 === sa.para.preset_properties.location && sa.getLocation();
    var s = _.getMPScene(e.scene);
    s && ((r.$scene = s), sa.registerApp({ $latest_scene: r.$scene })),
      (r.$url_query = _.setQuery(e.query)),
      _.extend(r, t),
      (!a || (sa.para.autoTrack && sa.para.autoTrack.appShow)) &&
        sa.track('$MPShow', r);
  }),
  (sa.appHide = function(e, t) {
    (e && _.isObject(e)) || (e = {});
    var a = new Date().getTime(),
      r = {};
    (r.$url_path = _.getCurrentPath()),
      mpshow_time &&
        a - mpshow_time > 0 &&
        (a - mpshow_time) / 36e5 < 24 &&
        (r.event_duration = (a - mpshow_time) / 1e3),
      _.extend(r, e),
      (!t || (sa.para.autoTrack && sa.para.autoTrack.appHide)) &&
        sa.track('$MPHide', r),
      sa.sendStrategy.onAppHide();
  }),
  (sa.pageShow = function(e, t) {
    var a = {},
      r = _.getCurrentPath();
    a.$url_path = r;
    var n = getCurrentPages(),
      s = n[n.length - 1];
    try {
      _.isObject(s)
        ? ((a.$url_query = s.sensors_mp_url_query
            ? s.sensors_mp_url_query
            : ''),
          first_show_page &&
            ((first_show_page = !1),
            !_.existLatestUtm() &&
              s.utm &&
              _.isObject(s.utm.pre2) &&
              sa.registerApp(s.utm.pre2)),
          s.utm && _.isObject(s.utm.pre1) && _.extend(a, s.utm.pre1))
        : (a.$url_query = '');
    } catch (e) {
      logger.info(e);
    }
    _.extend(a, e),
      (!t || (sa.para.autoTrack && sa.para.autoTrack.pageShow)) &&
        sa.track('$MPViewScreen', a);
  }),
  (sa.pageLoad = function(e, t) {
    if (t && _.isObject(t) && _.isObject(e))
      try {
        e.sensors_mp_url_query = _.setQuery(t);
        var a = {};
        (a.pre1 = _.getCustomUtmFromQuery(t, '$', '_', '$')),
          (a.pre2 = _.getCustomUtmFromQuery(
            t,
            '$latest_',
            '_latest_',
            '$latest_',
          )),
          (e.utm = a);
      } catch (e) {
        logger.info(e);
      }
  }),
  (sa.quick = function() {
    var e = arguments[0],
      t = arguments[1],
      a = arguments[2],
      r = _.isObject(a) ? a : {};
    'appLaunch' === e || 'appShow' === e
      ? t
        ? sa[e](t, r)
        : logger.info(
            'App\u7684launch\u548cshow\uff0c\u5728sensors.quick\u7b2c\u4e8c\u4e2a\u53c2\u6570\u5fc5\u987b\u4f20\u5165App\u7684options\u53c2\u6570',
          )
      : 'appHide' === e && ((r = _.isObject(t) ? t : {}), sa[e](r));
  }),
  Object.defineProperty($global, 'saAlipay', {
    enumerable: !1,
    configurable: !1,
    value: {},
  });
var mpHooks = {
  onLoad: 1,
  onShow: 1,
  onHide: 1,
  onReady: 1,
  onUnload: 1,
  onTitleClick: 1,
  onPullDownRefresh: 1,
  onReachBottom: 1,
  onPageScroll: 1,
  onResize: 1,
  onTabItemTap: 1,
  onOptionMenuClick: 1,
  onPopMenuClick: 1,
  onPullIntercept: 1,
  onAddToFavorites: 1,
  onShareAppMessage: 1,
  onShareTimeline: 1,
  eventHandler: 1,
  data: 1,
};
function click_proxy(e, t) {
  var a = e[t];
  e[t] = function() {
    var e = a.apply(this, arguments),
      t = {},
      r = '';
    if (_.isObject(arguments[0])) {
      var n = arguments[0].currentTarget || {},
        s = arguments[0].target || {};
      if (
        _.isObject(sa.para.framework) &&
        _.isObject(sa.para.framework.taro) &&
        !sa.para.framework.taro.createApp &&
        s.id &&
        n.id &&
        s.id !== n.id
      )
        return e;
      var i = n.dataset || {};
      (r = arguments[0].type),
        (t.$url_path = _.getCurrentPath()),
        (t.$element_id = n.id),
        (t.$element_type = n.tagName),
        (t.$element_content = i.content),
        (t.$element_name = i.name);
    }
    if (r && _.isClick(r)) {
      if (
        sa.para.preset_events &&
        sa.para.preset_events.collect_element &&
        !1 === sa.para.preset_events.collect_element(arguments[0])
      )
        return e;
      sa.track('$MPClick', t);
    }
    return e;
  };
}
(_.getMethods = function(e) {
  var t = [];
  for (var a in e) 'function' != typeof e[a] || mpHooks[a] || t.push(a);
  return t;
}),
  (_.isClick = function(e) {
    return !!{ tap: 1, longTap: 1 }[e];
  }),
  ($global.saAlipay.App = function(e) {
    var t = e.onShow,
      a = e.onLaunch,
      r = e.onHide;
    return (
      (e.onShow = function() {
        t && t.apply(this, arguments), sa.appShow(arguments[0], {}, !0);
      }),
      (e.onLaunch = function() {
        (this[sa.para.name] = sa),
          a && a.apply(this, arguments),
          sa.appLaunch(arguments[0], {}, !0);
      }),
      (e.onHide = function() {
        r && r.apply(this, arguments), sa.appHide({}, !0);
      }),
      ($global.saAlipay.useApp = !0),
      App(e)
    );
  }),
  ($global.saAlipay.Page = function(e) {
    var t = sa.para.autoTrack && sa.para.autoTrack.mpClick && _.getMethods(e);
    if (t) for (var a = 0, r = t.length; a < r; a++) click_proxy(e, t[a]);
    var n = e.onShow,
      s = e.onLoad;
    return (
      (e.onShow = function() {
        n && n.apply(this, arguments), sa.pageShow({}, !0);
      }),
      (e.onLoad = function() {
        s && s.apply(this, arguments), sa.pageLoad(this, arguments[0]);
      }),
      ($global.saAlipay.usePage = !0),
      Page(e)
    );
  });
var oldApp = App;
App = function(e) {
  if ((oldApp.apply(this, arguments), $global.saAlipay.useApp)) return !1;
  var t = e.onShow,
    a = e.onLaunch,
    r = e.onHide;
  (e.onLaunch = function() {
    (this[sa.para.name] = sa),
      a && a.apply(this, arguments),
      sa.appLaunch(arguments[0], {}, !0);
  }),
    (e.onShow = function() {
      t && t.apply(this, arguments), sa.appShow(arguments[0], {}, !0);
    }),
    (e.onHide = function() {
      r && r.apply(this, arguments), sa.appHide({}, !0);
    });
};
var oldPage = Page;
(Page = function(e) {
  if ($global.saAlipay.usePage) return !1;
  var t = sa.para.autoTrack && sa.para.autoTrack.mpClick && _.getMethods(e);
  if (t) for (var a = 0, r = t.length; a < r; a++) click_proxy(e, t[a]);
  var n = e.onShow,
    s = e.onLoad;
  (e.onShow = function() {
    n && n.apply(this, arguments), sa.pageShow({}, !0);
  }),
    (e.onLoad = function() {
      s && s.apply(this, arguments), sa.pageLoad(this, arguments[0]);
    }),
    oldPage.apply(this, arguments);
}),
  sa.initial();
export default sa;
