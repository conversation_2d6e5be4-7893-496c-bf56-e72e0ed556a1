// 类型定义
interface WindowConfig {
  backgroundTextStyle: string;
  navigationBarTextStyle: string;
  navigationBarTitleText: string;
  navigationBarBackgroundColor: string;
}

interface PluginConfig {
  version: string;
  provider: string;
}

interface PageConfig {
  pages: string[];
  window: WindowConfig;
  plugins?: Record<string, PluginConfig>;
  lazyCodeLoading?: string;
  requiredPrivateInfos?: string[];
}

// 常量定义
const CONSTANTS = {
  PAGES: {
    THE_PAGE: 'pages/normalGroup/art/index', // 八折29
    SEN_PAGE: 'pages/newPage36/index', // 36元10日页面
  },
  ENV: {
    WECHAT: 'weapp',
    ALIPAY: 'alipay',
    BYTEDANCE: 'tt',
  },
  WINDOW: {
    backgroundTextStyle: 'light',
    navigationBarTextStyle: 'black',
    navigationBarTitleText: '小熊美术',
    navigationBarBackgroundColor: '#fff',
  },
};

// 环境变量处理函数
const isEnv = (env: string): boolean => process.env.APP_ENV === env;
const isEnvType = (type: string): boolean => process.env.TARO_ENV === type;
const isEnvIn = (envs: string[]): boolean =>
  envs.includes(process.env.APP_ENV || '');

// 微信小程序页面配置
const getWechatPages = (): string[] => {
  // 基础页面配置
  let pages = [
    'pages/groupbuy/index', // 36元10日粉色
    'pages/ninePointNine/index', // 9.9五日
    'pages/addcompany/index', // 添加企业微信
    'pages/commonClub/index', // 加群
    'pages/launch/follow/index', // 添加老师微信
    'pages/groupbuy/addTeacher/index', // 添加老师微信
    'pages/groupbuy/addAddress/index', // 添加地址
    'pages/couponDetail/index', // 亲友圈
    'pages/midCoupon/index', // 完课券
    'pages/zeroCoupon/index', // 完课券-0元券
    'pages/normalGroup/art/discount9_9/index', // 八折29 亲友券
    'pages/normalGroup/art/discount14_9/index', // 14.9元购买
    'pages/normalGroup/art/formh5twentynine/index', // h5跳转小程序29元界面
    'pages/normalGroup/art/only9/index', // 9.9元购买
    'pages/normalGroup/art/zero/index', // 0元购买
    'pages/normalGroup/calligraphy/index',
    'pages/calligraphyExperience/newPage', // 书法0.15节
    'pages/teacherStatic/index', // 添加老师
    'pages/calligraphyExperience/zeroWrite', // 书法0元五节
    // 好友助力页面
    'pages/assist/index', // 瓜分小熊币
    'pages/commonPublic/index', // 瓜分小熊币关注公众号
    'pages/art/twentynine/index', // 29元美术
    'pages/assist/assistResult', // 好友助力抽奖
    'pages/webview/index',
    'pages/addqy/index', // 0元黏土
    'pages/addqynew/index', // 成长训练营免费领
    'pages/benefitActivity/index', // 爱心工艺
    'pages/zeroArtBynow/index/index', // 0元成长训练营
    'pages/zeroArtBynow/onlyBuy/index', // 0元5天小画家训练营
    'pages/orderh5/index', // 404
    'pages/conversion/course/index', // 选择级别添加地址
    'pages/activities/reviewWorks/index', // 小画家年度回顾
    'pages/invitationCourtesy/index', // 邀请有礼
  ];

  // 根据环境变量调整页面配置
  if (!isEnv('weappV0')) {
    pages.unshift(CONSTANTS.PAGES.THE_PAGE);
  } else {
    pages = pages.filter(
      (item) =>
        item !== 'pages/invitationCourtesy/index' &&
        item !== 'pages/midCoupon/index',
    );
    pages.push(CONSTANTS.PAGES.THE_PAGE);
  }

  if (isEnv('weappV16')) {
    pages.unshift(CONSTANTS.PAGES.SEN_PAGE);
  } else {
    pages.push(CONSTANTS.PAGES.SEN_PAGE);
  }

  if (isEnv('weappV2')) {
    pages.unshift('pages/normalGroup/art/discount10_9/index');
    pages.unshift('pages/normalGroup/art/longIndex/index');
  }

  // 兑换码兑换
  if (isEnv('weappV13')) {
    pages = [
      'pages/conversion/index/index', // 兑换码兑换
      'pages/conversion/coalesce/index', // 兑换码兑换 选择级别
      'pages/conversion/course/index', // 兑换码兑换 级别和地址
      'pages/conversion/addteacher/index', // 添加老师
      'pages/conversion/agree/index', // 用户协议
    ];
  }

  // 天天领
  if (isEnv('weappV14')) {
    pages = [
      'pages/newInvitetask/index/index', // 天天领
      'pages/newInvitetask/list/index', // 天天领 列表
      'pages/invitetask/index/index', // 邀请0元领
      'pages/music/introduce/index', // 音乐0.1元
      'pages/music/addTeacher/index', // 音乐添加老师
      'pages/writeExperience/zeroNine', // 0.1书法
      'pages/invitetask/detail/index', // 天天领 详情
      'pages/invitetask/Intermediate/index', // 天天领取界面
      'pages/normalGroup/art/index',
      'pages/launch/follow/index',
      'pages/groupbuy/addTeacher/index',
      'pages/groupbuy/addAddress/index',
      'pages/orderh5/index', // 订单
      'pages/writeExperience/addTeacher/index', // 书法添加老师
    ];
  }

  // 周周有礼
  if (isEnv('weappV15')) {
    pages = [
      'pages/weeklyShare/home/<USER>',
      'pages/weeklyShare/historyTask/index',
      'pages/weeklyShare/results/index',
      'pages/weeklyShare/upload/index',
    ];
  }

  if (isEnv('weappV17')) {
    pages.unshift('pages/groupBuying/index9/index'); // 拼团
    pages = pages.concat([
      'pages/groupBuying/groupTeacherInfo/index',
      'pages/groupBuying/newIndex/index',
      'pages/groupBuying/index/index',
    ]);
  }

  // 书法相关
  if (isEnvIn(['weappV4', 'weappV5', 'weappV6', 'weappV7', 'weappV8'])) {
    pages = [
      'pages/calligraphyExperience/newPage',
      'pages/calligraphyExperience/index',
      'pages/addcompany/index',
      'pages/commonClub/index',
      'pages/launch/follow/index',
      'pages/groupbuy/addTeacher/index',
      'pages/groupbuy/addAddress/index',
      'pages/calligraphyExperience/zeroWrite',
      // 好友助力页面
      'pages/assist/index',
      'pages/commonPublic/index',
      'pages/assist/assistResult',
      'pages/webview/index',
      'pages/addqy/index',
      'pages/calligraphyExperience/addTeacher/index',
    ];
  }

  if (isEnv('weappV9')) {
    const _pages = JSON.parse(JSON.stringify(pages));
    const _in = _pages.findIndex((o) => o === 'pages/art/twentynine/index');
    _pages.splice(_in, 1);
    pages = [
      'pages/art/twentynine/index',
      'pages/art/order/index',
      'pages/art/addteacher/index',
      'pages/art/jdpay/index',
    ].concat(_pages);
  }

  if (isEnv('weappV12')) {
    pages = [
      'pages/writeExperience/zeroWrite',
      'pages/writeExperience/nineWrite',
      'pages/writeExperience/index',
      'pages/writeExperience/addTeacher/index',
    ];
  }

  // 绘本
  if (isEnv('weappV18')) {
    pages.unshift('pages/pictureBook/index/index');
    pages = pages.concat([
      'pages/pictureBook/indexSimple/index',
      'pages/conversion/addteacher/index',
      'pages/pictureBook/system/index',
      'pages/pay/index',
      'pages/pay/openSuccess/index',
      'pages/address/list/index',
      'pages/address/edit/index',
      // 'pages/login/index',
    ]);
  }

  return pages;
};

// 微信小程序插件配置
const getWechatPlugins = (): Record<string, PluginConfig> => {
  // 基础插件配置
  let plugins: Record<string, PluginConfig> = {
    materialPlugin: {
      version: '1.0.8',
      provider: 'wx4d2deeab3aed6e5a',
    },
  };

  // 根据环境变量调整插件配置
  if (isEnv('weappV3')) {
    plugins['contactPlugin'] = {
      version: '1.4.5',
      provider: 'wx104a1a20c3f81ec2',
    };
  }

  // 特定环境重置插件
  if (
    isEnvIn([
      'weappV13',
      'weappV14',
      'weappV15',
      'weappV17',
      'weappV9',
      'weappV12',
      'weappV18',
    ])
  ) {
    plugins = {};
  }

  return plugins;
};

// 支付宝小程序页面配置
const getAlipayPages = (): string[] => {
  if (isEnv('alipayV0')) {
    return [
      'pages/thirtySix/index',
      'pages/thirtySixNew/index',
      'pages/zeroDotOne/index',
      'pages/thirtySix/chooseLevel/index',
      'pages/thirtySix/addAddress/index',
      'pages/thirtySix/addNewTeacher/index',
      'pages/music/index',
      'pages/music/addTeacher/index',
      'pages/thirtyOne/index',
      'pages/thirtySixBr/index',
      'pages/twentyEight/index',
      'pages/thirtyOne/chooseLevel/index',
      'pages/thirtyOne/addAddress/index',
      'pages/thirtyOne/addNewTeacher/index',
      'pages/orders/list/index',
      'pages/orders/logistics/index',
      'pages/music/nineteen/index',
      'pages/twentyNine/index',
      'pages/payOrder/index',
      'pages/launch/follow/index',
    ];
  }

  if (isEnv('alipayV1')) {
    return ['pages/msbSub/index'];
  }

  return [];
};

// 支付宝小程序插件配置
const getAlipayPlugins = (): Record<string, PluginConfig> => {
  if (isEnv('alipayV0')) {
    return {
      tradePay: {
        version: '*',
        provider: '2021003178648009',
      },
      goodsDetailPlugin: {
        version: '*',
        provider: '2021003177653028',
      },
    };
  }

  return {};
};

// 字节跳动小程序页面配置
const getBytedancePages = (): string[] => {
  return [
    'pages/thirtySixTenDays/index',
    'pages/thirtySix/chooseLevel/index',
    'pages/thirtySix/addAddress/index',
    'pages/thirtySix/addNewTeacher/index',
    'pages/thirtySixTenDays/orderdetail/index',
  ];
};

// 配置生成器
const createConfig = (): PageConfig => {
  let pages: string[] = [];
  let plugins: Record<string, PluginConfig> = {};
  let lazyCodeLoading: string | undefined;

  // 根据平台类型生成配置
  if (isEnvType(CONSTANTS.ENV.WECHAT)) {
    pages = getWechatPages();
    plugins = getWechatPlugins();
  } else if (isEnvType(CONSTANTS.ENV.ALIPAY)) {
    pages = getAlipayPages();
    plugins = getAlipayPlugins();

    if (isEnv('alipayV0')) {
      lazyCodeLoading = 'requiredComponents';
    }
  } else if (isEnvType(CONSTANTS.ENV.BYTEDANCE)) {
    pages = getBytedancePages();
  }

  // 返回完整配置
  return {
    pages,
    window: CONSTANTS.WINDOW,
    plugins,
    lazyCodeLoading,
    requiredPrivateInfos: ['chooseAddress'],
  };
};

// 日志输出
console.log(
  '🚀 ~ app.config.ts:6 ~ process.env.TARO_ENV:',
  process.env.TARO_ENV,
  'process.env.APP_ENV:',
  process.env.APP_ENV,
);

// 导出配置
export default createConfig();
