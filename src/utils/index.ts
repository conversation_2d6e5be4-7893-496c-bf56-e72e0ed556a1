import {
  Events,
  getSetting,
  getStorageSync,
  hideLoading,
  requestSubscribeMessage,
  setStorageSync,
  showLoading,
} from '@tarojs/taro';

export const eventBus = new Events();

export default function resetQuery(params: object): string {
  let query = '';
  for (let key in params) {
    query += `${key}=${params[key]}&`;
  }
  const routerTo = `${query.length ? '?' : ''}${query.substr(
    0,
    query.length - 1,
  )}`;
  return routerTo;
}

export function createSortMethodByPropInArr(
  propName: string,
  sortArr: (string | number)[],
) {
  return (pre, next) => {
    return sortArr.indexOf(pre[propName]) - sortArr.indexOf(next[propName]);
  };
}

export function decodeUrl(url: string) {
  var paraString = url.substring(url.indexOf('?') + 1, url.length).split('&');
  var paraObj = {};
  for (let nameValue of paraString) {
    var name = nameValue.substring(0, nameValue.indexOf('=')); // .toLowerCase();
    var value = nameValue.substring(
      nameValue.indexOf('=') + 1,
      nameValue.length,
    );
    if (value.indexOf('#') > -1) {
      value = value.split('#')[0];
    }
    paraObj[name] = value;
  }
  return paraObj;
}

export function dateFormat(date, fmt) {
  date = new Date(date);
  let o = {
    'M+': date.getMonth() + 1, //月份
    'd+': date.getDate(), //日
    'h+': date.getHours(), //小时
    'm+': date.getMinutes(), //分
    's+': date.getSeconds(), //秒
    'q+': Math.floor((date.getMonth() + 3) / 3), //季度
    S: date.getMilliseconds(), //毫秒
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (date.getFullYear() + '').substr(4 - RegExp.$1.length),
    );
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length === 1
          ? o[k]
          : ('00' + o[k]).substr(('' + o[k]).length),
      );
    }
  }
  return fmt;
}

export function countDownFormat(date, fmt) {
  let stamp = +new Date(date);
  // 相差天数
  let $day = Math.floor(stamp / 86400000);
  // 相差小时数
  stamp %= 86400000;
  let $hour: any = Math.floor(stamp / 3600000);
  // 相差分钟数
  stamp %= 3600000;
  let $min: any = Math.floor(stamp / 60000);
  // 相差秒数
  stamp %= 60000;
  let $sec: any = Math.floor(stamp / 1000);
  // 相差毫秒数
  stamp %= 1000;
  let $ms: any = Math.floor(stamp);

  $hour = String($hour).length === 1 ? `0${$hour}` : $hour;
  $min = String($min).length === 1 ? `0${$min}` : $min;
  $sec = String($sec).length === 1 ? `0${$sec}` : $sec;
  let o = {
    'd+': $day, //日
    'h+': $hour, //小时
    'm+': $min, //分
    's+': $sec, //秒
    S: $ms, //毫秒
  };
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length === 1
          ? o[k]
          : ('00' + o[k]).substr(('' + o[k]).length),
      );
    }
  }
  return fmt;
}

interface mapElCallBackProps {
  nodata?: Function;
  init: Function;
}

export function mapArray(data: any[], callback: mapElCallBackProps) {
  if (!Array.isArray(data) || data.length == 0) {
    return callback.nodata ? callback.nodata() : null;
  } else {
    return data.map((item, index) => {
      return callback.init(item, index);
    });
  }
}

// 截取字符串
export const ellipsisWord = (
  word: string,
  maxLen: number = 20,
  mixLen: number = 8,
) => {
  return word && word.length > maxLen ? word.slice(0, mixLen) + '......' : word;
};

// 富文本样式处理
export const nodesRep = str => {
  const html = str.replace(/(\<img)/gi, $0 => {
    return {
      '<img':
        '<img style=" max-width: 100% !important;height:auto;display:table-cell;" ',
    }[$0];
  });
  return html;
};

// 一天一次弹窗、订阅消息等
export const onceDay = () => {
  const currentTime = +new Date();
  let todayTime = dateFormat(currentTime, 'yyyy/MM/dd');
  todayTime = todayTime + ' 23:59:59';
  todayTime = +new Date(todayTime);
  const localTime = getStorageSync('__msb_rtime__');
  if (localTime > 0 && localTime < todayTime) {
    return false;
  }
  setStorageSync('__msb_rtime__', currentTime);
  return true;
};

/**
 *  用户订阅
 *  0 订阅成功
 *  1 用户总是允许、拒绝 操作
 *  -1 订阅失败，稍后重试吧~
 *  401 取消订阅
 *  400 没任何授权记录、或者仅仅单次授权过
 * */
export const subscribeMsgHandle = (
  subscribe_id: any = '',
  onlyGetSubscribe = false,
) => {
  return new Promise(resolve => {
    showLoading({ title: '加载中...' });
    getSetting({
      // @ts-ignore
      withSubscriptions: true,
      success(res) {
        // @ts-ignore
        const { itemSettings = null } = res.subscriptionsSetting;
        console.log('已授权信息', res);

        // 说明用户已经总是允许此操作（直接跳转）
        if (
          itemSettings &&
          ['accept', 'reject'].includes(
            itemSettings[
              Array.isArray(subscribe_id) ? subscribe_id[0] : [subscribe_id]
            ],
          )
        ) {
          const msg = `总是（${
            itemSettings[subscribe_id] === 'accept' ? '允许' : '拒绝'
          }）操作`;
          console.log(msg);
          hideLoading();
          return resolve({ code: 1, msg });
        }

        if (onlyGetSubscribe) {
          hideLoading();
          return resolve({
            code: 400,
            msg: '没任何授权记录、或者仅仅单次授权过',
          });
        }

        requestSubscribeMessage({
          tmplIds: Array.isArray(subscribe_id) ? subscribe_id : [subscribe_id],
          success(result) {
            hideLoading();
            console.log('result', result);
            if (
              result[
                Array.isArray(subscribe_id) ? subscribe_id[0] : subscribe_id
              ] === 'accept'
            ) {
              return resolve({ code: 0, msg: '订阅成功' });
            }
            if (
              result[
                Array.isArray(subscribe_id) ? subscribe_id[0] : subscribe_id
              ] === 'reject'
            ) {
              return resolve({ code: 401, msg: '取消订阅' });
            }
          },
          fail: () => {
            return resolve({ code: -1, msg: '订阅失败，稍后重试吧~' });
          },
        });
      },
      fail: () => {
        hideLoading();
      },
    });
  });
};

// 解析链接中的参数
export const getQueryString = (url: string) => {
  const queryArr = url.split('&');
  let obj = {};
  for (let i = 0; i < queryArr.length; i++) {
    const splitArr = queryArr[i].split('=');
    obj[splitArr[0]] = splitArr[1];
  }

  return obj;
};
