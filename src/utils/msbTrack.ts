import Taro from '@tarojs/taro';
import md5 from 'js-md5';
import store from '@/store/groupbuy/index';
import { CryptoJS } from './aes.js';
import request from '../api/request';

const serverConfig = {
  development: 'https://track-test.meishubao.com',
  dev: 'https://track-test.meishubao.com', // track-test.meishubao.com 503
  test: 'https://track-test.meishubao.com',
  gray: 'https://track.meishubao.com',
  online: 'https://track.meishubao.com',
};

const server = serverConfig[process.env.NODE_ENV];

const encrypt = (event, params) => {
  const time = new Date().getTime();
  const keyStr = md5(md5(`${time}`)).substring(8, 24);
  const ivStr = md5(`${time}`).substring(8, 24);
  const key = CryptoJS.enc.Utf8.parse(keyStr);
  const iv = CryptoJS.enc.Utf8.parse(ivStr);
  const trackInfo = Taro.getStorageSync('trackInfo');
  if (!trackInfo.user_id) {
    trackInfo.user_id = store.getState().userid;
  }
  const encrypted = CryptoJS.AES.encrypt(
    JSON.stringify({
      ...trackInfo,
      unique_id: getUuid(),
      event_time: time,
      event,
      properties: params,
    }),
    key,
    {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    },
  );
  return { data: encrypted.toString(), timestamp: base64_encode(`${time}`) };
};
const base64_encode = str => {
  let c1, c2, c3;
  let base64EncodeChars =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
  let i = 0,
    len = str.length,
    string = '';

  while (i < len) {
    c1 = str.charCodeAt(i++) & 0xff;
    if (i == len) {
      string += base64EncodeChars.charAt(c1 >> 2);
      string += base64EncodeChars.charAt((c1 & 0x3) << 4);
      string += '==';
      break;
    }
    c2 = str.charCodeAt(i++);
    if (i == len) {
      string += base64EncodeChars.charAt(c1 >> 2);
      string += base64EncodeChars.charAt(
        ((c1 & 0x3) << 4) | ((c2 & 0xf0) >> 4),
      );
      string += base64EncodeChars.charAt((c2 & 0xf) << 2);
      string += '=';
      break;
    }
    c3 = str.charCodeAt(i++);
    string += base64EncodeChars.charAt(c1 >> 2);
    string += base64EncodeChars.charAt(((c1 & 0x3) << 4) | ((c2 & 0xf0) >> 4));
    string += base64EncodeChars.charAt(((c2 & 0xf) << 2) | ((c3 & 0xc0) >> 6));
    string += base64EncodeChars.charAt(c3 & 0x3f);
  }
  return string;
};
const getUuid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
    let r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};
const userEventTrack = (event, data) => {
  return request(
    `${server}/v1/track/user/data`,
    'POST',
    encrypt(event, data),
    {},
  );
};

const msbTrack = {
  init: async ({
    business_name = '小熊艺术',
    app_name = '小熊艺术乐园',
    wx_openid = '',
    user_id = '',
  } = {}) => {
    if (!wx_openid) return;
    console.log('msbTrack init 🌈  wx_openid=', wx_openid, 'user_id=', user_id);
    const systemInfo = Taro.getSystemInfoSync();
    const {
      system: os_version,
      screenHeight: screen_height,
      screenWidth: screen_width,
      version: app_version,
      model,
    } = systemInfo;
    const network_type = await new Promise(resolve => {
      Taro.getNetworkType({
        success: res => {
          resolve(res.networkType);
        },
      });
    });

    const { data = {} } = (await request(
      `https://onlineapi.meishubao.com/api/third/ip_location`,
      'POST',
      '',
      {},
    )) as any;
    const trackInfo = {
      device_id: '',
      wx_openid,
      user_id: user_id || store.getState().openid,
      business_name,
      app_name,
      app_version,
      ip: data.ip,
      network_type,
      os: os_version.includes('iOS') ? 'IOS' : 'Android',
      os_version,
      screen_height,
      screen_width,
      model,
      sdk_type: 'MiniProgram',
      timezone_offset: new Date().getTimezoneOffset(),
    };
    Taro.setStorageSync('trackInfo', trackInfo);
    /**
     * 建议在加载页面上报时候 对 sensorsSta 进行监听 状态为true时候在进行上报 避免异步问题，导致上报的时候上面异步获取的公共数据会上报丢失公共数据的问题~~~~~
     * tips： 在redux中初始化sensors 有问题，redux 只能处理同步 处理里异步需要redux-saga 或者其他异步插件，建议用dva.js （懒得改 ^_^）先这样吧
     * const sensorsSta =
     * useSelector((state: UserStateType) => state.sensorsSta) || '';
     * useEffect(() => {
     *     if (sensorsSta) {
     *        sensors.track('Followpublicaccountspage_veiw', {});
     *      }
     * }, [sensorsSta]);
     * */
    store.dispatch({ type: 'CHANGE_SENSORS_STA' });
  },
  track: (event, params) => {
    setTimeout(
      () => {
        userEventTrack(event, params).then(() => {
          process.env.NODE_ENV != 'online' && console.log(event, '--✅');
        });
      },
      store.getState().openid ? 0 : 3000,
    );
  },
  getUuid: () => {
    return getUuid();
  },
};
export default msbTrack;
