import { getOrderIdAllNew } from '@/api/groupbuy';

class Checkinfo {
  userId: string = '';
  orderId: string = '';
  ordersPayload: any[] = [];
  subjects: string = '';

  constructor({ subjects, uid = '' }) {
    console.log(subjects, uid, 'subjects===??');

    this.subjects = subjects;
    this.userId = uid;
  }

  setUserId(uid) {
    this.userId = uid;
  }

  checkOrder(packagesIds) {
    return new Promise(resolve => {
      if (this.userId) {
        const _packagesIds = Array.isArray(packagesIds)
          ? packagesIds
          : [packagesIds];
        getOrderIdAllNew({
          userId: this.userId,
          regtypes: 'EXPERIENCE,EXPERIENCE_ONE_WEEK',
          statuses: 'PAYMENT,COMPLETED,WAIT_COMPLETED',
          subjects: this.subjects,
        }).then(res => {
          if (res.payload && res.payload.length) {
            res.payload.forEach(item => {
              const { isRefund, packagesId } = item;
              if (
                _packagesIds.includes(packagesId) &&
                isRefund != 'REFUNDEND' // 未退费订单
              ) {
                this.ordersPayload.push(item);
              }
            });
          }
          resolve(this.ordersPayload);
        });
      } else {
        resolve(this.ordersPayload);
      }
    });
  }
}

export default Checkinfo;
