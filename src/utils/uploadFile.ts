/* eslint-disable import/no-commonjs */
import Taro from '@tarojs/taro';
import { getOssSigned } from '@/api/index';

// 上传组件参数
interface IuploadFileParams {
  path: string; // 图片路径
  entryName: string; // 项目名称
}

// 函数返回
interface IuploadReturn {
  res?: any; // 结果
  url: string; // cdn地址
}

const NODE_ENV = process.env.NODE_ENV;
require('./hmac.js');
require('./sha1.js');

const uploadFile = async function(
  params: IuploadFileParams,
  cb: Function | null = null,
): Promise<any> {
  const filePath = params.path;
  if (!filePath) {
    console.error('文件错误');
    return;
  }

  const ossSignInfo: any = (await getOssSigned()) || {};
  const { code, payload } = ossSignInfo;
  if (code !== 0) {
    Taro.showToast({
      title: 'oss签名信息获取失败',
      icon: 'none',
    });
    return;
  }

  const { host, accessKeyId, realmName, signed, policy } = payload;

  const envPath = NODE_ENV === 'development' ? 'dev' : NODE_ENV;
  const dir = `ai-mp-users/${envPath}/screenshot`;
  const entryName = params.entryName;
  const filetime = Date.now();
  const fileradom = (Math.random() * 10000).toFixed(0);
  const filearr = filePath.split('.');
  const fileext = filearr[filearr.length - 1];
  const filename = `${filetime}${fileradom}.${fileext}`;
  const aliyunFileKey = `${dir}/${entryName}/${filename}`;
  // const policyBase64 = getPolicyBase64()
  return new Promise((resolve, reject) => {
    const uploadTask = wx.uploadFile({
      url: host,
      filePath: filePath,
      name: 'file',
      formData: {
        key: aliyunFileKey,
        policy,
        OSSAccessKeyId: accessKeyId,
        signature: signed,
        success_action_status: '200',
      },
      success: function(res) {
        if (res.statusCode != 200) {
          resolve({
            res: res,
            url: `https://${realmName}/${aliyunFileKey}`,
          });
        } else {
          resolve({
            res: res,
            url: `https://${realmName}/${aliyunFileKey}`,
          });
        }
      },
      fail: function(err) {
        console.error(err);
        reject(err);
      },
    });
    uploadTask.onProgressUpdate(progressInfo => {
      cb && cb(progressInfo);
    });
  });
};

// const getPolicyBase64 = function() {
//   let date = new Date()
//   date.setHours(date.getHours() + 87600) //这个是上传文件时Policy的失效时间
//   let srcT = date.toISOString()
//   const policyText = {
//     expiration: srcT, //设置该Policy的失效时间
//     conditions: [
//       ['content-length-range', 0, 5 * 1024 * 1024], // 设置上传文件的大小限制,5mb
//     ],
//   }

//   const policyBase64 = Base64.encode(JSON.stringify(policyText))
//   return policyBase64
// }

// const getSignature = function(policyBase64) {
//   const accesskey = config.AccessKeySecret

//   const bytes = Crypto.HMAC(Crypto.SHA1, policyBase64, accesskey, {
//     asBytes: true,
//   })
//   const signature = Crypto.util.bytesToBase64(bytes)

//   return signature
// }

export default uploadFile;
