import { checkOrdersByUserId, getOrderDetailApi } from '@/api/groupbuy';

interface optionsProps {
  userId?: string;
  channelId?: string;
  orderId?: string;
  regtype?: string;
  subjects?: string;
  packageId?: string;
}

class CheckLevel {
  userId: string;
  channelId: string;
  orderId: string;
  regtype: string;
  subjects: string;
  packageId?: any;

  constructor(options: optionsProps) {
    this.userId = options.userId || '';
    this.channelId = options.channelId || '';
    this.orderId = options.orderId || ''; //先选等级是没有订单id的需要手动指定单双周
    this.regtype = options.regtype || ''; // 没有orderId时可以指定单双周  EXPERIENCE/EXPERIENCE_ONE_WEEK
    this.subjects = options.subjects || 'ART_APP'; // 默认美术 可选ART_APP、WRITE_APP
    this.packageId = options.packageId || '';
  }

  getDetailByOrderId() {
    return new Promise((resolve, reject) => {
      this.orderId &&
        getOrderDetailApi(this.orderId).then(res => {
          if (res.code === 0) {
            const regtype = res.payload.order.regtype;
            resolve(regtype);
          } else {
            reject('');
          }
        });
    });
  }

  checkUserBuyLevel(regtype: string) {
    return new Promise((resolve, reject) => {
      this.userId &&
        this.channelId &&
        checkOrdersByUserId({
          userId: this.userId,
          channels: this.channelId,
          subjects: this.subjects,
          packageId: this.packageId,
        }).then(res => {
          if (res.code === 0) {
            const { subjectOrderMap, experienceCheckMap } = res.payload;
            // experienceCheckMap.ART_APP/WRITE_APP为true 表示之前已经买过体验课了
            if (experienceCheckMap[this.subjects]) {
              let supList: any[] = [];
              let supListOneWeek: any[] = [];
              if (
                subjectOrderMap &&
                subjectOrderMap[this.subjects] &&
                subjectOrderMap[this.subjects].EXPERIENCE
              ) {
                const experience: any[] =
                  subjectOrderMap[this.subjects].EXPERIENCE;
                for (let i = 0; i < experience.length; i++) {
                  supList.push(experience[i].sup);
                }
              }
              if (
                subjectOrderMap &&
                subjectOrderMap[this.subjects] &&
                subjectOrderMap[this.subjects].EXPERIENCE_ONE_WEEK
              ) {
                const experience =
                  subjectOrderMap[this.subjects].EXPERIENCE_ONE_WEEK;
                for (let i = 0; i < experience.length; i++) {
                  supListOneWeek.push(experience[i].sup);
                }
              }
              if (regtype === 'EXPERIENCE') {
                resolve(supList);
              } else {
                resolve(supListOneWeek);
              }
            } else {
              // experienceCheckMap.ART_APP/WRITE_APP为false 表示之前没有买过体验课了，是首单
              resolve([]);
            }
          } else {
            reject('');
          }
        });
    });
  }

  /**
   * 单双周体验课的级别在后端那是单独计算的，故买过单周体验课的级别（如S1），可以再买双周课的同级别（S1），反之同理
   测试时候注意测下这点，对应的级别是可选的
   */
  initCheck() {
    return new Promise((resolve, reject) => {
      if (this.regtype) {
        this.checkUserBuyLevel(this.regtype)
          .then(res => {
            resolve(res);
          })
          .catch(error => {
            reject(error);
          });
      } else {
        this.getDetailByOrderId().then((regtype: string) => {
          this.checkUserBuyLevel(regtype)
            .then(res => {
              resolve(res);
            })
            .catch(error => {
              reject(error);
            });
        });
      }
    });
  }
}

export default CheckLevel;
