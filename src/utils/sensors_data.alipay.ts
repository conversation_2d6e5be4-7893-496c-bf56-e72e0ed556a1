import Taro from '@tarojs/taro';
import msbTrack from './msbTrack';

class commonSensors {
  init(data) {
    msbTrack.init(data);
  }
  track(name, data?) {
    const report_view = msbTrack.getUuid();
    const trackInfo = Taro.getStorageSync('trackInfo') || {};
    if (!trackInfo.wx_openid) {
      setTimeout(() => {
        this.track(name, data);
      }, 3000);
      return;
    }
    data = { report_view, wx_openid: trackInfo.wx_openid, ...data };
    console.log('🚀', JSON.stringify(data));
    msbTrack.track(name, data);
  }
  login(data) {
    // bearSensors.login(data);
  }
}
const useCommonSensors = new commonSensors();
export const sensorsFun = () => {
  // 配置初始化参数
  useCommonSensors.init({});
  process.env.NODE_ENV !== 'online' && console.log('sensors init');
};
export default useCommonSensors;
