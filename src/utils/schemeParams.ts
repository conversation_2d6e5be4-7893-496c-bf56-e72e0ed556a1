import Taro from '@tarojs/taro';

/**
 * 获取支付宝小程序scheme链接参数
 * 从本地存储中获取之前保存的scheme参数
 * @returns 解析后的scheme参数对象
 */
export function getSchemeParams(): Record<string, any> {
  try {
    const paramsStr = Taro.getStorageSync('schemeParams');
    if (paramsStr) {
      return JSON.parse(paramsStr);
    }
  } catch (error) {
    console.error('获取scheme参数失败:', error);
  }
  return {};
}

/**
 * 获取指定的scheme参数值
 * @param key 参数名
 * @param defaultValue 默认值
 * @returns 参数值或默认值
 */
export function getSchemeParam(key: string, defaultValue: any = ''): any {
  const params = getSchemeParams();
  return params[key] !== undefined ? params[key] : defaultValue;
}
