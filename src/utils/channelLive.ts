import Taro from '@tarojs/taro';
import { ChannelLiveInfo } from '@/types/types';

/**
 * 版本比较工具函数
 * @param v1 版本1
 * @param v2 版本2
 * @returns 1: v1 > v2, -1: v1 < v2, 0: v1 = v2
 */
export const compareVersion = (v1: string, v2: string): number => {
  const v1Parts = v1.split('.').map(Number);
  const v2Parts = v2.split('.').map(Number);
  
  for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
    const part1 = v1Parts[i] || 0;
    const part2 = v2Parts[i] || 0;
    if (part1 > part2) return 1;
    if (part1 < part2) return -1;
  }
  return 0;
};

/**
 * 检测是否支持视频号直播功能
 * @returns 是否支持
 */
export const checkChannelLiveSupport = (): boolean => {
  try {
    const systemInfo = Taro.getSystemInfoSync();
    const SDKVersion = systemInfo.SDKVersion;
    return compareVersion(SDKVersion, '2.29.0') >= 0;
  } catch (error) {
    console.error('检测基础库版本失败:', error);
    return false;
  }
};

/**
 * 获取直播状态描述
 * @param status 直播状态码
 * @returns 状态描述
 */
export const getLiveStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    0: '未知状态',
    1: '直播中',
    2: '未开始',
    3: '已结束',
    4: '禁播',
    5: '暂停',
    6: '异常'
  };
  return statusMap[status] || '未知状态';
};

/**
 * 获取回放状态描述
 * @param replayStatus 回放状态码
 * @returns 回放状态描述
 */
export const getReplayStatusText = (replayStatus: number): string => {
  const replayMap: Record<number, string> = {
    0: '未知状态',
    1: '回放生成中',
    2: '回放生成完成',
    3: '回放生成失败'
  };
  return replayMap[replayStatus] || '未知状态';
};

/**
 * 获取跳转页面描述
 * @param status 直播状态
 * @param replayStatus 回放状态
 * @returns 跳转页面描述
 */
export const getJumpPageText = (status: number, replayStatus: number): string => {
  const pageMap: Record<number, string> = {
    1: '直播页面',
    2: '上一场直播的结束页',
    3: replayStatus === 2 ? '直播回放页' : '直播结束页'
  };
  return pageMap[status] || '未知页面';
};

/**
 * 获取视频号直播信息
 * @param finderUserName 视频号ID
 * @param startTime 开始时间戳（秒）
 * @param endTime 结束时间戳（秒）
 * @returns Promise<ChannelLiveInfo>
 */
export const getChannelsLiveInfo = async (
  finderUserName: string,
  startTime: number,
  endTime: number
): Promise<ChannelLiveInfo> => {
  return new Promise((resolve, reject) => {
    Taro.getChannelsLiveInfo({
      finderUserName,
      startTime,
      endTime,
      success: (res: any) => {
        if (res.errMsg === 'getChannelsLiveInfo:ok') {
          resolve(res as ChannelLiveInfo);
        } else {
          reject(new Error(`获取直播信息失败: ${res.errMsg}`));
        }
      },
      fail: (err: any) => {
        reject(new Error(`接口调用失败: ${err.errMsg || '未知错误'}`));
      }
    });
  });
};

/**
 * 生成时间范围（当前时间到1小时后）
 * @returns { startTime: number, endTime: number }
 */
export const generateTimeRange = (): { startTime: number; endTime: number } => {
  const now = Date.now();
  const ONE_HOUR = 3600 * 1000; // 1小时毫秒数
  
  return {
    startTime: Math.floor(now / 1000),
    endTime: Math.floor((now + ONE_HOUR) / 1000)
  };
};

/**
 * 格式化时间戳为可读时间
 * @param timestamp 时间戳（秒）
 * @returns 格式化后的时间字符串
 */
export const formatTimestamp = (timestamp: number): string => {
  return new Date(timestamp * 1000).toLocaleTimeString();
};
