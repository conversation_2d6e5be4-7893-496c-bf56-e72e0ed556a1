import { getUserOpenIdParams, getUserOpenIdSubject } from '@/api/groupbuy';
import store from '@/store/groupbuy';
import {
  changeMobile,
  changeOpenid,
  changeUnionid,
  changeUserid,
} from '@/store/groupbuy/action';
import Taro from '@tarojs/taro';

/**
 * @description 简化Taro.showToast 简单提示配置
 * @param title
 * @returns
 */
export const toast = (title: string) => Taro.showToast({ title, icon: 'none' });

export interface statusRes {
  status: '0' | '1';
}
// 检验授权
type Authtypes =
  | 'userInfo'
  | 'userLocation'
  | 'address'
  | 'invoiceTitle'
  | 'invoice'
  | 'werun'
  | 'record'
  | 'writePhotosAlbum'
  | 'camera';

/**
 *
 * @param auth 检验授权
 * @param isauthorize 未授权是否需要立即调起弹窗询问
 * @returns
 */
export const getSetting: (
  auth: Authtypes,
  isauthorize?: boolean,
) => Promise<statusRes> = (auth, isauthorize = false) => {
  return new Promise((resolve, reject) => {
    Taro.getSetting({
      success(res) {
        const scope = `scope.${auth}`;
        if (res.authSetting[scope]) {
          resolve({ status: '1' });
        } else {
          if (isauthorize) {
            Taro.authorize({
              scope,
              success() {
                resolve({ status: '1' });
              },
              fail() {
                reject({ status: '0' });
              },
            });
          } else {
            reject({ status: '0' });
          }
        }
      },
      fail() {
        reject({ status: '0' });
      },
    });
  });
};

// 保存图片到相册
export const saveImage: (url: string) => Promise<statusRes> = url => {
  return new Promise((resolve, reject) => {
    getSetting('writePhotosAlbum', true)
      .then(() => {
        Taro.getImageInfo({
          src: url,
          success(result) {
            Taro.saveImageToPhotosAlbum({
              filePath: result.path,
              success() {
                resolve({ status: '1' });
              },
              fail: () => {
                Taro.hideLoading();
                reject({ status: '0' });
              },
            });
          },
          fail: () => {
            Taro.hideLoading();
            reject({ status: '0' });
          },
        });
      })
      .catch(() => {
        Taro.hideLoading();
        Taro.showModal({
          title: '提示',
          content: '请授权保存到相册',
          success(res) {
            if (res.confirm) {
              Taro.openSetting({
                success() {
                  reject({ status: '1' });
                },
                fail() {
                  reject({ status: '0' });
                },
              });
            } else if (res.cancel) {
              reject({ status: '0' });
            }
          },
          fail() {
            reject({ status: '0' });
          },
        });
      });
  });
};

// 防抖
export function debounce(func, wait: number, immediate: boolean = false) {
  let timer;

  return function() {
    let context = this;
    let args = arguments;

    if (timer) clearTimeout(timer);
    if (immediate) {
      let callNow = !timer;
      timer = setTimeout(() => {
        timer = null;
      }, wait);
      if (callNow) func.apply(context, args);
    } else {
      timer = setTimeout(function() {
        func.apply(context, args);
      }, wait);
    }
  };
}

/**
 *  @description 手机脱敏
 * @param phone
 */
export function sencePhone(phone) {
  return (
    (phone && phone.toString().replace(/(\d{3})\d+(\d{4})/, '$1****$2')) || ''
  );
}

// 判断当前当前的小程序是体验、开发、还是正式版
export function judgeMinienv(env) {
  const accountInfo = Taro.getAccountInfoSync();
  if (env) {
    if (process.env.NODE_ENV !== 'online') return true;
    return accountInfo.miniProgram.envVersion === env;
  }
  return accountInfo.miniProgram.envVersion;
}

// 获取openId
export function wxLogin(channel = '') {
  return new Promise<string | null>(resolve => {
    Taro.login({
      success(res) {
        if (res.code) {
          let params: getUserOpenIdParams = { code: res.code };
          if (channel) {
            params.channel = channel;
          }
          getUserOpenIdSubject(params).then((data: any) => {
            if (data.code !== 0) {
              return;
            }
            const {
              openid = '',
              mobile = '',
              uid = '',
              unionid = '',
            } = data.payload;
            openid && store.dispatch(changeOpenid(openid));
            mobile && store.dispatch(changeMobile(mobile));
            uid && store.dispatch(changeUserid(uid));
            unionid && store.dispatch(changeUnionid(unionid));
            if (data.payload.token)
              Taro.setStorageSync('appToken', data.payload.token);
            Taro.setStorageSync('__msb_union_id__', unionid);
            Taro.setStorageSync('__msb_open_id__', openid);
            Taro.setStorageSync('__msb_user_id__', uid);
            resolve(openid);
          });
        }
      },
      fail() {
        Taro.showToast({
          title: '微信登录获取code失败',
          icon: 'none',
          duration: 2000,
        });
        resolve(null);
      },
    });
  });
}
