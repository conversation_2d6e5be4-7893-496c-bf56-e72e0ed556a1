import Taro from '@tarojs/taro';
import sensors from '@/utils/sensors_data';

const reducer = (state, action) => {
  switch (action.type) {
    case 'CHANGE_USERID':
      return {
        ...state,
        userid: action.userid,
      };
    case 'CHANGE_OPENID':
      // 美术宝埋点
      if (action.openid)
        setTimeout(() => {
          sensors.init({ wx_openid: action.openid, user_id: state.userid });
        }, 0);
      return {
        ...state,
        openid: action.openid,
      };
    case 'CHANGE_UNIONID':
      return {
        ...state,
        unionId: action.unionId,
      };
    case 'CHANGE_ORDERID':
      Taro.setStorageSync('orderid', action.orderId);
      return {
        ...state,
        orderId: action.orderId,
      };
    case 'CHANGE_CHANNELID':
      return {
        ...state,
        channelId: action.channelId,
      };
    case 'CHANGE_MOBILE':
      return {
        ...state,
        mobile: action.mobile,
      };
    case 'CHANGE_CLICKID':
      return {
        ...state,
        clickId: action.clickId,
      };
    case 'CHANGE_WXACCOUNTNU':
      return {
        ...state,
        wxAccountNu: action.wxAccountNu,
      };
    case 'CHANGE_ROUTEPATH':
      return {
        ...state,
        routePath: action.routePath,
      };
    case 'CHANGE_ADPLATFORM':
      return {
        ...state,
        adPlatform: action.adPlatform,
      };
    // 海报id
    case 'CHANGE_POSTERID':
      return {
        ...state,
        posterId: action.posterId,
      };
    case 'CHANGE_SENDID':
      return {
        ...state,
        sendId: action.sendId,
      };
    case 'CHANGE_SPREADID':
      return {
        ...state,
        spreadId: action.spreadId,
      };
    case 'PAY_TYPE':
      return {
        ...state,
        payType: action.payType,
      };
    case 'CHANGE_USERROLE':
      return {
        ...state,
        userRole: action.userRole,
      };
    case 'CHANGE_REDEEMFLAG':
      return {
        ...state,
        redeemFlag: action.redeemFlag,
      };
    case 'CHANGE_SESSIONKEY':
      return {
        ...state,
        sessionkey: action.sessionkey,
      };
    case 'CHANGE_ALIUSERID':
      return {
        ...state,
        aliUserId: action.aliUserId,
      };
    case 'CHANGE_AUTHCODE':
      return {
        ...state,
        authCode: action.authCode,
      };
    case 'CHANGE_SENSORS_STA':
      return {
        ...state,
        sensorsSta: true,
      };
    case 'UPDATE_USERINFO':
      return {
        ...state,
        userInfo: action.userinfo,
      };
    case 'CHOOSEADDRESSID':
      return {
        ...state,
        chooseAddressId: action.chooseAddressId,
      };
    default:
      return state;
  }
};

export default reducer;
