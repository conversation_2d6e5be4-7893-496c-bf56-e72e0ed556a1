import Taro from '@tarojs/taro';

export interface IUserInfo {
  id?: string;
  cid?: string;
  mid?: string;
  ctime?: string;
  utime?: string;
  del?: string;
  weixinUnionid?: string;
  weixinOpenid?: string;
  teacherId?: string;
  wechatNikename?: string;
  username?: string;
  nickname?: string;
  password?: string;
  head?: string;
  sex?: string;
  birthday?: string;
  mobile?: string;
  country?: string;
  type?: string;
  status?: string;
  sendId?: string;
  firstOrderSendId?: string;
  channel?: string;
  joinDate?: string;
  pageOrigin?: string;
  pageOriginId?: string;
  subscribe?: string;
  userNum?: string;
  basePainting?: string;
  sensorsId?: string;
  teamId?: string;
  lastTeacherId?: string;
  lastTeamId?: string;
  importTime?: string;
  importRemark?: string;
  exportRemark?: string;
  mobileProvince?: string;
  mobileCity?: string;
  user1v1Id?: string;
  wallBackground?: string;
  birthdayActivity?: string;
  spreadCode?: string;
  cityId?: string;
  provinceId?: string;
  addedWechat?: string;
  come?: string;
  grade?: string;
  email?: string;
  language?: string;
  area?: string;
  wechatFollowTime?: string;
  addedGroup?: string;
}

export interface UserStateType {
  userid: string;
  openid: string;
  unionId: string;
  sendId: string;
  spreadId: string;
  orderId: string;
  quotaRandom: number;
  channelId: string;
  mobile: string;
  clickId: string;
  wxAccountNu: string;
  routePath: string;
  adPlatform: string;
  payType: string;
  posterId: string;
  userRole: string;
  sessionkey: string;
  msbTrackType: boolean;
  authCode: string;
  aliUserId: string;
  sensorsSta: boolean;
  userInfo: IUserInfo;
  chooseAddressId: string;
}

const state = {
  userid: Taro.getStorageSync('__msb_user_id__') || null,
  openid: '',
  unionId: '',
  sendId: '',
  spreadId: '',
  orderId: Taro.getStorageSync('orderid') || '',
  quotaRandom: Math.floor(Math.random() * (20 - 3 + 1)) + 3,
  channelId: '18248',
  mobile: '',
  clickId: '',
  wxAccountNu: '',
  routePath: '',
  adPlatform: '',
  payType: '',
  posterId: '',
  userRole: '',
  redeemFlag: '',
  sessionkey: '',
  msbTrackType: false,
  authCode: '',
  aliUserId: '',
  sensorsSta: false,
  userInfo: {},
  chooseAddressId: 0,
};

export default state;
