import {
  ADPLATFORM,
  ALIUSERID,
  AUTHCODE,
  CHANNELID,
  CLICKID,
  MOBILE,
  OPENID,
  ORDERID,
  POSTERID,
  ROUTEPATH,
  SENDID,
  SESSIONKEY,
  SPREADID,
  UNIONID,
  UPDATE_USERINFO,
  USERID,
  USERROLE,
  WXACCOUNTNU,
  CHOOSEADDRESSID,
} from './constants';

export const changeUserid = userid => {
  return {
    type: USERID,
    userid,
  };
};
export const changeOpenid = openid => {
  return {
    type: OPENID,
    openid,
  };
};

export const changeUnionid = unionId => {
  return {
    type: UNIONID,
    unionId,
  };
};

export const changeOrderId = orderId => {
  return {
    type: ORDERID,
    orderId,
  };
};

export const changeChannelId = channelId => {
  return {
    type: CHANNELID,
    channelId,
  };
};
export const changeMobile = mobile => {
  return {
    type: MOBILE,
    mobile,
  };
};

export const changeClickId = clickId => {
  return {
    type: CLICKID,
    clickId,
  };
};

export const changeWxAccountNu = wxAccountNu => {
  return {
    type: WXACCOUNTNU,
    wxAccountNu,
  };
};

export const changeRoutePath = routePath => {
  return {
    type: ROUTEPATH,
    routePath,
  };
};

export const changeAdPlatform = adPlatform => {
  return {
    type: ADPLATFORM,
    adPlatform,
  };
};

export const changeSendId = sendId => {
  return {
    type: SENDID,
    sendId,
  };
};
export const changespreadId = spreadId => {
  return {
    type: SPREADID,
    spreadId,
  };
};

export const changePosterId = posterId => {
  return {
    type: POSTERID,
    posterId,
  };
};

export const changeUserRole = userRole => {
  return {
    type: USERROLE,
    userRole,
  };
};
export const changeSessionkey = sessionkey => {
  return {
    type: SESSIONKEY,
    sessionkey,
  };
};

export function asyncChangeSendId(sendId) {
  return dispatch => {
    setTimeout(() => {
      dispatch(changeSendId(sendId));
    }, 0);
  };
}

export function asyncChangeAdPlatform(adPlatform) {
  return dispatch => {
    setTimeout(() => {
      dispatch(changeAdPlatform(adPlatform));
    }, 0);
  };
}

export function asyncChangeRoutePath(routePath) {
  return dispatch => {
    setTimeout(() => {
      dispatch(changeRoutePath(routePath));
    }, 0);
  };
}

export function asyncChangeClickId(clickId) {
  return dispatch => {
    setTimeout(() => {
      dispatch(changeClickId(clickId));
    }, 0);
  };
}

export function asyncChangeWxAccountNu(wxAccountNu) {
  return dispatch => {
    setTimeout(() => {
      dispatch(changeWxAccountNu(wxAccountNu));
    }, 0);
  };
}

export function asyncChangeMobile(mobile) {
  return dispatch => {
    setTimeout(() => {
      dispatch(changeMobile(mobile));
    }, 0);
  };
}

export function asyncChangeChannelId(channelId) {
  return dispatch => {
    setTimeout(() => {
      dispatch(changeChannelId(channelId));
    }, 0);
  };
}

export function asyncChangeOrderId(orderId) {
  return dispatch => {
    setTimeout(() => {
      dispatch(changeOrderId(orderId));
    }, 0);
  };
}

export function asyncChangeOpenid(openid) {
  return dispatch => {
    setTimeout(() => {
      dispatch(changeOpenid(openid));
    }, 0);
  };
}

export function asyncChangeUserid(userid) {
  return dispatch => {
    setTimeout(() => {
      dispatch(changeUserid(userid));
    }, 0);
  };
}

export function asyncChangeUserRole(userRole) {
  return dispatch => {
    setTimeout(() => {
      dispatch(changeUserRole(userRole));
    }, 0);
  };
}

export const changeAliUserId = aliUserId => {
  return {
    type: ALIUSERID,
    aliUserId,
  };
};
export const changeAuthCode = authCode => {
  return {
    type: AUTHCODE,
    authCode,
  };
};

export const updateUserInfo = userinfo => {
  return {
    type: UPDATE_USERINFO,
    userinfo,
  };
};

export const setChooseAddressId = chooseAddressId => {
  console.log(555555, chooseAddressId);
  return {
    type: CHOOSEADDRESSID,
    chooseAddressId,
  };
};

export function asyncSetChooseAddressId(chooseAddressId) {
  console.log(33333, chooseAddressId);

  return dispatch => {
    setTimeout(() => {
      dispatch(setChooseAddressId(chooseAddressId));
    }, 0);
  };
}
