@function bgUrl($name) {
  @return 'https://fe-cdn.xiaoxiongmeishu.com/xiaoxiongmpactivity/weapp/wx9b8dcac074fb3151/common/#{$name}';
}
.invitetask-detail-wrap {
  width: 100%;
  background: #fff7e1;
  min-height: 100vh;
  .header {
    width: 100%;
    height: 268px;
    background: url(bgUrl('it-task-header.png')) no-repeat 0 0 /100%;
    padding: 70px 0 0 45px;
    box-sizing: border-box;
  }
  .tips {
    font-size: 24px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    text-align: left;
  }
  .section {
    width: 100%;
    background: #fff7e1;
    border-radius: 50px 50px 0px 0px;
    margin-top: -55px;
    box-sizing: border-box;
    padding: 40px 30px 20px 30px;
    .task-item-box {
      margin-bottom: 20px;
    }
  }
  .none {
    text-align: center;
    width: 690px;
    height: 203px;
    padding-top: 40px;
    border-radius: 30px;
    box-sizing: border-box;
    &-img {
      width: 136px;
    }
    margin: 0 auto;
    color: #999;
    font-size: 28px;
    text-align: center;
  }
}
