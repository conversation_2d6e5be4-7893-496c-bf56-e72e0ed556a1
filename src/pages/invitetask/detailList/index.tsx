import { View, Image } from '@tarojs/components';
import { useDidShow, showToast } from '@tarojs/taro';
import { replaceGoodsApi } from '@/api/invitetask';
import { useState } from 'react';
import { judgeMinienv } from '@/utils/auth';
import listNone from '@/assets/newInvitetask/list-none.png';
import Taskitem from '../components/taskitem';
import { useReducerContext } from '../reduce';
import Postermodal from '../components/postermodal';
import Overmodal from '../components/overModal';
import ExchangeGifts from '../components/exchangeGifts';
import styles from './index.module.scss';

export default function Detaillist() {
  const { state, dispatch, getInit, userid } = useReducerContext();
  const [posterVisible, setPosterVisible] = useState(false);
  const [exchangeVisible, setExchangeVisible] = useState(false);
  const [overVisible, setOverVisible] = useState(false);
  const [overType, setOverType] = useState('');
  const [overChangedGood, setOverChangedGood] = useState<any>('');
  const handlePosterModal = () => {
    setPosterVisible(val => !val);
  };

  const handleOverModal = () => {
    setOverVisible(val => !val);
  };

  const handleBtn = item => {
    const { status, id } = item;

    if (status === 'ACTIVING') {
      if (!judgeMinienv('release')) {
        return;
      }
      dispatch({
        type: 'getPrezzieId',
        payload: { prezzieId: id },
      });
      // 分享弹窗
      judgeMinienv('release') && setPosterVisible(true);
    }
    // if (status === 'NOGET') {
    // 填写地址
    setOverType('2');
    setOverVisible(true);
    // }
  };

  // 图片保存成功
  const saveSuccess = () => {
    setOverType('1');
    setOverVisible(true);
  };

  const handleExchange = item => {
    if (item) {
      setOverChangedGood(item);
    }
    setExchangeVisible(val => !val);
  };

  const hanleExchangeBtn = good => {
    const { goodsId, img, title, epc } = good;
    replaceGoodsApi({
      uid: userid,
      prezzieId: overChangedGood.id,
      goodsId,
      img,
      title,
      epc,
    })
      .then(res => {
        if (res.code === 0) {
          getInit();
          setExchangeVisible(false);
        } else {
          showToast({
            title: res.errors || '换品加载失败，请联系管理员',
            icon: 'none',
          });
        }
      })
      .catch(() => {
        showToast({
          title: '换品加载失败，请联系管理员',
          icon: 'none',
        });
      });
  };

  useDidShow(() => {
    // 切换后台刷新数据
    console.log('didShow');
    getInit();
  });

  return (
    <View className={styles['invitetask-detail-wrap']}>
      <View className={styles['header']}>
        <View className={styles['tips']}>
          礼物发货时间：领取后30个工作日 <br />
          物流查询：小熊美术APP-【我的】-【订单物流】
        </View>
      </View>
      <View className={styles['section']}>
        {state.taskList.map(item => {
          return (
            <View key={item.id} className={styles['task-item-box']}>
              <Taskitem
                showGoods={item}
                handleBtn={handleBtn}
                handleChange={() => handleExchange(item)}
              />
            </View>
          );
        })}
      </View>
      {!state.taskList.length && (
        <View className={styles['none']}>
          <Image
            src={listNone}
            mode='widthFix'
            className={styles['none-img']}
          />
          <View>暂无记录</View>
        </View>
      )}
      {/* 弹窗 */}
      <Postermodal
        show={posterVisible}
        close={handlePosterModal}
        saveSuccess={saveSuccess}
      />
      <Overmodal show={overVisible} close={handleOverModal} type={overType} />
      <ExchangeGifts
        show={exchangeVisible}
        close={handleExchange}
        handleBtn={hanleExchangeBtn}
        price={overChangedGood.costPrice}
        przzieId={overChangedGood.id}
      />
    </View>
  );
}
