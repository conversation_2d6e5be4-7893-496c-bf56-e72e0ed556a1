import { View, Image, ScrollView, Text } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { ellipsisWord } from '@/utils';
import { AtIcon } from 'taro-ui';
import { useMemo, useState } from 'react';
import { useReducerContext } from '../../reduce/index';
import Countdown from '../countdown';
import Userinfobtn from '../userinfobtn';
import styles from './index.module.scss';

export default function Taskprogress({ progressBtnClick }) {
  const { state } = useReducerContext();
  // 展示更多
  const [showMore, setShowMore] = useState(false);
  // 任务列表选择id
  const [currentIndex, setCurrentIndex] = useState('');

  // 进行中任务列表
  const showTaskList = useMemo(() => {
    if (state.taskList.length > 0) {
      let _a = state.taskList.filter(v =>
        ['ACTIVING', 'NOGET'].includes(v.status),
      );
      return _a;
    }
    return [];
  }, [state.taskList]);
  // banner展示物品
  const showGoods = useMemo(() => {
    const { taskList, taskGoods, retainGoodsId } = state;
    if (taskGoods.length === 0) {
      return {
        title: '',
        img: '',
        status: 'DEFAULT',
        teams: [],
        losetime: '',
        nowTime: '',
        num: '',
      };
    }
    // 无任务且关闭利益弹窗
    if (taskList.length === 0) {
      return taskGoods[0];
    }
    if (currentIndex) {
      return taskList.find(v => v.id == currentIndex) || taskList[0];
    }
    if (showTaskList.length == 0) {
      return taskGoods.find(v => v.goodsId == retainGoodsId) || taskGoods[0];
    }

    return taskList[0];
  }, [
    state.taskGoods,
    state.taskList,
    state.retainGoodsId,
    currentIndex,
    showTaskList,
  ]);

  // banner 按钮
  const getBtnClass = useMemo(() => {
    const _class = {
      ACTIVING: styles['task-btn-box_yq'],
      NOGET: styles['task-btn-box_wc'],
    };
    const _key = (showGoods && showGoods['status']) || '';
    return _class[_key] || styles['task-btn-box_def'];
  }, [showGoods]);
  // 是否展示point
  const isShowPoint = useMemo(() => {
    const _status = (showGoods && showGoods.status) || '';
    return !_status || (_status && ['DEFAULT', 'ACTIVING'].includes(_status));
  }, [showGoods]);
  // 是否显示倒计时
  const isShowCountdown = useMemo(() => {
    return showGoods && showGoods.status && showGoods.status == 'ACTIVING';
  }, [showGoods]);
  // banner任务是否展示邀请好友
  const isShowRecord = useMemo(() => {
    return showGoods && ['ACTIVING', 'NOGET'].includes(showGoods.status);
  }, [showGoods]);

  const getSubjectStatus = item => {
    const { status } = item;
    return status === 'COMPLETE' ? '已完课' : '待完课';
  };

  const getSubjectName = item => {
    const { subject } = item;
    const _subject = {
      MUSIC_APP: '音乐',
      ART_APP: '美术',
      WRITE_APP: '写字',
    };
    return _subject[subject];
  };

  // 事件
  const handleCheck = () => {
    setShowMore(val => !val);
  };

  const handleBtn = () => {
    progressBtnClick(showGoods, 'progress');
  };

  const handleList = item => {
    setCurrentIndex(item.id);
  };

  const jumpPage = () => {
    Taro.navigateTo({
      url: '/pages/invitetask/detail/index',
    });
  };

  return (
    <View className={styles['taskprogress-wrap']}>
      <View className={styles['title']}></View>
      {/* 展示banner任务 */}
      <View className={styles['goods-wrap']}>
        <View className={styles['good-introduce']}>
          <View className={styles['good-show']}>
            <Image mode='widthFix' src={showGoods?.img || ''} />
          </View>
          <View className={styles['good-status']}>
            <View className={styles['good-name']}>
              {ellipsisWord(showGoods?.title || '')}
            </View>
            <View className={styles['good-flex-1']}>
              邀请
              <Text className={styles['good-flex-1-span']}>
                {showGoods.num}位
              </Text>
              好友立即领好礼
            </View>
            {isShowCountdown && (
              <View className={styles['countdown']}>
                <Countdown
                  nowTime={showGoods?.nowTime}
                  losetime={showGoods?.losetime}
                />
              </View>
            )}
          </View>
        </View>
        <Userinfobtn>
          <View className={styles['task-btn']} onClick={handleBtn}>
            <View className={`${styles['task-btn-box']} ${getBtnClass}`}></View>
            {isShowPoint && <View className={styles['task-btn-point']}></View>}
          </View>
        </Userinfobtn>
        {/* 展示任务要求好友列表 */}
        {isShowRecord && (
          <View className={styles['task-record']}>
            {showGoods && showGoods?.teams.length > 0 ? (
              <>
                <View
                  className={`${styles['task-record-section']} ${
                    styles['task-record-section_experience']
                  } ${showMore ? styles['showmore-experience'] : ''}`}
                >
                  {showGoods.teams.map(item => {
                    return (
                      <View
                        className={styles['task-record-item']}
                        key={item.id}
                      >
                        <View className={styles['task-record-item-tel']}>
                          {item.mobile}
                        </View>
                        {item.subject !== 'ART_APP' && (
                          <View className={styles['task-record-item-status']}>
                            {getSubjectStatus(item)}
                          </View>
                        )}
                        <View className={styles['task-record-item-subject']}>
                          已领{getSubjectName(item)}
                        </View>
                      </View>
                    );
                  })}
                </View>
                <View
                  className={styles['task-record-tips']}
                  onClick={handleCheck}
                >
                  {showMore ? '收起' : '更多邀请记录'}
                  <AtIcon
                    value={showMore ? 'chevron-up' : 'chevron-down'}
                    size='16'
                    color='#999999'
                  ></AtIcon>
                </View>
              </>
            ) : (
              <View
                className={`${styles['task-record-section']} ${styles['task-record-section_noexperience']}`}
              >
                暂无好友体验，快去邀请好友吧！
              </View>
            )}
          </View>
        )}
      </View>
      {/* 展示进行中任务列表 */}
      {showTaskList.length > 0 && (
        <View className={styles['task-all']}>
          <View className={styles['task-all-scroll']}>
            <View className={styles['task-all-scroll_box']}>
              <ScrollView scrollX className={styles['scrollview']}>
                <View className={styles['content']}>
                  {showTaskList.map((item, index) => {
                    return (
                      <View
                        className={`${styles['task-all-item']} ${
                          item.id == currentIndex ||
                          (!currentIndex && index == 0)
                            ? styles['task-all-item_active']
                            : ''
                        }`}
                        key={item.id}
                        onClick={() => handleList(item)}
                      >
                        <View className={styles['task-all-item-good']}>
                          <Image src={item.img} mode='widthFix' />
                        </View>
                        {item.status === 'NOGET' && (
                          <View className={styles['task-item-index']}></View>
                        )}
                        {item.status === 'ACTIVING' && (
                          <View
                            className={styles['task-item-index-active']}
                          ></View>
                        )}
                      </View>
                    );
                  })}
                </View>
              </ScrollView>
            </View>
          </View>
          <View
            onClick={jumpPage}
            className={styles['task-all-item_word']}
          ></View>
        </View>
      )}
    </View>
  );
}
