@function bgUrl($name) {
  @return 'https://fe-cdn.xiaoxiongmeishu.com/xiaoxiongmpactivity/weapp/wx9b8dcac074fb3151/common/#{$name}';
}

.taskprogress-wrap {
  width: 700px;
  min-height: 515px;
  margin: -68px auto 0;
  background: #fff7e1;
  position: relative;
  box-shadow: 0px 3px 6px 0px #ffb357;
  border-radius: 30px;
  border: 3px solid #fc5c3b;
  box-sizing: border-box;
  padding: 28px 20px;
  .title {
    width: 100%;
    height: 56px;
    background-image: url(bgUrl('it-taskprogress-title.png'));
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
  }
  .goods-wrap {
    width: 660px;
    // min-height: 444px;
    background: #ffffff;
    box-shadow: 0px 2px 13px 0px rgba(135, 130, 101, 0.17);
    margin-top: 15px;
    border-radius: 30px;
    padding: 20px 30px 24px 20px;
    box-sizing: border-box;
    .good-introduce {
      display: flex;
    }
    .good-show {
      width: 200px;
      height: 200px;
      background: #f1f1f1;
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 32px;
      overflow: hidden;
      image {
        width: 100%;
        display: block;
      }
    }
    .good-status {
      flex: 1;
      margin-left: 48px;
      display: flex;
      // align-items: center;
      flex-direction: column;
      align-content: center;
      .good-name {
        text-align: left;
        max-height: 90px;
        font-size: 32px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #e40b28;
        line-height: 45px;
        margin-top: 8px;
        overflow: hidden;
      }
      .good-flex-1 {
        text-align: left;
        height: 33px;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #7c2e1a;
        line-height: 33px;
        margin-top: 8px;
      }
      .good-flex-1-span {
        height: 30px;
        background: #fd5342;
        border-radius: 8px;
        color: #ffffff;
        padding: 0 4px;
      }
      .countdown {
        height: 28px;
        font-size: 20px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #b06000;
        line-height: 28px;
        text-align: left;
        margin-top: 4px;
        .process-main-countdown {
          font-family: PingFangSC-Regular, PingFang SC;
        }
      }
    }
    .task-btn {
      width: 548px;
      height: 97px;
      margin: 22px auto;
      position: relative;
      .task-btn-box {
        width: 100%;
        height: 100%;
        background-size: contain;
        background-position: 0 0;
        background-repeat: no-repeat;
        &_def {
          background-image: url(bgUrl('it-task-btn-1.png'));
        }
        &_yq {
          background-image: url(bgUrl('it-task-btn-2.png'));
        }
        &_wc {
          background-image: url(bgUrl('it-task-btn-3.png'));
        }
      }
      .task-btn-point {
        width: 73px;
        height: 75px;
        background-image: url(bgUrl('it-task-point.png'));
        background-size: contain;
        background-position: 0 0;
        background-repeat: no-repeat;
        position: absolute;
        right: 100px;
        top: 30px;
        animation: scaleDrew 1.2s ease-in-out infinite;
      }
    }
    .task-record {
      width: 100%;
      margin-top: 32px;
      border-top: 1px dotted #979797;
      padding-top: 20px;
      .task-record-section {
        width: 600px;
        background: #fff0f0;
        border-radius: 10px;
        margin: 0 auto;
        box-sizing: border-box;
        &_noexperience {
          height: 50px;
          font-size: 20px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #999990;
          line-height: 50px;
          text-align: center;
        }
        &_experience {
          height: 50px;
          padding: 11px 18px 11px 41px;
          transition: height 1.5s;
          overflow: hidden;
          &.showmore-experience {
            min-height: 50px;
            height: auto;
          }
          .task-record-item {
            width: 100%;
            height: 28px;
            font-size: 20px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #666666;
            overflow: hidden;
            margin-bottom: 10px;
          }
          .task-record-item-tel {
            float: left;
          }
          .task-record-item-status {
            float: right;
            margin-left: 130px;
          }
          .task-record-item-subject {
            float: right;
          }
        }
      }
    }
    .task-record-tips {
      margin-top: 26px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 30px;
      font-size: 22px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999990;
      line-height: 30px;
    }
  }
  .task-all {
    width: 100%;
    margin-top: 30px;
    display: flex;
    .task-all-scroll {
      width: 520px;
      height: 156px;
      background: #ffffff;
      border-radius: 16px;
      position: relative;
      &::before {
        content: '';
        width: 0;
        height: 0;
        border-bottom: 20px solid #ffffff;
        border-right: 20px solid transparent;
        border-left: 20px solid transparent;
        position: absolute;
        left: 50%;
        top: -16px;
        transform: translateX(-50%);
      }
      &_box {
        position: relative;
        overflow: hidden;
        width: 520px;
        height: 156px;
        box-sizing: border-box;
        .scrollview {
          width: 100%;
          height: 100%;
        }
        .content {
          position: absolute;
          top: 0;
          left: 0;
          display: flex;
          padding-right: 20px;
        }
        .content::scrollbar {
          display: none;
        }
      }
    }
    .task-all-item {
      width: 112px;
      height: 112px;
      box-sizing: border-box;
      position: relative;
      border-radius: 16px;
      margin-left: 20px;
      margin-top: 21px;
      &_word {
        width: 112px;
        height: 114px;
        margin-left: 20px;
        margin-top: 21px;
        background-image: url(bgUrl('it-task-alllist.png'));
        background-size: cover;
        background-position: 0 0;
        background-repeat: no-repeat;
      }
      &_active {
        padding: 3px;
        background-image: url(bgUrl('it-task-card-border.png'));
        background-size: cover;
        background-position: 0 0;
        background-repeat: no-repeat;
        overflow: hidden;
        box-sizing: border-box;
      }
      .task-all-item-good {
        width: 100%;
        height: 100%;
        border-radius: 16px;
        overflow: hidden;
        font-size: 0;
        image {
          width: 100%;
        }
      }
      .task-item-index {
        width: 93px;
        height: 27px;
        background-image: url(bgUrl('it-task-list-item-available.png'));
        background-size: contain;
        background-position: 0 0;
        background-repeat: no-repeat;
        position: absolute;
        left: 0;
        top: 0;
      }
      .task-item-index-active {
        width: 93px;
        height: 27px;
        background-image: url(bgUrl('it-task-list-item-activite.png'));
        background-size: contain;
        background-position: 0 0;
        background-repeat: no-repeat;
        position: absolute;
        left: 0;
        top: 0;
      }
    }
  }
}
@keyframes scaleDrew {
  /* 定义关键帧、scaleDrew是需要绑定到选择器的关键帧名称 */
  0% {
    transform: scale(1);
  }

  25% {
    transform: scale(1.1);
  }

  50% {
    transform: scale(1);
  }

  75% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
