@function bgUrl($name) {
  @return 'https://fe-cdn.xiaoxiongmeishu.com/xiaoxiongmpactivity/weapp/wx9b8dcac074fb3151/common/#{$name}';
}
.moretaskList {
  width: 100%;
  .title {
    height: 61px;
    background-image: url(bgUrl('it-task-list-title.png'));
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    margin-bottom: 21px;
  }
  .list-wrap {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 18px;
    .list-item {
      width: 336px;
      height: 500px;
      background: #ffffff;
      box-shadow: 9px 10px 10px 0px rgba(0, 0, 0, 0.03);
      border-radius: 20px;
      margin-bottom: 21px;
      box-sizing: border-box;
      padding: 18px 18px 24px 18px;
      position: relative;
    }

    .list-item-show {
      width: 300px;
      height: 300px;
      background: #f4f3e7;
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      position: relative;
      img {
        width: 100%;
      }
      &.list-item_activite {
        &::before {
          content: ' ';
          width: 133px;
          height: 42px;
          background-image: url(bgUrl('it-task-list-item-activite.png'));
          background-size: contain;
          background-position: center;
          background-repeat: no-repeat;
          position: absolute;
          left: 0;
          top: 0;
        }
      }
      &.list-item_noget {
        &::before {
          content: ' ';
          width: 133px;
          height: 42px;
          background-image: url(bgUrl('it-task-list-item-available.png'));
          background-size: contain;
          background-position: center;
          background-repeat: no-repeat;
          position: absolute;
          left: 0;
          top: 0;
        }
      }
    }
    .list-item-title {
      height: 76px;
      font-size: 26px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 400;
      color: #7e2f16;
      line-height: 33px;
      text-align: left;
      margin-top: 10px;
      text-indent: 90px;
      position: relative;
      word-break: break-all;
    }
    .list-item-num {
      height: 33px;
      font-size: 18px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 33px;
      background: #ff8e01;
      border-radius: 6px;
      position: absolute;
      left: 0;
      top: 0;
      padding: 0 13px 0 7px;
      text-indent: 0;
    }
    .my-class {
      .btn-name {
        text-align: center;
        font-size: 28px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #000000;
        line-height: 60px;
      }
    }
    .list-item-btn {
      width: 268px;
      height: 72px;
      background-image: url(bgUrl('it-task-list-item-btn.png'));
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
      margin: 8px auto 0;
      &_noget {
        background-image: url(bgUrl('it-task-detail-btn2.png'));
      }
      &_changed {
        width: 256px;
        height: 60px;
        background-image: url(bgUrl('it-changed-bhbtn.png'));
      }
      .btn-name {
        text-align: center;
        font-size: 28px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 60px;
      }
    }
  }
}
