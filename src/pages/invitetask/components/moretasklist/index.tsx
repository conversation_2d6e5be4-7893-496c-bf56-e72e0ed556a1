import { ellipsisWord } from '@/utils';
import { View, Image } from '@tarojs/components';
import { useReducerContext } from '../../reduce';
import { COINS } from '../../reduce/const';
import Userinfobtn from '../userinfobtn';
import styles from './index.module.scss';

export default function Moretasklist({ handlePrizedetail, progressBtnClick }) {
  const { state, dispatch } = useReducerContext();

  const getShowGoodsClass = item => {
    const _class = {
      ACTIVING: 'list-item_activite',
      NOGET: 'list-item_noget',
    };
    return _class[item.status] ? styles[_class[item.status]] : '';
  };

  const getBtnClass = item => {
    if (item.excessInventory <= 0) {
      return styles['list-item-btn_changed'];
    }
    return item.status === 'NOGET' ? styles['list-item-btn_noget'] : '';
  };

  const getBtnName = item => {
    const { status, excessInventory } = item;
    if (excessInventory <= 0) {
      return '补货中';
    }
    if (['ACTIVING', 'NOGET'].includes(status)) {
      const _name = {
        ACTIVING: '立即邀请',
        NOGET: '领取礼物',
      };
      return _name[status];
    }
    return '点击领取';
  };

  const handleClick = item => {
    if (COINS.includes(item.epc)) {
      return;
    }
    dispatch({ type: 'getShowGoodsId', payload: { showGoodId: item.goodsId } });
    handlePrizedetail();
  };

  const handleTask = item => {
    progressBtnClick(item);
  };

  return (
    <View className={styles['moretaskList']}>
      <View className={styles['title']}></View>
      <View className={styles['list-wrap']}>
        {state.taskGoods.map(item => {
          return (
            <View className={styles['list-item']} key={item.epc}>
              <View
                className={`${styles['list-item-show']} ${getShowGoodsClass(
                  item,
                )}`}
                onClick={() => handleClick(item)}
              >
                <Image src={item.img} mode='widthFix' />
              </View>
              <View className={styles['list-item-title']}>
                {ellipsisWord(item.title, 15, 15)}
                <View className={styles['list-item-num']}>
                  邀{item.num}人领
                </View>
              </View>

              <Userinfobtn authSuccess={() => handleTask(item)}>
                <View
                  className={`${styles['list-item-btn']} ${getBtnClass(item)}`}
                >
                  <View className={styles['btn-name']}>{getBtnName(item)}</View>
                </View>
              </Userinfobtn>
            </View>
          );
        })}
      </View>
    </View>
  );
}
