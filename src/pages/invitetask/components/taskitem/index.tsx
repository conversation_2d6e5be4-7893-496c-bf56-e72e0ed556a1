import { dateFormat, ellipsisWord } from '@/utils';
import { View, Image, Text } from '@tarojs/components';
import btnIcon2 from '@/assets/invitetask/it-changed-btn2.png';
import { AtIcon } from 'taro-ui';
import { useState } from 'react';
import Countdown from '../countdown';
import styles from './index.module.scss';

export default function Taskitem({ showGoods, handleBtn, handleChange }) {
  const [showMore, setShowMore] = useState(false);
  const wrapClass = () => {
    const { status, losetime } = showGoods;
    if (status === 'NOGET') {
      return styles['goods-wrap_noget'];
    }
    if (status === 'ACTIVING') {
      if (losetime == '0' || !losetime) {
        return styles['goods-wrap_dwk'];
      }
      return styles['goods-wrap_activing'];
    }
    return '';
  };

  const btnClass =
    showGoods.status === 'NOGET' ? styles['btn2'] : styles['btn1'];

  const btnName = showGoods.status === 'NOGET' ? '领取礼物' : '立即邀请';

  const isShowBtn = ['ACTIVING', 'NOGET'].includes(showGoods.status);
  const isShowTeams = ['ACTIVING', 'NOGET', 'GET', 'REPLAE', 'OVER2'].includes(
    showGoods.status,
  );

  const overSeasn = () => {
    const { status, getTime } = showGoods;
    if (status === 'OVER2' || status === 'REPLACE') {
      return '因礼物兑完，任务中止';
    }
    if (status === 'OVER1') {
      return '任务已到时间';
    }
    const timeText = dateFormat(getTime * 1, 'yyyy-MM-dd-hh:mm:ss').split('-');
    return `领取时间：${timeText[0]}年${timeText[1]}月${timeText[2]}日 ${timeText[3]}`;
  };
  const overIconClass = () => {
    const { status } = showGoods;
    if (['OVER2', 'OVER1', 'REPLACE'].includes(status)) {
      return styles['over-icon-1'];
    }
    if (status === 'GET') {
      return styles['over-icon-2'];
    }
    return '';
  };

  const getSubjectStatus = item => {
    const { status } = item;
    return status === 'COMPLETE' ? '已完课' : '待完课';
  };

  const getSubjectName = item => {
    const { subject } = item;
    const _subject = {
      MUSIC_APP: '音乐',
      ART_APP: '美术',
      WRITE_APP: '写字',
    };
    return _subject[subject];
  };

  const handleCheck = () => {
    setShowMore(val => !val);
  };

  return (
    <View className={styles['taskdetail-item-box']}>
      <View className={`${styles['goods-wrap']} ${wrapClass}`}>
        <View className={styles['good-introduce']}>
          <View className={styles['good-show']}>
            <Image src={showGoods.img} mode='widthFix' />
          </View>
          {isShowBtn ? (
            <View className={styles['good-status']}>
              <View className={styles['good-name']}>
                {ellipsisWord(showGoods.title, 20, 10)}
              </View>
              <View className={styles['good-flex-1']}>
                邀请
                <Text className={styles['good-flex-1-span']}>
                  {showGoods.num}位
                </Text>
                好友立即领好礼
              </View>
              <View className={styles['countdown']}>
                <Countdown
                  losetime={showGoods.losetime}
                  nowTime={showGoods.nowTime}
                />
              </View>
              <View
                className={styles['task-btn']}
                onClick={() => handleBtn(showGoods)}
              >
                <View className={`${styles['task-btn-box']} ${btnClass}`}>
                  {btnName}
                </View>
              </View>
            </View>
          ) : (
            <View className={styles['good-status-over']}>
              <View className={styles['over-center']}>
                <View className={styles['good-name']}>
                  {ellipsisWord(showGoods.title)}
                </View>
                <View className={styles['over-reason']}>{overSeasn()}</View>
                {showGoods.status === 'REPLACE' && (
                  <View
                    className={styles['over-change']}
                    onClick={() => handleChange(showGoods)}
                  >
                    <Image src={btnIcon2} mode='widthFix' />
                  </View>
                )}
              </View>
              <View
                className={`${styles['over-icon']} ${overIconClass()}`}
              ></View>
            </View>
          )}
        </View>
        {isShowTeams && (
          <View className={styles['task-record']}>
            {showGoods.teams && showGoods.teams.length == 0 ? (
              <View
                className={`${styles['task-record-section']} ${styles['task-record-section_noexperience']}`}
              >
                暂无好友体验，快去邀请好友吧！
              </View>
            ) : (
              <>
                <View
                  className={`${styles['task-record-section']} ${
                    styles['task-record-section_experience']
                  } ${showMore ? styles['showmore-experience'] : ''}`}
                >
                  {showGoods.teams.map(item => {
                    return (
                      <View
                        className={styles['task-record-item']}
                        key={item.id}
                      >
                        <View className={styles['task-record-item-tel']}>
                          {item.mobile}
                        </View>
                        {item.subject !== 'ART_APP' && (
                          <View className={styles['task-record-item-status']}>
                            {getSubjectStatus(item)}
                          </View>
                        )}
                        <View className={styles['task-record-item-subject']}>
                          已领{getSubjectName(item)}课
                        </View>
                      </View>
                    );
                  })}
                </View>
                {showGoods.teams.length > 1 && (
                  <View
                    className={styles['task-record-tips']}
                    onClick={handleCheck}
                  >
                    {showMore ? '收起' : '更多邀请记录'}
                    <AtIcon
                      value={showMore ? 'chevron-up' : 'chevron-down'}
                      size='16'
                      color='#999999'
                    ></AtIcon>
                  </View>
                )}
              </>
            )}
          </View>
        )}
      </View>
    </View>
  );
}
