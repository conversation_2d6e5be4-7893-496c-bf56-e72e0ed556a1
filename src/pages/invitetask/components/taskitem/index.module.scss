@function bgUrl($name) {
  @return 'https://fe-cdn.xiaoxiongmeishu.com/xiaoxiongmpactivity/weapp/wx9b8dcac074fb3151/common/#{$name}';
}
.taskdetail-item-box {
  width: 100%;
  background: #ffffff;
  box-shadow: 0px 2px 13px 0px rgba(135, 130, 101, 0.17);
  border-radius: 30px;
  .goods-wrap {
    padding: 20px 30px 24px 20px;
    box-sizing: border-box;
    position: relative;
    &::before {
      content: '';
      width: 133px;
      height: 42px;
      background-size: contain;
      background-position: 0 0;
      background-repeat: no-repeat;
      position: absolute;
      left: 0;
      top: 0;
    }
    &_activing {
      &::before {
        background-image: url(bgUrl('it-task-list-item-activite.png'));
      }
    }
    &_noget {
      &::before {
        background-image: url(bgUrl('it-task-list-item-available.png'));
      }
    }
    &_dwk {
      &::before {
        background-image: url(bgUrl('it-task-list-item-dwk.png'));
      }
    }
    .good-introduce {
      display: flex;
    }
    .good-show {
      width: 234px;
      height: 234px;
      background: #f1f1f1;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 32px;
      border-radius: 20px;
      overflow: hidden;
      image {
        width: 100%;
        display: block;
      }
    }
    .good-status-over {
      flex: 1;
      margin-left: 48px;
      display: flex;
      align-content: center;
      align-items: center;
      position: relative;
      .over-center {
        flex-direction: column;
        display: flex;
        align-content: center;
      }
      .good-name {
        text-align: left;
        max-height: 90px;
        font-size: 32px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #000000;
        line-height: 45px;
        overflow: hidden;
      }
      .over-reason {
        height: 28px;
        font-size: 20px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #7c2e1a;
        line-height: 28px;
        margin-top: 17px;
      }
      .over-change {
        width: 190px;
        height: 60px;
        margin-top: 23px;
        image {
          width: 100%;
        }
      }
      .over-icon {
        width: 133px;
        height: 133px;
        background-size: contain;
        background-position: 0 0;
        background-repeat: no-repeat;
        position: absolute;
        right: 0;
        top: 0;
        z-index: 10;
        &.over-icon-1 {
          background-image: url(bgUrl('it-task-list-item-over1.png'));
        }
        &.over-icon-2 {
          background-image: url(bgUrl('it-task-list-item-over2.png'));
        }
      }
    }
    .good-status {
      flex: 1;
      margin-left: 48px;
      display: flex;
      flex-direction: column;
      align-content: center;
      position: relative;
      .good-name {
        text-align: left;
        max-height: 90px;
        font-size: 32px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #e40b28;
        line-height: 45px;
        overflow: hidden;
      }
      .good-flex-1 {
        text-align: left;
        height: 33px;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #7c2e1a;
        line-height: 33px;
        margin-top: 8px;
      }
      .good-flex-1-span {
        height: 30px;
        background: #fd5342;
        border-radius: 8px;
        color: #ffffff;
        padding: 0 4px;
      }
      .countdown {
        height: 28px;
        font-size: 20px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #b06000;
        line-height: 28px;
        text-align: left;
        margin-top: 4px;
      }
    }
    .task-btn {
      width: 268px;
      height: 72px;
      position: absolute;
      left: 0;
      bottom: -8px;
      .task-btn-box {
        width: 100%;
        height: 100%;
        background-size: contain;
        background-position: 0 0;
        background-repeat: no-repeat;
        font-size: 28px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        text-align: center;
        line-height: 60px;
        &.btn1 {
          background-image: url(bgUrl('it-task-detail-btn1.png'));
        }
        &.btn2 {
          background-image: url(bgUrl('it-task-detail-btn2.png'));
        }
      }
    }
    .task-record {
      width: 100%;
      margin-top: 32px;
      border-top: 1px dotted #979797;
      padding-top: 20px;
      .task-record-section {
        width: 600px;
        background: #fff0f0;
        border-radius: 10px;
        margin: 0 auto;
        box-sizing: border-box;
        &_noexperience {
          height: 50px;
          font-size: 20px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #999990;
          line-height: 50px;
          text-align: center;
        }
        &_experience {
          height: 50px;
          padding: 11px 18px 11px 41px;
          transition: height 1.5s;
          overflow: hidden;
          &.showmore-experience {
            min-height: 50px;
            height: auto;
          }
          .task-record-item {
            width: 100%;
            height: 28px;
            font-size: 20px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #666666;
            overflow: hidden;
            margin-bottom: 10px;
          }
          .task-record-item-tel {
            float: left;
          }
          .task-record-item-status {
            float: right;
            margin-left: 130px;
          }
          .task-record-item-subject {
            float: right;
          }
        }
      }
    }
    .task-record-tips {
      margin-top: 26px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 30px;
      font-size: 22px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999990;
      line-height: 30px;
    }
  }
}
