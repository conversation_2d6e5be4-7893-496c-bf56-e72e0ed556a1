import { Swiper, SwiperItem, Image, View, RichText } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useCallback, useEffect, useState } from 'react';
import { AtFloatLayout } from 'taro-ui';
import { getforbiddenlist } from '@/api/groupbuy';
import { getGoodsInfoApi } from '@/api/invitetask';
import defaultIcon from '@/assets/invitetask/product-default.png';
import { nodesRep } from '@/utils';
import { useReducerContext } from '../../reduce';
import './index.scss';

/* 
  paymentType   COIN（金币支付）CLASS（课时支付）CASH（现金支付） COINANDCASH（金币加现金）CLASSCASH（课时加现金) 
  coinPrice  金币
  classPrice  课时
  cashPrice 现金
  oneClassExchangePrice  1v1课时类型兑换价
  smallClassExchangePrice  小班课课时类型兑换价
*/
const matchType = {
  COIN: 'coinPrice',
  CLASS: 'classPrice',
  CASH: 'cashPrice',
  COINANDCASH: ['coinPrice', 'cashPrice'],
  CLASSCASH: ['classPrice', 'cashPrice'],
};

interface PrizedetailsProps {
  show: boolean;
  handlePrizedetail: () => void;
}

export default function Prizedetails({
  show,
  handlePrizedetail,
}: PrizedetailsProps) {
  const { state } = useReducerContext();

  const [prizeDetailData, setPrizeDetailData] = useState<any>({});
  const [banner, setBanner] = useState([]);
  const [prizeDetailShow, setPrizeDetailShow] = useState(false);
  const [text, setText] = useState<any[]>([]);

  const getGoodsInfo = useCallback(() => {
    if (!state.showGoodId) return;
    Taro.showLoading({ title: '加载中' });
    getGoodsInfoApi({
      businessType: 'BEAR',
      goodsId: state.showGoodId,
      currencyType: 'STONE',
    }).then(res => {
      if (res.code == 0) {
        let data = res.payload;
        if (data.imGoodsPaymentList.length) {
          let _it = data.imGoodsPaymentList.find(
            o => o.exhibitionStatus == 'SHOW',
          );
          if (!['COINANDCASH', 'CLASSCASH'].includes(data.paymentType)) {
            data.exchangePrice = _it[matchType[data.paymentType]];
          } else {
            data.exchangePrice =
              _it[matchType[data.paymentType][0]] +
              '+' +
              _it[matchType[data.paymentType][1]];
          }
        }
        getforbiddenlist({ productCode: data.epc }).then(res1 => {
          if (res1.data) {
            data._banList = res1.data.join('，');
          }
          setPrizeDetailData(data);
          const _banner = data.img
            ? data.img.split(',')
            : data.slideshow.split(',')
            ? data.slideshow.split(',')
            : [defaultIcon];
          setBanner(_banner);
          // 实物商品不显示使用规则

          if (data && data.type != 1) {
            setText([
              {
                title: '使用规则',
                part: data.rule || '暂无使用规则',
              },
              {
                title: '商品详情',
                part: data.imageText || '暂无商品详情',
              },
            ]);
          } else {
            setText([
              {
                title: '商品详情',
                part: data.imageText || '暂无商品详情',
              },
            ]);
          }
        });
      }
      Taro.hideLoading();
      setPrizeDetailShow(true);
    });
  }, [state.showGoodId]);

  const swiperchange = EventHandle => {
    console.log(EventHandle);
  };

  useEffect(() => {
    if (!show) {
      setPrizeDetailShow(false);
    }
    show && getGoodsInfo();
  }, [show]);

  return (
    <AtFloatLayout
      isOpened={prizeDetailShow}
      className='prize-at-layout'
      onClose={handlePrizedetail}
    >
      <View className='product-banner'>
        <View className='swiper-box'>
          <Swiper
            className='swiperview'
            circular
            autoplay
            onChange={swiperchange}
          >
            {banner.map((item, index) => {
              return (
                <SwiperItem key={`product${index}`} className='swiperitem'>
                  <View className='img-box'>
                    <Image src={item} className='pic' mode='widthFix' />
                  </View>
                  <View className='swiper-pagination'>{`${index + 1}/${
                    banner.length
                  }`}</View>
                </SwiperItem>
              );
            })}
          </Swiper>
        </View>
      </View>
      <View className='product-decorate'>
        <View className='product-name'>{prizeDetailData.epcReferName}</View>
        <View className='product-price'></View>
      </View>
      <View className='product-detail-list'>
        {text.map((item, index) => {
          return (
            <View className='part' key={index}>
              <View className='title'>{item.title}</View>
              {prizeDetailData._banList && index == 0 && (
                <View className='product-forbidden'>
                  禁发区域：{prizeDetailData._banList}
                </View>
              )}
              <View className='row'>
                <RichText nodes={nodesRep(item.part)} />
              </View>
            </View>
          );
        })}
      </View>
    </AtFloatLayout>
  );
}
