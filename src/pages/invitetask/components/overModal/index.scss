@function bgUrl($name) {
  @return 'https://fe-cdn.xiaoxiongmeishu.com/xiaoxiongmpactivity/weapp/wx9b8dcac074fb3151/common/#{$name}';
}
.overmodal-wrap {
  .at-curtain__btn-close {
    display: none;
  }
}
.curtain-wrap {
  position: relative;
  font-size: 0;
  image {
    width: 100%;
    display: block;
  }
  .at-curtain-wrap {
    background-color: rgba(0, 0, 0, 0.7);
  }
  .at-curtain__btn-close {
    display: none;
  }
  .at-curtain__container {
    width: 100%;
  }
  .save {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 488px;
    .content_wrapper {
      position: fixed;
      top: 430px;
      z-index: 100;
      padding: 0px 66px;
      width: 456px;
      height: 488px;
      background: linear-gradient(
        180deg,
        #ffffff 0%,
        #ffffff 85%,
        #ffbbbb 100%
      );
      border-radius: 30px;
      text-align: center;
      box-sizing: border-box;
      &.content_wrapper_changed {
        height: 548px;
        padding: 0px 56px;
      }
      &_img {
        top: -30px;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        width: 175px;
        height: 200px;
      }
      &_img_changed {
        width: 100%;
        height: 270px;
        margin-top: 14px;
        margin-bottom: 6px;
      }
      &_title {
        margin-top: 175px;
        font-size: 40px;
        font-family: 'PingFangSC-Medium, PingFang SC';
        font-weight: 500;
        color: #2f2f2f;
      }
      &_des {
        font-size: 30px;
        margin-top: 8px;
        font-family: 'PingFangSC-Regular, PingFang SC';
        font-weight: 400;
        color: #6c6c6c;
      }
      &_btn {
        width: 240px;
        height: 80px;
        margin: 0 auto;
        margin-top: 42px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 30px;
        font-family: 'PingFangSC-Medium, PingFang SC';
        font-weight: 500;
        color: #ffffff;
        background: linear-gradient(
          180deg,
          #fe5d44 0%,
          #fe5e44 13%,
          #fc4e40 78%,
          #fa1a36 100%
        );
        border-radius: 47px;
      }
    }
  }
  .toApp {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    background: url(bgUrl('it-task-retain-g.png')) 0 0 no-repeat;
    background-size: cover;
    .toApp-wrap {
      position: relative;
    }
    .toApp_wrap_img {
      width: 560px;
      height: 670px;
    }
    .toApp-btn {
      width: 340px;
      height: 80px;
      position: absolute;
      top: 540px;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
