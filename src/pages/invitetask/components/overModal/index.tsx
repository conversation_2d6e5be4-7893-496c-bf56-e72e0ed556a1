import { AtCurtain } from 'taro-ui';
import { View, Image } from '@tarojs/components';
import { useEffect, useState } from 'react';
import saveicon from '@/assets/invitetask/it-task-saveicon.png';
import konwicon from '@/assets/invitetask/it-task-konwbtn.png';
import toappicon from '@/assets/invitetask/it-task-toapp.png';
import bhicon from '@/assets/invitetask/it-changed-bh.png';
import './index.scss';

// type 1: 保存图片成功  2: 去app填写地址  3: 商品已兑完
export default function Overmodal({ type = '2', show, close }) {
  const [isOpened, setIsOpened] = useState(false);

  const initEl = () => {
    switch (type) {
      case '1': {
        return (
          <View className='save'>
            <View className='content_wrapper'>
              <Image
                className='content_wrapper_img'
                src={saveicon}
                mode='widthFix'
              />
              <View className='content_wrapper_title'>保存成功</View>
              <View className='content_wrapper_des'>
                海报已成功保存到相册 快去分享吧～
              </View>
              <View className='content_wrapper_btn' onClick={close}>
                知道了
              </View>
            </View>
          </View>
        );
      }
      case '2': {
        return (
          <View className='toApp'>
            <View className='toApp-wrap'>
              <Image
                className='toApp_wrap_img'
                src={toappicon}
                mode='widthFix'
              />
              <View className='toApp-btn' onClick={close}>
                <Image mode='widthFix' src={konwicon} />
              </View>
            </View>
          </View>
        );
      }
      case '3': {
        return (
          <View className='save'>
            <View className='content_wrapper content_wrapper_changed'>
              <Image
                className='content_wrapper_img_changed'
                src={bhicon}
                mode='widthFix'
              />
              <View className='content_wrapper_des'>
                商品已兑完，正在补货中 换个商品吧～
              </View>
              <View className='content_wrapper_btn' onClick={close}>
                好的
              </View>
            </View>
          </View>
        );
      }
      default:
        return null;
    }
  };

  useEffect(() => {
    setIsOpened(show);
  }, [show]);

  return (
    <AtCurtain
      isOpened={isOpened}
      onClose={close}
      customStyle={{ backgroundColor: 'rgba(0,0,0,.7)' }}
      className='overmodal-wrap'
    >
      <View className='curtain-wrap'>{initEl()}</View>
    </AtCurtain>
  );
}
