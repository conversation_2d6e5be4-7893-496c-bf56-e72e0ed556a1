import { View } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import styles from './index.module.scss';

export default function BaseLoading() {
  return (
    <View className={styles['base-loading']}>
      <View className={styles['loading-box']}>
        <View>
          <AtIcon value='loading-2' size='30' color='#999999'></AtIcon>
        </View>
        <View className={styles['word']}>加载中...</View>
      </View>
    </View>
  );
}
