.at-popup {
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
}
.at-popup-hiden {
  display: none;
}
.overlay {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}
.layout {
  border-radius: 40px 40px 0 0;
  background-color: #ffffff;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 100;
  padding-bottom: 40px;
}
.content-box {
  height: 1000px;
  overflow-y: auto;
}
.sub-title {
  font-size: 36px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #222222;
  margin-left: 30px;
  margin-bottom: 20px;
}
.sub-m-title {
  font-size: 28px;
  padding-left: 44px;
  margin: 10px 0;
}
.sub-desc {
  font-size: 28px;
  font-family: PingFangSC-Regular, PingFang SC;
  color: #666666;
  line-height: 40px;
  padding-left: 74px;
  padding-right: 30px;
}
.desc,
.descr {
  font-size: 28px;
  font-family: PingFangSC-Regular, PingFang SC;
  color: #666666;
  line-height: 40px;
  position: relative;
  padding-left: 74px;
  padding-right: 30px;
  &.color-red {
    color: #fc191c;
  }
}
.descr {
  padding-left: 46px;
  margin-top: 10px;
}
.desc::after {
  content: '';
  position: absolute;
  top: 14px;
  left: 48px;
  width: 8px;
  height: 8px;
  background: #cccccc;
  border-radius: 50%;
}
.title {
  font-size: 40px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #222222;
  text-align: center;
  padding-top: 40px;
}
.content {
  padding-bottom: 30px;
}
.close {
  position: absolute;
  top: 16px;
  right: 24px;
  width: 24px;
  height: 24px;
  padding: 20px;
  z-index: 101;
  image {
    width: 24px;
    height: 24px;
  }
}
.color-red {
  color: #fc191c;
}
