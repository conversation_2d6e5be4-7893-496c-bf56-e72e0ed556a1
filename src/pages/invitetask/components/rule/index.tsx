import { View, Image } from '@tarojs/components';
import closeIcon from '@/assets/invitetask/it-rule-close.png';
import styles from './index.module.scss';

export default function Rule({ visible, close }) {
  const handleClose = () => {
    close();
  };

  return (
    <View
      className={`styles['at-popup'] ${
        visible ? '' : styles['at-popup-hiden']
      }`}
    >
      <View className={styles['overlay']} onClick={handleClose}></View>
      <View className={styles['layout']}>
        <View className={styles['close']} onClick={handleClose}>
          <Image src={closeIcon} mode='widthFix' />
        </View>
        <View className={styles['title']}>— 活动规则 —</View>
        <View className={styles['content-box']}>
          <View className={styles['content']}>
            <View className={styles['sub-title']}>参与用户</View>
            <View className={styles['desc']}>小熊美术所有用户</View>
          </View>
          <View className={styles['content']}>
            <View className={styles['sub-title']}>推荐规则</View>
            <View className={styles['desc']}>
              每邀请1位/2位有效好友购买小熊美术体验版课程，获得礼物兑换资格
            </View>
            <View className={styles['desc']}>
              每邀请1位有效好友领取书法或音乐体验版课程，需体验版课程全部完课后，获得礼物兑换资格
            </View>
            <View className={styles['desc']}>
              活动任务领取后需2天内完成邀请，过期任务失效
            </View>
            <View className={styles['desc']}>
              用户需分享实物对应海报，才可获取该实物，分享其他活动海报无效
            </View>
            <View className={styles['desc']}>
              成功邀请好友后仅可领取对应任务的实物或小熊币，无其他基础奖励
            </View>
            <View className={styles['desc']}>
              有效好友：推荐好友需为正常有线上美术学习需求的用户且手机号并未购买该学科体验课，好友不满足以上条件则不计入有效推荐，推荐人无法获得兑换资格
            </View>
          </View>
          <View className={styles['content']}>
            <View className={styles['sub-title']}>兑换规则</View>
            <View className={styles['desc']}>
              任务选定：在活动首页选定心仪的礼物，并确定为任务。任务分为邀请1位好友/2位好友，确定任务后邀请好友，好友购买并且满足选定商品的数量要求，即可领取选定的商品
            </View>
            <View className={styles['desc']}>
              多任务：在活动首页可以选择多个心仪的礼物，可并行邀请多位好友，好友完成后均可领取已完成任务奖励
            </View>
            <View className={styles['desc']}>
              单任务邀请溢出：若单个任务邀请了2位符合条件的好友，仅可领取奖励一件，不可转让至其他任务中
            </View>
            <View className={styles['desc']}>
              完成任务后获得实物于30个工作日内发货，仅支持邮寄中国大陆地址
            </View>
            <View className={styles['desc']}>
              完成任务后获得金币奖励领取后发放，无法更换
            </View>
            <View className={styles['desc']}>
              若好友购买成功后退款，视为邀请失败
            </View>
            <View className={styles['desc']}>
              如获得实物奖励兑换资格后，在活动结束未领取对应实物，将视为放弃领取
            </View>
          </View>
          <View className={styles['content']}>
            <View className={styles['sub-title']}>其他说明</View>
            <View className={styles['desc']}>
              推荐人自己不能购买，如发现作弊，刷单等行为将取消推荐人获得奖励的资格
            </View>
            <View className={styles['desc']}>
              活动最终解释权归小熊美术所有。
            </View>
            <View className={styles['desc']}>
              如有特殊情况可联系班主任或客服处理
            </View>
          </View>
        </View>
      </View>
    </View>
  );
}
