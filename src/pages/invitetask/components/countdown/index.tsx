import { View, Text } from '@tarojs/components';
import { useEffect, useRef, useState } from 'react';
import { countDownFormat } from '@/utils';
import { useReducerContext } from '../../reduce';
import styles from './index.module.scss';

export default function Countdown({ nowTime, losetime }) {
  const { getInit } = useReducerContext();
  const taskTime = useRef(0);
  const taskTimer = useRef<any>(null);
  const [timeText, setTimeText] = useState<string[]>(['00', '00', '00', '00']);

  const startTimer = () => {
    setTimeText(['00', '00', '00', '00']);
    if (losetime && losetime - nowTime > 0) {
      taskTime.current = losetime - nowTime;
      taskTimer.current && clearInterval(taskTimer.current);
      taskTimer.current = setInterval(() => {
        taskTime.current -= 1000;
        const _timeText = countDownFormat(
          taskTime.current,
          'dd-hh-mm-ss',
        ).split('-');
        setTimeText(_timeText);
        if (taskTime.current <= 0) {
          taskTime.current = 0;
          clearInterval(taskTimer.current);
          // 倒计时结束 状态改变
          getInit();
        }
      }, 1000);
    }
  };

  useEffect(() => {
    startTimer();
    return () => {
      taskTimer.current && clearInterval(taskTimer.current);
    };
  }, [nowTime, losetime]);

  return (
    <>
      {taskTime.current > 0 && (
        <View className={styles['countdown']}>
          <Text>{timeText[0]}</Text>天<Text>{timeText[1]}</Text>小时
          <Text>{timeText[2]}</Text>分<Text>{timeText[3]}</Text>秒
        </View>
      )}
    </>
  );
}
