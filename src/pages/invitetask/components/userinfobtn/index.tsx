import { getProgramUserSubject } from '@/api/groupbuy';
import { Button, View } from '@tarojs/components';
import { setStorageSync, hideLoading } from '@tarojs/taro';
import { useReducerContext } from '../../reduce';
import styles from './index.module.scss';

const Userinfobtn = props => {
  const { authSuccess } = props;
  const { userid, openid, storeDispatch } = useReducerContext();

  // 手机号授权
  const getUserPhoneNumber = res => {
    if (res.detail.errMsg === 'getPhoneNumber:fail user deny') {
      hideLoading();
      console.log(res.detail.errMsg);
    } else {
      const { encryptedData, iv } = res.detail;
      getProgramUserSubject({
        openId: openid,
        encryptedData,
        iv,
      }).then(phone => {
        phone.payload.uid &&
          setStorageSync('__msb_user_id__', phone.payload.uid);
        phone.payload.uid &&
          storeDispatch({
            type: 'CHANGE_USERID',
            userid: phone.payload.uid,
          });
        if (phone.payload.token)
          Taro.setStorageSync('appToken', phone.payload.token);
        phone.payload.mobile &&
          storeDispatch({
            type: 'CHANGE_MOBILE',
            mobile: phone.payload.mobile,
          });
        authSuccess && authSuccess();
      });
    }
  };

  return (
    <>
      {userid ? (
        <View
          className={styles['btn-box']}
          onClick={() => {
            authSuccess && authSuccess();
          }}
        >
          {props.children}
        </View>
      ) : (
        <Button
          className={styles['base-btn']}
          openType='getPhoneNumber'
          onGetPhoneNumber={getUserPhoneNumber}
        >
          {props.children}
        </Button>
      )}
    </>
  );
};

export default Userinfobtn;
