import { View, Swiper, SwiperItem, Image, Text } from '@tarojs/components';
import { getRandRobot, getRandRobotCallBack } from '@/api/groupbuy';
import { useCallback, useEffect, useState } from 'react';
import { useReducerContext } from '../../reduce/index';
import styles from './index.module.scss';

export default function ExchangeSwiper() {
  const [robotlist, setRobotlist] = useState<getRandRobotCallBack[]>([]);
  const { state } = useReducerContext();
  const getRandRobotList = () => {
    getRandRobot({ num: 30 })
      .then(res => {
        if (res.code === 0) {
          setRobotlist(res.payload);
        } else {
          Taro.showToast({
            title: '获取中奖用户失败，请检查您的网络',
            icon: 'none',
          });
        }
      })
      .catch(() => {
        Taro.showToast({
          title: '获取中奖用户失败，请检查您的网络',
          icon: 'none',
        });
      });
  };

  const getGoodName = useCallback(() => {
    const len = state.taskGoods.length;
    if (len) {
      const index = Math.floor(Math.random() * len);
      return state.taskGoods[index]['title'];
    }
    return '商品';
  }, [state.taskGoods]);

  useEffect(() => {
    getRandRobotList();
  }, []);

  return (
    <>
      <View className={styles['swiper-container-draw']}>
        <Swiper
          className={styles['swiper-wrapper']}
          vertical
          circular
          autoplay
          interval={1000}
        >
          {robotlist.map(item => {
            return (
              <SwiperItem key={item.id}>
                <View className={styles['swiper-slide']}>
                  <View className={styles['swiper-item']}>
                    <View className={styles['head-box']}>
                      <Image
                        src={`https://s1.meixiu.mobi/${item.headimg}?x-oss-process=image/resize,h_50,m_lfit`}
                      />
                    </View>
                    <View className={styles['text']}>
                      <Text className={styles['words']}>{item.username}</Text>
                      <Text className={styles['ok']}>
                        已兑换 {getGoodName()}
                      </Text>
                    </View>
                  </View>
                </View>
              </SwiperItem>
            );
          })}
        </Swiper>
      </View>
    </>
  );
}
