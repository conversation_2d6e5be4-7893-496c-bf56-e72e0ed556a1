// 轮播图
.swiper-container-draw {
  position: absolute;
  top: 15px;
  left: 20px;
  right: 20px;
  height: 46px;
  overflow: hidden;
  .swiper-wrapper {
    height: 46px;
    .swiper-slide {
      max-width: 480px;
      .swiper-item {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: 46px;
        background: rgba(0, 0, 0, 0.3);
        border-radius: 23px;
        font-size: 22px;
        padding: 0px 20px;
        box-sizing: border-box;
        .head-box {
          display: flex;
          justify-self: center;
          align-items: center;
          width: 30px;
          height: 30px;
          border-radius: 50%;
          overflow: hidden;
          margin-right: 10px;
          background: #ffffff;
          .head {
            max-width: 100%;
            max-height: 100%;
          }
        }
        .text {
          max-width: 480px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: rgba(255, 255, 255, 1);
        }
      }
    }
  }
}
