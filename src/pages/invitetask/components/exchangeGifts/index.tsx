import { AtFloatLayout } from 'taro-ui';
import { View, Image } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { ellipsisWord } from '@/utils';
import btnIcon from '@/assets/invitetask/it-changed-btn.png';
import closeIcon from '@/assets/invitetask/it-changed-close.png';
import selectedIcon from '@/assets/invitetask/it-changed-selected.png';
import { getReplaceGoodsNewApi } from '@/api/invitetask';
import { useEffect, useState } from 'react';
import './index.scss';

export default function ExchangeGifts({
  show,
  close,
  handleBtn,
  price,
  przzieId,
}) {
  const [isOpened, setIsOpened] = useState(false);
  const [selectedEpc, setselectedEpc] = useState('');
  const [canUseGoodsList, setCanUseGoodsList] = useState<any[]>([]);

  const getReplaceGoods = () => {
    getReplaceGoodsNewApi({ prezzieId: przzieId })
      .then(res => {
        if (res.code === 0) {
          setCanUseGoodsList(res.payload);
        } else {
          Taro.showToast({
            title: '换品加载失败，请联系管理员',
            icon: 'none',
          });
        }
      })
      .catch(() => {
        Taro.showToast({
          title: '换品加载失败，请联系管理员',
          icon: 'none',
        });
      });
  };

  const hanleSelect = item => {
    setselectedEpc(item.epc);
  };

  const handleClick = () => {
    if (!selectedEpc) {
      Taro.showToast({
        title: '请选择更换礼物！',
        icon: 'none',
      });
      return;
    }
    const good = canUseGoodsList.find(v => v.epc == selectedEpc);
    handleBtn(good);
  };

  useEffect(() => {
    setIsOpened(show);
    if (show) {
      setselectedEpc('');
      getReplaceGoods();
    }
  }, [show, price]);

  return (
    <AtFloatLayout
      isOpened={isOpened}
      onClose={close}
      customStyle={{ backgroundColor: 'rgba(0,0,0,.7)' }}
      className='exchangeGifts'
    >
      <View className='exchangeGifts-section'>
        <View className='close' onClick={close}>
          <Image src={closeIcon} />
        </View>
        <View className='title'>更换礼物</View>
        <View className='title-sup'>
          当前任务因礼物兑完已终止，您可选择更换礼物
        </View>
        <View className='list-wrap'>
          {canUseGoodsList.map(item => {
            return (
              <View
                className={`list-item ${
                  item.epc == selectedEpc ? 'list-item-active' : ''
                }`}
                key={item.epc}
                onClick={() => hanleSelect(item)}
              >
                <View className='list-item-show'>
                  <Image src={item.img} mode='widthFix' />
                </View>
                <View className='list-item-title'>
                  {ellipsisWord(item.title, 20, 20)}
                </View>
                <View
                  className={`select-wrap ${
                    item.epc != selectedEpc ? '' : 'hide'
                  }`}
                ></View>
                <View
                  className={`selected-wrap ${
                    item.epc == selectedEpc ? '' : 'hide'
                  }`}
                >
                  <Image src={selectedIcon} mode='widthFix' />
                </View>
              </View>
            );
          })}
        </View>
        <View className='changed-btn' onClick={() => handleClick()}>
          <Image src={btnIcon} mode='widthFix' />
        </View>
      </View>
    </AtFloatLayout>
  );
}
