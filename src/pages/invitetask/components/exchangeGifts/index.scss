.exchangeGifts {
  width: 100%;
  overflow-x: hidden;
  image {
    width: 100%;
    height: auto;
  }
  .hide {
    display: none;
  }
  .layout {
    border-top-left-radius: 32px;
    border-top-right-radius: 32px;
    height: 1000px !important;
    max-height: 1000px;
    .layout-body {
      border-top-left-radius: 32px;
      border-top-right-radius: 32px;
      height: 100%;
      min-height: auto;
      max-height: max-content;
      padding: 0;
      overflow: hidden;
      background-color: #f7f7f7;
    }
    .layout-body__content {
      max-height: max-content;
      height: 100%;
    }
  }
  .exchangeGifts-section {
    position: relative;
    padding: 36px 28px 20px 28px;
    box-sizing: border-box;
  }
  .title {
    height: 56px;
    font-size: 40px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #333333;
    line-height: 56px;
    text-align: center;
  }
  .title-sup {
    height: 40px;
    font-size: 28px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
    line-height: 40px;
    text-align: center;
    margin: 6px 0 16px 0;
  }
  .close {
    width: 32px;
    height: 32px;
    position: absolute;
    top: 20px;
    right: 20px;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .changed-btn {
    width: 512px;
    height: 92px;
    position: fixed;
    bottom: 23px;
    left: 50%;
    margin-left: -256px;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .list-wrap {
    max-height: 800px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: space-between;
    overflow-y: auto;
    padding-bottom: 40px;
    box-sizing: border-box;
    -ms-overflow-style: none;
    overflow: -moz-scrollbars-none;
    &::-webkit-scrollbar {
      width: 0 !important;
    }
    .list-item {
      width: 336px;
      background: #ffffff;
      box-shadow: 9px 10px 10px 0px rgba(0, 0, 0, 0.03);
      border-radius: 20px;
      margin-bottom: 20px;
      box-sizing: border-box;
      padding: 18px 18px 24px 18px;
      position: relative;
      border: 4px solid #ffffff;
      &-active {
        border-color: #fd5943;
      }
    }

    .list-item-show {
      width: 300px;
      height: 300px;
      background: #f4f3e7;
      border-radius: 20px;
      overflow: hidden;
      image {
        width: 100%;
      }
    }
    .list-item-title {
      height: 66px;
      font-size: 26px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 400;
      color: #7e2f16;
      line-height: 33px;
      text-align: left;
      margin-top: 8px;
    }
    .select-wrap {
      width: 50px;
      height: 50px;
      background: #ffffff;
      border-radius: 50%;
      border: 4px solid rgba(0, 0, 0, 0.15);
      position: absolute;
      right: -4px;
      top: -4px;
      box-sizing: border-box;
    }
    .selected-wrap {
      width: 50px;
      height: 51px;
      border-radius: 50%;
      position: absolute;
      right: -4px;
      top: -4px;
      box-sizing: border-box;
    }
    image {
      width: 100%;
    }
  }
}
