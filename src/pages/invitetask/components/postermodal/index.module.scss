@function bgUrl($name) {
  @return 'https://fe-cdn.xiaoxiongmeishu.com/xiaoxiongmpactivity/weapp/wx9b8dcac074fb3151/common/#{$name}';
}
.poster-content {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 98;
  button {
    border: 0;
    background-color: transparent;
    margin: 0;
    padding: 0;
    font-size: 30px;
    line-height: normal;
    &::after {
      border: 0;
    }
  }
  &.hiden {
    display: none;
  }
  .swiper {
    width: 100%;
    height: 914px;
    margin-top: 80px;
    overflow: hidden;
    box-sizing: border-box;
  }
  .swiper-item {
    width: fit-content !important;
  }
  .swiper-item-view {
    width: 480px;
    height: 854px;
    transform: scale(0.9);
    transition: all 0.3s ease;
    background: #eee;
    box-shadow: 0px 6px 12px 0px rgba(0, 0, 0, 0.12);
    border-radius: 20px;
  }
  .swiper-img {
    width: 100%;
    height: 100%;
    display: block;
    border-radius: 20px;
  }
  .swiper-view-active {
    transform: scale(1);
  }
  .share-bottom-operation {
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: 91 !important;
    width: 100%;
    padding-bottom: 20px;
    background-image: url(bgUrl('it-task-sharebg1.png'));
    background-position: 0 0;
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .share-container {
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 85%;
    margin: 110px auto 0;
    text-align: center;
    image {
      width: 103px;
      height: 103px;
    }
    .s-c-tip {
      color: #6c6c6c;
      font-size: 30px;
    }
  }
  .share-concel {
    width: 100%;
    height: 90px;
    line-height: 90px;
    text-align: center;
    border-top: 1px solid #eee;
    font-size: 34px;
    margin-top: 36px;
    color: #6c6c6c;
  }
}
