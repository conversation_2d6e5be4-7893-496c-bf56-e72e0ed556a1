import { View, Swiper, SwiperItem, Image, Button } from '@tarojs/components';
import { useEffect, useMemo, useState } from 'react';
import sensors from '@/utils/msbTrack';
import { getPosterListApi } from '@/api/1v1k8s';
import { saveImage } from '@/utils/auth';
import { onceDay, subscribeMsgHandle } from '@/utils';
import Taro, {
  hideLoading,
  showLoading,
  useRouter,
  useShareAppMessage,
} from '@tarojs/taro';
import { userEventReport } from '@/api/groupbuy';

import share1 from '@/assets/invitetask/share_1.png';
import share2 from '@/assets/invitetask/share_2.png';
import share3 from '@/assets/invitetask/share_3.png';
import shareIcon from '@/assets/invitetask/it-task-shareicon.png';
import shareIcon2 from '@/assets/invitetask/shareicon.png';
import shareWriteIcon from '@/assets/invitetask/share-wirte.png';
import shareMusicIcon from '@/assets/invitetask/share-music.png';
import { useReducerContext } from '../../reduce';
import {
  getCodeUrl,
  posterIds,
  businessMap,
  channelIds,
  typeMap,
} from './posterdata';

import styles from './index.module.scss';

export default function Postermodal({ show, close, saveSuccess }) {
  const { params } = useRouter();
  const { state, userid } = useReducerContext();
  const [swiperIndex, setSwiperIndex] = useState<number>(0);
  const [isShowSwiper, setIsShowSwiper] = useState(false);
  const [artList, setArtList] = useState<any>([]);
  const [musicList, setMusicList] = useState<any>([]);
  const [writeList, setWriteList] = useState<any>([]);
  const [openType, setOpenType] = useState('share');
  const [nowPrezzieId, setNowPrezzieId] = useState('');

  const swiperChangeHandle = (e: any) => {
    if (e.detail.source === 'touch') {
      setSwiperIndex(e.detail.current);
      //   const currentPoster = allPosterList[e.detail.current];
      //   setOpenType(currentPoster['pType'] !== 'music' ? 'share' : '');
    }
  };

  const getPosterList = async typeId => {
    const res = await getPosterListApi({
      id: typeId,
      qrCode: getCodeUrl({
        typeId,
        // pzd: state.prezzieId,
        uid: userid,
        channelId: params.channel_id,
      }),
      uid: userid,
    });
    if (res.code === 0) {
      return res;
    } else {
      Taro.showToast({
        title: res.errors || '服务器开小差了',
      });
    }
  };

  const handleResult = (res, typeId) => {
    const { art, music, write } = posterIds;
    const { payload } = res;
    // 标准海报随机
    let randomNum = Math.floor(Math.random() * payload.length);
    if (typeId === art) {
      payload.map(v => (v.pType = businessMap[0]));
    } else if (typeId === write) {
      payload.map(v => (v.pType = businessMap[1]));
    } else if (typeId === music) {
      payload.map(v => (v.pType = businessMap[2]));
    }
    return [payload[randomNum]];
  };

  const getAllPoster = async () => {
    showLoading({
      title: '加载中',
    });
    const { art, music, write } = posterIds;
    const resArt = await getPosterList(art);
    const artResList = handleResult(resArt, art);
    const resWrite = await getPosterList(write);
    const writeResList = handleResult(resWrite, write);
    const resMusic = await getPosterList(music);
    const musicResList = handleResult(resMusic, music);
    setArtList(artResList);
    setMusicList(musicResList);
    setWriteList(writeResList);
    hideLoading();
    return;
  };

  // 保存图片
  const saveImageToAlbum = (url: string) => {
    saveImage(url).then(data => {
      hideLoading();
      if (data.status === '1') {
        // toast('海报已成功保存到相册');
        saveSuccess && saveSuccess();
      }
    });
  };

  const handleSave = (share_type = '') => {
    const currentPoster = allPosterList[swiperIndex];
    const url = currentPoster['posterUrl'];
    saveImageToAlbum(url);
    if (share_type) {
      sensors.track('xxys_invitetask_pop_shareclick', {
        poster_id: currentPoster.id,
        share_type: share_type === 'savepic' ? '保存图片' : '朋友圈',
        course_subject: typeMap[currentPoster.pType],
      });
      userEventReport({
        subject: 'ART_APP',
        uid: userid,
        channelId: params.channel_id || '',
        eventValue: 'xxys_invitetask_pop_shareclick',
        eventName: '分享点击',
        eventTitle: '天天领',
      });
    }
  };

  const handleShare = () => {
    const currentPoster = allPosterList[swiperIndex];
    sensors.track('xxys_invitetask_pop_shareclick', {
      poster_id: currentPoster.id,
      share_type: '微信好友',
      course_subject: typeMap[currentPoster.pType],
    });
    userEventReport({
      subject: 'ART_APP',
      uid: userid,
      channelId: params.channel_id || '',
      eventValue: 'xxys_invitetask_pop_shareclick',
      eventName: '分享点击',
      eventTitle: '天天领',
    });
    // if (currentPoster['pType'] === 'music') {
    //   handleSave();
    // }
  };

  const handleCancel = () => {
    if (!onceDay()) {
      close && close();
      return;
    }
    subscribeMsgHandle('RozdH9OC1AwblM6RkJfnDXETK5_Mb1QLHtnY2ueM3aU').then(
      res => {
        console.log(res, '订阅');
        close && close();
      },
    );
  };

  // 天天领，高价值品只分享美术
  const isHightValue = () => {
    const { taskList, prezzieId } = state;
    const taskdetail = taskList.find(v => v.id == prezzieId);
    return taskdetail && +taskdetail.costPrice >= 25;
  };
  // 天天领，25<=x <40 多一个书法
  const isBelow40 = () => {
    const { taskList, prezzieId } = state;
    const taskdetail = taskList.find(v => v.id == prezzieId);
    return (
      taskdetail && +taskdetail.costPrice < 40 && taskdetail.epc !== 'JB8000'
    );
  };
  // 特殊分享设置
  const isSpecialgoods = () => {
    // 2000金币 分享学科：书法、音乐
    const { taskList, prezzieId } = state;
    const taskdetail = taskList.find(v => v.id == prezzieId);
    return taskdetail && taskdetail.epc === 'JB2000';
  };
  const allPosterList = useMemo(() => {
    if (isSpecialgoods()) {
      return [...writeList, ...musicList];
    }
    if (isHightValue()) {
      if (isBelow40() && new Date().getTime() < 1680278400000)
        return [...artList, ...writeList];
      else return artList;
    } else {
      return [...artList, ...writeList, ...musicList];
    }
  }, [artList, writeList, musicList, show]);

  useEffect(() => {
    setIsShowSwiper(show);
    setSwiperIndex(0);

    if (
      show &&
      (allPosterList.length == 0 || nowPrezzieId != state.prezzieId)
    ) {
      setNowPrezzieId(state.prezzieId);
      getAllPoster();
    }
  }, [show]);

  useShareAppMessage(res => {
    const currentPoster = allPosterList[swiperIndex];
    console.log(
      171,
      swiperIndex,
      currentPoster,
      allPosterList,
      'allPosterList',
    );
    const msChannelId = params.channel_id || channelIds['art'];
    const xzChannelId = channelIds['write'];
    const yyChannelId = channelIds['music'];

    let shareArr: any = {
      art: {
        title: '今天可以免费领画材礼包，名额有限，快给宝贝领取！',
        path: `/pages/normalGroup/art/index?pzd=${state.prezzieId}&sendId=${userid}&channelId=${msChannelId}&msChannelId=${msChannelId}&poster_id=${currentPoster.id}`,
        imageUrl: shareIcon,
      },
      write: {
        title: '发现宝藏了！每天5分钟在家练好字，快来试试！',
        path: `/pages/writeExperience/zeroNine?pzd=${state.prezzieId}&sendId=${userid}&channelId=${xzChannelId}&poster_id=${currentPoster.id}`,
        imageUrl: shareWriteIcon,
      },
      music: {
        title: '发现宝藏了！0基础开心学弹唱，快来试试！',
        path: `/pages/music/introduce/index?pzd=${state.prezzieId}&sendId=${userid}&channelId=${yyChannelId}&poster_id=${currentPoster.id}`,
        imageUrl: shareMusicIcon,
      },
    };

    // const shareArr = [
    //   {
    //     title: '今天可以免费领画材礼包，名额有限，快给宝贝领取！',
    //     path: `/pages/normalGroup/art/index?pzd=${state.prezzieId}&sendId=${userid}&msChannelId=${msChannelId}&poster_id=${currentPoster.id}`,
    //     imageUrl: shareIcon,
    //   },
    //   {
    //     title: '发现宝藏了！每天5分钟在家练好字，快来试试！',
    //     path: `/pages/writeExperience/zeroNine?pzd=${state.prezzieId}&sendId=${userid}&channelId=${xzChannelId}&poster_id=${currentPoster.id}`,
    //     imageUrl: shareWriteIcon,
    //   },
    //   {
    //     title: '发现宝藏了！0基础开心学弹唱，快来试试！',
    //     path: `/pages/music/introduce/index?pzd=${state.prezzieId}&sendId=${userid}&channelId=${yyChannelId}&poster_id=${currentPoster.id}`,
    //     imageUrl: shareMusicIcon,
    //   },
    // ];
    // 来自页面内转发按钮
    if (res.from === 'button') {
      console.log(currentPoster.pType, 'currentPoster.pType');

      return shareArr[currentPoster.pType];
    }

    return {
      title: '这些好礼都能免费领，还包邮送到家，太值了！',
      path: `pages/invitetask/index/index?channel_id=${params.channel_id ||
        ''}&entrance_page=${params.entrance_page || ''}`,
      imageUrl: shareIcon2,
    };
  });

  return (
    <View
      className={`${styles['poster-content']} ${
        isShowSwiper ? '' : styles['hiden']
      }`}
    >
      <View className={styles['swiper-wrap']}>
        <Swiper
          circular
          nextMargin='134rpx'
          previousMargin='134rpx'
          indicatorColor='#E5E5E5'
          indicatorActiveColor='#FD742F'
          className={styles['swiper']}
          current={swiperIndex}
          onChange={swiperChangeHandle}
        >
          {allPosterList.map((item: any, index: number) => {
            return (
              <SwiperItem key={index} className={styles['swiper-item']}>
                <View
                  className={`${styles['swiper-item-view']} ${
                    swiperIndex === index ? styles['swiper-view-active'] : ''
                  }`}
                >
                  <Image
                    className={styles['swiper-img']}
                    src={item.posterUrl}
                    mode='aspectFill'
                    showMenuByLongpress
                  />
                </View>
              </SwiperItem>
            );
          })}
        </Swiper>
      </View>
      {allPosterList.length > 0 && (
        <View className={styles['share-bottom-operation']}>
          <View className={styles['share-container']}>
            {/* <View className={styles['shareFriend']} onClick={handleShare}>
            <Image src={share1} />
            <View className={styles['s-c-tip']}>微信好友</View>
          </View> */}
            <Button
              openType={openType}
              className={styles['shareFriend']}
              onClick={handleShare}
            >
              <Image src={share1} />
              <View className={styles['s-c-tip']}>微信好友</View>
            </Button>
            <View
              className={styles['shareFriend']}
              onClick={() => handleSave('friends')}
            >
              <Image src={share2} />
              <View className={styles['s-c-tip']}>朋友圈</View>
            </View>
            <View
              className={styles['shareFriend']}
              onClick={() => handleSave('savepic')}
            >
              <Image src={share3} />
              <View className={styles['s-c-tip']}>保存图片</View>
            </View>
          </View>
          <View className={styles['share-concel']} onClick={handleCancel}>
            取消
          </View>
        </View>
      )}
    </View>
  );
}
