import { View, Image } from '@tarojs/components';
import btnIcon from '@/assets/invitetask/it-task-retain-btn.png';
import { AtCurtain } from 'taro-ui';
import { useEffect, useMemo, useState } from 'react';
import { GoodsProps, useReducerContext } from '../../reduce';
import './index.scss';
import { COINS } from '../../reduce/const';

/**
 * 
 *  利益弹窗 => type = 2
 *        1.用户当前无进行中任务，进入活动页面时弹出
 *        2.库存>0
          3.优先推荐排序第1位的品，如果当前用户已领，推荐排序第2位。以此类推
          4.推荐商品不包括金币 (JB8000)
    二次确认 => type = 3
          1.当前选择的商品
 */

export default function Retention({
  popupType = '3',
  show = false,
  onClose,
  onSubmit,
}) {
  const { state, dispatch } = useReducerContext();
  const [visible, setVisible] = useState(false);
  const showGood = useMemo(() => {
    let _currentGood: GoodsProps = {
      title: '',
      img: '',
      status: 'DEFAULT',
      goodsId: '',
      epc: '',
      excessInventory: '',
      losetime: '',
      nowTime: '',
      teams: [],
      id: '',
      costPrice: '',
      getTime: '',
    };
    let _goods = state.taskGoods.length ? state.taskGoods : [_currentGood];
    if (popupType === '3') {
      _currentGood =
        _goods.find(v => v.goodsId === state.selectedGoodsId) || _currentGood;
    } else {
      const myTaskList = state.taskList;
      _goods = _goods.filter(
        v => !COINS.includes(v.epc) && v.excessInventory > 0,
      );
      if (myTaskList.length === 0) {
        _currentGood = _goods[0];
      } else {
        // 优先推荐排序第1位的品，如果当前用户已领，推荐排序第2位
        // const hasActiving = myTaskList.filter(v => v.status === 'ACTIVING');

        const hasGet = myTaskList.filter(v =>
          ['NOGET', 'ACTIVING'].includes(v.status),
        );
        const hasGetEpc = hasGet.map(v => v.epc);
        const noGet = _goods.filter(v => !hasGetEpc.includes(v.epc));
        // 如果没有未领过的商品
        _currentGood = noGet[0] || _goods[0];
        if (popupType == '2') {
          dispatch({
            type: 'getRetainGoodsId',
            payload: { retainGoodsId: _currentGood.goodsId },
          });
          dispatch({
            type: 'getSelectedGoodsId',
            payload: { selectedGoodsId: '' },
          });
          // dispatch({ type: 'getPrezzieId', payload: { prezzieId: '' } });
        }
      }
    }

    return _currentGood;
  }, [popupType, state.selectedGoodsId, state.taskGoods, state.taskList]);

  useEffect(() => {
    setVisible(show);
  }, [show]);

  return (
    <AtCurtain
      isOpened={visible}
      closeBtnPosition='top-right'
      onClose={onClose}
      className='at-curtain-wrap'
    >
      <View className='popup_div'>
        <View className='contain'>
          <View className='h3'>恭喜你获得</View>
          <View className='show-goods'>
            <View className='bg-light'></View>
            <View className='bg-plat'></View>
            <View className='good-name'>“{showGood.title}”免费领取资格</View>
            <View className='good-plat'>
              <View className='good-plat-img'>
                <Image src={showGood.img} mode='widthFix' />
              </View>
            </View>
          </View>
          <View className='btn' onClick={() => onSubmit(showGood)}>
            <Image src={btnIcon} mode='widthFix' />
            <View className='point'></View>
          </View>
        </View>
      </View>
    </AtCurtain>
  );
}
