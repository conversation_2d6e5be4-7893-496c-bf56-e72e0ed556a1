@function bgUrl($name) {
  @return 'https://fe-cdn.xiaoxiongmeishu.com/xiaoxiongmpactivity/weapp/wx9b8dcac074fb3151/common/#{$name}';
}
.at-curtain-wrap {
  background-color: rgba(0, 0, 0, 0.7);
}
.popup_div {
  width: 552px;
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
  image {
    width: 100%;
  }
  .close {
    width: 40px;
    height: 40px;
    background: url(bgUrl('it-task-retain-close.png')) no-repeat left top;
    background-size: 100% 100%;
    position: absolute;
    top: 16px;
    right: 16px;
  }
  .contain {
    width: 552px;
    .h3 {
      height: 70px;
      font-size: 50px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #ffdc99;
      line-height: 70px;
      text-align: center;
      margin: 23px 0 13px 0;
    }
    .show-goods {
      width: 100%;
      position: relative;
      .bg-light {
        width: 552px;
        height: 554px;
        background: url(bgUrl('it-task-retain-g.png')) no-repeat left top;
        background-size: 100% 100%;
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
      }
      .bg-plat {
        width: 409px;
        height: 150px;
        background: url(bgUrl('it-task-retain-pt.png')) no-repeat left top;
        background-size: 100% 100%;
        position: absolute;
        top: 330px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 90;
      }
      .good-name {
        font-size: 28px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        text-align: center;
        word-break: break-all;
      }
      .good-plat {
        margin: 28px auto 0;
        width: 300px;
        height: 300px;
        border: 6px solid #ffffff;
        border-radius: 20px;
        position: relative;
        overflow: hidden;
        z-index: 100;
        .good-plat-img {
          width: 100%;
          height: 100%;
          border-radius: 20px;
        }
      }
    }
    // .retain-number {
    //   width: 279px;
    //   height: 40px;
    //   background-image: url('../../../../assets/invitetask/it-task-retain-num.png');
    //   background-size: contain;
    //   background-position: 0 0;
    //   background-repeat: no-repeat;
    //   margin: 14px auto 0;
    // }
    .btn {
      width: 399px;
      height: 101px;
      margin: 130px auto 0;
      position: relative;
      image {
        width: 100%;
        display: block;
      }
      .point {
        width: 73px;
        height: 75px;
        background-image: url(bgUrl('it-task-point.png'));
        background-size: contain;
        background-position: 0 0;
        background-repeat: no-repeat;
        position: absolute;
        right: -9px;
        top: 33px;
        animation: scaleDrew 1.2s ease-in-out infinite;
      }
    }
  }
}
@keyframes scaleDrew {
  /* 定义关键帧、scaleDrew是需要绑定到选择器的关键帧名称 */
  0% {
    transform: scale(1);
  }

  25% {
    transform: scale(1.1);
  }

  50% {
    transform: scale(1);
  }

  75% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
