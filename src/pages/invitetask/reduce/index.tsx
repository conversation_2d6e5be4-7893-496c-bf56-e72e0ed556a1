import { UserStateType } from '@/store/groupbuy/state';
import { wxLogin } from '@/utils/auth';
import { getGlobalData, setGlobalData } from '@/utils/global';
import msbTrack from '@/utils/sensors_data.weapp';
import {
  createContext,
  Dispatch,
  useContext,
  useEffect,
  useReducer,
  useState,
} from 'react';
import { useDispatch, useSelector } from 'react-redux';
import BaseLoading from '../components/baseloading';
import InviteTask from './task';

export type TASKSTATUS =
  | 'DEFAULT'
  | 'ACTIVING'
  | 'NOGET'
  | 'GET'
  | 'OVER1'
  | 'OVER2'
  | 'REPLACE';
export type TASKSUBJECT = 'MUSIC_APP' | 'ART_APP' | 'WRITE_APP';

export interface GoodsProps {
  epc: string;
  getTime: string;
  goodsId: string;
  img: string;
  losetime: string;
  nowTime: string;
  status: TASKSTATUS;
  teams: unknown[];
  title: string;
  id: string;
  costPrice: string | number;
  excessInventory: string | number;
  num?: number;
}

export interface TeamsProps {
  id: string;
  mobile: string;
  status: TASKSTATUS;
  subject: TASKSUBJECT;
}

/** 
 * 未领取过 => 开启新任务 '0' 所有商品按钮 点击领取 (接口查无任务列表数据未领过)
 * 开启新任务、点击领取 => 增加一个任务进度 按钮立即邀请 商品状态 进行中 tab显示倒计时
 * 立即邀请 => 分享弹窗
 * 任务已完成 => 填写地址
 * 
 * 任务状态
 * DEFAULT, 未发起活动
    ACTIVING,  活动进行中
    NOGET, 未领取
    GET, 已领取
    OVER1, 过了有效期，失败
    OVER2;  商品下架或库存为0
    REPLACE: 换品
 */
export interface IState {
  status: string;
  taskGoods: GoodsProps[];
  taskList: GoodsProps[];
  retainGoodsId: string;
  selectedGoodsId: string;
  postDailog: boolean;
  prezzieId: string;
  showGoodId: string;
}

interface IContext {
  state: IState;
  dispatch: Dispatch<{
    type: string;
    payload?: Partial<IState>;
  }>;
  userid: string;
  openid: string;
  storeDispatch: any;
  getInit: () => void;
}

export interface ProviderProps {}

const initialState: IState = {
  status: '',
  taskGoods: [], // 所有商品
  taskList: [], //任务列表
  selectedGoodsId: '', //当前选择商品的goodsId=>定位发起任务商品id
  postDailog: false, // 海报弹窗
  prezzieId: '', //任务id => 定位发起任务及提交地址
  retainGoodsId: '', // 挽留弹窗物品id => 定位大卡片展示商品及挽留弹窗展示商品
  showGoodId: '', //点击展示商品详情
};
// 创建 context
const Context = createContext<IContext>({
  state: initialState,
  dispatch: () => {},
  userid: '',
  openid: '',
  storeDispatch: () => {},
  getInit: () => {},
});

export const ReducerContextProvider: React.FC<ProviderProps> = props => {
  const [initOk, setInitOk] = useState(false);
  const storeDispatch = useDispatch();
  const reducer = (
    preState: IState,
    action: {
      type: string;
      payload?: Partial<IState>;
    },
  ) => {
    const { type, payload } = action;

    switch (type) {
      default:
        return preState;
      case 'getTaskGoods':
        return {
          ...preState,
          taskGoods: payload?.taskGoods || [],
        };
      case 'getTaskList':
        return {
          ...preState,
          taskList: payload?.taskList || [],
        };
      case 'getShowGoodsId':
        // 物品详情
        return {
          ...preState,
          showGoodId: payload?.showGoodId || '',
        };
      case 'getRetainGoodsId':
        return {
          ...preState,
          retainGoodsId: payload?.retainGoodsId || '',
        };
      case 'getSelectedGoodsId':
        return {
          ...preState,
          selectedGoodsId: payload?.selectedGoodsId || '',
        };
      case 'getPrezzieId':
        return {
          ...preState,
          prezzieId: payload?.prezzieId || '',
        };

      case 'reset':
        return {
          ...preState,
          ...payload,
        };
    }
  };

  const [state, dispatch] = useReducer(reducer, initialState);

  const userid = useSelector((store: UserStateType) => store.userid);
  const openid = useSelector((store: UserStateType) => store.openid);

  // 获取基础数据 未登录用户uid=-1
  const getInit = async () => {
    const task = new InviteTask({ uid: userid || '-1' });
    const list = await task.getPrezzieList();
    dispatch({ type: 'getTaskGoods', payload: { taskGoods: list } });
    if (userid) {
      const myTaskList = await task.getMyList();
      dispatch({
        type: 'getTaskList',
        payload: { taskList: myTaskList },
      });
    }

    setInitOk(true);
  };

  useEffect(() => {
    wxLogin().then(resOpenid => {
      if (resOpenid) {
        msbTrack.init({ wx_openid: resOpenid });
        setGlobalData('isWxLogin', true);
        getInit();
      }
    });
  }, []);

  useEffect(() => {
    const isWxLogin = getGlobalData('isWxLogin');
    isWxLogin && getInit();
  }, [userid, openid]);

  return (
    <Context.Provider
      value={{ state, dispatch, openid, userid, storeDispatch, getInit }}
    >
      {initOk ? props.children : <BaseLoading />}
    </Context.Provider>
  );
};

export const useReducerContext = () => {
  return useContext(Context);
};
