import {
  getPrezzie<PERSON>ist<PERSON><PERSON>,
  get<PERSON>y<PERSON>ist<PERSON><PERSON>,
  prezzieinit,
  getPrezzieListV2,
} from '@/api/invitetask';

class InviteTask {
  userId: string = '';
  constructor({ uid }) {
    this.userId = uid;
  }

  async getPrezzieList() {
    const res = await getPrezzieListApi(this.userId);
    if (res.code === 0) {
      return res.payload;
    }
    return [];
  }

  // 获取任务列表
  async getMyList() {
    const res = await getMyListApi(this.userId);
    if (res.code === 0) {
      return res.payload;
    }
    return [];
  }
  /* 新版 */
  //   初始化
  async prezzieinit() {
    const res = await prezzieinit(this.userId);
    if (res.code === 0) {
      return res.payload;
    }
    return [];
  }
  //   新版获取商品
  async getPrezzieListV2() {
    const res = await getPrezzieListV2(this.userId);
    if (res.code === 0) {
      return res.payload;
    }
    return [];
  }
}

export default InviteTask;
