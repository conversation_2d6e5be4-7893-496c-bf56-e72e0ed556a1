@function bgUrl($name) {
  @return 'https://fe-cdn.xiaoxiongmeishu.com/xiaoxiongmpactivity/weapp/wx9b8dcac074fb3151/common/#{$name}';
}
.wrap {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  box-sizing: border-box;
  .banner {
    width: 100%;
    height: 1138px;
    image {
      width: 100%;
    }
  }
}
.btn {
  width: 399px;
  height: 101px;
  margin: 75px auto 0;
  position: relative;
  image {
    width: 100%;
    display: block;
  }
  .point {
    width: 73px;
    height: 75px;
    background-image: url(bgUrl('it-task-point.png'));
    background-size: contain;
    background-position: 0 0;
    background-repeat: no-repeat;
    position: absolute;
    right: -9px;
    top: 33px;
    animation: scaleDrew 1.2s ease-in-out infinite;
  }
}
@keyframes scaleDrew {
  /* 定义关键帧、scaleDrew是需要绑定到选择器的关键帧名称 */
  0% {
    transform: scale(1);
  }

  25% {
    transform: scale(1.1);
  }

  50% {
    transform: scale(1);
  }

  75% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
