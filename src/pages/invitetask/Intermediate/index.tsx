import { View, Image } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import banner from '@/assets/invitetask/it-task-jump.png';
import btnIcon from '@/assets/invitetask/it-task-retain-btn.png';
import './index.scss';

export default function Intermediate() {
  const { params } = useRouter();
  console.log(params, 'ppparams');

  const handleClick = () => {
    const { pzd, msChannelId, sendId } = params;
    const path = `pages/normalGroup/art/index`;
    Taro.navigateToMiniProgram({
      appId: 'wx326319ed9257c109',
      path,
      envVersion: process.env.NODE_ENV === 'online' ? 'release' : 'trial',
      extraData: {
        pzd,
        sendId,
        msChannelId,
      },
      success() {
        console.log('跳转成功');
      },
      fail() {
        Taro.showToast({ title: '打开失败，请联系管理员' });
      },
    });
  };

  return (
    <View className='wrap'>
      <View className='banner'>
        <Image src={banner} mode='widthFix' />
      </View>
      <View className='btn' onClick={handleClick}>
        <Image src={btnIcon} mode='widthFix' />
        <View className='point'></View>
      </View>
    </View>
  );
}
