import { useEffect } from 'react';
import sensors from '@/utils/msbTrack';
import { useRouter } from '@tarojs/taro';
import Home from '../home';
import { ReducerContextProvider } from '../reduce/index';

export default function Index() {
  const { params } = useRouter();
  useEffect(() => {
    sensors.track('xxys_invitetask_homepage_view', {
      entrance_page: params.entrance_page,
      channel_id: params.channel_id,
    });
  }, []);

  return (
    <ReducerContextProvider>
      <Home />
    </ReducerContextProvider>
  );
}
