import { View } from '@tarojs/components';
import Taro, { useDidShow, useRouter } from '@tarojs/taro';
import sensors from '@/utils/msbTrack';
import bannerIcon from '@/assets/invitetask/it-banner.png';
import ruleIcon from '@/assets/invitetask/it-rule.png';
import tomytaskIcon from '@/assets/invitetask/it-tomytask.png';
import { UserStateType } from '@/store/groupbuy/state';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { joinPrezzieApi } from '@/api/invitetask';
import { userEventReport } from '@/api/groupbuy';

import { getGlobalData } from '@/utils/global';
import { judgeMinienv } from '@/utils/auth';
import { useReducerContext } from '../reduce/index';
import Userinfobtn from '../components/userinfobtn';
import ExchangeSwiper from '../components/exchangeSwiper';
import Taskprogress from '../components/taskprogress';
import Moretasklist from '../components/moretasklist';
import Rule from '../components/rule';
import Prizedetails from '../components/prizeDetails';
import Retention from '../components/retention';
import Postermodal from '../components/postermodal';
import Overmodal from '../components/overModal';
import InviteTask from '../reduce/task';

import styles from './index.module.scss';

export default function Home() {
  const { params } = useRouter();
  const { dispatch, state, getInit } = useReducerContext();
  const [ruleVisible, setruleVisible] = useState(false);
  const [prizedetailVisible, setPrizedetailVisible] = useState(false);
  const [retentionVisible, setRetentionVisible] = useState(false);
  const [retentionType, setRetentionType] = useState('');
  const [posterVisible, setPosterVisible] = useState(false);
  const [overVisible, setOverVisible] = useState(false);
  const [overType, setOverType] = useState('');

  const userid = useSelector((store: UserStateType) => store.userid);

  const handleRule = () => {
    setruleVisible(val => !val);
  };

  const handlePrizedetail = () => {
    setPrizedetailVisible(val => !val);
  };
  const handleRetention = () => {
    setRetentionVisible(val => !val);
  };
  const handlePosterModal = () => {
    setPosterVisible(val => !val);
  };

  const handleOverModal = () => {
    setOverVisible(val => !val);
  };

  const retainSubmit = goodItem => {
    if (goodItem.status === 'DEFAULT') {
      sensors.track('xxys_invitetask_task_start', {
        goods_code: goodItem.epc,
      });
      joinPrizzie(goodItem).then(() => {
        setRetentionVisible(false);
        Taro.pageScrollTo({
          scrollTop: 0,
        });
        judgeMinienv('release') && setPosterVisible(true);
      });
    }
  };

  const joinPrizzie = async goodItem => {
    Taro.showLoading({ title: '加载中' });
    const { goodsId, epc, img, title } = goodItem;
    const res = await joinPrezzieApi({
      goodsId,
      epc,
      img,
      title,
      uid: userid,
    });
    if (res.code == 0) {
      const task = new InviteTask({ uid: userid });
      const list = await task.getPrezzieList();
      const myTaskList = await task.getMyList();

      dispatch({
        type: 'getTaskGoods',
        payload: { taskGoods: list },
      });
      dispatch({
        type: 'getTaskList',
        payload: { taskList: myTaskList },
      });
      dispatch({
        type: 'getPrezzieId',
        payload: { prezzieId: myTaskList[0].id },
      });
      Taro.hideLoading();
    } else {
      Taro.showToast({
        title: res.errors || '服务器开小差了',
        icon: 'none',
      });
    }
  };

  const progressBtnClick = (goodItem, from) => {
    const { status, goodsId, excessInventory } = goodItem;

    if (excessInventory <= 0) {
      if (from !== 'progress') {
        setOverType('3');
        setOverVisible(true);
        return;
      }
    }

    if (status === 'DEFAULT') {
      if (from === 'progress') {
        joinPrizzie(goodItem);
      } else {
        dispatch({
          type: 'getSelectedGoodsId',
          payload: { selectedGoodsId: goodsId },
        });
        setRetentionType('3');
        setRetentionVisible(true);
      }

      return;
    }
    if (status === 'ACTIVING') {
      if (!judgeMinienv('release')) {
        return;
      }
      // 分享弹窗
      dispatch({
        type: 'getPrezzieId',
        payload: { prezzieId: goodItem.id },
      });
      handlePosterModal();
      sensors.track('xxys_invitetask_homepage_shareclick', {});
      userEventReport({
        subject: 'ART_APP',
        uid: userid,
        channelId: params.channel_id || '',
        eventValue: 'xxys_invitetask_homepage_shareclick',
        eventName: '分享点击',
        eventTitle: '天天领',
      });
    }
    if (status === 'NOGET') {
      setOverType('2');
      setOverVisible(true);
    }
  };

  // 主动弹窗
  const activePopover = () => {
    if (userid) {
      setRetentionType('');
      setRetentionVisible(false);
      const taskList = state.taskList;

      const _tlist = taskList.filter(v =>
        ['ACTIVING', 'NOGET'].includes(v.status),
      );

      const isShowLYTC =
        taskList.length === 0 || (taskList.length > 0 && _tlist.length == 0);
      if (isShowLYTC) {
        setRetentionType('2');
        setRetentionVisible(true);
      }
    }
  };

  // 图片保存成功
  const saveSuccess = () => {
    setOverType('1');
    setOverVisible(true);
  };

  const jumpPage = () => {
    Taro.navigateTo({
      url: '/pages/invitetask/detail/index',
    });
  };

  useEffect(() => {
    const isWxLogin = getGlobalData('isWxLogin');
    if (isWxLogin) {
      activePopover();
    }
    if (userid) {
      userEventReport({
        subject: 'ART_APP',
        uid: userid,
        channelId: params.channel_id || '',
        eventValue: 'xxys_invitetask_homepage_view',
        eventName: '浏览',
        eventTitle: '天天领',
      });
    }
  }, [userid, state.taskList]);

  useDidShow(() => {
    // 切换后台刷新数据
    console.log('didShow');

    getInit();
  });

  return (
    <View className={styles['invitetask-home']}>
      <View
        className={styles['header']}
        style={{ backgroundImage: `url(${bannerIcon})` }}
      >
        {/* 机器人滚动 */}
        <ExchangeSwiper />
        {/* 活动规则 */}
        <View
          className={styles['rule']}
          style={{ backgroundImage: `url(${ruleIcon})` }}
          onClick={handleRule}
        ></View>
        <Userinfobtn>
          <View
            className={styles['tomytask']}
            style={{ backgroundImage: `url(${tomytaskIcon})` }}
            onClick={jumpPage}
          ></View>
        </Userinfobtn>
      </View>
      {/* 任务进度 */}
      <Taskprogress progressBtnClick={progressBtnClick} />
      {/* 更多礼物 */}
      <View className={styles['moretask-box']}>
        <Moretasklist
          handlePrizedetail={handlePrizedetail}
          progressBtnClick={progressBtnClick}
        />
      </View>
      {/* 弹窗 */}
      <Rule visible={ruleVisible} close={handleRule} />
      <Prizedetails
        show={prizedetailVisible}
        handlePrizedetail={handlePrizedetail}
      />
      <Retention
        show={retentionVisible}
        onClose={handleRetention}
        onSubmit={retainSubmit}
        popupType={retentionType}
      />
      <Postermodal
        show={posterVisible}
        close={handlePosterModal}
        saveSuccess={saveSuccess}
      />
      <Overmodal show={overVisible} close={handleOverModal} type={overType} />
    </View>
  );
}
