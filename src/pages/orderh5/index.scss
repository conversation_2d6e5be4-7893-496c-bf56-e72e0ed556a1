.container {
  background: #ffffff;
  border-radius: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  display: flex;
  flex-direction: column;
  padding: 32px 24px;
  margin-bottom: 16px;
  .title {
    display: flex;
    justify-content: space-between;
    font-size: 28px;
    margin-bottom: 24px;
    color: #666666;
    .orderNuber {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    .status {
      color: #ff5050;
    }
    .graySatatus {
      color: #999999;
    }
  }

  .productContent {
    position: relative;
    .productleft {
      display: flex;
      .productTitle {
        width: 50%;
        display: flex;
        justify-content: start;
        align-items: center;
        font-size: 32px;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #333333;
        padding-bottom: 30px;
      }
      .coverImage {
        position: relative;
        white-space: nowrap;
        overflow: hidden;
        image {
          width: 176px;
          height: 176px;
          border-radius: 16px;
          margin-right: 16px;
        }
      }
    }

    .price {
      position: absolute;
      background: rgba(255, 255, 255, 0.9);
      right: 0;
      top: 0;
      bottom: 0;
      padding-left: 24px;
      font-size: 36px;
      font-family: DINAlternate-Bold, DINAlternate;
      font-weight: bold;
      color: #333333;
      display: flex;
      flex-direction: column;
      align-items: right;
      justify-content: center;
      text-align: right;
      .packageNumber {
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #999999;
      }
      .i {
        font-style: normal;
      }
      .s {
        text-decoration: none;
        font-size: 24px;
      }
    }
  }
}
