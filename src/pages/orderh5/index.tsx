import { WebView, View, Text, Image } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { judgeMinienv } from '@/utils/auth';
import { useEffect } from 'react';
import './index.scss';

const Indexwebview = () => {
  var url;
  url =
    process.env.NODE_ENV == 'online'
      ? 'https://www.xiaoxiongmeishu.com/h5/orderLogistics/orderLogisticsManage?from=wx'
      : 'https://dev.meixiu.mobi/ai-app-h5-dev/orderLogistics/orderLogisticsManage?from=wx';
  url = decodeURIComponent(url);
  const handleOnLoad = () => {
    Taro.hideLoading();
  };

  useEffect(() => {
    !judgeMinienv('release') && Taro.showLoading({ title: '加载中...' });
  }, []);

  return (
    <>
      {!judgeMinienv('release') ? (
        <>
          <View className='container'>
            <View className='title'>
              <Text className='orderNuber'>订单编号: 2023063018054110371 </Text>
              <Text className='status'>已完成</Text>
            </View>
            <View className='productContent'>
              <View className='productleft'>
                <View className='coverImage'>
                  <Image src='https://fe-cdn.xiaoxiongmeishu.com/apph5/test/202307031729/assets/img/ART_cover.73bebefd.png' />
                </View>
                <View className='productTitle'> 小熊美术年系统版 </View>
              </View>
              <View className='price'>
                <Text>
                  <Text className='s'>¥</Text>
                  <Text className='i'>0.01</Text>
                </Text>
                <Text className='packageNumber'>共1件</Text>
              </View>
            </View>
          </View>
          <View className='container'>
            <View className='title'>
              <Text className='orderNuber'>订单编号: 2023063018054110371 </Text>
              <Text className='status'>已完成</Text>
            </View>
            <View className='productContent'>
              <View className='productleft'>
                <View className='coverImage'>
                  <Image src='https://fe-cdn.xiaoxiongmeishu.com/apph5/test/202307031729/assets/img/ART_cover.73bebefd.png' />
                </View>
                <View className='productTitle'> 小熊美术年系统版 </View>
              </View>
              <View className='price'>
                <Text>
                  <Text className='s'>¥</Text>
                  <Text className='i'>0.01</Text>
                </Text>
                <Text className='packageNumber'>共1件</Text>
              </View>
            </View>
          </View>
          <View className='container'>
            <View className='title'>
              <Text className='orderNuber'>订单编号: 2023063018054110371 </Text>
              <Text className='status'>已完成</Text>
            </View>
            <View className='productContent'>
              <View className='productleft'>
                <View className='coverImage'>
                  <Image src='https://fe-cdn.xiaoxiongmeishu.com/apph5/test/202307031729/assets/img/ART_cover.73bebefd.png' />
                </View>
                <View className='productTitle'> 小熊美术年系统版 </View>
              </View>
              <View className='price'>
                <Text>
                  <Text className='s'>¥</Text>
                  <Text className='i'>0.01</Text>
                </Text>
                <Text className='packageNumber'>共1件</Text>
              </View>
            </View>
          </View>
        </>
      ) : (
        <>
          <WebView src={url} onLoad={handleOnLoad} />
        </>
      )}
    </>
  );
};

export default Indexwebview;
