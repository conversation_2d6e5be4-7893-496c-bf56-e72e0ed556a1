import Taro, { useDidShow, useRouter } from '@tarojs/taro';
import { useState, useEffect } from 'react';
import { View, Image, Text } from '@tarojs/components';
import headerLeft from '@/assets/thirtySix/addTeacher/headerLeft.png';
import headerRight from '@/assets/thirtySix/addTeacher/headerRight.png';
import groupBg from '@/assets/thirtySix/addTeacher/groupBg.png';
import icon_1 from '@/assets/thirtySix/addTeacher/icon_1.png';
import icon_2 from '@/assets/thirtySix/addTeacher/icon_2.png';
import icon_3 from '@/assets/thirtySix/addTeacher/icon_3.png';
// import icon_arr from '@/assets/thirtySix/addTeacher/icon_arr.png';
import { getTeacherInfo } from '@/api/groupbuy';
import { UserStateType } from '@/store/groupbuy/state';
import { useSelector } from 'react-redux';
import ImgLongPress from '@/components/ImgLongPress';
import sensors from '@/utils/sensors_data';

import './index.scss';

export default function FollowIndex() {
  let { params } = useRouter();
  //redux
  const orderId = useSelector((state: UserStateType) => state.orderId);
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  // 老师微信号
  const [teacherNo, setTeacherNo] = useState('');
  //复制老师微信
  function copyData() {
    sensors.track('xxms_testcourse_addteacherpage_buttonclick', {
      add_method: '复制微信号',
    });
    Taro.setClipboardData({
      data: teacherNo,
      success: function() {
        Taro.showToast({
          title: '您已复制老师微信号,打开微信首页-右上角添加好友',
          icon: 'none',
          duration: 2000,
        });
      },
    });
  }
  useEffect(() => {
    sensors.track('ai_marketing_AlipayminiAPP_teacherpagebrowse', {
      channel_id: params.channelId,
      urlType: params.urlType,
    });
    getTeacherInfo({
      orderNo: params.orderNo || orderId,
      addSource: params.addSource || '1',
    }).then(res => {
      if (res.code === 0) {
        setQrCodeUrl(res.payload.teacherWeChatQrCode);
        setTeacherNo(res.payload.teacherWeChat);
      }
    });
  }, [params, orderId]);

  useDidShow(() => {
    if (process.env.TARO_ENV === 'tt') {
      tt.hideHomeButton();
    } else {
      my.hideBackHome();
    }
  });
  const onLongPresshandle = () => {
    if (Taro.getEnv() === 'ALIPAY') {
      sensors.track('ai_marketing_AlipayminiAPP_teacherpageclick', {
        channel_id: params.channelId,
        urlType: params.urlType,
      });
      Taro.saveImageToPhotosAlbum({
        filePath: qrCodeUrl,
        success: () => {
          Taro.showToast({
            title: '保存成功',
            icon: 'success',
            duration: 2000,
          });
        },
      });
    }
  };
  return (
    <View className='Index'>
      <Image
        src={groupBg}
        className='groupBg absolute margin'
        mode='widthFix'
      ></Image>
      <View className='content'>
        <View className='header'>
          <Image src={headerLeft} className='headerImage' />
          <Text className='headerText'>添加您的专属老师</Text>
          <Image src={headerRight} className='headerImage' />
        </View>
        <View className='body'>
          {/* <View className='head'>加入微信群，激活您的课程</View> */}
          <View className='qr-con'>
            <View className='qrcode'>
              {Taro.getEnv() === 'WEAPP' ? (
                <Image
                  className='qr-img'
                  src={qrCodeUrl}
                  mode='widthFix'
                  showMenuByLongpress
                />
              ) : (
                <ImgLongPress
                  styleName='qr-img'
                  currentUrl={qrCodeUrl}
                  showMenuByLongpress
                  onLongPress={onLongPresshandle}
                />
              )}
            </View>
            <View className='qr-text'>
              {/* <Image className='qr-text-img' src={icon_arr}></Image> */}
              {Taro.getEnv() === 'ALIPAY' ? '长按或' : ''}
              截图保存二维码，在微信中识别打开
            </View>
            <View className='wxcode'>微信号：{teacherNo}</View>
            <View className='copybtn' onClick={copyData}>
              复制老师微信号
            </View>
          </View>
        </View>
        <View className='foot'>
          <View className='foot-item'>
            <Image className='foot-item-img' src={icon_1}></Image>
            「在线辅导」提升孩子学习效率
          </View>
          <View className='foot-item'>
            <Image className='foot-item-img' src={icon_2}></Image>
            「答疑解惑」解决您的各种问题
          </View>
          <View className='foot-item'>
            <Image className='foot-item-img' src={icon_3}></Image>
            「全程跟踪」助力孩子全面成长
          </View>
        </View>
      </View>
    </View>
  );
}
