import Taro, { useRouter, useShareAppMessage } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import { Button, Image, View } from '@tarojs/components';
import { useDispatch, useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import { decryptAliUserApi } from '@/api/groupbuy';
import { AtFloatLayout, AtModal, AtModalContent } from 'taro-ui';
import { getOrderId } from '@/common/order';

import redeemImg from '@/assets/groupbuy/thirtySix/redeem-img2.png';
import selIcon from '@/assets/groupbuy/thirtySix/sel_icon.png';
import modalCloseImg from '@/assets/groupbuy/thirtySix/close.png';
import bottomPay from '@/assets/groupbuy/thirtySix/purchase-bg.png';
import buy from '@/assets/groupbuy/twentyNine/purchase-btn.png';
// import { useThirtySixPay } from '@/hooks/payhook/useThirtySixPay';
import sensors from '@/utils/sensors_data';

// @ts-ignore
import CommonTop from '@/components/commonTop';
import WxLogin from '@/components/wxlogin';
import { useLogin } from '@/hooks/loginhook/useLogin';
import LayoutOrderV1 from '@/components/alipay/AlipayOrderV1';
import Section from './components/section';
//@ts-ignore
import Paybtn from './components/payBtn/index';
import CheckOrderBtn from './components/checkOrderBtn';

import './index.scss';

export default () => {
  const { params } = useRouter();
  const defChannelList = {
    alipay: {
      development: '4243',
      dev: '4243',
      test: '7044',
      gray: '14703',
      online: '14703',
    },
  };
  const def_channelId =
    defChannelList[process.env.TARO_ENV][process.env.NODE_ENV];

  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  const aliUserId = useSelector((state: UserStateType) => state.aliUserId);

  //redux
  const dispatch = useDispatch();
  const [urlType] = useState('thirtySix');
  const { iLoading } = useLogin({ subject: 'ART_APP' });
  // 显示隐藏订单浮窗
  const [isShowOrder, setIsShowOrder] = useState<boolean>(false);
  // 支付页面的数据
  const [payPageData, setPayPageData] = useState<object | null>({
    sup: 'default',
  });
  const [showRedeemModal, setShowRedeemModal] = useState<boolean>(false);
  const [needShowRedeem, setNeedShowRedeem] = useState<boolean>(true);
  // 随材赠品展示
  const [giveaway] = useState({
    img: selIcon,
    detail: [
      '小熊模切',
      '超轻粘土',
      '小熊作品纸',
      '黑色勾线笔',
      '小熊马克笔',
      '重彩油画棒',
      '手指画颜料',
      '其他材料若干',
    ],
    notes: '该图仅为展示，具体以实物为主，每期根据课程不同收到的随材会有所差异',
  });
  // 监听订单浮窗的打开和关闭
  const watchShowOrder = (state, pageData) => {
    if (!state && needShowRedeem) {
      setShowRedeemModal(true);
      setNeedShowRedeem(false);
      return;
    }
    setNeedShowRedeem(true);
    setIsShowOrder(state);
    pageData && setPayPageData(pageData);
  };
  // 关闭挽留弹窗
  const hideRedeemHandle = () => {
    setShowRedeemModal(false);
  };

  const authSuccess = () => {
    my.getPhoneNumber({
      success: res => {
        const { sign, response } = JSON.parse(res.response);
        decryptAliUserApi({
          sign,
          response,
          subject: 'ART_APP',
          aliUserId: aliUserId,
        }).then(result => {
          if (result.code == 0) {
            const payload = result.payload;
            payload.uid &&
              dispatch({
                type: 'CHANGE_USERID',
                userid: payload.uid,
              });
            payload.mobile &&
              dispatch({
                type: 'CHANGE_MOBILE',
                mobile: payload.mobile,
              });
            if (result.payload.token)
              Taro.setStorageSync('appToken', result.payload.token);
            sensors.track('xxms_testcourse_loginsignupresult', {
              is_success: '是',
            });
            bottonBtnClick();
          } else {
            sensors.track('xxms_testcourse_loginsignupresult', {
              is_success: '否',
            });
          }
        });
      },
      fail: res => {
        Taro.showModal({
          content: res.errorMessage,
          showCancel: false,
        });
      },
    });
  };

  useEffect(() => {
    //区分类型
    dispatch({
      type: 'PAY_TYPE',
      payType: '29',
    });
  }, [dispatch]);

  useEffect(() => {
    userId && !iLoading && getOrderId(userId, undefined, false, urlType);
  }, [userId]);

  useEffect(() => {
    sensors.track('ai_marketing_AlipayminiAPP_buypagebrowse', {
      channel_id: params.channelId,
      urlType: urlType,
    });
  }, []);

  useShareAppMessage(() => {
    return {
      title: '小熊美术',
      path: `/pages/thirtySix/index${
        params.channelId ? `?channelId=${params.channelId}` : ''
      }`,
      success() {
        console.log('分享成功');
      },
      fail() {
        console.log('分享失败');
      },
    };
  });

  const bottonBtnClick = () => {
    // 小程序购买页立即购买点击
    sensors.track('ai_marketing_AlipayminiAPP_buypageclick', {
      channel_id: params.channelId,
      urlType: urlType,
    });
    // 小程序选择级别浏览
    sensors.track('ai_marketing_AlipayminiAPP_selectionlevelbrowse', {
      channel_id: params.channelId,
      urlType: urlType,
    });
    !iLoading && setIsShowOrder(true);
  };

  return (
    <View className='index container w100 relative'>
      {process.env.TARO_ENV === 'weapp' && (
        /* 微信登录组件 */
        <WxLogin subject='ART_APP' isIntroduce={false} />
      )}
      {/* 头部导航栏 */}
      <CommonTop
        currentName='小熊美术'
        isIntroduce={false}
        channelId={def_channelId}
      />
      {/* 图片区 */}
      <Section />
      {/* 购买按钮 */}
      <CheckOrderBtn packagesId={610} />
      <View className='suction-bottom'>
        <View className='fix-bg'></View>
        <Image mode='widthFix' src={bottomPay} className='pay-img' />
        {userId ? (
          <Button className='pay-btn' onClick={bottonBtnClick}>
            <Image mode='widthFix' src={buy} className='pay-fixed' />
          </Button>
        ) : (
          <Paybtn className='pay-btn' authSuccess={authSuccess}>
            <Image mode='widthFix' src={buy} className='pay-fixed' />
          </Paybtn>
        )}
      </View>
      {/* 弹窗 */}
      <AtFloatLayout
        className='order-layout'
        isOpened={isShowOrder}
        onClose={() => {
          watchShowOrder(false, null);
        }}
      >
        {payPageData && (
          <LayoutOrderV1
            isShowOrder={isShowOrder}
            watchCloseOrder={watchShowOrder}
            payPageData={payPageData}
            urlType={urlType}
            orderType='29'
            subject='ART_APP'
            packagesId={610}
            classNum={10}
            topicId={3}
            pType='art'
            isIntroduce={false}
            pName='10日趣味美术体验包'
            giveaway={giveaway}
            model={4}
          />
        )}
      </AtFloatLayout>
      <AtModal
        className='redeem-modal'
        isOpened={showRedeemModal}
        closeOnClickOverlay={false}
      >
        <Image
          className='close-img'
          src={modalCloseImg}
          onClick={hideRedeemHandle}
        ></Image>
        <AtModalContent>
          <Image className='redeem-img' src={redeemImg}></Image>
          <View className='footer'>
            <View className='btns' onClick={hideRedeemHandle}>
              继续支付
            </View>
          </View>
        </AtModalContent>
      </AtModal>
    </View>
  );
};
