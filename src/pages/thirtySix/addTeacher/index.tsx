import Taro, { useShareAppMessage, useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import qs from 'qs';
import { View, Image, Button } from '@tarojs/components';
import { useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import { getTeacherInfo } from '@/api/groupbuy';
import shareImg from '@/assets/groupbuy/addTeacher/share-img3.png';
import teacherImg from '@/assets/groupbuy/addTeacher/broswer-teacher.png';
import './index.scss';

export default function AddTeacher() {
  //redux
  const orderId = useSelector((state: UserStateType) => state.orderId);
  //channelId
  const channelId = useSelector((state: UserStateType) => state.channelId);
  // 区分36还是49
  const payType = useSelector((state: any) => state.payType);
  // 老师微信号
  const [teacherNo, setTeacherNo] = useState('');
  const newImg =
    'https://fe-cdn.xiaoxiongmeishu.com/ai-mp-user/image/newAddTeacher.png?1';
  const router = useRouter();
  let params = router.params;

  //调用获取老师微信号接口
  useEffect(() => {
    orderId &&
      getTeacherInfo({
        orderNo: orderId,
        addSource: params.addSource || '1',
      }).then(res => {
        console.log(res);
        if (res.code === 0) {
          setTeacherNo(res.payload.teacherWeChat);
        }
      });
  }, [orderId]);

  //复制老师微信
  function copyData() {
    Taro.setClipboardData({
      data: teacherNo,
      success: function() {
        Taro.showToast({
          title: '微信号已复制\n添加老师为好友',
          icon: 'none',
          duration: 2000,
        });
      },
    });
  }
  //分享配置
  useShareAppMessage(() => {
    let pathName: string;
    if (payType == '36') {
      if (channelId) {
        pathName = `/pages/groupbuy/thirtySix?channelId=${channelId}`;
      } else {
        pathName = `/pages/groupbuy/thirtySix`;
      }
    } else {
      if (channelId) {
        pathName = `/pages/groupbuy/index?channelId=${channelId}`;
      } else {
        pathName = `/pages/groupbuy/index`;
      }
    }
    return {
      title: `妈妈们注意了！${
        payType == '36' ? '36' : '49'
      }元10节在线儿童美术课！再不抢就晚了！`,
      path: pathName,
      imageUrl: shareImg, //自定义图片路径，可以是本地文件路径、代码包文件路径或者网络图片路径，支持PNG及JPG，不传入 imageUrl 则使用默认截图。显示图片长宽比是 5:4
      success: function(res) {
        // 转发成功之后的回调
        if (res.errMsg == 'shareAppMessage:ok') {
        }
      },
      fail: function() {},
    };
  });

  return (
    <View className='add-teacher'>
      <View className='card'>
        <View className='without-wechat-title'>添加老师 学习无忧</View>
        <View className='desc broswer-desc'>添加专属老师，激活课程服务</View>
        <Button
          className='add-btn'
          openType='contact'
          showMessageCard
          sendMessagePath={qs.stringify(params)}
          sendMessageImg={newImg}
        >
          立即添加
        </Button>
        <Image className='broswer-teacher' src={teacherImg}></Image>
        <View className='add-way-title'>添加方式-复制微信</View>
        <View className='teachers-wechat-item'>
          <View className='left'>美术老师微信：{teacherNo}</View>
          <View className='right' onClick={copyData}>
            复制
          </View>
        </View>
        <View className='add-way-tips'>打开微信首页-右上角添加好友</View>
      </View>
    </View>
  );
}
AddTeacher.config = {
  navigationBarTitleText: '邀好友一起学',
};
