@import '../../../theme/groupbuy/common.scss';

.add-teacher {
  font-size: 0;
  background: $smoke-white;
  min-height: 100vh;
  padding: 32px;
  box-sizing: border-box;
  .card {
    padding: 53px 28px 120px;
    background: #ffffff;
    border-radius: 20px;
    position: relative;
    text-align: center;
    margin-bottom: 38px;
    box-sizing: border-box;
    .without-wechat-title {
      width: fit-content;
      height: 56px;
      font-size: 40px;
      font-weight: bold;
      color: #333333;
      line-height: 56px;
      display: flex;
      align-items: center;
      margin: 0 auto;
      &::before {
        display: block;
        content: '';
        width: 45px;
        height: 20px;
        background: url('../../../assets/groupbuy/addTeacher/title-left.png')
          no-repeat;
        background-size: 100%;
        background-position: center;
        margin-right: 20px;
      }
      &::after {
        display: block;
        content: '';
        width: 45px;
        height: 20px;
        background: url('../../../assets/groupbuy/addTeacher/title-right.png')
          no-repeat;
        background-size: 100%;
        background-position: center;
        margin-left: 20px;
      }
    }
    .desc {
      height: 46px;
      font-size: 32px;
      font-weight: bold;
      color: #3e2d21;
      line-height: 46px;
      text-align: center;
      user-select: none;
      pointer-events: none;
    }
    .broswer-desc {
      margin: 64px 0 50px;
    }
    .add-btn {
      width: 554px;
      height: 86px;
      background: #ff7000;
      border-radius: 43px;
      font-size: 36px;
      font-weight: bold;
      color: #ffffff;
      line-height: 86px;
      text-align: center;
      margin: 0 auto;
      animation: scaleJumpbtn 0.9s ease-in-out infinite;
    }
    .broswer-teacher {
      width: 100%;
      height: 265px;
      vertical-align: top;
      margin: 64px 0 50px;
    }
    .add-way-title {
      height: 40px;
      font-size: 28px;
      color: #8b8b8b;
      line-height: 40px;
      text-align: center;
      margin-bottom: 32px;
    }
    .teachers-wechat-item {
      width: 629px;
      height: 86px;
      background: #f7f7f7;
      border-radius: 11px;
      display: flex;
      padding: 0 16px;
      box-sizing: border-box;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 14px;
      .left {
        height: 38px;
        font-size: 26px;
        color: #666666;
        line-height: 38px;
      }
      .right {
        width: fit-content;
        height: 46px;
        line-height: 44px;
        border-radius: 23px;
        text-align: center;
        padding: 0 18px;
        font-size: 24px;
        box-sizing: border-box;
        border: 1px solid #ff7000;
        box-sizing: border-box;
        color: #ff7000;
      }
    }
    .add-way-tips {
      height: 38px;
      font-size: 26px;
      color: #666666;
      line-height: 38px;
      margin-top: 32px;
    }
    @keyframes scaleJumpbtn {
      0% {
        transform: scale(0.93);
      }
      50% {
        transform: scale(1.07);
      }
      100% {
        transform: scale(0.93);
      }
    }
  }
}
