import Taro, { useRouter } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { useEffect, useState } from 'react';
import { AtFloatLayout } from 'taro-ui';
import levelBack from '@/assets/thirtySix/chooseLevel/levelBack0.png';
import sensors from '@/utils/sensors_data';

import { useSelector, useDispatch } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import {
  submitSupApi,
  queryOrderByUserId,
  getSupManagements,
} from '@/api/groupbuy';

import { useThirtySixPay } from '@/hooks/payhook/useThirtySixPay';
import LayoutLevel from '../components/level';
import RetainModal from '../components/retainModal';
import UpgradeTSModal from '../components/upgradeTSModal';
import RedeemModal from '../components/redeemModal';

import './index.scss';

const ChooseLevel = () => {
  const { params } = useRouter();
  // orderId 或是传过来 或是redux中存储获取
  const { level, orderId, packagesId } = params;
  const orderid = useSelector((state: UserStateType) => state.orderId);
  //userid
  const userId =
    useSelector((state: UserStateType) => state.userid) || '544550368198266880';
  const [convList, setConvList] = useState<number[]>([]);
  const [showLevel, setShowLevel] = useState(false);

  const [levelData, setLevelData] = useState<any>({});

  const [showUpgrade, setShowUpgrade] = useState(false);
  const [showRedeem, setShowRedeem] = useState(false);
  const channelId = useSelector((state: UserStateType) => state.channelId);

  const dispatch = useDispatch();

  // 挽留弹窗
  const [showRetain, setShowRetain] = useState(false);
  const [iNeedRetainShow, setINeedRetainShow] = useState(true);
  const retainModalProps = {
    ishow: showRetain,
    btnName: '继续选择级别',
    closeModal: type => {
      setShowRetain(false);
      if (type === 'btn') {
        sensors.track(
          'xxms_testcourse_Chooselevelpage_popup_choosebuttonclick',
          {},
        );
        setShowLevel(true);
      }
    },
    redeemImg: levelBack,
    btnStyle: {
      backgroundColor: '#ff5520',
    },
  };
  const upgradeTSModalProps = {
    ishow: showUpgrade,
    sup: levelData.label,
    closeModal: () => {
      setShowUpgrade(false);
      setShowRedeem(true);
      sensors.track('xxms_testcourse_Packageupgradepopup_buttonclick', {
        is_update: '关闭弹窗',
      });
    },
  };
  const redeemModalProps = {
    ishow: showRedeem,
    closeModal: type => {
      if (type === 'up') {
        payConfirm();
        sensors.track(
          'xxms_testcourse_Packageupgraderetentionpopover_buttonclick',
          {
            is_update: '继续升级',
          },
        );
      } else if (type === 'giveUp') {
        sensors.track(
          'xxms_testcourse_Packageupgraderetentionpopover_buttonclick',
          {
            is_update: '确认放弃添加老师',
          },
        );
        const id = Taro.getStorageSync('zeroDotOneOrderId');
        dispatch({
          type: 'CHANGE_ORDERID',
          orderId: id,
        });
        jumpTeacher();
      }
    },
  };
  const { setPeriod, payConfirm } = useThirtySixPay({
    topicId: 4,
    packagesId: 618,
    isIntroduce: false,
    subject: 'ART_APP',
    pType: 'art',
    payPageData: null,
    sup: levelData.label,
  });
  // 监听订单浮窗的打开和关闭
  const watchShowOrder = (state, selectLevelData) => {
    setShowLevel(false);
    setLevelData(selectLevelData);
    if (selectLevelData.label) {
      sensors.track('xxms_testcourse_Chooselevelpage_Selectbuttonclick');
      sensors.track('ai_marketing_AlipayminiAPP_selectionlevelclick', {
        channel_id: params.channelId,
        urlType: params.urlType,
      });
    }
    getSupManagements({
      type: 'TESTCOURSE',
      sup: selectLevelData.label,
      subject: 'ART_APP',
    }).then(res => {
      const result = res.payload && res.payload;
      if (result) {
        setPeriod(result.period);
      }
    });
    if (selectLevelData && Object.keys(selectLevelData).length > 0) {
      setConvList([1, 2, 3]);
    } else {
      setINeedRetainShow(false);
      iNeedRetainShow && setShowRetain(true);
    }
  };

  // 检验是否可买 36
  const queryOrderHandle = () => {
    queryOrderByUserId({
      userId: userId,
      channels: channelId,
      subjects: 'ART_APP',
    }).then(res => {
      const {
        channelCheck,
        systemCheckMap,
        pluralCheckMap,
        subjectOrderMap,
        experienceCheckMap,
      } = res.payload;
      // experienceCheckMap.ART_APP为true 表示之前已经买过体验课了
      if (experienceCheckMap.ART_APP) {
        if (
          subjectOrderMap &&
          subjectOrderMap.ART_APP &&
          subjectOrderMap.ART_APP.EXPERIENCE &&
          subjectOrderMap.ART_APP.EXPERIENCE.length > 0
        ) {
          const experience: never[] = subjectOrderMap['ART_APP']['EXPERIENCE'];
          let supList = [];
          for (let i = 0; i < experience.length; i++) {
            supList.push(experience[i]['sup']);
          }
          setTimeout(() => {
            if (supList.some(i => i == levelData.label)) {
              // 不可以重复购买
              setShowUpgrade(false);
              jumpTeacher();
            } else {
              // 可以重复购买
              setShowUpgrade(true);
              sensors.track(
                'xxms_testcourse_Chooselevelpage_Packageupgradepopup_view',
              );
            }
          }, 200);
        }
        if (channelCheck && !systemCheckMap.ART_APP && pluralCheckMap.ART_APP) {
          // 可以重复购买
          setTimeout(() => {
            setShowUpgrade(true);
            sensors.track(
              'xxms_testcourse_Chooselevelpage_Packageupgradepopup_view',
            );
          }, 500);
        } else {
          // 不可以重复购买
          setShowUpgrade(false);
          jumpTeacher();
        }
      } else {
        // experienceCheckMap.ART_APP为false 表示之前没有买过体验课了，是首单
        setShowUpgrade(true);
        sensors.track(
          'xxms_testcourse_Chooselevelpage_Packageupgradepopup_view',
        );
      }
    });
  };

  // 提交
  const handleOk = () => {
    sensors.track('xxms_testcourse_Chooselevelpage_Confirmsubmitbuttonclick', {
      level: levelData.label,
      is_choose: '是',
    });
    submitSupApi({
      orderId: orderId || orderid || '',
      subject: 'ART_APP',
      sup: levelData.label,
      userId,
    }).then(res => {
      if (res.code == 0) {
        if (packagesId == '617') {
          queryOrderHandle();
        } else {
          jumpAddaddress();
        }
        sensors.track(
          'xxms_testcourse_Chooselevelpage_Confirmsubmitbuttonclick',
          {
            level: levelData.label,
            is_choose: '是',
          },
        );
      } else {
        sensors.track(
          'xxms_testcourse_Chooselevelpage_Confirmsubmitbuttonclick',
          {
            level: levelData.label,
            is_choose: '否',
          },
        );
        Taro.showToast({ title: res.errors, icon: 'none' });
      }
    });
  };

  const jumpAddaddress = () => {
    Taro.redirectTo({
      url: `/pages/thirtySix/addAddress/index${
        params.urlType ? `?urlType=${params.urlType}` : ``
      }`,
    });
  };

  const jumpTeacher = () => {
    Taro.redirectTo({
      url: `/pages/thirtySix/addNewTeacher/index${
        params.urlType ? `?urlType=${params.urlType}` : ``
      }`,
    });
  };

  useEffect(() => {
    setTimeout(() => {
      setConvList([1]);
    }, 200);
    setTimeout(() => {
      setConvList([1, 2]);
    }, 777);
  }, []);
  useEffect(() => {
    if (level) {
      setLevelData({
        label: level,
      });
      setTimeout(() => {
        setConvList([1, 2, 3]);
      }, 777);
    }
  }, [level]);

  return (
    <View className='choose-level'>
      <View className='conv-list'>
        {convList.map((item, index) => {
          return (
            <View className='item' key={item}>
              <View className='left'></View>
              <View className='right'>
                <View className='name'>辅导老师</View>
                {index === 0 && (
                  <View className='card'>
                    为了帮助辅导老师更好地了解孩子的基本情况，匹配合适的体验内容，请根据孩子的实际情况进行回答哦～
                  </View>
                )}
                {index === 1 && (
                  <View
                    className='card'
                    onClick={() => {
                      sensors.track(
                        'xxms_testcourse_Chooselevelpage_Selectbuttonclick',
                      );
                      sensors.track(
                        'ai_marketing_AlipayminiAPP_selectionlevelbrowse',
                        {
                          channel_id: params.channelId,
                          urlType: params.urlType,
                        },
                      );
                      setShowLevel(true);
                    }}
                  >
                    <View className='title'>1.请选择孩子的级别</View>
                    <View className='date-wrap'>
                      <View className='left-date'></View>
                      <View className='right-date'>
                        <Text
                          className={`selectedSup ${
                            levelData.label ? 'birth' : ''
                          }`}
                        >
                          {levelData.label
                            ? `小熊美术${levelData.label}`
                            : '选择级别'}
                        </Text>
                      </View>
                    </View>
                  </View>
                )}
                {index === 2 && (
                  <View className='card'>
                    <View className='info'>
                      感谢您的回答，请核对选项，如果确认无误，请点击提交，我们将立即为孩子匹配合适的体验内容！
                    </View>
                    <View className='submit-btn' onClick={handleOk}>
                      确认提交
                    </View>
                  </View>
                )}
              </View>
            </View>
          );
        })}
      </View>
      <AtFloatLayout
        isOpened={showLevel}
        onClose={() => {
          watchShowOrder(false, levelData);
        }}
        title='选择级别'
      >
        <LayoutLevel watchShowOrder={watchShowOrder} />
      </AtFloatLayout>

      <RetainModal {...retainModalProps} />
      <UpgradeTSModal {...upgradeTSModalProps} />
      <RedeemModal {...redeemModalProps} />
    </View>
  );
};

export default ChooseLevel;
