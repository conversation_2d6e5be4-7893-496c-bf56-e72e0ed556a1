.choose-level {
  min-height: 100vh;
  background: #f2f2f2;
  .conv-list {
    padding: 52px 44px 24px 32px;
    box-sizing: border-box;
    .item {
      display: flex;
      margin-bottom: 38px;
      .left {
        width: 88px;
        height: 88px;
        border-radius: 16px;
        background-image: url('../../../assets/thirtySix/chooseLevel/avatar.png');
        background-position: center;
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
      .right {
        margin-left: 20px;
        flex: 1;
        .name {
          height: 38px;
          font-size: 26px;
          color: #555555;
          line-height: 38px;
        }
        .card {
          border-radius: 16px;
          margin-top: 10px;
          padding: 22px 24px 28px 26px;
          box-sizing: border-box;
          background: #fff;
          font-size: 26px;
          font-weight: bold;
          color: #333333;
          line-height: 38px;
          .title {
            padding-bottom: 13px;
            border-bottom: 1px solid #eeeeee;
          }
          .date-wrap {
            display: flex;
            margin-top: 19px;
            justify-content: space-between;
            padding: 0 36px 0 34px;
            box-sizing: border-box;
            .left-date {
              background: transparent;
              height: 38px;
            }
            .right-date {
              width: fit-content;
              color: #999;
            }
            .birth {
              color: #ff7817;
            }
          }
          .info {
            letter-spacing: -0.1px;
          }
          .submit-btn {
            width: 362px;
            height: 68px;
            margin: 23px auto 0;
            background: linear-gradient(270deg, #ffa300 0%, #ff6a00 100%);
            border-radius: 34px;
            font-size: 30px;
            font-weight: bold;
            color: #ffffff;
            line-height: 68px;
            text-align: center;
          }
        }
      }
    }
  }
  .choose-age-picker {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
  }
  .level-popup {
    padding: 0 32px;
    box-sizing: border-box;
  }
}
