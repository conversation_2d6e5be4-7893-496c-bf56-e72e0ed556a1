import { useState } from 'react';
import { Image } from '@tarojs/components';
import banner3 from '@/assets/groupbuy/index/banner3.png';
import banner4 from '@/assets/groupbuy/index/banner4.png';
import banner8 from '@/assets/groupbuy/index/banner8.png';
import './index.scss';

export default function Index(props) {
  const { bannerType } = props;
  const [bannerImgs] = useState([banner8, banner3, banner4]);
  return (
    <Image
      mode='widthFix'
      src={bannerImgs[bannerType]}
      className='banner'
    ></Image>
  );
}
