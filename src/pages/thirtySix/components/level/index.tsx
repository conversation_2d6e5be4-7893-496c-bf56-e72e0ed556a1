import { useEffect, useState } from 'react';
import { useRouter } from '@tarojs/taro';
import { Text, View } from '@tarojs/components';
import { supAgeInterval, supSwitch } from '@/api/groupbuy';
import { levelV1, levelV2 } from '@/common/data.config';
import { useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import CheckLevel from '@/utils/checkLevel';
// import LevelTips from '@/components/levelTips';
import './index.scss';

export default function Index(props) {
  // 选择卡片后通知父组件显示订单浮窗
  const { watchShowOrder } = props;
  // 选择激活的卡片
  const [tabActive, setTabActive] = useState<number>(-1);
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  const router = useRouter();
  let params = router.params;
  const { orderId } = params;
  const orderid = useSelector((state: UserStateType) => state.orderId);
  //channelId
  const channelId = useSelector((state: UserStateType) => state.channelId);
  const [hasBuyLevel, setHasBuyLevel] = useState<any[]>([]);

  const [level, setLevel] = useState<any>([]);
  const [title, setTitle] = useState<string>('');
  const [supSwitchType, setSupSwitchType] = useState<any>(false);
  useEffect(() => {
    supSwitch()
      .then(res => {
        setSupSwitchType(res.payload);
        if (res.payload) {
          newLevel();
        } else {
          oldLevel();
        }
      })
      .catch(() => {
        newLevel();
      });
  }, []);

  const oldLevel = () => {
    supAgeInterval().then(res => {
      levelV1.map((item, index) => {
        item.range = res.payload['S' + (index + 1)];
      });
      setLevel(levelV1);
    });
    setTitle('为保证孩子的学习体验，请正确选择孩子的年龄');
  };

  const newLevel = () => {
    setLevel(levelV2);
    setTitle('为保证孩子的学习体验，请正确选择对应的级别');
  };

  const filterBuyLevel = sup => {
    return (
      hasBuyLevel &&
      hasBuyLevel.length &&
      hasBuyLevel.findIndex(v => v == sup) > -1
    );
  };

  useEffect(() => {
    new CheckLevel({
      userId,
      channelId,
      orderId: orderid || orderId,
      regtype: '',
      subjects: 'ART_APP',
    })
      .initCheck()
      .then((res: any[]) => {
        setHasBuyLevel(res);
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId, channelId]);

  return (
    <View className='level'>
      <View className='level-tip'>*{title}</View>
      {level.map(
        (item, index) =>
          index != 3 && (
            <View
              className={`card ${
                filterBuyLevel(item.label) ? `selected` : ``
              } ${tabActive === index ? 'active' : ''}`}
              key={`card-${index}`}
              onClick={() => {
                if (filterBuyLevel(item.label)) {
                  // Taro.showToast({ title: '已购买过请选择其他级别', icon: 'none' });
                  return;
                }
                setTabActive(index);
                watchShowOrder(true, level[index]);
              }}
            >
              <View className='selected-mark'></View>
              <View
                className='label'
                style={`${
                  filterBuyLevel(item.label)
                    ? ``
                    : `background: ${item.bgcolor}`
                }`}
              >
                {item.label}
              </View>
              <View className='info'>
                <View className='title'>
                  <Text className='fit'>{item.fit}</Text>
                </View>
                <View
                  className='desc'
                  style={`${
                    filterBuyLevel(item.label) ? `` : `color: ${item.bgcolor}`
                  }`}
                >
                  {item.range}
                </View>
              </View>
            </View>
          ),
      )}
      {/* <LevelTips /> */}
    </View>
  );
}
