import { Image, View } from '@tarojs/components';
import { AtModal, AtModalContent } from 'taro-ui';
import modalCloseImg from '@/assets/groupbuy/thirtySix/close.png';
import sensors from '@/utils/sensors_data';
import { useEffect } from 'react';
import './index.scss';

interface IRetainmodal {
  ishow: boolean;
  closeModal: (type: string) => void;
  btnName: string;
  redeemImg: string;
  btnStyle?: object;
}

const RetainModal = (props: IRetainmodal) => {
  const {
    ishow,
    closeModal,
    btnName,
    redeemImg,
    btnStyle = {
      backgroundColor: 'ff9c00',
      color: 'ffffff',
    },
  } = props;
  useEffect(() => {
    if (ishow) {
      sensors.track('xxms_testcourse_Chooselevelpage_popup_view', {});
    }
  }, [ishow]);
  return (
    <AtModal
      className='redeem-modal'
      isOpened={ishow}
      closeOnClickOverlay={false}
    >
      <Image
        className='close-img'
        src={modalCloseImg}
        onClick={() => closeModal('icon')}
        mode='widthFix'
      ></Image>
      <AtModalContent>
        <Image className='redeem-img' src={redeemImg} mode='widthFix'></Image>
        <View className='footer'>
          <View
            className='btn'
            style={btnStyle}
            onClick={() => closeModal('btn')}
          >
            {btnName}
          </View>
        </View>
      </AtModalContent>
    </AtModal>
  );
};

export default RetainModal;
