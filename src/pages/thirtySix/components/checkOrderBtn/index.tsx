import { decrypt<PERSON>liUser<PERSON><PERSON>, getOrderListAndExpressApi } from '@/api/groupbuy';
import { UserStateType } from '@/store/groupbuy/state';
import { Button, View } from '@tarojs/components';
import { useState } from 'react';
import Taro from '@tarojs/taro';
import { useDispatch, useSelector } from 'react-redux';
import sensors from '@/utils/sensors_data';
//@ts-ignore
import Paybtn from '../payBtn';
import './index.scss';

export default ({ packagesId }) => {
  const dispatch = useDispatch();
  const [iToggle, setIToggle] = useState(false);
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  const aliUserId = useSelector((state: UserStateType) => state.aliUserId);

  const getOrderListAndExpress = uid => {
    return new Promise(resolve => {
      getOrderListAndExpressApi({ userId: uid || userId })
        .then(res => {
          resolve(res.payload || []);
        })
        .catch(() => {
          resolve([]);
        });
    });
  };

  const jumpToLogistics = (uid = userId) => {
    Taro.hideLoading();
    getOrderListAndExpress(uid).then((res: any[]) => {
      if (res.length > 0) {
        Taro.navigateTo({
          url: `/pages/orders/list/index`,
        });
      } else {
        Taro.showToast({
          title: '暂无您的订单信息，请先购买',
          icon: 'none',
          duration: 2000,
        });
      }
    });
  };
  const authError = () => {};
  const authSuccess = () => {
    Taro.showLoading();
    my.getPhoneNumber({
      success: res => {
        const { sign, response } = JSON.parse(res.response);
        decryptAliUserApi({
          sign,
          response,
          subject: 'ART_APP',
          aliUserId: aliUserId,
        }).then(result => {
          if (result.code == 0) {
            const payload = result.payload;
            payload.uid &&
              dispatch({
                type: 'CHANGE_USERID',
                userid: payload.uid,
              });
            payload.mobile &&
              dispatch({
                type: 'CHANGE_MOBILE',
                mobile: payload.mobile,
              });
            if (result.payload.token)
              Taro.setStorageSync('appToken', result.payload.token);
            sensors.track('xxms_testcourse_loginsignupresult', {
              is_success: '是',
            });
            jumpToLogistics(payload.uid);
          } else {
            sensors.track('xxms_testcourse_loginsignupresult', {
              is_success: '否',
            });
          }
        });
        if (packagesId == 617) {
          sensors.track(
            'xxms_testcourse_registrationpaylayer_Getauthorization',
            {
              is_authorization: '允许',
            },
          );
        }
      },
      fail: res => {
        if (packagesId == 617) {
          sensors.track(
            'xxms_testcourse_registrationpaylayer_Getauthorization',
            {
              is_authorization: '拒绝',
            },
          );
        }
        Taro.hideLoading();
        Taro.showModal({
          content: res.errorMessage,
          showCancel: false,
        });
      },
    });
  };
  const handleToggle = () => {
    setIToggle(val => !val);
  };
  const BtnEl = ({ children }) => {
    if (userId) {
      return (
        <Button className='check-btn' onClick={() => jumpToLogistics()}>
          {children}
        </Button>
      );
    }
    return (
      <Paybtn
        className='check-btn'
        onError={authError}
        authSuccess={authSuccess}
      >
        {children}
      </Paybtn>
    );
  };

  return (
    <View className='check-order-btn'>
      <View className={`btn-wrap ${iToggle ? 'hide' : ''}`}>
        <View className='left' onClick={handleToggle}>
          <View className='left-icon'></View>
        </View>
        <View className='right'>
          <BtnEl>
            <View className='right-icon'></View>
            <View className='right-word'>订单查询</View>
          </BtnEl>
        </View>
      </View>
    </View>
  );
};
