.check-order-btn {
  position: fixed;
  right: 0;
  bottom: 400px;
  z-index: 21;

  height: 120px;
  box-shadow: 10px 10px 20px rgba($color: #eabda0, $alpha: 0.5);
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
  background-color: #ffffff;
  .btn-wrap {
    display: flex;
    &.hide {
      .left {
        .left-icon {
          background-image: url('../../../../assets/thirtySix/checkorderbtn/show-icon.png');
        }
      }
      .right {
        width: 0;
      }
    }
  }
  .left {
    width: 30px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    .left-icon {
      width: 17px;
      height: 26px;
      background: url('../../../../assets/thirtySix/checkorderbtn/hide-icon.png')
        no-repeat left top;
      background-size: contain;
    }
  }
  .right {
    width: 120px;
    transition: width 0.1s cubic-bezier(0.755, 0.05, 0.855, 0.06);
  }
  .check-btn {
    width: 100%;
    height: 100%;
    background-color: transparent;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .right-icon {
      width: 51px;
      height: 48px;
      background: url('../../../../assets/thirtySix/checkorderbtn/search.png')
        no-repeat left top;
      background-size: contain;
      margin-top: 19px;
    }
    .right-word {
      margin-top: 10px;
      font-size: 24px;
      height: 30px;
      line-height: 30px;
    }
  }
}
