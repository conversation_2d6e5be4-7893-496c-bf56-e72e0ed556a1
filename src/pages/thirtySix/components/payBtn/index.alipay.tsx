import { Button } from '@tarojs/components';

interface Paybtnprops {
  className?: string;
  authError: (res: any) => void;
  authSuccess: (res: any) => void;
  btnName?: string;
  children?: any;
}
const Paybtn = ({
  className = 'button',
  authError,
  authSuccess,
  btnName,
  children,
}: Paybtnprops) => {
  // 获取用户手机号
  return (
    <Button
      className={className}
      openType='getAuthorize'
      scope='phoneNumber'
      onError={authError}
      onGetAuthorize={authSuccess}
    >
      {btnName || children || '确认支付'}
    </Button>
  );
};

export default Paybtn;
