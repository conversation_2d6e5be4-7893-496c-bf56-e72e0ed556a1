import Taro from '@tarojs/taro';
import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { View, Text, Image, Button } from '@tarojs/components';
import { dateFormat } from '@/utils/index';
import { UserStateType } from '@/store/groupbuy/state';
import { getSupManagements } from '@/api/groupbuy';
import { useThirtySixPay } from '@/hooks/payhook/useThirtySixPay';

// 倒计时
// 支付宝禁止激发用户抢购的行为 如需兼容微信需放开处理
// import CountDown from '@/components/groupbuy/countdown';
//@ts-ignore
import Paybtn from '../payBtn/index';

import './index.scss';

export default function Index(props) {
  const {
    watchCloseOrder,
    payPageData,
    orderType,
    giveaway,
    pType,
    packagesId,
    topicId,
    pName,
    classNum,
    subject,
    isIntroduce = true,
  } = props;

  // 定义倒计时初始值
  // const [countDownNum, setCountDown] = useState(0);
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);

  const [courseday, setCourseday] = useState<string>();
  const { authSuccess, setPeriod, payConfirm, redeemFlag } = useThirtySixPay({
    topicId,
    packagesId,
    isIntroduce,
    subject,
    pType,
    payPageData,
  });

  const authError = () => {};

  // useEffect(() => {
  //   if (countDownNum === 0 && props.isShowOrder) {
  //     setCountDown(600000);
  //   }
  // }, [props.isShowOrder, countDownNum]);
  useEffect(() => {
    payPageData &&
      getSupManagements({
        type: 'TESTCOURSE',
        sup: payPageData.sup,
        subject,
      }).then(res => {
        const result = res.payload && res.payload;
        if (result) {
          const openCourseDate = dateFormat(
            result.courseDay * 1,
            'MM' + '月' + 'dd' + '日',
          );
          setCourseday(openCourseDate);
          setPeriod(result.period);
        } else {
          Taro.showToast({
            title: res.errors,
            icon: 'none',
          });
        }
      });
  }, [payPageData, subject, setPeriod]);

  return (
    <View className='order'>
      <View className='title'>
        <View className='time' style={{ height: 20 }}>
          {/* <CountDown payCountDown={countDownNum} />
          <View className='text'>剩余支付时间</View> */}
        </View>
        <View
          className='close'
          onClick={() => watchCloseOrder(false, null, redeemFlag)}
        ></View>
      </View>
      <View className='subTitle'>
        <View className='level'>小熊{pName}体验课</View>
        {/* {payPageData.courseday && (
          <View className='start-time'>{courseday}开课</View>
        )} */}
      </View>
      <View className='packages'>
        <View className='row'>
          <View className='label'>【优惠】</View>
          <View className='desc'>
            新人专享{orderType}元{classNum}节课
          </View>
        </View>
        <View className='row'>
          <View className='label'>【赠品】</View>
          <View className='desc'>
            配套随材礼包
            <Text className='label'>（收货信息将在付款后填写）</Text>
          </View>
        </View>
        <View className='row'>
          <View className='label'>【提醒】</View>
          <View className='desc'>
            随材礼盒为课程配套物品，不同级别的礼盒略有差异
          </View>
        </View>
      </View>
      <View className='gift-box-row'>
        <View className={pType + ' gift-box'}>
          <Image src={giveaway.img} mode='widthFix' className='img' />
        </View>
        <View className={pType + ' label'}>
          {giveaway.detail.map((item, index) => {
            return (
              <View className={pType + ' label-item'} key={`label-${index}`}>
                {item}
              </View>
            );
          })}
        </View>
      </View>
      <View className='pay'>
        <View className='price-box'>
          <View className='symbol'>¥</View>
          <View className='price'>{orderType}</View>
        </View>
        {userId ? (
          <Button className='button' onClick={() => payConfirm()}>
            确认支付
          </Button>
        ) : (
          <Paybtn authError={authError} authSuccess={authSuccess} />
        )}
      </View>
    </View>
  );
}
