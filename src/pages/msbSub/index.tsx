import { useDidShow, useShareAppMessage, useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import { View, Image, Button } from '@tarojs/components';
import { useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import { getOrderId } from '@/common/order';
import { getSchemeParam } from '@/utils/schemeParams';
import bottomPay from '@/assets/msbSub/img-footer.png';
import { useThirtySixPay } from '@/hooks/payhook/useThirtySixPay';
import sensors from '@/utils/sensors_data';

// @ts-ignore
import CommonTop from '@/components/commonTop';
import { useLogin } from '@/hooks/loginhook/useLogin';
import Section from './components/section';
import './index.scss';

export default () => {
  const { params } = useRouter();
  const [urlType] = useState('msbSub');
  const { iLoading } = useLogin({
    subject: 'ART_APP',
    mobile: params.mobile || getSchemeParam('mobile'),
    urlType,
  });
  const userId = useSelector((state: UserStateType) => state.userid);
  const channelId = useSelector((state: UserStateType) => state.channelId);
  const { payConfirm } = useThirtySixPay({
    topicId: 4,
    packagesId: 1981, // 【支付宝小程序订阅】市场推广录播课
    isIntroduce: false,
    subject: 'ART_APP',
    pType: 'art',
    payPageData: null,
    urlType,
  });

  useDidShow(() => {
    userId && getOrderId(userId, undefined, false, urlType);
  });

  const bottonBtnClick = () => {
    // 小程序购买页立即购买点击
    sensors.track('ai_marketing_AlipayminiAPP_buypageclick', {
      channel_id: params.channelId,
      urlType: urlType,
    });
    !iLoading && payConfirm();
  };

  useShareAppMessage(() => {
    return {
      title: '美术宝绘画课程',
      path: `/pages/msbSub/index${channelId ? `?channelId=${channelId}` : ''}`,
    };
  });

  return (
    <View className='index container w100 relative'>
      {/* 头部导航栏 */}
      <CommonTop currentName='美术宝绘画课程' isIntroduce={false} />
      {/* 图片区 */}
      <Section />
      {/* 购买按钮 */}
      <View className='suction-bottom'>
        <Button className='pay-btn' onClick={bottonBtnClick}>
          <Image mode='widthFix' src={bottomPay} className='pay-img' />
        </Button>
      </View>
    </View>
  );
};
