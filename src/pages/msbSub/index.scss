@import '@/theme/groupbuy/common.scss';

.index {
  background: linear-gradient(to right, #fc704d, #fd870f);
  .suction-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 122rpx;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    padding-bottom: calc(env(safe-area-inset-bottom) * 0.7);
    .pay-btn {
      position: absolute;
      width: 100%;
      height: auto;
      right: auto;
      // animation: scaleAt infinite ease-in-out 0.8s;
      z-index: 10;
      background: none;
      border: 0;
      .pay-img {
        width: 100%;
        height: auto;
      }
    }
    .pay-fixed {
      width: 320rpx;
      height: 90rpx;
    }
  }
  @keyframes scaleAt {
    0% {
      transform: scale(0.95);
    }
    50% {
      transform: scale(1.05);
    }
    100% {
      transform: scale(0.95);
    }
  }
}
