import { View, Image } from '@tarojs/components';
import banner from '@/assets/msbSub/img-1.png';
import bannerPay from '@/assets/msbSub/img-2.png';
import childrenLike from '@/assets/msbSub/img-3.png';
import study from '@/assets/msbSub/img-4.png';
import content from '@/assets/msbSub/img-5.png';

import './index.scss';

const Section = () => {
  const imgList = [childrenLike, study, content];

  return (
    <View className='section'>
      <View className='banner'>
        <Image mode='widthFix' src={banner} className='banner'></Image>
      </View>
      <View>
        <View>
          <Image mode='widthFix' src={bannerPay} className='payText'></Image>
        </View>
        <View className='bannerContent'>
          <View className='bannerList'>
            {imgList.map((e, i) => {
              return (
                <View className='bannerDiv' key={i}>
                  <Image mode='widthFix' src={e} className='bannerImg'></Image>
                </View>
              );
            })}
          </View>
        </View>
      </View>
    </View>
  );
};

export default Section;
