import { useEffect, useMemo, useState } from 'react';
import Taro from '@tarojs/taro';
import { Image, Swiper, SwiperItem, Text, View } from '@tarojs/components';
import { AtCountdown, AtModal, AtModalContent } from 'taro-ui';
import { getBenefitActiveInit } from '@/api/groupbuy';
import sensors from '@/utils/sensors_data';
import CredentialImg from '@/assets/benefitActivity/credential-exam.png';
import BtmLogo from '@/assets/benefitActivity/btm-logo.png';
import Banner1 from '@/assets/benefitActivity/banner1.png';
import Banner2 from '@/assets/benefitActivity/banner2.png';
import Banner3 from '@/assets/benefitActivity/banner3.png';
import Des1 from '@/assets/benefitActivity/des1.png';
import Des2 from '@/assets/benefitActivity/des2.png';
import Des3 from '@/assets/benefitActivity/des3.png';
import Modal1Img from '@/assets/benefitActivity/modal1.png';
import Modal1BtnImg from '@/assets/benefitActivity/modal1-btn.png';
import Modal2Img from '@/assets/benefitActivity/modal2-img.png';
import Modal2BtnImg from '@/assets/benefitActivity/modal2-btn.png';
import styles from './index.module.scss';

const banners = [Banner1, Banner2, Banner3];

/**
 * 周周公益
 * */
const benefitActivity = () => {
  // swiper idx
  const [cux, setCux] = useState<number>(1);
  const [modalOne, setModalOne] = useState(true);
  const [modalTwo, setModalTwo] = useState(false);
  const [progressInfo, setProgressInfo] = useState({
    joinNumber: 0,
    participationProgressCoefficient: 0,
  });
  const [channelId] = useState(process.env.NODE_ENV == 'online' ? 11596 : 7845);

  useEffect(() => {
    if (modalOne || modalTwo) {
      sensors.track('xxys_getGift_publicGood_popView', {
        pop_type: modalOne
          ? '页面首屏弹窗'
          : modalTwo
          ? '立即领取触发弹出弹窗'
          : '',
      });
    }
  }, [modalOne, modalTwo]);

  useEffect(() => {
    getBenefitActiveInitHandle();
  }, []);

  // 计算进度
  const progressPercent = useMemo(() => {
    const val = progressInfo.participationProgressCoefficient / (300 * 135);
    const percent = (val * 100).toFixed(2);
    return Number(percent) > 100 ? 100 : percent;
  }, [progressInfo]);

  // 计算时间
  const timeInfo = useMemo(() => {
    const date = new Date();
    const day = 7 - date.getDay();
    const hours = 23 - date.getHours();
    const minutes = 60 - date.getMinutes();
    const seconds = 60 - date.getSeconds();

    return { day, hours, minutes, seconds };
  }, []);

  // 初始化数据
  const getBenefitActiveInitHandle = () => {
    getBenefitActiveInit({ subject: 'ART_APP' }).then(res => {
      if (res.code === 0) {
        setProgressInfo(res.payload);
      }
    });
  };

  // 跳转 29元页面
  const linkToHandle = () => {
    sensors.track('xxys_getGift_publicGood_buyClick', {});
    const env = process.env.NODE_ENV == 'online';
    const posterId = env ? 67 : 119;
    setTimeout(() => {
      Taro.navigateTo({
        url: `/pages/normalGroup/art/index?channelId=${channelId}&poster_id=${posterId}`,
      });
    }, 300);
  };

  return (
    <View className={styles['benefit-main']}>
      <View className={styles['header-main']}>
        <Swiper
          autoplay
          circular
          className={styles['header']}
          onChange={e => setCux(e.detail.current + 1)}
        >
          {banners.map((url, idx) => (
            <SwiperItem key={idx}>
              <Image
                src={url}
                className={styles['header-img']}
                mode='aspectFill'
              ></Image>
            </SwiperItem>
          ))}
        </Swiper>
        <View className={styles['swiper-idx']}>
          <Text className={styles['swiper-idx-txt']}>
            {cux}/{banners.length}
          </Text>
        </View>
      </View>
      <View className={styles['main']}>
        <View className={styles['progress']}>
          <Text className={styles['t-Text']}>
            受捐学校：安化县羊角塘镇金鸡完小
          </Text>
          <View className={styles['progress-main']}>
            <View
              className={styles['progress-bar']}
              style={{ width: progressPercent + '%' }}
            ></View>
          </View>
          <View className={styles['progress-tips']}>
            <Text className={styles['t-p']}>
              已有
              <Text className={styles['t-s']}>{progressInfo.joinNumber}</Text>
              人参与
            </Text>
            <Text className={styles['progress-txt']}>
              完成进度{progressPercent}%
            </Text>
          </View>
        </View>
      </View>
      <View className={styles['countdown-main']}>
        <View className={styles['count-title']}>
          <Text className={styles['count-title-txt']}>距离本周捐赠截止：</Text>
          <AtCountdown
            className='custom-time-main'
            isShowDay
            day={timeInfo.day}
            hours={timeInfo.hours}
            minutes={timeInfo.minutes}
            seconds={timeInfo.seconds}
            format={{ day: '天', hours: '时', minutes: '分' }}
          />
        </View>
        <View className={styles['main-b']}>
          <View className={styles['main-l']}>
            <Image
              src={CredentialImg}
              mode='aspectFill'
              className={styles['main-l-img']}
            ></Image>
            <Text className={styles['main-l-span']}>查看证书</Text>
          </View>
          <View className={styles['main-r']}>
            <Text className={styles['main-r-h3']}>参与2次颁发官方公益证书</Text>
            <Text className={styles['main-r-span']}>您暂未参与活动</Text>
            <Text
              className={styles['main-r-btn']}
              onClick={() => setModalTwo(true)}
            >
              分享爱心 免费捐画材
            </Text>
          </View>
        </View>
      </View>
      <View className={styles['benefit-wrap']}>
        <View className={styles['benefit-info']}>
          <Text className={styles['benefit-info-h3']}>项目介绍</Text>
          <View className={styles['info-item']}>
            <Text className={styles['info-item-h4']}>
              <Text className={styles['info-item-h4-span']}>
                助力乡村儿童实现艺术梦想
              </Text>
            </Text>
            <View className={styles['info-item-des']}>
              <Text className={styles['info-item-des-span']}>小熊美术</Text>与
              <Text className={styles['info-item-des-span']}>
                麦田教育基金会-彩虹口袋
              </Text>
              项目合作，捐助“彩虹口袋”美育画材，目的是培养乡村孩子发现美、欣赏美、创造美、表现美的能力，不断提高孩子们的艺术素养，为孩子们的艺术梦想搭起一座彩虹桥。
              <Image
                src={Des1}
                mode='widthFix'
                className={styles['info-item-des-img']}
              ></Image>
            </View>
          </View>
          <View className={styles['info-item']}>
            <Text className={styles['info-item-h4']}>
              <Text className={styles['info-item-h4-span']}>
                海报传递爱心 捐赠有你参与
              </Text>
            </Text>
            <View className={styles['info-item-des']}>
              <Text className={styles['info-item-des-span']}>参与活动宣传</Text>
              分享海报，让更多的人关注乡村青少年儿童的美育培养。
              <Text className={styles['info-item-des-span']}>
                你的爱心分享将转化为包含13件画材的“彩虹口袋”画材包
              </Text>
              ，由小熊美术提供善款，交由麦田教育基金会-彩虹口袋项目执行，捐赠给需要帮助的乡村地区儿童，并为他们提供
              <Text className={styles['info-item-des-span']}>
                1节美育素质课
              </Text>
              。孩子们将通过线下面对面绘画教学的方式，启发创造力。
              <Image
                src={Des2}
                mode='widthFix'
                className={styles['info-item-des-img']}
              ></Image>
            </View>
          </View>
          <View className={styles['info-item']}>
            <Text className={styles['info-item-h4']}>
              <Text className={styles['info-item-h4-span']}>
                期待你参与本周“彩虹口袋”公益活动
              </Text>
            </Text>
            <View className={styles['info-item-des']}>
              参与本周公益活动，即可获得麦田基金会
              <Text className={styles['info-item-des-span']}>公益证书</Text>。
              你的宣传与分享，可助力乡村学校开展美育素质课，帮助乡村学校的小画家们发现和培养个人爱好，成长为充满自信和创造力的新一代青少年。
              <Image
                src={Des3}
                mode='widthFix'
                className={styles['info-item-des-img']}
              ></Image>
            </View>
          </View>
        </View>
        <View className={styles['company-info']}>
          <Text className={styles['benefit-info-h3']}>机构执行情况</Text>
          <View className={styles['company-main']}>
            <View className={styles['company-main-li']}>
              <Text className={styles['key-title']}>立项时间：</Text>
              <Text className={styles['key-des']}>2012年</Text>
            </View>
            <View className={styles['company-main-li']}>
              <Text className={styles['key-title']}>收款机构：</Text>
              <Text className={styles['key-des']}>广东省麦田教育基金会</Text>
            </View>
            <View className={styles['company-main-li']}>
              <Text className={styles['key-title']}>执行机构：</Text>
              <Text className={styles['key-des']}>广东省麦田教育基金会</Text>
            </View>
            <View className={styles['company-main-li']}>
              <Text className={styles['key-title']}>
                负责<Text style={{ marginLeft: '16px' }}>人</Text>：
              </Text>
              <Text className={styles['key-des']}>
                莫凡，麦田计划发起人，法人及理事长，艺术与设计工作者。
              </Text>
            </View>
            <View className={styles['company-main-li']}>
              <Text className={styles['key-title']}>执行效果：</Text>
              <Text className={styles['key-des']}>
                截止2021年12月，本公益项目累计受益学校
                <Text className={styles['company-main-span']}>2000多</Text>
                所，受益学生
                <Text className={styles['company-main-span']}>72669</Text>
                人，覆盖<Text className={styles['company-main-span']}>21</Text>
                个省，
                <Text>104</Text>座城市。
              </Text>
            </View>
          </View>
        </View>
        <View className={styles['footer-logo']}>
          <Image
            src={BtmLogo}
            className={styles['footer-logo-img']}
            mode='scaleToFill'
          />
        </View>
      </View>
      <View className={styles['footer']}>
        <Text className={styles['btn-base']} onClick={() => setModalTwo(true)}>
          分享爱心 免费捐画材
        </Text>
      </View>
      {/* 弹窗 */}
      <AtModal
        isOpened={modalOne}
        className='custom-modal'
        onClose={() => setModalOne(false)}
      >
        <AtModalContent>
          <Image
            src={Modal1Img}
            className={styles['modal1-img']}
            mode='widthFix'
          ></Image>
          <View style={{ textAlign: 'center' }}>
            <Image
              onClick={() => linkToHandle()}
              src={Modal1BtnImg}
              mode='widthFix'
              className={styles['modal1-btn']}
            ></Image>
          </View>
        </AtModalContent>
      </AtModal>

      <AtModal
        isOpened={modalTwo}
        className='custom-modal'
        onClose={() => setModalTwo(false)}
      >
        <Image
          src={Modal2Img}
          className={styles['modal2-img']}
          mode='widthFix'
        ></Image>
        <View className={styles['modal2-footer']}>
          <Image
            onClick={() => linkToHandle()}
            src={Modal2BtnImg}
            mode='widthFix'
            className={styles['modal2-btn']}
          ></Image>
          <Text className={styles['modal2-footer-txt']}>
            已有<Text className={styles['footer-txt-white']}>7261</Text>
            人领取成功
          </Text>
        </View>
      </AtModal>
    </View>
  );
};
export default benefitActivity;
