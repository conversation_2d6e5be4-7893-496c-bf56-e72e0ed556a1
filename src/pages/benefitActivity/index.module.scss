.benefit-main {
  padding-bottom: 152px;
  background-color: #f5f4f9;

  .header-main {
    position: relative;

    .swiper-idx {
      position: absolute;
      right: 26px;
      bottom: 40px;

      .swiper-idx-txt {
        color: #fff;
        font-size: 28px;
      }
    }
  }

  .header {
    height: 560px;

    .header-img {
      width: 100%;
      height: 100%;
    }
  }

  .main {
    padding: 0 2.5%;
    background-color: #f5f4f9;

    .progress {
      box-sizing: border-box;
      height: 218px;
      background: #ffffff;
      box-shadow: 0px 5px 5px 0px #efedf7;
      border-radius: 16px;
      transform: translateY(-20px);
      padding: 32px;

      .t-h4 {
        margin: 0;
        font-size: 36px;
        color: #333;
      }

      .progress-main {
        height: 16px;
        background: #feded1;
        border-radius: 8px;
        margin-top: 22px;

        .progress-bar {
          height: 100%;
          width: 30%;
          background: linear-gradient(90deg, #ff5520 0%, #ff9539 100%);
          border-radius: 8px;
        }
      }

      .progress-tips {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;

        .t-p {
          font-size: 26px;
          color: #666;
        }

        .t-s {
          color: #f44c33;
        }

        .progress-txt {
          color: #666;
          font-size: 22px;
        }
      }
    }
  }

  .countdown-main {
    margin: 0 1.5%;
    height: 444px;
    background: url('../../assets/benefitActivity/card.png') no-repeat center
      center;
    background-size: 100% 100%;

    .count-title {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      height: 100px;
      padding-right: 40px;

      .count-title-txt {
        color: #666666;
        font-size: 24px;
      }
    }

    .main-b {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 34px;

      .main-l {
        position: relative;
        width: 168px;
        height: 226px;

        .main-l-img {
          width: 100%;
          height: 100%;
        }

        .main-l-span {
          position: absolute;
          display: block;
          width: 132px;
          height: 48px;
          line-height: 48px;
          background: linear-gradient(144deg, #fff6c4 0%, #ffce2b 100%);
          box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.24);
          border-radius: 24px;
          border: 1px solid #f7ff5b;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          font-size: 24px;
          color: #7b2600;
          text-align: center;
        }
      }

      .main-r {
        margin-left: 36px;

        .main-r-h3 {
          font-size: 36px;
          color: #333333;
          display: block;
        }

        .main-r-span {
          font-size: 28px;
          color: #666666;
        }

        .main-r-btn {
          display: block;
          width: 420px;
          height: 84px;
          background: linear-gradient(180deg, #ff6b55 0%, #f04329 100%);
          box-shadow: 0px 8px 19px 0px rgba(255, 137, 137, 0.5);
          border-radius: 44px;
          color: #ffffff;
          font-size: 34px;
          line-height: 84px;
          text-align: center;
          margin-top: 38px;
        }
      }
    }
  }

  .benefit-info,
  .company-info {
    margin: 0 2.5%;
    background: #ffffff;
    box-shadow: 0px 2px 9px 0px #efedf7;
    border-radius: 16px;
    padding: 30px;
    margin-top: 20px;

    .benefit-info-h3 {
      font-size: 36px;
      margin: 0;
    }

    .info-item {
      text-align: center;
      margin-top: 40px;

      .info-item-h4 {
        font-size: 34px;
        text-align: center;
        position: relative;
        display: inline-block;

        .info-item-h4-span {
          position: relative;
          z-index: 10;
        }

        &:after {
          content: '';
          display: block;
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
          height: 12px;
          background-color: #ffece4;
          z-index: 9;
        }
      }

      .info-item-des {
        font-size: 28px;
        color: #666666;
        line-height: 50px;
        text-align: left;
        margin-top: 20px;

        .info-item-des-img {
          display: block;
          margin: 20px auto 0;
          width: auto;
          max-width: 100%;
        }

        .info-item-des-span {
          color: #ff4f24;
          font-weight: bold;
        }
      }
    }

    .company-main {
      margin-top: 30px;

      .company-main-li {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20px;

        .company-main-p {
          font-size: 28px;
          margin: 0;
        }

        .company-main-span {
          color: #ff4f24;
        }

        .key-des {
          color: #666;
          margin: 0;
          flex: 1;
        }

        .key-title {
          color: #9b9b9b;
          width: 26%;
        }
      }
    }
  }

  .footer-logo {
    text-align: center;
    padding: 35px 0;

    .footer-logo-img {
      width: 445px;
      height: 65px;
    }
  }

  .footer {
    position: fixed;
    left: 0;
    right: 0;
    height: 152px;
    bottom: 0;
    background: #ffffff;
    box-shadow: 0px -2px 9px 0px rgba(205, 209, 213, 0.55);
    z-index: 11;

    .btn-base {
      width: 534px;
      height: 88px;
      border-radius: 44px;
      line-height: 88px;
      color: #fff;
      font-size: 34px;
      text-align: center;
      position: absolute;
      left: 50%;
      top: 32px;
      transform: translateX(-50%);
      background: linear-gradient(180deg, #ff6b55 0%, #f04329 100%);
      box-shadow: 0px 5px 10px 0px rgba(255, 137, 137, 0.5);
    }
  }

  .modal1-img {
    width: 100%;
  }

  .modal2-img {
    width: 95%;
  }

  .modal1-btn {
    width: 386px;
    margin: -44px auto 0;
  }

  .modal2-footer {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: -170px;

    .modal2-footer-txt {
      font-size: 24px;
      color: #7c0000;
      margin-top: 15px;

      .footer-txt-white {
        color: #fff;
      }
    }

    .modal2-btn {
      width: 386px;
    }
  }
}
