.newCalligraphyExperience1 {
  width: 100%;
  height: 100%;
  font-size: 0;
  padding-bottom: 130px;
  background-color: #fefae7;
  view {
    font-size: 0;
  }
  .banner {
    width: 100%;
  }
  .bannerTop {
    width: 100%;
    margin-top: -40px;
  }
  .none {
    width: 100%;
    height: 120px;
  }
  .modal {
    padding-bottom: 0;
  }
  .bottomPay {
    width: 100%;
    box-shadow: 0px 0px 13px 0px rgba(136, 136, 136, 0.2);
    position: fixed;
    bottom: 0;
    z-index: 2;
    padding: 0;
  }
  .bottomPayNew {
    width: 100%;
    height: 155px;
    box-shadow: 0px 0px 13px 0px rgba(136, 136, 136, 0.2);
    position: fixed;
    bottom: 0;
    z-index: 2;

    .payButton {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    image {
      display: block;
      width: 584x;
      height: 82px;
      transform: scale(0.8);
    }
  }
  .btn-pay {
    position: fixed;
    width: 100%;
    bottom: 0;
  }
}
