import { useShareAppMessage, useRouter } from '@tarojs/taro';
import { AtFloatLayout } from 'taro-ui';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import part3Btn from '@/assets/zeroArtBynow/part3-btn.jpg';
import partbbt from '@/assets/zeroArtBynow/share-bbt.png';
import { View, Image } from '@tarojs/components';
import WxLogin from '@/components/wxlogin';
import { UserStateType } from '@/store/groupbuy/state';
import sensors from '@/utils/sensors_data';
import LayoutLevel from '../components/level/index';
import Modal from '../components/payModal';
// import PushModal from '../components/pushModal';
import './index.scss';

const artRequireContext = require.context(
  '@/assets/zeroArtBynow/',
  true,
  /^\.\/.*part5-img-\d|dd\.jpg$/,
);

const imgList = artRequireContext.keys().map(artRequireContext);

export default function CalligraphyExperience() {
  const params = useRouter().params;
  const dispatch = useDispatch();
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  // 显示隐藏浮窗
  const [isOpened, setIsOpened] = useState<boolean>(false);
  useEffect(() => {
    params.channelId &&
      dispatch({
        type: 'CHANGE_CHANNELID',
        channelId: params.channelId,
      });
    params.sendId &&
      dispatch({
        type: 'CHANGE_SENDID',
        sendId: params.sendId,
      });
  }, [params, userId]);

  useEffect(() => {
    sensors.track('xxms_testcourse_homepage_view', {
      userId: userId,
      page_source: '0元5节',
      entrance_page: params.entrance_page || '',
      channelId: params.channelId || '',
    });
  }, []);

  useShareAppMessage(() => {
    return {
      title: '新人免费领，5节趣味绘画课，24小时内有效',
      path: `/pages/zeroArtBynow/index/index?channelId=16675&sendId=${userId}`,
      imageUrl: partbbt,
    };
  });
  return (
    <View className='newCalligraphyExperience1'>
      {imgList.map((e: any, i) => {
        return (
          <View key={i}>
            <Image src={e} className='banner' mode='widthFix'></Image>
          </View>
        );
      })}
      <View className='none'></View>

      {/* <Modal
        price='0'
        packagesId={process.env.NODE_ENV === 'online' ? '1798' : '7719'}
        Img={part3Btn}
      ></Modal> */}
      <Image
        src={part3Btn}
        className='btn-pay'
        mode='widthFix'
        onClick={() => setIsOpened(true)}
      ></Image>
      <WxLogin subject='ART_APP' isIntroduce={false} />
      <AtFloatLayout
        className={`'custom-float-layout'`}
        isOpened={isOpened}
        onClose={() => {
          setIsOpened(false);
        }}
        title='选择级别（年龄）'
      >
        <LayoutLevel pType='art' regtype='EXPERIENCE'></LayoutLevel>
      </AtFloatLayout>
    </View>
  );
}
