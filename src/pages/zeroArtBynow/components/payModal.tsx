import Taro from '@tarojs/taro';
import { AtModal } from 'taro-ui';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import sensors from '@/utils/sensors_data';
import { View, Text, Image } from '@tarojs/components';
import { useThirtySixPay } from '@/hooks/payhook/useThirtySixPay';
import { getSupManagements } from '@/api/groupbuy';
import Paybtn from '../../thirtySix/components/payBtn/index.weapp';
import './payModal.scss';

var change = false,
  _period;

export default function Modal(props: any) {
  const { inEnterpriseWeChat = false, afterModalHandler = null } = props;
  const [payPageData, setPayPageData] = useState<object>({
    sup: '',
    courseday: '0',
    period: 0,
    subject: 'ART_APP',
    addTeacher: props.addTeacher == false ? false : true,
  });
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  //channelId
  const channelId =
    useSelector((state: UserStateType) => state.channelId) || '';

  const payMessage = useThirtySixPay({
    topicId: '3',
    packagesId: props.packagesId,
    isIntroduce: false,
    ispictureBook: true,
    isZero: true,
    subject: 'ART_APP',
    payPageData,
    inEnterpriseWeChat,
    showAfterModal: () => showAfterModal(),
  });
  const authSuccess = res => {
    res.sup = props['sup'];
    res._period = _period;
    sensors.track('xxms_testcourse_home_buybuttonclick', {});
    sensors.track('xxms_testcourse_registrationpaylayer_payButtonClick', {});
    payMessage.authSuccess(res);
  };

  const showAfterModal = () => {
    if (afterModalHandler != null) afterModalHandler();
  };

  useEffect(() => {
    let _payPageData = JSON.parse(JSON.stringify(payPageData));
    if (change || !props.sup) return;
    change = true;
    if (props.sup != _payPageData.sup) {
      _payPageData.sup = props.sup;
      setPayPageData(_payPageData);
    }
  }, [props.sup]);

  useEffect(() => {
    if (payPageData['sup'])
      getSupManagements({
        type: 'ZERO_TESTCOURSE',
        sup: payPageData && payPageData['sup'],
        subject: 'ART_APP',
      }).then(res => {
        const { payload, status } = res;
        if (status == 'OK') {
          let _payPageData = JSON.parse(JSON.stringify(payPageData));
          _payPageData.period = payload.period;
          _period = payload.period;
          setPayPageData(_payPageData);
        }
        change = false;
      });
  }, [payPageData['sup']]);

  const authError = () => {
    sensors.track('xxms_testcourse_home_buybuttonclick', {});
    Taro.showToast({
      title: `授权失败`,
      icon: 'none',
    });
  };

  return (
    <View className='modal'>
      <View className='modaPay1'>
        <Paybtn
          className='modaPayButton new'
          authError={authError}
          authSuccess={authSuccess}
        ></Paybtn>
      </View>
    </View>
  );
}
