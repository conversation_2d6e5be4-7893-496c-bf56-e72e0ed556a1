.modal {
  padding-bottom: 20px;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  .layout-header {
    background: #fff;
  }
  .layout-header__title {
    text-align: center;
    padding: 0;
    font-size: 36px;
    font-weight: 500;
    color: #333333;
  }
  .at-float-layout .layout-body {
    padding: 0;
  }
  .at-float-layout .layout-body__content {
    padding-bottom: 10px;
    overflow: hidden;
  }
  .modalCenter {
    width: 670px;
    margin: 0 auto;
  }
  .modalTitle {
    padding: 20px 0 35px 0;
    font-size: 26px;
    color: #666666;
    text-align: center;
    .modalSubTitle {
      color: #fd2205;
    }
  }
  .modalLevel {
    width: 670px;
    height: 157px;
    border: 2px solid #f6f6f6;
    box-shadow: 0px 6px 15px 1px rgba(39, 39, 39, 0.13);
    border-radius: 30px;
    display: flex;
    align-items: center;
  }

  .hasSelected {
    .modalLevelSelect,
    .modalLevel,
    .modalSeniorSelect,
    .modalLeveSenior {
      border: 1px solid #eaeaea;
      box-shadow: 0px 0px 13px 0px rgba(177, 177, 177, 0.26);
      position: relative;
    }
    .selected-mark {
      width: 109px;
      height: 92px;
      position: absolute;
      top: -2px;
      right: 0;
      background-image: url('../../../assets/thirtySix/chooseLevel/hasbuy.png');
      background-repeat: no-repeat;
      background-position: left top;
      background-size: contain;
    }
    .modalLevelTitle,
    .modalLeveSeniorTitle {
      background: #eaeaea;
      color: #ffffff;
    }
    .modalLevelFit,
    .modalLeveSeniorFit {
      color: #eaeaea;
    }
  }
  .modalLevelSelect {
    width: 670px;
    height: 157px;
    border: 2px solid #ffa82d;
    box-shadow: 0px 6px 15px 1px rgba(39, 39, 39, 0.13);
    border-radius: 30px;
    display: flex;
    align-items: center;
    position: relative;
  }
  .ModalSelect {
    width: 55px;
    height: 41px;
    border: 2px solid #ffa82d;
    position: absolute;
    top: 0;
    right: 0;
    background: #ffa82d;
    border-radius: 0 20px 0px 20px;
  }
  .ModalSelect::after {
    content: '';
    position: absolute;
    right: 12px;
    top: 1px;
    width: 25px;
    height: 12px;
    border: 6px solid #ffffff;
    border-radius: 2px;
    border-top: none;
    border-right: none;
    background: transparent;
    transform: rotate(-45deg);
  }

  .modalLevelTitle {
    width: 146px;
    height: 70px;
    line-height: 70px;
    font-size: 40px;
    text-align: center;
    color: #ffffff;
    border-radius: 0 50px 50px 0;
  }
  .modalLevelFit {
    font-size: 40px;
    font-weight: 500;
    color: #333333;
    padding-left: 31px;
  }

  .modalLeveSenior {
    width: 670px;
    height: 157px;
    border: 2px solid #f6f6f6;
    box-shadow: 0px 6px 15px 1px rgba(39, 39, 39, 0.13);
    border-radius: 30px;
    display: flex;
    align-items: center;
    margin-top: 32px;
  }
  .modalSeniorSelect {
    width: 670px;
    height: 157px;
    border: 2px solid #ffa82d;
    box-shadow: 0px 6px 15px 1px rgba(39, 39, 39, 0.13);
    border-radius: 30px;
    display: flex;
    align-items: center;
    margin-top: 32px;
    position: relative;
  }
  .modalLeveSeniorTitle {
    width: 183px;
    height: 85px;
    background: linear-gradient(0deg, #93e749, #7dc83c);
    font-size: 40px;
    font-weight: 500;
    color: #ffffff;
    display: flex;
    align-items: center;
    border-radius: 0 50px 50px 0;
  }
  .modalLeveSeniorFit {
    font-size: 40px;
    font-weight: 500;
    color: #333333;
    padding-left: 31px;
  }

  .modaPay1 {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .modaPayButton {
      width: 100%;
      height: 100%;
      background: linear-gradient(to right, #ffa700, #fa6c3a);
      border-radius: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 36px;
      color: #ffffff;
      padding: 0 !important;
      margin: 0 !important;
      position: relative;
      &.new {
        width: 100%;
        background: transparent;
        overflow: visible;
        &::after {
          border: none;
        }
      }
      image {
        width: 100%;
        // height: 132px;
      }
    }
  }
}
.at-float-layout__container {
  max-height: 1050px;
  .layout-body {
    max-height: 1050px;
    .layout-body__content {
      max-height: 1050px;
    }
  }
}
.giveMask {
  .at-modal__container {
    top: 45%;
    width: 600px;
    height: 800px;
    overflow: visible;
    background: transparent;
    .view {
      width: 600px;
      height: 800px;
      position: relative;
      .afterModal {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
      }
      .iknow {
        position: absolute;
        width: 400px;
        height: 96px;
        bottom: 32px;
        left: 100px;
      }
      .close {
        position: absolute;
        bottom: -96px;
        width: 64px;
        height: 64px;
        left: 50%;
        margin-left: -32px;
      }
    }
  }
}
