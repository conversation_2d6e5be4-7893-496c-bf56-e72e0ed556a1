import { useState, useEffect } from 'react';
// import { AtCountdown, AtFloatLayout } from "taro-ui";
import { View, Text } from '@tarojs/components';
import Modal from '../payModal';
import './index.scss';

export default function Index(props) {
  const {
    inEnterpriseWeChat = false,
    addTeacher = true,
    afterModalHandler = null,
  } = props;
  // 选择激活的卡片
  const [tabActive, setTabActive] = useState<number>(-1);

  const [level, setLevel] = useState<any>([]);
  const [title, setTitle] = useState<string>('');
  useEffect(() => {
    newLevel();
  }, []);
  const newLevel = () => {
    let newArray = [
      {
        bgcolor: '#ff9c00',
        label: 'S1',
        fit: `刚拿画笔、初步认识颜色（3-4.5岁）`,
        range: '学习重点：点线涂鸦 | 兴趣探究 | 趣味手工',
        desc: 'S1级别是孩子成长的美术涂鸦阶段，学习重点是感受与触动',
        courseday: '0',
        period: 0,
      },
      {
        bgcolor: '#91dc4b',
        label: 'S2',
        fit: `零基础或少量绘画经验（4.5-7岁）`,
        range: '学习重点：创意绘画 | 百科知识 | 思维发散',
        desc: 'S2级别是孩子美术学习的的启蒙阶段，学习重点是认知与联想',
        courseday: '0',
        period: 0,
      },
      {
        bgcolor: '#30baea',
        label: 'S3',
        fit: '适合专项技能提升（推荐7岁以上）',
        range: '学习重点：色彩搭配 | 造型练习 | 线条装饰',
        desc: 'S3级别是孩子美术学习的的中级阶段，学习重点是创意与表达',
        courseday: '0',
        period: 0,
      },
    ];
    setLevel(newArray);
    setTitle('为保证孩子的学习体验，请正确选择对应的级别');
  };

  return (
    <View className='levels'>
      <View className='level-tip'>*{title}</View>
      {level.map((item, index) => (
        <View
          className={`card ${tabActive === index ? 'active' : ''}`}
          key={`card-${index}`}
          onClick={() => {
            setTabActive(index);
          }}
        >
          <View className='label' style={`background: ${item.bgcolor}`}>
            {item.label}
          </View>
          <View className='info'>
            <View className='title'>
              <Text className='fit'>{item.fit}</Text>
            </View>
            <View className='desc' style={`color: ${item.bgcolor}`}>
              {item.range}
            </View>
          </View>
          <Modal
            price='0'
            addTeacher={addTeacher}
            packagesId={process.env.NODE_ENV === 'online' ? '1798' : '7719'}
            sup={tabActive > -1 ? level[tabActive].label : ''}
            inEnterpriseWeChat={inEnterpriseWeChat}
            afterModalHandler={afterModalHandler}
          />
        </View>
      ))}
    </View>
  );
}
