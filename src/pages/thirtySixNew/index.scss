@import '@/theme/groupbuy/common.scss';

.index {
  font-size: 0;
  position: relative;
  padding-bottom: calc(112px + env(safe-area-inset-bottom));

  .fixedTop {
    position: sticky;
  }

  .divider {
    width: 100%;
    height: 16px;
    background: #f7f7f7;
  }

  .commodity-price {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 147px;

    .unit-price {
      display: flex;
      align-items: center;
      width: 480px;
      height: 100%;
      box-sizing: border-box;
      padding: 0 32px;
      background: $red-to-orange;

      .price-symbol {
        color: $white;
        font-size: $font-40;
        margin-top: 40px;
      }

      .price {
        color: $white;
        font-size: $font-96;
      }

      .price-details {
        margin-top: 6px;
        margin-left: 20px;

        .price-detailed {
          margin-top: 4px;
          color: $white;
          font-size: $font-24;
        }
      }
    }

    .assemble-price {
      display: flex;
      align-items: flex-start;
      justify-content: center;
      flex-direction: column;
      width: calc(100% - 480px);
      height: 100%;
      background: $orange-to-yellow;
      position: relative;
      padding-left: 70px;

      &::before {
        content: '';
        display: block;
        position: absolute;
        top: 0;
        bottom: 0;
        left: -5%;
        width: 0;
        height: 0;
        margin: auto 0;
        border-top: 20px solid transparent;
        border-right: 20px solid #fd880d;
        border-bottom: 20px solid transparent;
      }

      .groupbuy-reduction {
        font-size: $font-36;
        color: $white;
      }

      .reduce-price {
        font-size: $font-36;
        color: #fcd850;
        // text-decoration: line-through;
      }
    }
  }

  .introduce {
    display: flex;
    justify-content: space-between;
    padding: 32px 24px;

    .product-introduction {
      width: calc(100% - 100px);
      font-size: $font-28;
      color: $dim-gray;
      text-align: justify;
      font-weight: bold;
    }
  }

  .promise-introduce {
    width: 100%;
    padding: 24px 32px;
    box-sizing: border-box;

    .row {
      display: flex;
      width: 100%;
      justify-content: space-between;
      padding: 10px 0;
      font-size: $font-26;

      .title {
        color: $light-grey;
      }

      .content {
        flex: 1;
        color: $dim-gray;
        padding-left: 24px;

        .promotion-row {
          margin-bottom: 20px;
        }

        .promotion-text {
          margin-left: 16px;
          font-size: $font-26;
          color: $dim-gray;
        }
      }

      .view {
        display: flex;
        align-items: center;
        color: $light-grey;

        .arrow {
          width: 12px;
          height: 22px;
          margin-left: 16px;
        }
      }
    }
  }

  .groupbuy-detail {
    background: $white;
    padding: 14px 32px;

    .title {
      font-size: $font-28;
      color: $dim-gray;
      font-weight: bold;
      margin-bottom: 26px;
    }

    .content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .swiper-head {
        width: 330px;
        height: 60px;

        .head {
          display: flex;
          align-items: center;

          .head-box {
            position: relative;
            width: 60px;
            height: 60px;
            border-radius: 30px;
            overflow: hidden;
            background: $light-grey;

            .head-img {
              max-width: 100%;
              max-height: 100%;
              position: absolute;
              left: 0;
              right: 0;
              bottom: 0;
              top: 0;
              margin: auto;
            }
          }

          .nickname {
            width: 254px;
            font-size: $font-28;
            color: $dim-gray;
            margin-left: 16px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }

      .clustering-info {
        display: flex;

        .clustering-info-box {
          display: flex;
          flex-direction: column;

          .number {
            text-align: right;
            font-size: $font-24;
            color: $dim-gray;

            .highlight {
              color: $red;
            }
          }

          .countdown {
            color: $light-grey;
            font-size: $font-24;

            .at-countdown {
              width: 110px;
              text-align: right;
            }

            .at-countdown__time-box {
              min-width: 0;
              font-family: '黑体';
              color: $light-grey;
              font-size: $font-26;
            }

            .at-countdown__separator {
              font-size: $font-26;
              padding: 0;
            }

            .at-countdown__item,
            .at-countdown__time {
              color: $light-grey;
            }
          }
        }

        .groupbuy-btn {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 144px;
          height: 60px;
          background: $red-to-yellow;
          font-size: $font-28;
          color: $white;
          border-radius: 10px;
          margin-left: 20px;

          .arrow {
            width: 12px;
            height: 21px;
            margin-left: 6px;
          }
        }
      }
    }
  }

  .groupbuy-procedure {
    .title {
      padding: 16px 0 0 32px;
      color: #252525;
      font-size: $font-32;
      font-weight: bold;
    }

    .img {
      width: 100%;
    }
  }

  .qa {
    padding: 0 32px 20px 32px;

    .all {
      display: flex;
      justify-content: space-between;
      padding: 24px 0;

      .number {
        font-size: $font-26;
        color: $light-grey;
        font-weight: bold;
      }

      .view {
        display: flex;
        align-items: center;
        color: $light-grey;
        font-size: $font-26;

        .arrow {
          width: 12px;
          height: 22px;
          margin-left: 16px;
        }
      }
    }

    .problem-row {
      display: flex;
      justify-content: space-between;
      padding-bottom: 20px;

      .problem-icon {
        width: 35px;
        height: 35px;
      }

      .problem {
        flex: 1;
        font-size: $font-24;
        font-weight: bold;
        color: $dim-gray;
        margin-left: 24px;
      }

      .answer {
        font-size: $font-24;
        color: $light-grey;
      }
    }
  }

  .big-img {
    width: 100%;
  }

  @keyframes zoomIn {
    0% {
      transform: scale(0.9);
    }
    50% {
      transform: scale(1);
    }
    100% {
      transform: scale(0.9);
    }
  }

  .pay-img {
    width: 100%;
  }

  .suction-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 172px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    padding-bottom: env(safe-area-inset-bottom);

    .fix-bg {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      padding-bottom: env(safe-area-inset-bottom);
      background: $white;
    }

    .pay-fixed {
      position: absolute;
      width: 271px;
      height: 128px;
      right: 18px;
      top: 50px;
      animation: zoomIn 0.75s linear infinite;
    }

    .synopsis {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 310px;
      height: 112px;

      .course {
        display: flex;
        justify-content: center;
        align-items: center;

        .number {
          color: #ff6c00;
          font-size: $font-50;
          text-align: end;
          font-family: DINAlternate-Bold, DINAlternate;
        }

        .section {
          color: $dim-gray;
          font-size: $font-34;
          margin-left: 5px;
        }

        .icon {
          font-size: $font-22;
          color: $white;
          font-weight: bold;
          padding: 5px 8px;
          margin-left: 18px;
          border-radius: 0px 10px 10px 10px;
          background: $orange-to-yellow;
        }
      }

      .copywriting {
        color: $silver;
        font-size: $font-24;
      }
    }

    .origin-price {
      width: 180px;
      height: 100%;
      background: $orange;
    }

    .groupbuy-price {
      flex: 1;
      height: 100%;
      background: $red;
    }

    .origin-price,
    .groupbuy-price {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      color: $white;

      .price {
        font-size: $font-36;
        font-weight: bold;
      }

      .text {
        font-size: $font-26;
      }
    }
  }

  .layout-header {
    background: $white;
    padding: 40px 0;

    .layout-header__title {
      text-align: center;
      font-size: $font-30;
      padding-left: 80px;
    }
  }

  .problem-layout {
    .layout {
      height: 70%;
    }
  }

  .order-layout {
    .layout {
      max-height: 80vh;
      border-radius: 40px 40px 0 0;

      .layout-body {
        max-height: 80vh;
      }

      .layout-body__content {
        max-height: 80vh;
      }
    }
  }

  .redeem-modal {
    .at-modal__overlay {
      background-color: rgba(0, 0, 0, 0.7);
    }

    .at-modal__container {
      padding-top: 90px;
      box-sizing: border-box;
      width: 580px;
      border-radius: 30px;
      overflow: auto;
      background: transparent;

      .close-img {
        width: 60px;
        height: 60px;
        position: absolute;
        top: 0;
        right: 0;
      }

      .at-modal__content {
        padding: 0;
        overflow: auto;
        background: transparent;

        .redeem-img {
          width: 100%;
          height: 519px;
          vertical-align: top;
        }

        .footer {
          height: 168px;
          background: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;

          .btns {
            width: 500px;
            height: 88px;
            background: #ff9c00;
            border-radius: 44px;
            font-size: 36px;
            font-weight: bold;
            color: #ffffff;
            line-height: 88px;
            text-align: center;
          }
        }
      }
    }
  }
}
