import { Image, Swiper, SwiperItem, Text, View } from '@tarojs/components';
import { useState } from 'react';
import Banner1 from '@/assets/groupbuy/thirtySixNew/banner1.png';
import Banner2 from '@/assets/groupbuy/thirtySixNew/banner2.png';
import Banner3 from '@/assets/groupbuy/thirtySixNew/banner3.png';
import PriceInfoImg from '@/assets/groupbuy/thirtySixNew/price-info.png';
import sign from '@/assets/groupbuy/index/sign.png';

import './index.scss';

const imgList = [
  require('@/assets/groupbuy/thirtySixNew/section1.png'),
  require('@/assets/groupbuy/thirtySixNew/section2.png'),
  require('@/assets/groupbuy/thirtySixNew/section3.png'),
  require('@/assets/groupbuy/thirtySixNew/section4.png'),
  require('@/assets/groupbuy/thirtySixNew/section5.png'),
];

const banners = [Banner1, Banner2, Banner3];

const swipers = {
  1: [
    require('@/assets/groupbuy/thirtySixNew/section2-1.png'),
    require('@/assets/groupbuy/thirtySixNew/section2-2.png'),
  ],
};

const Section = () => {
  const [idx, setIdx] = useState<number>(0);

  return (
    <>
      <View className='header'>
        <View className='ingot'>
          <Text>{idx + 1}</Text>/{banners.length}
        </View>
        <Swiper
          className='swiper-banner'
          circular
          autoplay
          interval={3000}
          onChange={e => setIdx(e.detail.current)}
        >
          {banners.map((url, index) => {
            return (
              <SwiperItem key={index}>
                <View className='banner'>
                  <Image
                    mode='widthFix'
                    src={url}
                    className={`banner${index}`}
                  ></Image>
                </View>
              </SwiperItem>
            );
          })}
        </Swiper>
      </View>
      <View>
        <View>
          <Image mode='widthFix' src={PriceInfoImg} className='payText'></Image>
        </View>
        <View className='bannerContent'>
          <View className='bannerList'>
            {imgList.map((url: string, i) => {
              return (
                <View className='bannerDiv' key={i}>
                  <Image
                    mode='widthFix'
                    src={url}
                    className='bannerImg'
                  ></Image>
                  {swipers[i] && (
                    <Swiper
                      className={`inner-swiper${i}`}
                      autoplay
                      circular
                      indicatorDots
                      indicatorActiveColor='#FFB300'
                      indicatorColor='#F5F5F5'
                      interval={3000}
                    >
                      {swipers[i].map((url1, index) => {
                        return (
                          <SwiperItem key={index}>
                            <Image src={url1} mode='widthFix'></Image>
                          </SwiperItem>
                        );
                      })}
                    </Swiper>
                  )}
                </View>
              );
            })}
          </View>
        </View>
        <View>
          <Image mode='widthFix' src={sign} className='payText'></Image>
        </View>
      </View>
    </>
  );
};

export default Section;
