@import '../../../theme/groupbuy/common.scss';

.add-address {
  min-height: calc(100vh - 112px - env(safe-area-inset-bottom));
  padding-bottom: calc(112px + env(safe-area-inset-bottom));
  font-family: PingFangSC-Medium, PingFang SC;
  background: #f7f7f7;
  overflow-x: hidden;

  .address-area-float {
    .layout-header {
      background: $white;
      padding: 40px 0;

      .layout-header__title {
        font-size: $font-30;
        padding-left: 30px;
        font-weight: 400;
      }
    }

    .layout {
      height: 80%;
    }

    .at-float-layout__container {
      .layout-body {
        .layout-body__content {
          max-height: 840px;
        }
      }
    }
  }

  .textareaHide {
    display: none;
  }

  .area-box {
    padding-top: 60px;

    .area-tabs {
      width: 100%;
      position: fixed;
      top: 130px;
      padding-left: 15px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      border-bottom: 1px solid #f5f5f5;
      background-color: #fff;
      transform: translateZ(0);
      -webkit-overflow-scrolling: touch;

      .active {
        color: #ff9c00;
      }

      .area-tabs-item {
        margin: 10px 0 20px 30px;

        &:first-child {
          margin-left: 0;
        }
      }
    }

    .area-list {
      .choose-right {
        width: 25px;
        height: 18px;
        margin-right: 10px;
      }

      margin-left: 15px;

      .active {
        display: block;
      }

      .hide {
        display: none;
      }

      .area-list-item {
        margin-top: 40px;

        &:last-child {
          margin-bottom: 40px;
        }
      }
    }
  }

  // .head-box {
  //   width: 100%;
  //   height: 350px;
  //   margin-bottom: $font-32;
  //   text-align: center;
  //   background: $white;
  //   .right-img {
  //     width: 80px;
  //     height: 80px;
  //     padding-top: 44px;
  //   }
  //   .buy-success {
  //     height: 48px;
  //     padding-top: $font-28;
  //     margin-bottom: $font-32;
  //     color: $dim-gray;
  //     font-size: $font-34;
  //     font-weight: 500;
  //     line-height: 48px;
  //   }
  //   .tip {
  //     height: 33px;
  //     margin-top: 8px;
  //     color: $light-grey;
  //     font-size: 24px;
  //     font-weight: 400;
  //     line-height: 33px;
  //   }
  //   .color-red {
  //     color: #ff6a00;
  //   }
  // }
  .head-tips {
    height: 80px;
    background: #76de73;

    view {
      width: fit-content;
      font-size: 34px;
      font-weight: bold;
      color: #ffffff;
      line-height: 80px;
      margin: 0 auto;
      display: flex;
      align-items: center;

      &::before {
        display: block;
        content: '';
        width: 38px;
        height: 38px;
        background: url('data:image/png;base64,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')
          no-repeat;
        background-size: 100%;
        background-position: center;
        margin-right: 16px;
      }
    }
  }

  .header-v1 {
    text-align: center;
    padding-top: 38px;

    .form-title {
      width: fit-content;
      height: 56px;
      font-size: 42px;
      font-weight: bold;
      color: #333333;
      line-height: 60px;
      display: flex;
      align-items: center;
      margin: 0 auto;

      &::before {
        display: block;
        content: '';
        width: 45px;
        height: 20px;
        background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAoCAYAAAB+Qu3IAAAB9klEQVRoQ+3asUocURTG8f+xkBRBaxsfQCGFCPYhCKntNJ0aEC3UJ/AJ1hSKELRb7awDItYKmkLYPEAaa3ULsdgTZseRZYkw9453QPfbbuCcM8tvhnPvuYzR9/MNJnGWMGZxxoGP/TG6/q9AG+MvzgnGvm3T6o2y4sK3GOaOBs4KzpAwKwgYHYw9Rti0LR6zSl3oLvItv3A+Vyiv1H4B44xRvmbYOfQmO3RYlVQCgSF2rcGadXsyXKtdJEDOe0YH+GS+zjawnug2KpsL/Mje6BbOhEQSChh/sjf6Xlu4hMh56bagkxsX0God6amfWocWw/TU3cVQ27uU0MX2TgNLSmWgGFg0gieE7h/Bn7F1qPQ66i8dKvVW1zFptHW5Y9Lo8kosJfB8Hl0qWkHRAoKOpgtLFHSYV3S0oKPpwhIFHeYVHS3oaLqwREGHeUVHCzqaLixR0GFe0dGCjqYLSxR0mFd0dO3Q7m78PlzCWcRsEvf39W2fWRv3FsYBUwv7ZubZ06kV2i+Px7CHJu6D8emZ2Rn+4ZtNz93UBp2/yUenA4NcNJkMe2r+S33QV81lnJ/RTe4tJxrf64O+bJ4DM2/Zq8J/v6gP+urw/t0tfGXlzdqCLotVJa5WaLWOKo+qfK5rMSyPVSVS27sqeoG5GlgCwaqED+oI/g+Lvb+FLrcynwAAAABJRU5ErkJggg==')
          center/100% no-repeat;
        margin-right: 22px;
      }

      &::after {
        display: block;
        content: '';
        width: 45px;
        height: 20px;
        background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAoCAYAAAB+Qu3IAAACDklEQVRoQ+3asU8UQRTH8e+PglgI1Db+AZJYAInWxJhY28l1IgnRAvgL+AvAAmOi2B121ibGUGsCFibnH2BDrVxBKO6Z3XWRXDTZeetsFN51m7w3e/e5yZt5kxFjH1tnFmMZcRfjOnB1PCaefyswRHzFeIfY1TaD81GqH2yTSb6zhbGKMRGYLQTECPGcaTa0yWkxUgldIn/jLcZii+EjdVxA7DPDvQK7gt5ghxGPQyqDwATPtMUTlTUZPke5yIBc1YwRcFO2xjawluk1MWwl8LSY0QOMGyGSUUB8KWb0cWzhMiJXQw8DOrtxDR2lIz/1z9IRi2F+6nIxjO1dTuh6excNS05loG5YogXPCD3egp9hx6HS31H/06HS+dHjmNRt3eyY1D18JDYSODuPbhQdQW6BgHbTpSUGdJqXOzqg3XRpiQGd5uWODmg3XVpiQKd5uaMD2k2XlhjQaV7u6IB206UlBnSalzv615UwM/FpbxnjIdIsZhfrzp00xGyAeMXc0q4kc6s5EqubSgdvrqGTPmaX40qYtI9d6Wnh/pHDzJUiK2fy6/eXBrlmKrDnHtzpambLDvuPMF64/qb/PUmsaL73soufITvofwBudfGyf/AdH7XQu93F95Id7h1fuIWvqZw01PzSVNPwNnEB3Rl0lI6uSkcshm1KQtPc2N511LhEw9J0SraMixa8JWDT9B/yccap56KHfwAAAABJRU5ErkJggg==')
          center/100% no-repeat;
        margin-left: 22px;
      }
    }

    .form-desc {
      height: 56px;
      font-size: 38px;
      color: #333333;
      font-weight: bold;
      line-height: 60px;
      margin-bottom: 28px;
      text-align: center;

      &.ss {
        padding-left: 30px;
        text-align: left;
        padding-top: 20px;
      }
    }

    .img-title {
      width: 460px;
      height: 133px;
    }

    .info {
      background-color: #fff;
      margin-top: 20px;
      text-align: left;
      padding: 25px 30px;

      .info-detail {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 0 20px;
        position: relative;

        view {
          position: absolute;
          left: 10px;
          top: 20px;

          image {
            width: 76px;
            height: 76px;
          }
        }

        & > image {
          &:first-of-type {
            width: 75%;
            margin-right: 24px;
          }
        }
      }

      text {
        font-size: 36px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #333333;
      }
    }
  }

  .tips-bottom {
    padding: 0 110px 60px;
    box-sizing: border-box;

    .title {
      height: 46px;
      font-size: 32px;
      color: #727272;
      line-height: 46px;
      text-align: center;
      margin-bottom: 18px;
    }

    .content {
      font-size: 24px;
      color: #b4b4b4;
      line-height: 32px;
    }
  }

  .form-card {
    height: 842px;
    background: #ffffff;
    border-radius: 40px;
    margin: 40px 32px;
    padding: 53px 0 48px 20px;
    box-sizing: border-box;
    overflow: hidden;

    &.form-card-v1 {
      height: auto;
      padding: 30px 0 20px;
      margin: 20px 0 0;
      text-align: left;
      border-radius: 0;

      .form-title {
        width: 100%;
        display: block;
        padding-left: 28px;
        box-sizing: border-box;
        height: auto;

        &:after,
        &:before {
          display: none;
        }
      }
    }

    .form-title {
      width: fit-content;
      height: 56px;
      font-size: 40px;
      font-weight: bold;
      color: #333333;
      line-height: 56px;
      display: flex;
      align-items: center;
      margin: 0 auto;

      &::before {
        display: block;
        content: '';
        width: 45px;
        height: 20px;
        background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAoCAYAAAB+Qu3IAAAB9klEQVRoQ+3asUocURTG8f+xkBRBaxsfQCGFCPYhCKntNJ0aEC3UJ/AJ1hSKELRb7awDItYKmkLYPEAaa3ULsdgTZseRZYkw9453QPfbbuCcM8tvhnPvuYzR9/MNJnGWMGZxxoGP/TG6/q9AG+MvzgnGvm3T6o2y4sK3GOaOBs4KzpAwKwgYHYw9Rti0LR6zSl3oLvItv3A+Vyiv1H4B44xRvmbYOfQmO3RYlVQCgSF2rcGadXsyXKtdJEDOe0YH+GS+zjawnug2KpsL/Mje6BbOhEQSChh/sjf6Xlu4hMh56bagkxsX0God6amfWocWw/TU3cVQ27uU0MX2TgNLSmWgGFg0gieE7h/Bn7F1qPQ66i8dKvVW1zFptHW5Y9Lo8kosJfB8Hl0qWkHRAoKOpgtLFHSYV3S0oKPpwhIFHeYVHS3oaLqwREGHeUVHCzqaLixR0GFe0dGCjqYLSxR0mFd0dO3Q7m78PlzCWcRsEvf39W2fWRv3FsYBUwv7ZubZ06kV2i+Px7CHJu6D8emZ2Rn+4ZtNz93UBp2/yUenA4NcNJkMe2r+S33QV81lnJ/RTe4tJxrf64O+bJ4DM2/Zq8J/v6gP+urw/t0tfGXlzdqCLotVJa5WaLWOKo+qfK5rMSyPVSVS27sqeoG5GlgCwaqED+oI/g+Lvb+FLrcynwAAAABJRU5ErkJggg==')
          center/100% no-repeat;
        margin-right: 22px;
      }

      &::after {
        display: block;
        content: '';
        width: 45px;
        height: 20px;
        background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAoCAYAAAB+Qu3IAAACDklEQVRoQ+3asU8UQRTH8e+PglgI1Db+AZJYAInWxJhY28l1IgnRAvgL+AvAAmOi2B121ibGUGsCFibnH2BDrVxBKO6Z3XWRXDTZeetsFN51m7w3e/e5yZt5kxFjH1tnFmMZcRfjOnB1PCaefyswRHzFeIfY1TaD81GqH2yTSb6zhbGKMRGYLQTECPGcaTa0yWkxUgldIn/jLcZii+EjdVxA7DPDvQK7gt5ghxGPQyqDwATPtMUTlTUZPke5yIBc1YwRcFO2xjawluk1MWwl8LSY0QOMGyGSUUB8KWb0cWzhMiJXQw8DOrtxDR2lIz/1z9IRi2F+6nIxjO1dTuh6excNS05loG5YogXPCD3egp9hx6HS31H/06HS+dHjmNRt3eyY1D18JDYSODuPbhQdQW6BgHbTpSUGdJqXOzqg3XRpiQGd5uWODmg3XVpiQKd5uaMD2k2XlhjQaV7u6IB206UlBnSalzv615UwM/FpbxnjIdIsZhfrzp00xGyAeMXc0q4kc6s5EqubSgdvrqGTPmaX40qYtI9d6Wnh/pHDzJUiK2fy6/eXBrlmKrDnHtzpambLDvuPMF64/qb/PUmsaL73soufITvofwBudfGyf/AdH7XQu93F95Id7h1fuIWvqZw01PzSVNPwNnEB3Rl0lI6uSkcshm1KQtPc2N511LhEw9J0SraMixa8JWDT9B/yccap56KHfwAAAABJRU5ErkJggg==')
          center/100% no-repeat;
        margin-left: 22px;
      }
    }

    .form-desc {
      width: fit-content;
      height: 70px;
      line-height: 70px;
      text-align: center;
      margin: 24px auto 48px;
      background: #fff9f1;
      border-radius: 35px;
      font-size: 28px;
      color: #ff7000;
      padding: 0 28px 0 44px;
      box-sizing: border-box;
      color: #ff7000;
    }

    .birthday-css {
      border-top: 1px solid #e6eef5;

      &::after {
        border: none;
      }

      .at-input--disabled {
        opacity: 1;
      }
    }

    margin-bottom: 40px;

    .at-textarea {
      border: 0;
      margin-left: 14px;
      padding-top: 14px;
    }

    .arrow {
      width: 12px;
      height: 22px;
      padding: 0px;
      margin-right: 10px;
    }

    .mobile-text {
      color: $light-grey;
      font-size: $font-30;
      padding-right: 16px;
      padding-left: 0px;
    }

    .region {
      .at-input--disabled {
        opacity: 1;
      }
    }

    .addr-textarea {
      display: flex;
      padding-left: 16px;
      box-sizing: border-box;
      color: #333;

      .addr-left {
        margin: 26px 8px 0 16px;
        width: 172px;
        font-size: 32px;
        vertical-align: middle;
        text-align: left;
        color: #555;

        &::before {
          display: inline-block;
          margin-right: 8px;
          color: #ff4949;
          font-size: 28px;
          font-family: SimSun, sans-serif;
          line-height: 1;
          content: '*';
        }
      }

      .at-textarea {
        flex: 1;
        margin: 0;
        padding: 26px 20px 0 0;
        margin-left: -1px;
      }
    }

    .at-input__children {
      &::after {
        border: 0;
      }
    }

    .at-form::after {
      border: 0;
    }

    .at-input {
      height: 100px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
    }

    .at-input__title {
      color: #555;
      font-weight: normal;
    }

    .at-input__input {
      color: #333;
    }

    .at-textarea__textarea {
      color: #333;
      font-weight: normal !important;
    }
  }

  // .Tips {
  //   padding-right: 105px;
  //   padding-left: 105px;
  //   text-align: center;
  //   margin-bottom: 60px;
  //   .Tips-title {
  //     height: 45px;
  //     margin-bottom: 17px;
  //     color: $silver;
  //     font-size: 32px;
  //     font-weight: 400;
  //     line-height: 45px;
  //   }
  //   .Tips-text {
  //     height: 99px;
  //     color: $light-grey;
  //     font-size: 24px;
  //     font-weight: 400;
  //     line-height: 40px;
  //     text-align: justify;
  //   }
  // }
  // .foot-box {
  //   width: 100%;
  //   height: 112px;
  //   background: $white;
  //   box-shadow: 0px -3px 11px 0px rgba(0, 0, 0, 0.06);
  //   display: flex;
  //   justify-content: center;
  //   align-items: center;
  //   position: fixed;
  //   left: 0;
  //   bottom: 0;
  //   .foot-btn {
  //     width: 638px;
  //     height: 72px;
  //     background: $red-to-yellow;
  //     border-radius: 40px;
  //     text-align: center;
  //     color: $white;
  //     line-height: 72px;
  //   }
  // }
  .foot-box {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 144px;
    padding-bottom: calc(env(safe-area-inset-bottom) * 0.5);
    background-color: #fff;

    .foot-btn {
      border: 0;
      width: 100%;
      margin: 0 30px;
      height: 96px;
      line-height: 96px;
      text-align: center;
      background: linear-gradient(270deg, #ffa300 0%, #ff6a00 100%);
      border-radius: 52px;
      color: #fff;
      font-size: 36px;
      font-weight: bold;
    }
  }
}
