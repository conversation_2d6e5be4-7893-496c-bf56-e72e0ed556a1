import Taro, { useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import { Image, Text, View } from '@tarojs/components';
import DefaultAddress from '@/components/defaultAddress';
import sensors from '@/utils/sensors_data';
import { getQueryString } from '@/utils';
import { judgeMinienv } from '@/utils/auth';
import { AtButton, AtFloatLayout, AtForm, AtInput, AtTextarea } from 'taro-ui';
import { useSelector } from 'react-redux';
import {
  createAddressForExpressAndOrder,
  createUserAddress,
  getAddressTownList,
  getCenterAddressList,
  updateUserAddress,
} from '@/api/groupbuy';
import arrowRight from '@/assets/groupbuy/index/arrow-right.png';
import chooseRight from '@/assets/groupbuy/addAddress/chooseCity.png';
import GiftDesImg from '@/assets/groupbuy/gift-des-v2.png';
import GiftDesImgV1 from '@/assets/groupbuy/gift-des-v1.png';
import GiftV1 from '@/assets/groupbuy/index/gift-new-v2.png';
import selIcon4 from '@/assets/groupbuy/index/gift-new4.jpg';
import selIcon from '@/assets/ninePointNine/gift-9_9-v1.png';
import DesSel from '@/assets/ninePointNine/des-9.9.png';
/* import S1Img from '@/assets/groupbuy/s1-icon.png';
import S2Img from '@/assets/groupbuy/s2-icon.png';
import S3Img from '@/assets/groupbuy/s3-icon.png';
import S4Img from '@/assets/groupbuy/s4-icon.png'; */
import { UserStateType } from '@/store/groupbuy/state';
import './index.scss';

// S级别角标
/* const levelTags = {
  S1: S1Img,
  S2: S2Img,
  S3: S3Img,
  S4: S4Img,
};
 */
/**
 * @params queryRouter
 * uid 用户id
 * orderId  订单id
 * vType = 2 ？  购课流程优化 模式4  orElse 默认填写地址页面
 * sup 级别
 * recall ？召回流程 ：正常购课流程
 * */

export default function AddAddress() {
  // 获取路由挂参
  const router = useRouter();

  // 处理带有sence 解析参数
  const queryRouter: any = router.params.scene
    ? getQueryString(decodeURIComponent(router.params.scene))
    : router.params;

  const orderId =
    queryRouter.orderId || useSelector((state: UserStateType) => state.orderId);
  const userId =
    queryRouter.uid || useSelector((state: UserStateType) => state.userid);
  //channelId
  const channelId = useSelector((state: UserStateType) => state.channelId);
  const storeMobile = useSelector((state: UserStateType) => state.mobile);
  const [receiver, setReceiver] = useState<string>('');
  // const [birthday, setBirthday] = useState<string>('');
  const [mobile, setMobile] = useState<string>(storeMobile);
  const [address, setAddress] = useState<string>('');
  const [details, setDetails] = useState<string>('');
  const [province, setProvince] = useState('');
  const [telAreaCode, setTelAreaCode] = useState('');
  const [city, setCity] = useState('');
  const [area, setArea] = useState('');
  const [street, setStreet] = useState('');
  const [currentTab, setcurrentTab] = useState(0);
  const [tabList] = useState(['请选择']);
  const [addressList, setAddressList] = useState<any>([]);
  const [cityList, setCityList] = useState<any>([]);
  const [county, setCounty] = useState<any>([]);
  const [town, setTown] = useState<any>([]);
  const [isOpen, setIsOpen] = useState(false);
  // 添加默认地址弹窗
  const [addressId, setAddressId] = useState('');
  const [addressSource, setaddressSource] = useState(1);
  const [defaultAddressModal, setDefaultAddressModal] = useState({
    show: false,
    type: '1',
    isCheck: true,
    isChange: false,
    newAddress: false,
  });
  const [defaultAddressData, setDefaultAddressData] = useState({
    addressId: '',
    telAreaCode: '',
    province: '',
    city: '',
    area: '',
    street: '',
    addressData: '',
    addressDetail: '',
    name: '',
    phone: '',
  });

  // 填写地址版本 (1、默认  2、支付流程优化)
  const [addressType, setAddressType] = useState<number>(1);
  // 级别
  const [sup, setSup] = useState<string>('');
  //   切换时列表滚动到顶部距离
  const [scrollTop, setscrollTop] = useState<any>();
  // 小学艺术乐园专有埋点
  const [isFromH5AndNeedPoint] = useState(() => {
    if (Taro.getEnv() === 'WEAPP') {
      return (
        Taro.getAccountInfoSync().miniProgram.appId === 'wx34831c7cbd4406e5'
      );
    }
    return false;
  });
  let timer: any = null;

  let lock = true;

  useEffect(() => {
    if (queryRouter.vType) {
      setAddressType(Number(queryRouter.vType));
    }
    if (queryRouter.sup) {
      setSup(queryRouter.sup);
    }
    if (isFromH5AndNeedPoint) {
      sensors.track('ai_ArtSystemCourse_appletaddaddressbrowse', {
        open_source: router.params.openSource === 'H5' ? 2 : 1,
        abtest: queryRouter.recall ? '召回流程' : '购买流程',
        channel_id: router.params.channelId || channelId,
        clickID: router.params.clickid,
        userType: 1,
      });
    }
    sensors.track(
      router.params.isV9 == '1'
        ? 'xxms_testcourse_appletaddresspage_view'
        : 'xxys_experienceCoursePage_address_view',
      {
        buy_model: 'model_4',
        abtest: queryRouter.recall ? '召回流程' : '购买流程',
      },
    );
  }, []);

  useEffect(() => {
    getCenterAddressList().then((res) => {
      const { data } = res.payload;
      setAddressList(data);
    });
    return () => {
      timer && clearTimeout(timer);
    };
  }, [timer]);

  useEffect(() => {
    setscrollTop(currentTab);
  }, [currentTab]);
  //保存地址接口
  function onSubmit() {
    // 校验是否为空
    const flag = vuerify();
    if (!flag) return;
    // 检查地址是否修改
    if (vuerifyaddress()) {
      setDefaultAddressModal({
        show: true,
        type: '2',
        isCheck: true,
        isChange: false,
        newAddress: false,
      });
    } else {
      if (lock) {
        lock = false;
        Taro.showLoading();
        const accountInfo = Taro.getAccountInfoSync();
        const appId = accountInfo.miniProgram.appId;
        // addressSource =1:手动填写/2:微信地址/3:系统地址填充
        if (appId === 'wxf0543f82c4836de6') {
          sensors.track('xxms_testcourse_appletaddresspage_submitbuttonclick', {
            buy_model: 'model_4',
            addressSource: addressSource,
          });
        }
        if (isFromH5AndNeedPoint) {
          sensors.track('ai_ArtSystemCourse_appletaddaddressclick', {
            open_source: router.params.openSource === 'H5' ? 2 : 1,
            channel_id: router.params.channelId || channelId,
            addressSource: addressSource,
            clickID: router.params.clickid,
            userType: 1,
          });
        }
        createNewAddress()
          .then((resAddress: any) => {
            createAddressForExpressAndOrder({
              userId,
              orderId,
              addressId: resAddress.addressId,
            })
              .then((res) => {
                lock = true;
                Taro.hideLoading();
                if (res.code === 0) {
                  // const outTradeNo = Taro.getStorageSync('outTradeNo');
                  // let _url = `/pages/groupbuy/addTeacher/index?uid=${userId}&type=1`;
                  // if (queryRouter.type && queryRouter.type == '106') {
                  //   _url = `/pages/groupbuy/addTeacher/index?uid=${userId}&type=106&outTradeNo=${outTradeNo}`;
                  // }
                  // 非groupbuy下的落地页，跳转follow下的引导页，type=1是添加老师，非1是关注公众号
                  // if (queryRouter.type && queryRouter.type === 'new')
                  // 优化链路 美术都跳关注公众号
                  let _url;
                  // 非拼团
                  if (!router.params.orderGroup) {
                    _url = `/pages/launch/follow/index?sup=${queryRouter.sup}&uid=${userId}&orderId=${orderId}`;
                    if (!queryRouter.type)
                      _url = _url + '&source=H5&channel=' + channelId;
                    if (queryRouter.from)
                      _url = _url + `&from=${queryRouter.from}`;

                    if (queryRouter.openSource === 'H5')
                      _url = _url + `&openSource=H5`;

                    //   是否来自转介绍
                    if (queryRouter.soul)
                      _url = _url + `&soul=${queryRouter.soul}`;

                    //  头条clickid
                    if (router.params.clickid)
                      _url = _url + `&clickid=${router.params.clickid}`;

                    //   是否是加购订单
                    if (queryRouter.isAddPic)
                      _url =
                        _url +
                        `&isAddPic=true&unionCombineOrders=${queryRouter.unionCombineOrders}`;
                  } else {
                    _url = `/pages/groupBuying/groupTeacherInfo/index`;
                    if (router.params.from)
                      _url = _url + `?from=${queryRouter.from}`;
                  }
                  // 支付流程优化 不区分小程序
                  /*if (appId === 'wxf0543f82c4836de6') {
                    _url = `/pages/art/addteacher/index?sup=${queryRouter.sup}&orderIds=${queryRouter.orderId}`;
                    }*/
                  Taro.navigateTo({
                    url: _url,
                  });
                } else {
                  Taro.showToast({
                    title: res.errors || '服务器开小差了',
                    icon: 'none',
                    duration: 2000,
                  });
                }
              })
              .catch(() => {
                lock = true;
                Taro.hideLoading();
              });
          })
          .catch((error) => {
            lock = true;
            Taro.showToast({
              title: error.errors || '服务器开小差了',
              icon: 'none',
              duration: 2000,
            });
            // Taro.hideLoading();
          });
      }
    }
  }

  useEffect(() => {
    if (street) {
      province !== city
        ? setAddress(`${province}/${city}/${area}/${street}`)
        : setAddress(`${province}/${area}/${street}`);
    } else if (province && city && area) {
      province !== city
        ? setAddress(`${province}/${city}/${area}`)
        : setAddress(`${province}/${area}`);
    }
  }, [province, city, area, street]);

  function onReceiver(res) {
    clearTimeout(timer);
    timer = setTimeout(() => {
      setReceiver(res);
    }, 400);
  }

  function onMobile(res) {
    clearTimeout(timer);
    timer = setTimeout(() => {
      setMobile(res);
    }, 400);
  }

  function onDetails(res) {
    clearTimeout(timer);
    timer = setTimeout(() => {
      setDetails(res);
    }, 400);
  }

  function onAddressInput() {}

  function vuerify() {
    const errors: Array<String> = [];

    if (!receiver) {
      errors.push('Name required.');
      Taro.showToast({
        title: '请输入收货人姓名',
        icon: 'none',
        duration: 2000,
      });
    } else if (!mobile) {
      errors.push('Phone required.');
      Taro.showToast({
        title: '请输入手机号码',
        icon: 'none',
        duration: 2000,
      });
    } else if (!validPhone(mobile)) {
      errors.push('Valid phone required.');
      Taro.showToast({
        title: '请输入正确的手机号码',
        icon: 'none',
        duration: 2000,
      });
    } else if (!address) {
      errors.push('province required.');
      Taro.showToast({
        title: '请选择省市区',
        icon: 'none',
        duration: 2000,
      });
    } else if (!details) {
      errors.push('detail required.');
      Taro.showToast({
        title: '请输入详细地址',
        icon: 'none',
        duration: 2000,
      });
    }
    // else if (!birthday) {
    //   errors.push('birthday required.');
    //   Taro.showToast({
    //     title: '请选择宝贝生日',
    //     icon: 'none',
    //     duration: 2000,
    //   });
    // }

    if (!errors.length) {
      return true;
    }
  }

  function validPhone(phone) {
    if (phone.indexOf('*') > -1) {
      return phone.length == 11;
    }
    const re = /^1[3456789]\d{9}$/;
    return re.test(phone);
  }

  const handleClick = (value) => {
    setcurrentTab(value);
  };
  const openArea = () => {
    setIsOpen(true);
  };
  const closeArea = () => {
    setIsOpen(false);
  };
  const handleClickProvince = (item) => {
    tabList[0] = item.provinceName;
    tabList[1] = '请选择';
    tabList[2] = '';
    tabList[3] = '';
    setTelAreaCode(item.telAreaCode);
    setProvince(item.provinceName);
    setcurrentTab(1);
    setCityList(item.citys);
  };
  const handleClickCity = (item) => {
    tabList[1] = item.cityName;
    tabList[2] = '请选择';
    tabList[3] = '';
    setCity(item.cityName);
    setcurrentTab(2);
    setCounty(item.countys);
  };
  const handleClickRegion = (item) => {
    tabList[2] = item.countyName;
    tabList[3] = '请选择';
    setArea(item.countyName);
    setcurrentTab(3);
    getAddressTownList({ code: item.countyCode }).then((res) => {
      let data = res.payload;
      data.unshift({ townName: '暂不选择' });
      setTown(data);
    });
  };
  const handleClickTown = (item) => {
    setcurrentTab(4);
    if (item.townName !== '暂不选择') {
      tabList[3] = item.townName;
      setStreet(item.townName);
    } else {
      tabList[3] = '请选择';
      setStreet('');
    }
    setcurrentTab(3);
    setIsOpen(false);
  };

  // 默认地址与页面数据关联
  function variableAssignment(onlysetId: boolean, data: any, type = 3) {
    if (!onlysetId) {
      setReceiver(data.name);
      setMobile(data.phone);
      setAddress(data.addressData);
      setDetails(data.addressDetail);
      setProvince(data.province);
      setTelAreaCode(data.telAreaCode);
      setCity(data.city);
      setArea(data.area);
      setStreet(data.street);
      setDefaultAddressData(data);
    }
    setaddressSource(type);
    setAddressId(data.addressId);
  }

  //校验默认地址是否修改 或者未使用默认地址
  function vuerifyaddress() {
    if (!addressId) {
      return false;
    }
    let isNeedCheck = true;
    if (defaultAddressModal.isCheck) {
      if (
        receiver === defaultAddressData.name &&
        mobile === defaultAddressData.phone &&
        details === defaultAddressData.addressDetail &&
        province === defaultAddressData.province &&
        city === defaultAddressData.city &&
        area === defaultAddressData.area &&
        street === defaultAddressData.street &&
        telAreaCode === defaultAddressData.telAreaCode
      ) {
        isNeedCheck = false;
      }
    } else {
      isNeedCheck = false;
    }

    return isNeedCheck;
  }

  // 新建地址
  function createNewAddress() {
    return new Promise((resolve, reject) => {
      // 判断是否有默认地址或者修改了默认地址
      if (!addressId || defaultAddressModal.newAddress) {
        createUserAddress({
          subject: 'ART_APP',
          userId: userId,
          receiptName: receiver,
          receiptTel: mobile,
          province: province,
          city: city,
          area: area,
          street: street,
          addressDetail: details,
          telAreaCode: telAreaCode,
          areaCode: '',
          idCode: '',
          isDefault: addressId ? '0' : '1',
        })
          .then((res) => {
            if (res.code === 0) {
              resolve({ addressId: res.payload.id });
            } else {
              reject(res);
            }
          })
          .catch((error) => {
            reject(error);
          });
      } else if (defaultAddressModal.isChange) {
        updateUserAddress({
          addressId: addressId,
          userId: userId,
          receiptName: receiver,
          receiptTel: mobile,
          province: province,
          city: city,
          area: area,
          street: street,
          addressDetail: details,
          telAreaCode: telAreaCode,
          areaCode: '',
          idCode: '',
          isDefault: '1',
        }).then((res) => {
          if (res.code === 0) {
            resolve({ addressId: res.payload.id });
          } else {
            setDefaultAddressModal({
              ...defaultAddressModal,
              isCheck: true,
              isChange: false,
            });
            reject(res);
          }
        });
      } else {
        // 使用默认地址可以直接关联订单生成物流信息
        resolve({ addressId: addressId });
      }
    });
  }

  useEffect(() => {
    console.log('submit==');

    defaultAddressModal.isChange && onSubmit();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultAddressModal.isChange]);

  return (
    <View className='add-address'>
      {judgeMinienv('release') && (
        <View>
          <DefaultAddress
            setDefaultAddressModal={setDefaultAddressModal}
            defaultAddressModal={defaultAddressModal}
            variableAssignment={variableAssignment}
          />
          {addressType == 2 ? (
            <View className='header-v1'>
              <View className='form-title'>
                {queryRouter.recall ? '您的礼包尚未领取' : '确认收货地址'}
              </View>
              <View className='form-desc'>
                {queryRouter.recall
                  ? '请尽快填写地址'
                  : `画材${
                      queryRouter.orderType == '9.9' ? '礼包' : '大礼盒'
                    }正准备发货`}
              </View>
              <View className='info'>
                <Text>
                  画材{queryRouter.orderType == '9.9' ? '礼包' : '大礼盒'}
                </Text>
                <View className='info-detail'>
                  {/* <View>
                    {levelTags[sup] && (
                      <Image src={levelTags[sup]} mode='widthFix'></Image>
                    )}
                  </View> */}
                  <Image
                    src={
                      queryRouter.orderType == '9.9'
                        ? selIcon
                        : queryRouter.sup == 'S4'
                        ? selIcon4
                        : GiftV1
                    }
                    mode='widthFix'
                  ></Image>
                  <Image
                    style={
                      queryRouter.orderType == '9.9' ? { width: '66%' } : {}
                    }
                    src={
                      queryRouter.orderType == '9.9'
                        ? DesSel
                        : queryRouter.sup == 'S4'
                        ? GiftDesImgV1
                        : GiftDesImg
                    }
                    mode='widthFix'
                  ></Image>
                </View>
              </View>
            </View>
          ) : (
            <View className='head-tips'>
              <View>恭喜您购课成功</View>
            </View>
          )}

          <View
            className={`form-card ${addressType == 2 ? 'form-card-v1' : ''}`}
          >
            <View className='form-title'>填写收货地址</View>
            {addressType != 2 && (
              <View className='form-desc'>确保孩子学习前能收到随材礼盒！</View>
            )}
            {/* <View className='from-box'> */}
            <AtForm>
              <AtInput
                required
                name='receiver'
                title='收货人'
                type='text'
                placeholder='请输入收货人姓名'
                value={receiver}
                onChange={onReceiver}
              />
              <AtInput
                required
                name='mobile'
                type='phone'
                title='手机号码'
                maxlength={11}
                placeholder='请输入手机号码'
                value={mobile}
                onChange={onMobile}
              >
                {/* <View>
                 <Text className='mobile-text'>+86</Text>
                 <Image src={arrowRight} className='arrow' />
                 </View> */}
              </AtInput>
              <View className='region'>
                <AtInput
                  required
                  name='address'
                  type='text'
                  title='地区'
                  placeholder='省市区'
                  value={address}
                  editable={false}
                  onChange={onAddressInput}
                  onClick={openArea}
                >
                  <View>
                    <Image src={arrowRight} className='arrow' />
                  </View>
                </AtInput>
              </View>
              <View className='addr-textarea'>
                <View className='addr-left'>详细地址</View>
                <AtTextarea
                  className={isOpen ? 'textareaHide' : ''}
                  count={false}
                  value={details}
                  onChange={onDetails}
                  maxLength={200}
                  height={160}
                  placeholder='如街道、小区门牌号等'
                />
              </View>

              {/* <Picker
               mode='date'
               value={`${new Date().toLocaleDateString().replace(/\//g, '-')}`}
               start={`${new Date().getFullYear() - 15}-01-01`}
               end={`${new Date().toLocaleDateString().replace(/\//g, '-')}`}
               onChange={onDateChange}
               >
               <AtInput
               className='birthday-css'
               required
               name='birthday'
               type='text'
               placeholder='孩子生日（根据宝贝年龄安排专业老师）'
               value={birthday}
               editable={false}
               onChange={onBirthday}
               />
               </Picker> */}
              <View className='foot-box'>
                <AtButton className='foot-btn' onClick={onSubmit}>
                  {addressType == 2 ? '领取大礼包' : '确认提交'}
                </AtButton>
              </View>
            </AtForm>
            {/* </View> */}
          </View>

          <AtFloatLayout
            className='address-area-float'
            isOpened={isOpen}
            onClose={closeArea}
            scrollY
            scrollTop={scrollTop}
            title='请选择所在地区'
          >
            <View className='area-box'>
              <View className='area-tabs'>
                {tabList.map((item, index) => {
                  return (
                    <View
                      className={`area-tabs-item ${
                        currentTab === index ? 'active' : ''
                      }`}
                      key={`tab-${index}`}
                      onClick={() => handleClick(index)}
                    >
                      {item}
                    </View>
                  );
                })}
              </View>
              <View className='area-list'>
                <View
                  className={`area-province ${
                    currentTab === 0 ? 'active' : 'hide'
                  }`}
                >
                  {addressList.map((item, index) => {
                    return (
                      <View
                        className='area-list-item'
                        key={`area-province${index}`}
                        onClick={() => handleClickProvince(item)}
                      >
                        {item.provinceName === province && (
                          <Image src={chooseRight} className='choose-right' />
                        )}
                        {item.provinceName}
                      </View>
                    );
                  })}
                </View>
                <View
                  className={`area-city ${
                    currentTab === 1 ? 'active' : 'hide'
                  }`}
                >
                  {cityList.map((item, index) => {
                    return (
                      <View
                        className='area-list-item'
                        key={`area-city${index}`}
                        onClick={() => handleClickCity(item)}
                      >
                        {item.cityName === city && (
                          <Image src={chooseRight} className='choose-right' />
                        )}
                        {item.cityName}
                      </View>
                    );
                  })}
                </View>
                <View
                  className={`area-region ${
                    currentTab === 2 ? 'active' : 'hide'
                  }`}
                >
                  {county.map((item, index) => {
                    return (
                      <View
                        className='area-list-item'
                        key={`area-region${index}`}
                        onClick={() => handleClickRegion(item)}
                      >
                        {item.countyName === area && (
                          <Image src={chooseRight} className='choose-right' />
                        )}
                        {item.countyName}
                      </View>
                    );
                  })}
                </View>
                <View
                  className={`area-street ${
                    currentTab === 3 ? 'active' : 'hide'
                  }`}
                >
                  {town.map((item, index) => {
                    return (
                      <View
                        className='area-list-item'
                        key={`area-street${index}`}
                        onClick={() => handleClickTown(item)}
                      >
                        {item.townName === street && (
                          <Image src={chooseRight} className='choose-right' />
                        )}
                        {item.townName}
                      </View>
                    );
                  })}
                </View>
              </View>
            </View>
          </AtFloatLayout>

          {addressType != 2 && (
            <View className='tips-bottom'>
              <View className='title'>温馨提示</View>
              <View className='content'>
                礼盒会在3-5个工作日内邮寄到货，发出后会短信通知，您可以下载
                {queryRouter.from == '1' ? '艺术宝' : '小熊艺术'}
                APP，用购课手机号登录，在【我的-订单物流】中查询物流进度。
              </View>
            </View>
          )}
        </View>
      )}
    </View>
  );
}
AddAddress.config = {
  navigationBarTitleText: '购课成功',
};
