.bannerBox {
  width: 100%;
}

.bannerpart {
  width: 100%;
  position: relative;
  top: -16px;
}
.bannerContent {
  box-sizing: border-box;
  padding-top: 40px;
  width: 100%;
  font-size: 0;
  .bannerContentImg {
    margin-bottom: 40px;
    padding: 0 32px;
    &:nth-child(6) {
      margin-bottom: 0;
      padding: 0;
    }
  }
}

.slide-index-2 {
  height: 1810px;
  box-sizing: border-box;
  position: relative;

  .insertSwiper {
    height: 634px !important;
    width: 634px;
    top: 200px;
    left: 26px;
    position: absolute;
    .slideItem,
    .slide1 {
      height: 634px !important;
      width: 634px !important;
    }
  }
}

.suctionbottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 120px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  padding-bottom: env(safe-area-inset-bottom) !important;
  z-index: 100;
  background-color: #fff;
  box-sizing: content-box;
  font-size: 12px;
  .leftimg {
    width: 100%;
    vertical-align: top;
    pointer-events: none;
  }
  .bntgif {
    position: absolute;
    right: 5px;
    top: 20px;
    bottom: 0;
    width: 340px;
  }
}
