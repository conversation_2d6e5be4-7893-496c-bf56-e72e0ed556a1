@import '../../theme/groupbuy/common.scss';

.banner {
  width: 100%;
}

.swiper-banner {
  width: 100%;
  height: 800px;
}

.bannerContent {
  background: #ffcf00;
  padding-bottom: 42px;

  &.introUI {
    background: #fffad9;
  }
}

.bannerList {
  width: 686px;
  margin: 0 auto;
}

.payText {
  width: 100%;
  position: relative;
  z-index: 20;
  margin-top: -22px;
  font-size: 0;
}

.bannerImg {
  width: 100%;
  height: 100%;
}

.bannerDiv {
  padding-top: 80px;
}

.index {
  font-size: 0;
  position: relative;
  padding-bottom: calc(112rpx + env(safe-area-inset-bottom));

  .fixedTop {
    position: sticky;
  }

  .divider {
    width: 100%;
    height: 16 rpx;
    background: #f7f7f7;
  }

  .commodity-price {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 147 rpx;

    .unit-price {
      display: flex;
      align-items: center;
      width: 480 rpx;
      height: 100%;
      box-sizing: border-box;
      padding: 0 32 rpx;
      background: $red-to-orange;

      .price-symbol {
        color: $white;
        font-size: $font-40;
        margin-top: 40 rpx;
      }

      .price {
        color: $white;
        font-size: $font-96;
      }

      .price-details {
        margin-top: 6 rpx;
        margin-left: 20 rpx;

        .price-detailed {
          margin-top: 4 rpx;
          color: $white;
          font-size: $font-24;
        }
      }
    }

    .assemble-price {
      display: flex;
      align-items: flex-start;
      justify-content: center;
      flex-direction: column;
      width: calc(100% - 480rpx);
      height: 100%;
      background: $orange-to-yellow;
      position: relative;
      padding-left: 70 rpx;

      &::before {
        content: '';
        display: block;
        position: absolute;
        top: 0;
        bottom: 0;
        left: -5%;
        width: 0;
        height: 0;
        margin: auto 0;
        border-top: 20 rpx solid transparent;
        border-right: 20 rpx solid #fd880d;
        border-bottom: 20 rpx solid transparent;
      }

      .groupbuy-reduction {
        font-size: $font-36;
        color: $white;
      }

      .reduce-price {
        font-size: $font-36;
        color: #fcd850;
        // text-decoration: line-through;
      }
    }
  }

  .introduce {
    display: flex;
    justify-content: space-between;
    padding: 32 rpx 24 rpx;

    .product-introduction {
      width: calc(100% - 100rpx);
      font-size: $font-28;
      color: $dim-gray;
      text-align: justify;
      font-weight: bold;
    }
  }

  .promise-introduce {
    width: 100%;
    padding: 24 rpx 32 rpx;
    box-sizing: border-box;

    .row {
      display: flex;
      width: 100%;
      justify-content: space-between;
      padding: 10 rpx 0;
      font-size: $font-26;

      .title {
        color: $light-grey;
      }

      .content {
        flex: 1;
        color: $dim-gray;
        padding-left: 24 rpx;

        .promotion-row {
          margin-bottom: 20 rpx;
        }

        .promotion-text {
          margin-left: 16 rpx;
          font-size: $font-26;
          color: $dim-gray;
        }
      }

      .view {
        display: flex;
        align-items: center;
        color: $light-grey;

        .arrow {
          width: 12 rpx;
          height: 22 rpx;
          margin-left: 16 rpx;
        }
      }
    }
  }

  .groupbuy-detail {
    background: $white;
    padding: 14 rpx 32 rpx;

    .title {
      font-size: $font-28;
      color: $dim-gray;
      font-weight: bold;
      margin-bottom: 26 rpx;
    }

    .content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .swiper-head {
        width: 330 rpx;
        height: 60 rpx;

        .head {
          display: flex;
          align-items: center;

          .head-box {
            position: relative;
            width: 60 rpx;
            height: 60 rpx;
            border-radius: 30 rpx;
            overflow: hidden;
            background: $light-grey;

            .head-img {
              max-width: 100%;
              max-height: 100%;
              position: absolute;
              left: 0;
              right: 0;
              bottom: 0;
              top: 0;
              margin: auto;
            }
          }

          .nickname {
            width: 254 rpx;
            font-size: $font-28;
            color: $dim-gray;
            margin-left: 16 rpx;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }

      .clustering-info {
        display: flex;

        .clustering-info-box {
          display: flex;
          flex-direction: column;

          .number {
            text-align: right;
            font-size: $font-24;
            color: $dim-gray;

            .highlight {
              color: $red;
            }
          }

          .countdown {
            color: $light-grey;
            font-size: $font-24;

            .at-countdown {
              width: 110 rpx;
              text-align: right;
            }

            .at-countdown__time-box {
              min-width: 0;
              font-family: '黑体';
              color: $light-grey;
              font-size: $font-26;
            }

            .at-countdown__separator {
              font-size: $font-26;
              padding: 0;
            }

            .at-countdown__item,
            .at-countdown__time {
              color: $light-grey;
            }
          }
        }

        .groupbuy-btn {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 144 rpx;
          height: 60 rpx;
          background: $red-to-yellow;
          font-size: $font-28;
          color: $white;
          border-radius: 10 rpx;
          margin-left: 20 rpx;

          .arrow {
            width: 12 rpx;
            height: 21 rpx;
            margin-left: 6 rpx;
          }
        }
      }
    }
  }

  .groupbuy-procedure {
    .title {
      padding: 16 rpx 0 0 32 rpx;
      color: #252525;
      font-size: $font-32;
      font-weight: bold;
    }

    .img {
      width: 100%;
    }
  }

  .qa {
    padding: 0 32 rpx 20 rpx 32 rpx;

    .all {
      display: flex;
      justify-content: space-between;
      padding: 24 rpx 0;

      .number {
        font-size: $font-26;
        color: $light-grey;
        font-weight: bold;
      }

      .view {
        display: flex;
        align-items: center;
        color: $light-grey;
        font-size: $font-26;

        .arrow {
          width: 12 rpx;
          height: 22 rpx;
          margin-left: 16 rpx;
        }
      }
    }

    .problem-row {
      display: flex;
      justify-content: space-between;
      padding-bottom: 20 rpx;

      .problem-icon {
        width: 35 rpx;
        height: 35 rpx;
      }

      .problem {
        flex: 1;
        font-size: $font-24;
        font-weight: bold;
        color: $dim-gray;
        margin-left: 24 rpx;
      }

      .answer {
        font-size: $font-24;
        color: $light-grey;
      }
    }
  }

  .big-img {
    width: 100%;
  }

  .pay-img {
    width: 100%;
  }

  .suction-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 112 rpx;
    background: $white;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    padding-bottom: env(safe-area-inset-bottom);
    z-index: 100;

    .pay-fixed {
      position: absolute;
      width: 384px;
      right: 5px;
      top: 13px;
    }

    .synopsis {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 310 rpx;
      height: 112 rpx;

      .course {
        display: flex;
        justify-content: center;
        align-items: center;

        .number {
          color: #ff6c00;
          font-size: $font-50;
          text-align: end;
          font-family: DINAlternate-Bold, DINAlternate;
        }

        .section {
          color: $dim-gray;
          font-size: $font-34;
          margin-left: 5 rpx;
        }

        .icon {
          font-size: $font-22;
          color: $white;
          font-weight: bold;
          padding: 5 rpx 8 rpx;
          margin-left: 18 rpx;
          border-radius: 0 rpx 10 rpx 10 rpx 10 rpx;
          background: $orange-to-yellow;
        }
      }

      .copywriting {
        color: $silver;
        font-size: $font-24;
      }
    }

    .origin-price {
      width: 180 rpx;
      height: 100%;
      background: $orange;
    }

    .groupbuy-price {
      flex: 1;
      height: 100%;
      background: $red;
    }

    .origin-price,
    .groupbuy-price {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      color: $white;

      .price {
        font-size: $font-36;
        font-weight: bold;
      }

      .text {
        font-size: $font-26;
      }
    }
  }

  .layout-header {
    background: $white;
    padding: 40px 0 16px;

    .layout-header__title {
      text-align: center;
      font-size: $font-30;
      padding-left: 80px;
    }
  }

  .problem-layout {
    .layout {
      height: 70%;
    }
  }

  .order-layout {
    .layout {
      height: 850 rpx;

      .layout-body {
        height: 850 rpx;
        max-height: 850 rpx;
      }

      .layout-body__content {
        max-height: 850 rpx;
      }
    }
  }

  .redeem-modal {
    .at-modal__overlay {
      background-color: rgba(0, 0, 0, 0.7);
    }

    .at-modal__container {
      padding-top: 90px;
      box-sizing: border-box;
      width: 580px;
      border-radius: 30px;
      overflow: auto;
      background: transparent;

      .close-img {
        width: 60px;
        height: 60px;
        position: absolute;
        top: 0;
        right: 0;
      }

      .at-modal__content {
        padding: 0;
        overflow: auto;
        background: transparent;

        .redeem-img {
          width: 100%;
          height: 519px;
          vertical-align: top;
        }

        .footer {
          height: 168px;
          background: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;

          .btns {
            width: 500px;
            height: 88px;
            background: #ff9c00;
            border-radius: 44px;
            font-size: 36px;
            font-weight: bold;
            color: #ffffff;
            line-height: 88px;
            text-align: center;
          }
        }
      }
    }
  }
}
