import { useEffect, useLayoutEffect, useState } from 'react';
import { AtFloatLayout, AtModal, AtModalContent, AtToast } from 'taro-ui';
import { Image, Swiper, SwiperItem, View } from '@tarojs/components';
import { useDispatch, useSelector } from 'react-redux';
import { levelV1, levelV2 } from '@/common/data.config';
import '@/theme/custom-taro-ui.scss';
import sensors from '@/utils/sensors_data';
import { UserStateType } from '@/store/groupbuy/state';
import swiperListOne from '@/assets/groupbuy/index/swiperListOne.png';
import swiperListTwo from '@/assets/groupbuy/index/swiperListTwo.png';
import swiperListThree from '@/assets/groupbuy/index/swiperListThree.png';
import bannerPay from '@/assets/groupbuy/index/bannerPay.png';
import childrenLike from '@/assets/groupbuy/index/childrenLike.png';
import study from '@/assets/groupbuy/index/study.png';
import content from '@/assets/groupbuy/index/content.png';
import harvest from '@/assets/groupbuy/index/harvest.png';
import question from '@/assets/groupbuy/index/question.png';
import gift from '@/assets/groupbuy/index/gift.png';
import sign from '@/assets/groupbuy/index/sign.png';
import bottomPay from '@/assets/groupbuy/index/bottomPay.png';
import buy from '@/assets/groupbuy/index/buy.gif';
import selIcon from '@/assets/groupbuy/index/gift-new.jpg';
import selIconV1 from '@/assets/groupbuy/index/gift-new-v2.png';
import redeemImg from '@/assets/groupbuy/thirtySix/redeem-img2.png';
import modalCloseImg from '@/assets/groupbuy/thirtySix/close.png';
// 转介绍图片
import introBanner from '@/assets/groupbuy/marketIntroduce/intro-banner.png';
import introduceImg9 from '@/assets/groupbuy/marketIntroduce/introduce-img-9.png';
import introduceDecoration from '@/assets/groupbuy/marketIntroduce/introduce-decoration.png';

// @ts-ignore
import CommonTop from '../../components/commonTop';
// @ts-ignore
import LayoutGiveaway from './components/giveaway';
// @ts-ignore
import LayoutPromise from './components/promise';
// @ts-ignore
import LayoutProblem from './components/problem';
// @ts-ignore
import LayoutLevel from '../../components/level';
// @ts-ignore
import LayoutOrderV1 from '../../components/orderV1';
// @ts-ignore
import WxLogin from '../../components/wxlogin';
import './index.scss';

/**
 *  @param popupType 浮窗类型
 *  0、随材赠品展示
 *  1、郑重承若
 *  2、大家都关心的问题
 *  3、选择级别（年龄）
 * */

/**
 * @param schemaType 模式类型 (预留参数)
 * 4、模式4
 * 5、模式5（待定）
 * */

/**
 *  @param levelType 级别类型
 *  0、选择级别（年龄）
 *  1、选择级别（写死描述）
 *  2、支付和级别选择一起
 * */

export default () => {
  const dispatch = useDispatch();
  // UI区分
  const [introUI] = useState<boolean>(false);
  // Toast轻提示
  const [showToast, setShowToast] = useState<boolean>(false);
  // 显示隐藏浮窗
  const [isOpened, setIsOpened] = useState<boolean>(false);
  // 显示隐藏订单浮窗
  const [isShowOrder, setIsShowOrder] = useState<boolean>(false);
  // 浮窗类型
  const [popupType, setPopupType] = useState<number>(0);
  // 级别类型
  //   const [levelType] = useState<number>(Math.floor(Math.random() * 3));
  const [levelType] = useState<number>(1);

  // 支付页面的数据
  const [payPageData, setPayPageData] = useState<object | null>(null);
  const imgList = [childrenLike, study, content, harvest, gift, question];
  const [newImgList] = useState<any>(imgList);
  const [showRedeemModal, setShowRedeemModal] = useState<boolean>(false);
  const [needShowRedeem, setNeedShowRedeem] = useState<boolean>(true);

  // 浮窗标题
  const [popupTitle] = useState([
    '随材赠品展示',
    '郑重承若',
    '大家都关心的问题',
    '选择级别',
  ]);

  let channelId = useSelector((state: UserStateType) => state.channelId);
  let userRole = useSelector((state: UserStateType) => state.userRole);

  // 改变弹窗类型
  const changePopupType = (newPopupType) => {
    if (levelType == 2) {
      watchShowOrder(true, {});
      setIsShowOrder(true);
      return;
    }
    setIsOpened(true);
    setPopupType(newPopupType);
  };

  // 监听订单浮窗的打开和关闭
  const watchShowOrder = (state, pageData) => {
    if (!state && needShowRedeem) {
      setShowRedeemModal(true);
      setNeedShowRedeem(false);
      return;
    }
    setNeedShowRedeem(true);
    setIsShowOrder(state);
    setShowToast(false);
    pageData && setPayPageData(pageData);

    pageData &&
      Object.keys(pageData).length > 0 &&
      !isShowOrder &&
      sensors.track('xxys_experienceCoursePage_courseSup_click', {
        course_sup: pageData.sup,
        channel_id: channelId,
        user_role: userRole,
        buy_model: 'model_4',
        abtest: levelType == 0 ? '单年龄' : '年龄&介绍',
      });
  };
  // 关闭挽留弹窗
  const hideRedeemHandle = () => {
    setShowRedeemModal(false);
  };

  useLayoutEffect(() => {
    // setCountDown(getOverTime());
  }, []);

  useEffect(() => {
    //区分类型
    dispatch({
      type: 'PAY_TYPE',
      payType: '36',
    });
  }, [dispatch]);

  // 随材赠品展示
  const [giveaway] = useState({
    img: selIcon,
    sImg: selIconV1,
    detail: [
      '小熊模切',
      '超轻粘土',
      '小熊作品纸',
      '黑色勾线笔',
      '小熊马克笔',
      '重彩油画棒',
      '手指画颜料',
      '其他材料若干',
    ],
    notes: '该图仅为展示，具体以实物为主，每期根据课程不同收到的随材会有所差异',
  });
  return (
    <View className='index container w100 relative'>
      {/* <CodeSnippet /> */}
      {/* 微信登录组件 */}
      <WxLogin subject='ART_APP' isIntroduce={false} />
      {/* 头部导航栏 */}
      <CommonTop currentName='小熊美术' isIntroduce={false} />
      <View>
        <Swiper className='swiper-banner' circular autoplay>
          {!introUI ? (
            <>
              <SwiperItem>
                <Image
                  mode='widthFix'
                  src={swiperListOne}
                  className='banner'
                ></Image>
              </SwiperItem>
              <SwiperItem>
                <Image
                  mode='widthFix'
                  src={swiperListTwo}
                  className='banner'
                ></Image>
              </SwiperItem>
              <SwiperItem>
                <Image
                  mode='widthFix'
                  src={swiperListThree}
                  className='banner'
                ></Image>
              </SwiperItem>
            </>
          ) : (
            <SwiperItem>
              <Image
                mode='widthFix'
                src={introBanner}
                className='banner'
              ></Image>
            </SwiperItem>
          )}
        </Swiper>
      </View>
      <View>
        <View>
          <Image
            mode='widthFix'
            src={!introUI ? bannerPay : introduceDecoration}
            className='payText'
          ></Image>
        </View>
        <View className={`bannerContent ${introUI ? 'introUI' : ''}`}>
          <View className='bannerList'>
            {newImgList.map((e, i) => {
              return (
                <View className='bannerDiv' key={i}>
                  <Image mode='widthFix' src={e} className='bannerImg'></Image>
                </View>
              );
            })}
          </View>
        </View>
        <View>
          <Image mode='widthFix' src={sign} className='payText'></Image>
        </View>
      </View>

      <View className='suction-bottom' onClick={() => changePopupType(3)}>
        {!introUI ? (
          <>
            <Image mode='widthFix' src={bottomPay} className='pay-img' />

            <Image mode='widthFix' src={buy} className='pay-fixed' />
          </>
        ) : (
          <Image mode='widthFix' src={introduceImg9} className='pay-img' />
        )}
      </View>
      {levelType !== 2 && (
        <AtFloatLayout
          className={`'custom-float-layout' ${
            popupType === 2 && isOpened ? 'problem-layout' : ''
          }`}
          isOpened={isOpened}
          onClose={() => {
            setIsOpened(false);
            setShowToast(false);
          }}
          title={popupTitle[popupType]}
        >
          {popupType === 0 && <LayoutGiveaway />}
          {popupType === 1 && <LayoutPromise />}
          {popupType === 2 && <LayoutProblem />}
          {popupType === 3 && (
            <LayoutLevel
              oldLevelArray={levelType == 1 ? levelV2 : levelV1}
              levelType={levelType}
              pType='art'
              regtype='EXPERIENCE'
              watchShowOrder={watchShowOrder}
            />
          )}
        </AtFloatLayout>
      )}
      <AtFloatLayout
        className='order-layout custom-float-layout'
        isOpened={isShowOrder}
        onClose={() => {
          watchShowOrder(false, null);
        }}
      >
        {payPageData && (
          <LayoutOrderV1
            isShowOrder={isShowOrder}
            watchCloseOrder={watchShowOrder}
            payPageData={payPageData}
            orderType='36'
            subject='ART_APP'
            packagesId={62}
            classNum={10}
            topicId={3}
            pType='art'
            isIntroduce={false}
            pName='美术'
            giveaway={giveaway}
            levelType={levelType}
            regtype='EXPERIENCE'
          />
        )}
      </AtFloatLayout>
      <AtToast text='系统已帮您自动成团～' isOpened={showToast} />

      <AtModal
        className='redeem-modal'
        isOpened={showRedeemModal}
        closeOnClickOverlay={false}
      >
        <Image
          className='close-img'
          src={modalCloseImg}
          onClick={hideRedeemHandle}
        ></Image>
        <AtModalContent>
          <Image className='redeem-img' src={redeemImg}></Image>
          <View className='footer'>
            <View className='btns' onClick={hideRedeemHandle}>
              继续支付
            </View>
          </View>
        </AtModalContent>
      </AtModal>
    </View>
  );
};
