import { useState } from 'react';
import Taro from '@tarojs/taro';
import Undertask from '@/components/undertake';
import Contactshare from './components/contactshare';

export default function AddTeacher() {
  const [iShow, setIShow] = useState(true);
  Taro.hideHomeButton(); //隐藏返回首页

  return (
    <>
      {iShow ? (
        <Undertask handleShow={() => setIShow(false)} />
      ) : (
        <Contactshare />
      )}
    </>
  );
}
AddTeacher.config = {
  navigationBarTitleText: '邀好友一起学',
};
