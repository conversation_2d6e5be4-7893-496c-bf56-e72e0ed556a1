import { useState } from 'react';
import prescription from '@/assets/groupbuy/index/prescription-icon.png';
import teacher from '@/assets/groupbuy/index/teacher-icon.png';
import refund from '@/assets/groupbuy/index/refund-icon.png';
import { View, Image } from '@tarojs/components';
import './index.scss';

export default function Index() {
  // 郑重承诺
  const [promise] = useState([
    {
      icon: prescription,
      title: '课程长期有效',
      desc: '所购课程长期有效，在小熊美术App中可反复观看学习',
      width: '35rpx',
    },
    {
      icon: teacher,
      title: '严选辅导老师',
      desc: '全程在线点评辅导',
      width: '44rpx',
    },
    {
      icon: refund,
      title: '实物奖励体系',
      desc: '学画画得积分，兑换精美实物奖品',
      width: '33rpx',
    },
  ]);
  return (
    <View className='promise'>
      {promise.map((item, index) => {
        return (
          <View className='row' key={`promise-${index}`}>
            <View className='icon-box'>
              <Image
                src={item.icon}
                mode='widthFix'
                className='img'
                style={`width: ${item.width}`}
              />
            </View>
            <View className='copywriting'>
              <View className='title'>{item.title}</View>
              <View className='desc'>{item.desc}</View>
            </View>
          </View>
        );
      })}
    </View>
  );
}
