@import '../../../../theme/groupbuy/common.scss';

.giveaway {
  padding: 40rpx 32rpx;
  .row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 40rpx;
    .gift-box {
      width: 283rpx;
      height: 192rpx;
      padding: 17rpx 22rpx;
      background: #f5f5f5;
      border-radius: 20rpx;
      box-sizing: border-box;
      .img {
        max-width: 100%;
        max-height: 100%;
      }
    }
    .label {
      width: 370rpx;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      .label-item {
        display: flex;
        align-items: center;
        width: 48%;
        font-size: $font-24;
        color: $light-grey;
        position: relative;
        overflow: hidden;
        &::before {
          display: inline-block;
          content: '·';
          font-size: $font-38;
          vertical-align: middle;
          margin-right: 8rpx;
        }
      }
    }
  }
  .notes {
    font-size: $font-24;
    color: $orange;
    position: relative;
    padding-left: 20rpx;
    &::before {
      content: '*';
      position: absolute;
      top: 4rpx;
      left: 0;
    }
  }
}
