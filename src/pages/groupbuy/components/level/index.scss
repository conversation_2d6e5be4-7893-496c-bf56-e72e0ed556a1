@import '../../../../theme/groupbuy/common.scss';

.level {
  padding: 0 32rpx 20rpx 32rpx;
  .card {
    display: flex;
    align-items: center;
    padding: 32rpx 10rpx 32rpx 0;
    border: 1rpx solid #cccccc;
    border-radius: 10rpx;
    margin-bottom: 20rpx;
    .label {
      width: 80rpx;
      height: 80rpx;
      line-height: 80rpx;
      color: $white;
      font-size: $font-40;
      font-weight: bold;
      border-radius: 0 100rpx 100rpx 0;
      padding-left: 11rpx;
      margin-right: 30rpx;
      box-sizing: border-box;
    }
    .info {
      flex: 1;
      .title {
        display: flex;
        align-items: center;
        height: 50rpx;
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;
        .fit {
          font-size: $font-36;
          color: #222222;
          font-weight: bold;
        }
        .range {
          font-size: $font-24;
          color: #222222;
          font-weight: bold;
        }
      }
      .desc {
        font-size: $font-28;
        color: $silver;
      }
    }
  }
  .active {
    border: 1rpx solid #ff9c00;
    background: #fffbf4;
  }
  .level-tip {
    font-size: 26px;
    color: #888888;
    text-align: center;
    margin-bottom: 30px;
  }
}
