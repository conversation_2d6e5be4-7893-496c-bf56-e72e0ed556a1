@import '../../../../theme/groupbuy/common.scss';

.problem-component {
  padding: 80rpx 32rpx 64rpx 32rpx;
  .tabs {
    height: 56rpx;
    background: $white;
    padding: 52rpx 0 32rpx 0;
    position: fixed;
    top: 80rpx;
    left: 32rpx;
    right: 32rpx;
    margin: 0 auto;
    z-index: 10;
    transform: translateZ(0);
    -webkit-overflow-scrolling: touch;
    .tab {
      display: inline-block;
      padding: 8rpx 16rpx;
      border: 1rpx solid $silver;
      font-size: $font-28;
      color: $silver;
      border-radius: 10rpx;
      margin-right: 32rpx;
    }
    .active {
      border: 1rpx solid $orange !important;
      color: $orange !important;
    }
  }
  .content {
    .qa {
      margin-top: 32rpx;
      padding: 32rpx 30rpx 32rpx 24rpx;
      background: #fffbec;
      border-radius: 10rpx;
      .title {
        font-size: $font-30;
        color: $dim-gray;
        font-weight: bold;
        padding-left: 56rpx;
        position: relative;
        &::before {
          content: '问';
          display: block;
          width: 32rpx;
          height: 32rpx;
          border-radius: 50%;
          background: $orange;
          color: $white;
          text-align: center;
          font-size: $font-20;
          font-weight: bold;
          position: absolute;
          left: 0;
          top: 4rpx;
        }
      }
      .answer {
        font-size: $font-24;
        color: $silver;
        padding-left: 56rpx;
        position: relative;
        &::before {
          content: '答';
          display: block;
          width: 32rpx;
          height: 32rpx;
          border-radius: 50%;
          background: $green;
          color: $white;
          text-align: center;
          font-size: $font-20;
          font-weight: bold;
          position: absolute;
          left: 0;
          top: 4rpx;
        }
      }
    }
  }
}
