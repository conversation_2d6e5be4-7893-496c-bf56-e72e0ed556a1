import Taro, { useRouter } from '@tarojs/taro';
import { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { View, Text, Image, Button } from '@tarojs/components';
import { dateFormat } from '@/utils/index';
import { UserStateType } from '@/store/groupbuy/state';
import {
  getProgramUserSubject,
  getPackagesCreate,
  getWeixinProgramPay,
  postReportReady,
  getSupManagements,
} from '@/api/groupbuy';

// import { AtCountdown } from "taro-ui";
import giftImg from '@/assets/groupbuy/index/gift-img.png';
import giftBox from '@/assets/groupbuy/thirtySix/giftBox.png';
import CountDown from '../../../../components/groupbuy/countdown';
import './index.scss';

export default function Index(props) {
  const router = useRouter();
  // 通知父组件关闭订单弹窗
  const { watchCloseOrder, payPageData, orderType } = props;
  // 定义倒计时初始值
  const [countDownNum, setCountDown] = useState(0);
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  //openid
  const openId = useSelector((state: UserStateType) => state.openid);
  //channelId
  const channelId = useSelector((state: UserStateType) => state.channelId);
  //clickId
  const clickId = useSelector((state: UserStateType) => state.clickId);
  //wxAccountNu
  const wxAccountNu = useSelector((state: UserStateType) => state.wxAccountNu);
  //routePath
  const routePath = useSelector((state: UserStateType) => state.routePath);
  //adPlatform
  const adPlatform = useSelector((state: UserStateType) => state.adPlatform);
  //sendId
  const sendId = useSelector((state: UserStateType) => state.sendId);
  //sendId
  const spreadId = useSelector((state: UserStateType) => state.spreadId);
  //支付flag
  const [payFlag, setPayFlag] = useState<boolean>(true);
  const [courseday, setCourseday] = useState<string>();
  const [period, setPeriod] = useState<number>(0);

  const dispatch = useDispatch();
  // 随材赠品展示
  const [giveaway] = useState({
    img: giftImg,
    detail: [
      '小熊马克笔',
      '小熊勾线笔',
      'AR涂色卡',
      '各类作品纸',
      '绘画成长手册',
      '学习图谱等',
    ],
    notes: '该图仅为展示，具体以实物为主，每期根据课程不同收到的随材会有所差异',
  });
  // 跳转成功页
  const goAddAddress = () => {
    // form=1代表是从艺术宝app跳转过来到购买页的
    Taro.navigateTo({
      url: `/pages/groupbuy/addAddress/index${
        router.params.from == '1' ? '?from=1' : ''
      }`,
    });
  };
  function pay(uid = userId) {
    setPayFlag(false);
    getPackagesCreate({
      type: 'ALONE',
      userId: uid,
      packagesId: orderType == '36' ? 62 : 5,
      stage: period,
      sup: payPageData.label,
      channel: channelId,
      sendId,
      spreadId,
      topicId: 3,
    }).then(res => {
      if (res.status === 'EXCEPTION') {
        setPayFlag(true);
        if (res.code == 80000053) {
          Taro.showToast({
            title: '您已购买体验课，不支持再次购买',
            icon: 'none',
            duration: 2000,
          });
        } else {
          Taro.showToast({
            title: res.errors || '下单失败！',
            icon: 'none',
            duration: 2000,
          });
        }
        return false;
      }
      if (res.code === 0) {
        clickId &&
          postReportReady({
            orderId: res.payload.order.id,
            platform: adPlatform ? adPlatform.toLocaleUpperCase() : 'WX',
            type: 'RESERVATION',
            clickId: clickId,
            url: routePath,
            params: '',
            wxAccountNu: wxAccountNu,
          });

        dispatch({
          type: 'CHANGE_ORDERID',
          orderId: res.payload.order.id,
        });
        getWeixinProgramPay({
          openId,
          orderId: res.payload.order.id,
          userId: uid,
          payType: 'AI_WXPROGRAM',
          notifyUrl: '',
        })
          .then(data => {
            if (data.code === 0) {
              const {
                timeStamp,
                nonceStr,
                package: packageId,
                paySign,
              } = data.payload;
              Taro.requestPayment({
                timeStamp,
                nonceStr,
                package: packageId,
                signType: 'HMAC-SHA256',
                paySign,
                success: function(payRes) {
                  setPayFlag(true);
                  console.log(payRes);
                  goAddAddress();
                },
                fail: function(failRes) {
                  setPayFlag(true);
                  console.log(failRes);
                  // `${failRes.errMsg}` ||
                  Taro.showToast({
                    title: '下单失败！',
                    icon: 'none',
                    duration: 2000,
                  });
                },
              });
            }
          })
          .catch(err => {
            setPayFlag(true);
            console.log(err);
          });
      }
    });
  }
  // 获取用户手机号
  const getPhoneNumber = res => {
    if (res.detail.errMsg === 'getPhoneNumber:fail user deny') {
      console.log(res.detail.errMsg);
    } else {
      const { encryptedData, iv } = res.detail;
      getProgramUserSubject({
        openId,
        encryptedData,
        iv,
      }).then(phone => {
        phone.payload.uid &&
          dispatch({
            type: 'CHANGE_USERID',
            userid: phone.payload.uid,
          });
        if (phone.payload.token)
          Taro.setStorageSync('appToken', phone.payload.token);
        phone.payload.mobile &&
          dispatch({
            type: 'CHANGE_MOBILE',
            mobile: phone.payload.mobile,
          });
        payFlag && pay(phone.payload.uid);
      });
    }
  };
  const payConfirm = () => {
    payFlag && pay();
  };
  useEffect(() => {
    if (countDownNum === 0 && props.isShowOrder) {
      setCountDown(600000);
    }
  }, [props.isShowOrder, countDownNum]);
  useEffect(() => {
    payPageData &&
      getSupManagements({ type: 'TESTCOURSE', sup: payPageData.label }).then(
        res => {
          const result = res.payload && res.payload;
          if (result) {
            const openCourseDate = dateFormat(
              result.courseDay * 1,
              'MM' + '月' + 'dd' + '日',
            );
            setCourseday(openCourseDate);
            setPeriod(+result.period);
          }
        },
      );
  }, [payPageData]);

  return (
    <View className='order'>
      <View className='title'>
        {/* <View className='quota'>
          <View className='highlight'>仅剩{quotaRandom}名</View>
          <View className='text'>剩余购课名额</View>
        </View> */}
        <View className='time'>
          <CountDown payCountDown={countDownNum} />
          <View className='text'>剩余支付时间</View>
        </View>
        <View className='close' onClick={() => watchCloseOrder(false)}></View>
      </View>
      <View className='subTitle'>
        <View className='level'>小熊美术体验版{payPageData.label}</View>
        {payPageData.courseday && (
          <View className='start-time'>{courseday}开课</View>
        )}
      </View>
      <View className='packages'>
        <View className='row'>
          <View className='label'>【优惠】</View>
          <View className='desc'>
            新人专享{orderType == '36' ? '36' : '49'}元10节课
          </View>
        </View>
        <View className='row'>
          <View className='label'>【赠品】</View>
          <View className='desc'>
            配套随材礼包
            <Text className='label'>（收货信息将在付款后填写）</Text>
          </View>
        </View>
        <View className='row'>
          <View className='label'>【提醒】</View>
          <View className='desc'>
            随材礼盒为课程配套物品，不同级别的礼盒略有差异
          </View>
        </View>
      </View>
      <View className='gift-box-row'>
        <View className='gift-box'>
          <Image
            src={orderType == '36' ? giftBox : giftImg}
            mode='widthFix'
            className='img'
          />
        </View>
        <View className='label'>
          {giveaway.detail.map((item, index) => {
            return (
              <View className='label-item' key={`label-${index}`}>
                {item}
              </View>
            );
          })}
        </View>
      </View>
      <View className='pay'>
        <View className='price-box'>
          <View className='symbol'>¥</View>
          <View className='price'>{orderType == '36' ? '36' : '49'}</View>
        </View>
        {userId ? (
          <Button className='button' onClick={payConfirm}>
            确认支付
          </Button>
        ) : (
          <Button
            className='button'
            open-type='getPhoneNumber'
            onGetPhoneNumber={getPhoneNumber}
          >
            确认支付
          </Button>
        )}
      </View>
    </View>
  );
}
