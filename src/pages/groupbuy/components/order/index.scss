@import '../../../../theme/groupbuy/common.scss';

.order {
  height: 850rpx;
  padding: 48rpx 32rpx 0 32rpx;
  box-sizing: border-box;
  .title {
    display: flex;
    justify-content: center;
    margin-bottom: 48rpx;
    padding: 0 120rpx;
    position: relative;
    .close {
      width: 100rpx;
      height: 100rpx;
      position: absolute;
      top: -32rpx;
      right: -32rpx;
      &::before,
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        display: inline-block;
        width: 36rpx;
        height: 2px;
        border-radius: 1px;
        background: #ccc;
      }
      &::before {
        transform: translate3d(-50%, -50%, 0) rotate(45deg);
      }
      &::after {
        transform: translate3d(-50%, -50%, 0) rotate(-45deg);
      }
    }
    .quota,
    .time {
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      .highlight {
        color: $red;
        font-size: $font-44;
        font-weight: 600;
      }
      .text {
        color: $silver;
        font-size: $font-28;
      }
    }
  }
  .subTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 18rpx;
    border-bottom: 1rpx solid #e6e6e6;
    margin-bottom: 24rpx;
    .level {
      color: $dim-gray;
      font-size: $font-32;
    }
    .start-time {
      color: $grey;
      font-size: $font-24;
    }
  }
  .packages {
    margin: 24rpx 0 32rpx 0;
    display: flex;
    flex-direction: column;
    .row {
      width: 100%;
      display: flex;
      align-items: center;
      margin-bottom: 12rpx;
      .label {
        align-self: flex-start;
        color: $orange;
        font-size: $font-24;
      }
      .desc {
        flex: 1;
        color: $silver;
        font-size: $font-24;
        .text {
          margin-bottom: 12rpx;
        }
      }
    }
  }
  .gift-box-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 20rpx;
    border: 3rpx dashed $light-grey;
    padding: 21rpx 26rpx;
    margin-bottom: 40rpx;
    .gift-box {
      width: 206rpx;
      height: 137rpx;
      box-sizing: border-box;
      .img {
        max-width: 100%;
        max-height: 100%;
      }
    }
    .label {
      width: 385rpx;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      .label-item {
        width: 48%;
        font-size: $font-26;
        color: $light-grey;
        position: relative;
        overflow: hidden;
        &::before {
          display: inline-block;
          content: '·';
          font-size: $font-38;
          vertical-align: middle;
          margin-right: 8rpx;
        }
      }
    }
  }
  .pay {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 28rpx;
    .price-box {
      display: flex;
      align-items: baseline;
      color: #ff7000;
      font-weight: bold;
      .symbol {
        font-size: $font-40;
        margin-right: 15rpx;
      }
      .price {
        font-size: $font-68;
      }
    }
    .button {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 216px;
      height: 80rpx;
      border-radius: 40rpx;
      background: $orange-to-yellow;
      color: $white;
      font-size: $font-32;
      font-weight: bold;
      margin-right: 0;
    }
  }
}
