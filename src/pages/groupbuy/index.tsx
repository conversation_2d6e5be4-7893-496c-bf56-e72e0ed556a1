import { useEffect, useRef, useState } from 'react';
import { AtFloatLayout, AtModal, AtModalContent } from 'taro-ui';
import { Image, Swiper, SwiperItem, View, Button } from '@tarojs/components';
import { useDispatch, useSelector } from 'react-redux';
import { levelV1, levelV2 } from '@/common/data.config';
import Taro, { useRouter } from '@tarojs/taro';
// @ts-ignore
import CommonTop from '@/components/commonTop';
import { getProgramUserSubject } from '@/api/groupbuy';

import '@/theme/custom-taro-ui.scss';
import sensors from '@/utils/sensors_data';
import { UserStateType } from '@/store/groupbuy/state';

import banner1 from '@/assets/groupbuy/twentyNine20231219/banner1.png';
import banner2 from '@/assets/groupbuy/twentyNine20231219/banner2.png';
import artPriceFull from '@/assets/groupbuy/twentyNine20231219/decorationForTen.png';

import BtnL from '@/assets/groupbuy/twentyNine20231219/ten-img-l.png';
import BtnR from '@/assets/groupbuy/twentyNine20231219/ten-img-r.gif';

import selIcon from '@/assets/groupbuy/index/gift-new.jpg';
import selIconV1 from '@/assets/groupbuy/index/gift-new-v2.png';
import redeemImg from '@/assets/groupbuy/thirtySix/redeem-img2.png';
import modalCloseImg from '@/assets/groupbuy/thirtySix/close.png';

import LayoutGiveaway from './components/giveaway';
import LayoutPromise from './components/promise';
import LayoutProblem from './components/problem';

import LayoutLevel from '../../components/level';
import LayoutOrderV1 from '../../components/orderV1';
import WxLogin from '../../components/wxlogin';
import './index.scss';
import styles from './index.module.scss';

/**
 *  @param popupType 浮窗类型
 *  0、随材赠品展示
 *  1、郑重承若
 *  2、大家都关心的问题
 *  3、选择级别（年龄）
 * */

/**
 * @param schemaType 模式类型 (预留参数)
 * 4、模式4
 * 5、模式5（待定）
 * */

/**
 *  @param levelType 级别类型
 *  0、选择级别（年龄）
 *  1、选择级别（写死描述）
 *  2、支付和级别选择一起
 * */

export default () => {
  const router = useRouter();

  const [banner, setBanner] = useState();
  const userId = useSelector((state: UserStateType) => state.userid);
  const openid = useSelector((store: UserStateType) => store.openid);
  const [artBodyPartImages] = useState([
    require('@/assets/groupbuy/twentyNine20231219/ten-img-1.png'),
    require('@/assets/groupbuy/twentyNine20231219/ten-img-2.png'),
    require('@/assets/groupbuy/twentyNine20231219/ten-img-6.png'),
    require('@/assets/groupbuy/twentyNine20231219/ten-img-3.png'),
    {
      bgImg: require('@/assets/groupbuy/twentyNine20231219/ten-img-4.png'),
      Swipers: [
        require('@/assets/groupbuy/twentyNine20231219/ten-img-4-1.png'),
        // require('@/assets/images/thirtySixForTenDays/ten-img-4-2.png')
      ],
    },
    require('@/assets/groupbuy/twentyNine20231219/ten-img-5.png'),
  ]);

  useEffect(() => {
    const { age = 1 } = router.params;
    if (age == 1) {
      setBanner(banner2);
    } else {
      setBanner(banner1);
    }
  }, []);

  const dispatch = useDispatch();

  // 显示隐藏浮窗
  const [isOpened, setIsOpened] = useState<boolean>(false);
  // 显示隐藏订单浮窗
  const [isShowOrder, setIsShowOrder] = useState<boolean>(false);
  // 浮窗类型
  const [popupType, setPopupType] = useState<number>(0);
  // 级别类型
  //   const [levelType] = useState<number>(Math.floor(Math.random() * 3));
  const [levelType] = useState<number>(1);

  // 支付页面的数据
  const [payPageData, setPayPageData] = useState<object | null>(null);

  const [showRedeemModal, setShowRedeemModal] = useState<boolean>(false);
  const [needShowRedeem, setNeedShowRedeem] = useState<boolean>(true);

  // 浮窗标题
  const [popupTitle] = useState([
    '随材赠品展示',
    '郑重承若',
    '大家都关心的问题',
    '选择级别',
  ]);

  // timer
  const timer = useRef<NodeJS.Timeout | null>(null);

  let channelId = useSelector((state: UserStateType) => state.channelId);
  let userRole = useSelector((state: UserStateType) => state.userRole);

  // 改变弹窗类型
  const changePopupType = (newPopupType) => {
    if (levelType == 2) {
      watchShowOrder(true, {});
      setIsShowOrder(true);
      return;
    }
    setIsOpened(true);
    setPopupType(newPopupType);
  };

  // 监听订单浮窗的打开和关闭
  const watchShowOrder = (state, pageData) => {
    if (!state && needShowRedeem) {
      setShowRedeemModal(true);
      setNeedShowRedeem(false);
      return;
    }
    setNeedShowRedeem(true);
    setIsShowOrder(state);
    pageData && setPayPageData(pageData);

    pageData &&
      Object.keys(pageData).length > 0 &&
      !isShowOrder &&
      sensors.track('xxys_experienceCoursePage_courseSup_click', {
        course_sup: pageData.sup,
        channel_id: channelId,
        user_role: userRole,
        buy_model: 'model_4',
        abtest: levelType == 0 ? '单年龄' : '年龄&介绍',
      });
  };
  // 关闭挽留弹窗
  const hideRedeemHandle = () => {
    setShowRedeemModal(false);
  };

  useEffect(() => {
    if (channelId) {
      timer.current && clearTimeout(timer.current);
      timer.current = setTimeout(() => {
        sensors.track('xxms_testcourse_applethomepage_view', {
          buy_model: 'model_4',
          channel_id: channelId,
          page_source: '36元粉色版',
        });
      }, 500);
    }
  }, [channelId]);

  useEffect(() => {
    //区分类型
    dispatch({
      type: 'PAY_TYPE',
      payType: '29',
    });
  }, [dispatch]);

  // 随材赠品展示
  const [giveaway] = useState({
    img: selIcon,
    sImg: selIconV1,
    detail: [
      '小熊模切',
      '超轻粘土',
      '小熊作品纸',
      '黑色勾线笔',
      '小熊马克笔',
      '重彩油画棒',
      '手指画颜料',
      '其他材料若干',
    ],
    notes: '该图仅为展示，具体以实物为主，每期根据课程不同收到的随材会有所差异',
  });

  // 手机号授权
  const getUserPhoneNumber = (res) => {
    if (res.detail.errMsg === 'getPhoneNumber:fail user deny') {
      console.log(res.detail.errMsg);
      Taro.showToast({
        title: res.detail.errMsg || '获取手机号失败!',
        icon: 'none',
      });
    } else {
      const { encryptedData, iv } = res.detail;
      Taro.showLoading();
      getProgramUserSubject({
        openId: openid,
        encryptedData,
        iv,
        subject: 'ART_APP',
      })
        .then((phone) => {
          if (phone.payload.token)
            Taro.setStorageSync('appToken', phone.payload.token);
          phone.payload.uid &&
            Taro.setStorageSync('__msb_user_id__', phone.payload.uid);
          phone.payload.uid &&
            dispatch({
              type: 'CHANGE_USERID',
              userid: phone.payload.uid,
            });
          phone.payload.mobile &&
            dispatch({
              type: 'CHANGE_MOBILE',
              mobile: phone.payload.mobile,
            });
          changePopupType(3);
        })
        .finally(() => {
          Taro.hideLoading();
        });
    }
  };
  return (
    <View className='index container w100 relative'>
      {/* <CodeSnippet /> */}
      {/* 微信登录组件 */}
      <WxLogin subject='ART_APP' isIntroduce={false} />
      {/* 头部导航栏 */}
      <CommonTop currentName='小熊美术' isIntroduce={false} />
      {/* banner */}
      <View className={styles.bannerBox}>
        <Image className='banner w100' mode='widthFix' src={banner}></Image>
      </View>
      <View className={styles.bannerpart}>
        <Image className='w100' src={artPriceFull} mode='widthFix'></Image>
      </View>

      <View className={styles.bannerContent}>
        {artBodyPartImages.map((item, index) => {
          return (
            <View className={styles.bannerContentImg} key={`body-img-${index}`}>
              {index === 4 ? (
                <View className={styles['slide-index-2']}>
                  <Image
                    className='w100'
                    mode='widthFix'
                    src={item.bgImg}
                  ></Image>
                  <View className={styles.slideInsert}>
                    <Swiper
                      className={styles.insertSwiper}
                      circular
                      // indicatorDots
                      autoplay
                    >
                      {item.Swipers.map((SlideItem, SlideIndex) => {
                        return (
                          <SwiperItem
                            key={'SlideItem' + SlideIndex}
                            className={styles.slideitem}
                          >
                            <Image
                              src={SlideItem}
                              className={styles.slide1}
                            ></Image>
                          </SwiperItem>
                        );
                      })}
                    </Swiper>
                  </View>
                </View>
              ) : (
                <Image className='w100' mode='widthFix' src={`${item}`}></Image>
              )}
            </View>
          );
        })}
      </View>

      {userId ? (
        <View
          className={styles.suctionbottom}
          onClick={() => changePopupType(3)}
        >
          <Image mode='widthFix' src={BtnL} className={styles.leftimg} />
          <Image mode='widthFix' src={BtnR} className={styles.bntgif} />
        </View>
      ) : (
        <Button
          className={styles.suctionbottom}
          openType='getPhoneNumber'
          onGetPhoneNumber={getUserPhoneNumber}
        >
          <Image mode='widthFix' src={BtnL} className={styles.leftimg} />
          <Image mode='widthFix' src={BtnR} className={styles.bntgif} />
        </Button>
      )}

      {levelType !== 2 && (
        <AtFloatLayout
          className={`'custom-float-layout' ${
            popupType === 2 && isOpened ? 'problem-layout' : ''
          }`}
          isOpened={isOpened}
          onClose={() => {
            setIsOpened(false);
          }}
          title={popupTitle[popupType]}
        >
          {popupType === 0 && <LayoutGiveaway />}
          {popupType === 1 && <LayoutPromise />}
          {popupType === 2 && <LayoutProblem />}
          {popupType === 3 && (
            <LayoutLevel
              oldLevelArray={levelType == 1 ? levelV2 : levelV1}
              levelType={levelType}
              pType='art'
              regtype='EXPERIENCE'
              watchShowOrder={watchShowOrder}
            />
          )}
        </AtFloatLayout>
      )}
      <AtFloatLayout
        className='order-layout custom-float-layout order-layout-v2'
        isOpened={isShowOrder}
        onClose={() => {
          watchShowOrder(false, null);
        }}
      >
        {payPageData && (
          <LayoutOrderV1
            isShowOrder={isShowOrder}
            watchCloseOrder={watchShowOrder}
            payPageData={payPageData}
            orderType='29'
            subject='ART_APP'
            packagesId={610}
            classNum={10}
            topicId={3}
            pType='art'
            isIntroduce={false}
            pName='美术'
            giveaway={giveaway}
            levelType={levelType}
            regtype='EXPERIENCE'
            timing={300000}
            payui={2}
          />
        )}
      </AtFloatLayout>

      <AtModal
        className='redeem-modal'
        isOpened={showRedeemModal}
        closeOnClickOverlay={false}
      >
        <Image
          className='close-img'
          src={modalCloseImg}
          onClick={hideRedeemHandle}
        ></Image>
        <AtModalContent>
          <Image className='redeem-img' src={redeemImg}></Image>
          <View className='footer'>
            <View className='btns' onClick={hideRedeemHandle}>
              继续支付
            </View>
          </View>
        </AtModalContent>
      </AtModal>
    </View>
  );
};
