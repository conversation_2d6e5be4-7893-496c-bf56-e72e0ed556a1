// import { useState } from 'react';
import { useRouter, useShareAppMessage } from '@tarojs/taro';
import { View, Image, Swiper, SwiperItem } from '@tarojs/components';
import { useMusicPay } from '@/hooks/payhook/useMusicPay.weapp';
import { useEffect, useState } from 'react';
import shareMusicIcon from '@/assets/invitetask/share-music.png';
import { wxLogin } from '@/utils/auth';

import AuthPhoneBtn from '@/components/AuthPhoneBtn';
import { useDispatch, useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import { apiGetLessonList } from '@/api/xxyyapi';
// @ts-ignore
import CommonTop from '@/components/commonTop';
import Config from '../config';

import './index.scss';

export default () => {
  var def_channelId: any = process.env.NODE_ENV == 'online' ? 13055 : 7916;
  const { params } = useRouter();
  const dispatch = useDispatch();

  const packageId = process.env.NODE_ENV === 'online' ? '754' : '754';
  const packageInfo = Config.packageData[packageId];
  const userId = useSelector((state: UserStateType) => state.userid);
  const [lessonResponse, setLessonResponse] = useState();
  const imgList: any[] = [packageInfo.priceImg, ...packageInfo.contentImg];
  const bannerList = [packageInfo.bannerBg];

  const { authError, authSuccess } = useMusicPay({
    packageId,
    topicId: '4',
  });

  const getSup = async () => {
    // 如果是即时开课就不获取排期

    if (packageInfo.isRightNow) return;

    const res = await apiGetLessonList(packageInfo.lessonListData);

    setLessonResponse(res.payload);
  };

  // 分享
  useShareAppMessage(() => {
    return {
      title: '发现宝藏了！学4天音乐互动唱跳全体验，让你家宝贝也来试试！',
      path: `/pages/music/introduce/index?channelId=${params.channelId ||
        def_channelId}&come=${params.come || ''}`,
      imageUrl: shareMusicIcon,
      success() {
        console.log('分享成功');
      },
      fail() {
        console.log('分享失败');
      },
    };
  });

  useEffect(() => {
    // 天天领用户分享自己点进来 或已登录过 固判断下是否需要在获取一次openid
    !userId && wxLogin();
    if (params.channelId) {
      def_channelId = params.channelId;
      dispatch({
        type: 'CHANGE_CHANNELID',
        channelId: params.channelId,
      });
    }
  }, []);

  useEffect(() => {
    getSup();
  }, [userId]);

  return (
    <View className='index container w100 relative'>
      {/* 头部导航栏 */}
      <CommonTop
        currentName='小熊音乐'
        isIntroduce={false}
        channelId={def_channelId}
      />
      <Swiper className='swiper-banner' circular autoplay interval={3000}>
        {bannerList.map((e, i) => {
          return (
            <SwiperItem key={i}>
              <Image mode='widthFix' src={e} className='bannerImg'></Image>
            </SwiperItem>
          );
        })}
      </Swiper>
      {imgList.map((e, i) => {
        return (
          <View
            className={i === 0 ? 'bannerDiv priceimg' : 'bannerDiv'}
            key={i}
          >
            <Image mode='widthFix' src={e} className='bannerImg'></Image>
          </View>
        );
      })}
      <AuthPhoneBtn authError={authError} authSuccess={() => authSuccess({})}>
        <View className='suction-bottom-wrapper'>
          <View className='suction-bottom'>
            <Image
              mode='widthFix'
              src={packageInfo.bottomPayImg}
              className='pay-img'
            />
            <Image
              mode='widthFix'
              src={packageInfo.buyImg}
              className='pay-fixed'
            />
          </View>
        </View>
      </AuthPhoneBtn>
    </View>
  );
};
