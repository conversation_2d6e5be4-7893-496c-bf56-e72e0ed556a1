// import { useState } from 'react';
import { useRouter, useShareAppMessage } from '@tarojs/taro';
import { View, Image, Button, Swiper, SwiperItem } from '@tarojs/components';
import { useMusicPay } from '@/hooks/payhook/useMusicPay.alipay';

import banner1 from '@/assets/music/nineteen/banner.png';
import price from '@/assets/music/nineteen/price.png';
import img1 from '@/assets/music/nineteen/img-01.png';
import img2 from '@/assets/music/nineteen/img-02.png';
import img3 from '@/assets/music/nineteen/img-03.png';
import img4 from '@/assets/music/nineteen/img-04.png';
import img5 from '@/assets/music/nineteen/img-05.png';
import img6 from '@/assets/music/nineteen/img-06.png';
import bottomPay from '@/assets/music/nineteen/bottom-01.png';
import buy from '@/assets/groupbuy/index/buy.gif';

// @ts-ignore
import CommonTop from '@/components/commonTop';

import './index.scss';

export default () => {
  const defChannelList = {
    alipay: {
      development: '3438',
      dev: '3438',
      test: '3438',
      gray: '9087',
      online: '9087',
    },
  };
  const def_channelId =
    defChannelList[process.env.TARO_ENV][process.env.NODE_ENV];
  const { params } = useRouter();

  const packageId = process.env.NODE_ENV === 'online' ? '889' : '884';

  console.log(params, useRouter());
  const imgList: string[] = [price, img1, img2, img3, img4, img5, img6];
  const bannerList = [banner1];
  // const userId = useSelector((state: UserStateType) => state.userid);

  const { authError, authSuccess } = useMusicPay({
    packageId,
    isGoAddress: true,
    topicId: '4',
  });

  // 分享
  useShareAppMessage(() => {
    return {
      title: '小熊音乐',
      path: `/pages/music/nineteen/index?channelId=${params.channelId ||
        def_channelId}&come=${params.come || ''}`,
      success() {
        console.log('分享成功');
      },
      fail() {
        console.log('分享失败');
      },
    };
  });

  return (
    <View className='index container w100 relative'>
      {/* 头部导航栏 */}
      <CommonTop currentName='' isIntroduce={false} channelId={def_channelId} />
      <Swiper className='swiper-banner' circular autoplay interval={3000}>
        {bannerList.map((e, i) => {
          return (
            <SwiperItem key={i}>
              <Image mode='widthFix' src={e} className='bannerImg'></Image>
            </SwiperItem>
          );
        })}
      </Swiper>
      {imgList.map((e, i) => {
        return (
          <View
            className={i === 0 ? 'bannerDiv priceimg' : 'bannerDiv'}
            key={i}
          >
            <Image mode='widthFix' src={e} className='bannerImg'></Image>
          </View>
        );
      })}
      <View className='suction-bottom-wrapper'>
        <Button
          className='suction-bottom'
          openType='getAuthorize'
          scope='phoneNumber'
          onError={authError}
          onGetAuthorize={authSuccess}
        >
          <Image mode='widthFix' src={bottomPay} className='pay-img' />
          <Image mode='widthFix' src={buy} className='pay-fixed' />
        </Button>
      </View>
    </View>
  );
};
