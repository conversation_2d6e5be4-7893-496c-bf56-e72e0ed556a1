// import { useState } from 'react';
import { useRouter, useShareAppMessage } from '@tarojs/taro';
import { View, Image, Button, Swiper, SwiperItem } from '@tarojs/components';
import { useMusicPay } from '@/hooks/payhook/useMusicPay.alipay';

import banner1 from '@/assets/music/index/banner-1.png';
import banner2 from '@/assets/music/index/banner-2.png';
import banner3 from '@/assets/music/index/banner-3.png';
import price from '@/assets/music/index/price.png';
import img1 from '@/assets/music/index/img-1.png';
import img2 from '@/assets/music/index/img-2.png';
import img3 from '@/assets/music/index/img-3.png';
import img4 from '@/assets/music/index/img-4.png';
import img5 from '@/assets/music/index/img-5.png';
import img6 from '@/assets/music/index/img-6.png';
import bottomPay from '@/assets/music/index/bottomPay.png';
import buy from '@/assets/groupbuy/index/buy.gif';

// @ts-ignore
import CommonTop from '@/components/commonTop';

import './index.scss';

export default () => {
  const defChannelList = {
    alipay: {
      development: '4243',
      dev: '4243',
      test: '7047',
      gray: '9087',
      online: '9087',
    },
  };
  const def_channelId =
    defChannelList[process.env.TARO_ENV][process.env.NODE_ENV];
  const { params } = useRouter();

  console.log(params, useRouter());
  const imgList: string[] = [price, img1, img2, img3, img4, img5, img6];
  const bannerList = [banner1, banner2, banner3];
  // const userId = useSelector((state: UserStateType) => state.userid);

  const { authError, authSuccess } = useMusicPay({ isGoAddress: false });

  // 分享
  useShareAppMessage(() => {
    return {
      title: '小熊音乐',
      path: `/pages/music/index?channelId=${params.channelId ||
        def_channelId}&come=${params.come || ''}`,
      success() {
        console.log('分享成功');
      },
      fail() {
        console.log('分享失败');
      },
    };
  });

  return (
    <View className='index container w100 relative'>
      {/* 头部导航栏 */}
      <CommonTop
        currentName='小熊美术'
        isIntroduce={false}
        channelId={def_channelId}
      />
      <Swiper className='swiper-banner' circular autoplay interval={3000}>
        {bannerList.map((e, i) => {
          return (
            <SwiperItem key={i}>
              <Image mode='widthFix' src={e} className='bannerImg'></Image>
            </SwiperItem>
          );
        })}
      </Swiper>
      {imgList.map((e, i) => {
        return (
          <View
            className={i === 0 ? 'bannerDiv priceimg' : 'bannerDiv'}
            key={i}
          >
            <Image mode='widthFix' src={e} className='bannerImg'></Image>
          </View>
        );
      })}
      <View className='suction-bottom-wrapper'>
        <Button
          className='suction-bottom'
          openType='getAuthorize'
          scope='phoneNumber'
          onError={authError}
          onGetAuthorize={authSuccess}
        >
          <Image mode='widthFix' src={bottomPay} className='pay-img' />
          <Image mode='widthFix' src={buy} className='pay-fixed' />
        </Button>
      </View>

      <view className='footer'>
        <View>联系电话：************</View>
        <View>浙公网安备 33010802011501号｜浙ICP备 2020039007号-6</View>
        <View>杭州艺旗音乐宝科技有限公司/或其相关联公司及特许人版权所有</View>
        <View>联系地址：浙江省杭州市滨江区三维大厦A座</View>
      </view>
    </View>
  );
};
