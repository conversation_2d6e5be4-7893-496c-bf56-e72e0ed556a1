@import '../../../theme/groupbuy/common.scss';

.add-teacher {
  font-size: 0;
  background: #4c44f8;
  min-height: 100vh;
  box-sizing: border-box;
  .tip {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 72rpx;
    font-size: 30rpx;
    font-weight: 400;
    color: #5b54fe;
    background: #f2f2ff;

    .success {
      width: 40rpx;
      height: 40rpx;
      margin-right: 10rpx;
    }
  }
  .banner {
    width: 100%;
    height: 360rpx;
  }
  .code-wrapper {
    margin: -30rpx auto 0;
    width: 670rpx;
    height: 724rpx;
    background: #ffffff;
    border-radius: 40rpx;
    overflow: hidden;
    .qr-code {
      display: block;
      margin: 90rpx auto 0;
      width: 380rpx;
      height: 380rpx;
      .qr-code-img {
        width: 100%;
        height: 100%;
      }
    }
    .qr-tips {
      margin-top: 20rpx;
      font-size: 24rpx;
      color: #888888;
      text-align: center;
    }
    .btn {
      position: relative;
      margin: 60rpx auto;
      padding-left: 30rpx;
      width: 540rpx;
      height: 80rpx;
      line-height: 80rpx;
      background: #f2f2ff;
      font-size: 28rpx;
      font-weight: 400;
      color: #4e4e4e;
      border-radius: 20rpx;

      .copy {
        position: absolute;
        right: 30rpx;
        top: 16rpx;
        width: 108rpx;
        height: 48rpx;
        margin-left: 88rpx;
        line-height: 48rpx;
        border-radius: 25rpx;
        border: 1rpx solid #5b54fe;
        text-align: center;
        color: #5b54fe;
      }
    }
  }
}
