import Taro, { useShareAppMessage } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import { View, Image } from '@tarojs/components';
import { useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import { getWriteTeacherInfo } from '@/api/groupbuy';
import success from '@/assets/music/addTeacher/success.png';
import banner from '@/assets/music/addTeacher/banner.png';
import './index.scss';

export default function AddTeacher() {
  //redux
  const orderId = useSelector((state: UserStateType) => state.orderId);

  //channelId
  const channelId = useSelector((state: UserStateType) => state.channelId);

  // 老师微信号
  const [teacherNo, setTeacherNo] = useState('');
  // 老师二维码
  const [teacherCode, setTeacherCode] = useState('');

  const isWx = process.env.TARO_ENV === 'weapp';
  //调用获取老师微信号接口
  useEffect(() => {
    orderId &&
      getWriteTeacherInfo({
        orderNo: orderId,
        subject: 'MUSIC_APP',
      }).then(res => {
        if (res.code === 0) {
          res.payload.teacherWeChat && setTeacherNo(res.payload.teacherWeChat);
          res.payload.teacherWeChatQrCode &&
            setTeacherCode(res.payload.teacherWeChatQrCode);
        }
      });
  }, [orderId]);

  //复制老师微信
  function copyData() {
    Taro.setClipboardData({
      data: teacherNo,
      success: function() {
        Taro.showToast({
          title: '您已复制老师微信号,打开微信首页-右上角添加好友',
          icon: 'none',
          duration: 2000,
        });
      },
    });
  }
  //分享配置
  useShareAppMessage(() => {
    return {
      title: '小熊音乐',
      path: `/pages/music/addTeacher/index?channelId=${channelId}`,
      success: function() {},
      fail: function() {},
    };
  });

  return (
    <View className='add-teacher'>
      <View className='tip'>
        <Image className='success' src={success}></Image>
        报名成功
      </View>
      <Image mode='widthFix' className='banner' src={banner}></Image>
      <View className='code-wrapper'>
        <View className='qr-code'>
          {isWx ? (
            <Image
              mode='widthFix'
              className='qr-code-img'
              src={teacherCode}
              showMenuByLongpress
            ></Image>
          ) : (
            <Image
              mode='widthFix'
              className='qr-code-img'
              src={teacherCode}
            ></Image>
          )}
        </View>
        {!isWx && (
          <>
            <View className='qr-tips'>截图保存二维码在微信中打开</View>
            <View className='btn'>
              老师微信号：{teacherNo}
              <View className='copy' onClick={copyData}>
                复制
              </View>
            </View>
          </>
        )}
      </View>
    </View>
  );
}
