const config = {
  packageData: {
    754: {
      // 获取排期参数
      price: '0.1',
      lessonListData: {
        sup: 'S3',
        type: 'CATEGORYTESTCOURSE',
        category: '754',
        subject: 'MUSIC_APP',
      },
      // 轮播背景图
      bannerBg: require('@/assets/music/introduce/banner-v1.png'),
      // 轮播图
      carousel: [],
      // 中间区域的图片
      contentImg: Array.from({ length: 12 }).map((_item, idx) =>
        require(`@/assets/music/introduce/img-box${idx + 1}.png`),
      ),
      // 价格图
      priceImg: require('@/assets/music/introduce/price-v1.png'),
      bottomPayImg: require('@/assets/music/introduce/btn-price-0.1.png'),
      buyImg: require('@/assets/groupbuy/index/buy.gif'),
      specialPrice: ``,
      orderImg: ``, // 购买弹层展示图片
      isAddress: false, // 是否进入填写地址页
      isRightNow: true, // 是否即时开课 是则绕过获取排期接口
    },
  },
};

/**
 * 针对 19元、9.9拼团两种类型
 * 1 19元
 * 2 9.9拼团
 * */
export const getPackageId = type => {
  const isLive = process.env.SERVER_ENV === 'live';
  const isTest = process.env.SERVER_ENV === 'test';
  const keyVal = {
    1: !isTest ? 886 : 882,
    2: isLive ? 824 : 824,
  };
  return keyVal[type];
};

export default config;
