.loginindex {
  min-height: 100vh;
  background: #fff;
  box-sizing: border-box;
  padding: 30px 30px 56px 30px;

  // 头部提示信息
  .tips {
    width: 100%;
    height: 96px;
    background: #fffcf7;
    display: flex;
    align-items: center;
    margin-bottom: 64px;
    box-sizing: border-box;
    padding-top: 20px;
    line-height: 30px;
    .tipsimg {
      margin-left: 15px;
    }
    .text {
      margin-left: 15px;
      font-size: 24px;
      .tipsText {
        font-weight: 500;
      }
      .longText {
        font-weight: 500;
        color: #666666;
      }
    }
  }
  // 输入手机号码
  .phone {
    width: 690px;
    height: 96px;
    background: #f7f7f7;
    border-radius: 48px;
    margin-bottom: 40px;
    display: flex;
    align-items: center;
    .number {
      width: 84px;
      height: 45px;
      border-right: 1px solid #e6e6e6;
      font-size: 32px;
      margin-left: 46px;
      line-height: 45px;
    }
    .inputContent {
      flex: 1;
      margin-left: 22px;
    }
  }
  // 输入验证码
  .code {
    width: 690px;
    height: 96px;
    background: #f7f7f7;
    border-radius: 48px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    .inputContent {
      margin-left: 44px;
      input {
        background: none;
        outline: none;
        border: none;
        width: 300px;
        // 防止输入框输入卡顿
        -webkit-user-select: auto;
        font-size: 32px;
        // input框placeholder文字，垂直居中
        line-height: normal;
      }
      input::placeholder {
        font-size: 32px;
        color: #999;
      }
    }
    .obtainCode {
      width: 200px;
      height: 45px;
      font-size: 32px;
      color: #ff9c31;
      border-left: 1px solid #e6e6e6;
      margin-right: 36px;
      text-align: center;
      line-height: 45px;
    }
  }
  // 登录按钮
  .loginBtn {
    margin-top: 40px;
    width: 690px;
    height: 96px;
    background: #cccccc;
    border-radius: 48px;
    text-align: center;
    line-height: 96px;
    font-size: 36px;
    color: #fff;
    font-weight: 500;
    &.enable {
      background: #ffbc00;
      color: #fff;
    }
  }
  // 底部协议
  .topText {
    margin-top: 400px;
    font-size: 24px;
    color: #999;
    text-align: center;
    margin-bottom: 16px;
  }
  .bottomAgreement {
    font-size: 24px;
    color: #3e8eff;
    text-align: center;
  }
}
