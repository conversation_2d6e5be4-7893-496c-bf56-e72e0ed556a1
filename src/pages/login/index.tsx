import Taro, { useRouter } from '@tarojs/taro';
import { View, Input, Text } from '@tarojs/components';
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import { AtIcon, AtToast } from 'taro-ui';
import { sendCodeV2, getLoginV2 } from '@/api/groupbuy';
import { ToastStausTypes } from '@/types/types';

import './index.scss';

export default () => {
  const { params } = useRouter();
  const dispatch = useDispatch();
  const channelId = useSelector((state: UserStateType) => state.channelId);
  const sendId = useSelector(
    (state: UserStateType) => state.sendId || params.sendId || '',
  );
  const openid = useSelector((store: UserStateType) => store.openid);
  const [mobile, setMobile] = useState<string>('');
  const [code, setCode] = useState<string>('');
  // toast 提示
  const [ToastStaus, setToastStaus] = useState<ToastStausTypes>({
    status: undefined,
    text: '',
    duration: 1000,
    isOpened: false,
  });
  const [codeVal, setCodeVal] = useState<string>('获取验证码');
  const [disabled, setDisabled] = useState<boolean>(false);

  const [codecountdown, setCodecountdown] = useState<number>(0);
  const [isLoging, setIsLoging] = useState<boolean>(false);

  const inputMobile = value => {
    setMobile(value.detail.value);
  };
  const inputCode = value => {
    setCode(value.detail.value);
  };
  // 获取验证码倒计时
  useEffect(() => {
    let timer: NodeJS.Timer | null = null;
    timer = setInterval(() => {
      setCodecountdown(countdown => {
        if (countdown <= 0) {
          clearInterval(Number(timer));
          setCodeVal('获取验证码');
          setDisabled(false);
        } else {
          --countdown;
          setCodeVal(`${countdown}`);
          setDisabled(true);
        }
        return countdown;
      });
    }, 1000);
    return () => {
      clearInterval(Number(timer));
    };
  }, [codecountdown]);
  //获取验证码
  const getCode = () => {
    if (disabled) {
      changeToastStaus({
        text: `请${codecountdown}秒后重试`,
        status: 'error',
      });
      return;
    }
    const telStr = /^1(3|4|5|6|7|8|9)\d{9}$/;
    if (!telStr.test(mobile)) {
      changeToastStaus({
        text: `请输入正确手机号`,
        status: 'error',
      });
      return false;
    }
    // 手机号格式正确,调取获取验证码接口
    sendCodeV2({ mobile: mobile, subject: 'WRITE_APP', bizOrigin: 'H5' })
      .then(res => {
        // 获取验证码后记录次数
        setCodecountdown(60);
      })
      .catch(err => {
        changeToastStaus({
          text: `网络异常`,
          status: 'error',
        });
      });
  };

  // 点击登录/确定按钮
  const loginClick = () => {
    if (isLoging) return;
    const telStr = /^1(3|4|5|6|7|8|9)\d{9}$/;
    if (!telStr.test(mobile)) {
      changeToastStaus({
        text: `请输入正确手机号`,
      });
      return false;
    }
    if (!code || code.length < 6) {
      changeToastStaus({
        text: `请输入正确验证码`,
      });
      return false;
    }
    setIsLoging(true);
    // 验证码输入正确,调取登录接口
    // 调用艺术登录接口，返回的是美术信息，
    // 需注意使用身份等信息时要使用userId单独获取写字身份下的状态
    getLoginV2({
      mobile: mobile,
      code: code,
      weixinOpenId: openid,
      channel: channelId,
      sendId: sendId,
    })
      .then(res => {
        const { payload, status } = res;
        if (payload && status == 'OK') {
          changeToastStaus({
            text: `登录成功`,
          });
          if (payload.id) {
            dispatch({
              type: 'CHANGE_USERID',
              userid: payload.id,
            });
            Taro.setStorageSync('__msb_user_id__', payload.id);
          }
          payload.id &&
            dispatch({
              type: 'CHANGE_USERID',
              userid: payload.id,
            });
          payload.mobile &&
            dispatch({
              type: 'CHANGE_MOBILE',
              mobile: payload.mobile,
            });

          if (payload.token) {
            Taro.setStorageSync('appToken', payload.token);
          }
          // 如果传入backUrl，则回跳backUrl
          let backUrl = params.backUrl;
          if (backUrl) {
            Taro.navigateTo({
              url: '/' + decodeURIComponent(backUrl),
            });
          } else {
            // 返回上一页
            Taro.navigateBack();
          }
        }
        setIsLoging(false);
      })
      .catch(err => {
        setIsLoging(false);
        changeToastStaus({
          text: `网络异常`,
        });
      });
  };

  const handleAgreementClick = (agreementName: string) => {
    let href = `https://www.xiaoxiongmeishu.com/h5/account/allAgreement?agreementName=${agreementName}`;
    Taro.navigateTo({
      url: `/pages/webview/index?url=${encodeURIComponent(href)}`,
    });
  };

  const changeToastStaus = value => {
    setToastStaus({
      ...ToastStaus,
      ...value,
      isOpened: true,
    });
  };

  return (
    <View className='loginindex'>
      {/* 头部提示信息 */}
      <View className='tips'>
        <AtIcon
          className='tipsimg'
          value='alert-circle'
          color='#ff9c31'
        ></AtIcon>
        <View className='text'>
          <Text className='tipsText'>提示：</Text>
          <Text className='longText'>
            输入新手机号和验证码即可在APP和服务号内同步该账号课程信息
          </Text>
        </View>
      </View>
      {/* 输入手机号码 */}
      <View className='phone'>
        <View className='number'>+86</View>
        <View className='inputContent'>
          <Input
            type='number'
            placeholder='请输入手机号'
            maxlength={11}
            onInput={inputMobile}
          />
        </View>
      </View>
      {/* 输入验证码 */}
      <View className='code'>
        <View className='inputContent'>
          <Input
            type='number'
            placeholder='请输入验证码'
            maxlength={6}
            onInput={inputCode}
          />
        </View>
        <View className='obtainCode' onClick={() => getCode()}>
          {codeVal}
        </View>
      </View>
      {/* 登录按钮  账号绑定*/}
      <View
        className={`$'loginBtn' ${code.length === 6 ? 'enable' : ''}`}
        onClick={() => loginClick()}
      >
        登录
      </View>
      {/* <LoginProblemsTipsCom /> */}
      {/* 底部协议 */}
      <View className='topText'>注册即代表您已同意</View>
      <View className='bottomAgreement'>
        <Text
          className='registration'
          onClick={() => handleAgreementClick('registAgreement')}
        >
          《用户注册协议 》
        </Text>
        <Text
          className='privacy'
          onClick={() => handleAgreementClick('privacyprotocol')}
        >
          《用户隐私协议》
        </Text>
        <Text
          className='children'
          onClick={() => handleAgreementClick('childrenSprivacy')}
        >
          《未成年人个人信息处理规则》
        </Text>
      </View>
      <AtToast {...ToastStaus}></AtToast>
    </View>
  );
};
