// @import '../../../theme/groupbuy/common.scss';
// @import '../../../../theme/normalGroup/common.scss';
page {
  background: #ffedc2;
}
.container {
  background: #ffedc2;
  .head-v {
    // background: url('../../assets/invitationCourtesy/head-v1.png')
    //   no-repeat 0 0 /100%;
    position: relative;
    .img {
      width: 100%;
    }
    .rule {
      width: 83px;
      height: 40px;
      background: #fff;
      border-radius: 23px 0px 0px 23px;
      position: absolute;
      top: 20px;
      right: 0;
      font-size: 22px;
      color: #fa6a44;
      line-height: 42px;
      text-align: center;
      border: none;
    }
  }
  .invite-part {
    width: 702px;
    height: 144px;
    background-color: #fff;
    border-radius: 24px;
    display: flex;
    margin: 0 auto;
    margin-top: -140px;
    align-items: center;
    padding: 0 40px;
    box-sizing: border-box;
    position: relative;
    z-index: 9;
    &-item {
      flex: 1;
      display: flex;
      align-items: center;
      &:nth-child(1) {
        margin-right: 30px;
        border-right: 1px solid #e8e8e8;
      }
      &-icon {
        width: 64px;
        height: 64px;
        margin-right: 18px;
        .icon-img {
          width: 100%;
          height: 100%;
        }
      }
      &-info {
        font-size: 28px;
        color: #333;
        line-height: 30px;
        Text {
          font-size: 48px;
          font-weight: 600;
          color: #fa6c3a;
        }
        .info-details {
          margin-top: 6px;
          color: #666;
          font-size: 24px;
          font-weight: 400;
        }
      }
    }
  }
  .invite-part-200 {
    margin-top: -260px;
  }
  .invite-icon-part {
    width: 691px;
    // height: 120px;
    background-color: #fff;
    border-radius: 24px;
    margin: 20px auto;
    text-align: center;
    padding: 40px 32px;
    box-sizing: border-box;
    .img {
      width: 100%;
      margin-bottom: 30px;
    }
    .img-part-title {
      width: 580px;
      height: 48px;
    }
  }
  .share-btns {
    display: flex;
    justify-content: space-between;
    padding: 0 9px;
    .share-btn {
      width: 292px;
      height: 84px;
      border: 0;
      padding: 0;
      background-color: transparent;
      &::after {
        border: none;
      }
      .btn {
        margin-bottom: 0;
        height: 84px;
        margin: 0;
      }
    }
  }
  .fixed-btn {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 167px;
    z-index: 9;
    .share-btn-s {
      width: 734px;
      height: 132px;
      margin: 20px auto;
      animation: movepoint 2s infinite;
      border: 0;
      padding: 0;
      background-color: transparent;
      &::after {
        border: none;
      }
      image {
        width: 100%;
        height: 100%;
      }
    }
    .inviteMore {
      width: 312px;
      height: 56px;
      position: absolute;
      right: 30px;
      top: -10px;
      z-index: 10;
    }
  }
  .product-list {
    .new-list-title {
      padding: 38px 34px 26px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .new-title {
        width: 198px;
        .img {
          width: 100%;
        }
      }
      .new-icon-income {
        background-color: #fffad3;
        height: 48px;
        line-height: 48px;
        color: #a24c00;
        font-size: 26px;
        border-radius: 48px;
        font-weight: 600;
        display: flex;
        box-sizing: border-box;
        padding-right: 20px;
        align-items: center;
        img {
          width: 30px;
          margin: 0 8px 0 14px;
        }
      }
    }
  }
}

@keyframes movepoint {
  0% {
    transform: scale(1);
  }

  25% {
    transform: scale(1.05);
  }

  50% {
    transform: scale(1);
  }

  75% {
    transform: scale(1.05);
  }
}
