import { useEffect, useState } from 'react';
import Ta<PERSON>, { useRouter, useShareAppMessage } from '@tarojs/taro';
import { AtFloatLayout } from 'taro-ui';
import { Button, Image, View, Text } from '@tarojs/components';
import CommonTop from '@/components/commonTop/index.weapp';
import Rule from '@/components/Rule';
import sensors from '@/utils/sensors_data';

/** 图片部分 **/
import icon_invite_num from '@/assets/invitationCourtesy/icon-invite-num.png';
import icon_income from '@/assets/invitationCourtesy/icon-income.png';
import invite_icon_part_title from '@/assets/invitationCourtesy/invite-icon-part-title.png';
import invite_icon_part_content from '@/assets/invitationCourtesy/invite-icon-part-content.png';
import new_share_fc from '@/assets/invitationCourtesy/new-share-fc.png';
import new_share_fr from '@/assets/invitationCourtesy/new-share-fr.png';
import headV1 from '@/assets/invitationCourtesy/head-v1.png';
import shareImg from '@/assets/normalGroup/art/share-img-********.jpg';
import shareBtn from '@/assets/invitationCourtesy/shareBtn.png';
/** 图片部分完 **/

import '@/theme/custom-taro-ui.scss';

import {
  getAccountInfo,
  getInviteInfo,
  getProgramUserSubject,
} from '@/api/groupbuy';
import { useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import { wxLogin } from '@/utils/auth';
import ExchangeSwiper from '../invitetask/components/exchangeSwiper';
import SharePoster from './components/sharePoster';

import './index.scss';

export default () => {
  const userId = useSelector((state: UserStateType) => state.userid);
  const openId = useSelector((state: UserStateType) => state.openid);
  const [visiblePoster, setVisiblePoster] = useState(false);
  const [ruleShow, setRuleShow] = useState(false);
  const [earningsCountNums, setEarningsCountNums] = useState(0);
  const [balanceNums, setBalanceNums] = useState(0);
  const router = useRouter();

  const toInviteRecord = () => {
    sensors.track('xxys_piecegroupPage_Piecingtogetherpage_click', {
      operation_type: '重新发起',
    });
    Taro.showToast({
      title: '请前往APP-我的-邀请有礼里查看',
      icon: 'none',
    });
  };

  const toInviteProfit = () => {
    sensors.track('xxys_piecegroupPage_Piecingtogetherpage_click', {
      operation_type: '重新发起',
    });
    Taro.showToast({
      title: '请前往APP-我的-邀请有礼里查看',
      icon: 'none',
    });
  };

  const newHandler = (type, name) => {
    if (type === 'Fcircle') {
      setVisiblePoster(true);
    }
    const xzChannelId = router.params.xzChannelId;
    const msChannelId = router.params.msChannelId;
    const entrance_page = router.params.entrance_page;
    sensors.track('xxys_posterCenterPage_view', {
      xz_channel_id: xzChannelId,
      channel_id: msChannelId,
      entrance_page: entrance_page,
      activity_type: '邀请有礼',
      // course_subject: this.subject,
    });
  };

  useEffect(() => {
    !userId && wxLogin();
  }, []);

  useEffect(() => {
    if (userId) {
      const xzChannelId = router.params.xzChannelId;
      const msChannelId = router.params.msChannelId;
      const entrance_page = router.params.entrance_page;
      sensors.track('xxys_inviteGiftPage_view', {
        xz_channel_id: xzChannelId || '',
        channel_id: msChannelId || '',
        entrance_page: entrance_page || '',
        user_role: '可参与',
        buy_role: '系统课',
        user_region: '国内',
        ad_type: '50元',
      });
      getInviteInfo({ uid: userId }).then((res) => {
        if (res.code === 0) {
          setEarningsCountNums(res.payload.inviteCount);
        }
      });
      getAccountInfo({ userId, accountType: 'CASH' }).then((res) => {
        if (res.code === 0) {
          setBalanceNums(res.payload.balance);
        }
      });
    }
  }, [userId]);

  // 'xxys_posterCenterPage_view', {
  //   xz_channel_id: this.xzChannelId,
  //   channel_id: this.msChannelId,
  //   entrance_page: this.entrance_page,
  //   activity_type: '邀请有礼',
  //   course_subject: this.subject
  // }
  // 获取用户手机号
  const getPhoneNumber = (res) => {
    if (res.detail.errMsg === 'getPhoneNumber:fail user deny') {
      console.log(res.detail.errMsg);
    } else {
      const { encryptedData, iv } = res.detail;
      // getProgramUserSubject兼容getProgramUser
      getProgramUserSubject({
        openId,
        encryptedData,
        iv,
        subject: 'ART_APP',
      }).then(() => {
        wxLogin();
      });
    }
  };

  useShareAppMessage(() => {
    const _channelId =
      router.params.channelId || router.params.msChannelId || 13073;
    return {
      title: '今天可以免费领画材礼包，名额有限，快给宝贝领取！',
      path: `/pages/normalGroup/art/index?channelId=${_channelId}&sendId=${userId}`,
      imageUrl: shareImg,
    };
  });

  return (
    <View className='container'>
      <CommonTop currentName='邀请有礼' isIntroduce={false} />
      <View>
        <View className='head head-v'>
          <ExchangeSwiper />
          <View className='rule' onClick={() => setRuleShow(!ruleShow)}>
            规则
          </View>
          <Image src={headV1} className='img' mode='widthFix' />
        </View>

        <View className='invite-part'>
          <View className='invite-part-item'>
            <View className='invite-part-item-icon'>
              <Image src={icon_invite_num} className='icon-img' />
            </View>
            <View className='invite-part-item-info'>
              <View className='peo-num'>
                <Text>{earningsCountNums}</Text> 人
              </View>
              <View className='info-details' onClick={toInviteRecord}>
                邀请记录 {'>'}
              </View>
            </View>
          </View>
          <View className='invite-part-item'>
            <View className='invite-part-item-icon'>
              <Image src={icon_income} className='icon-img' />
            </View>
            <View className='invite-part-item-info'>
              <View className='peo-num'>
                <Text>{balanceNums}</Text> 元
              </View>
              <View className='info-details' onClick={toInviteProfit}>
                邀请收入 {'>'}
              </View>
            </View>
          </View>
        </View>
        <View className='invite-icon-part'>
          <Image src={invite_icon_part_title} className='img img-part-title' />
          <Image
            src={invite_icon_part_content}
            className='img'
            mode='widthFix'
          />
          <View className='share-btns'>
            {userId ? (
              <View
                className='share-btn'
                onClick={() => newHandler('Fcircle', '朋友圈')}
              >
                <Image src={new_share_fc} className='btn img' />
              </View>
            ) : (
              <Button
                className='share-btn'
                open-type='getPhoneNumber'
                onGetPhoneNumber={getPhoneNumber}
              >
                <Image src={new_share_fc} className='btn img' />
              </Button>
            )}
            {userId ? (
              <Button
                openType='share'
                className='share-btn'
                onClick={() => newHandler('wechat', '微信好友')}
              >
                <Image src={new_share_fr} className='btn img' />
              </Button>
            ) : (
              <Button
                className='share-btn'
                open-type='getPhoneNumber'
                onGetPhoneNumber={getPhoneNumber}
              >
                <Image src={new_share_fr} className='btn img' />
              </Button>
            )}
          </View>
        </View>

        <View className='fixed-btn'>
          <Button
            className='share-btn-s'
            openType='share'
            onClick={() => newHandler('wechat', '微信好友')}
          >
            <Image src={shareBtn} />
          </Button>
          <Image
            src='https://fe-cdn.xiaoxiongmeishu.com/apph5/live/202403141646/assets/img/inviteMore.93d0357c.png'
            alt=''
            className='inviteMore'
          />
        </View>
      </View>
      <AtFloatLayout
        className='address-area-float'
        isOpened={ruleShow}
        title='— 活动规则 —'
        onClose={() => setRuleShow(false)}
      >
        <Rule />
      </AtFloatLayout>
      {/* 海报分享 */}
      <SharePoster
        visible={visiblePoster}
        closeModal={() => setVisiblePoster(false)}
      />
    </View>
  );
};
