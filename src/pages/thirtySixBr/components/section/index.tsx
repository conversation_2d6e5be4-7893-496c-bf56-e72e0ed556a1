import { Image, Swiper, SwiperItem, Text, View } from '@tarojs/components';
import { useState } from 'react';
import Banner from '@/assets/groupbuy/thirtySixBr/banner.png';
import PriceInfoImg from '@/assets/groupbuy/thirtySixBr/price-info.png';
import sign from '@/assets/groupbuy/index/sign.png';

import './index.scss';

const imgList = [
  require('@/assets/groupbuy/thirtySixBr/section1.png'),
  require('@/assets/groupbuy/thirtySixBr/section2.png'),
  require('@/assets/groupbuy/thirtySixBr/section3.png'),
  require('@/assets/groupbuy/thirtySixBr/section4.png'),
  require('@/assets/groupbuy/thirtySixBr/section5.png'),
];

const Section = () => {
  return (
    <>
      <View className='header'>
        <View className='banner'>
          <Image mode='widthFix' src={Banner} className='banner'></Image>
        </View>
      </View>
      <View>
        <View>
          <Image mode='widthFix' src={PriceInfoImg} className='payText'></Image>
        </View>
        <View className='bannerContent'>
          <View className='bannerList'>
            {imgList.map((url: string, i) => {
              return (
                <View className='bannerDiv' key={i}>
                  <Image
                    mode='widthFix'
                    src={url}
                    className='bannerImg'
                  ></Image>
                </View>
              );
            })}
          </View>
        </View>
        <View>
          <Image mode='widthFix' src={sign} className='payText'></Image>
        </View>
      </View>
    </>
  );
};

export default Section;
