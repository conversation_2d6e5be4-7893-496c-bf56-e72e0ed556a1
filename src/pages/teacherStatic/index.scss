page {
  background: #ffa700;
}
.content {
  position: relative;
  padding-top: 178px;
  .content-bg {
    position: absolute;
    width: 750px;
    height: 1448px;
    top: 0;
    left: 0;
    z-index: 0;
  }
  .teacher-info {
    width: 686px;
    height: 1130px;
    background-color: #fff;
    border-radius: 50px;
    position: relative;
    margin: 0 auto;
    text-align: center;
    &-head {
      width: 224px;
      height: 200px;
      margin-top: -112px;
    }
    &-name {
      font-size: 48px;
      margin: 12px auto 64px;
    }
    &-qrcode {
      width: 300px;
      height: 300px;
      border-radius: 16px;
      box-sizing: border-box;
      padding: 20px;
      margin-bottom: 16px;
      border: 1px solid #ffa700;
    }
    &-desc {
      font-size: 36px;
    }
    .line-part {
      height: 40px;
      display: flex;
      background: #fff;
      align-items: center;
      justify-content: space-between;
      margin-top: 40px;
      .circle {
        overflow: hidden;
        width: 40px;
        height: 40px;
        border-radius: 40px;
        background-color: #ffa700;
        &.left {
          margin-left: -20px;
          background-color: #ffaf19;
        }
        &.right {
          margin-right: -20px;
        }
      }
      .line {
        flex: 1;
        border-bottom: 1px dashed rgba(255, 167, 0, 0.16);
      }
    }
    .titleBox {
      display: flex;
      justify-content: center;
      box-sizing: border-box;
      margin-top: 40px;
      .leftImg {
        width: 45px;
        height: 20px;
        margin-right: 24px;
        image {
          width: 100%;
          height: 100%;
        }
      }
      .teacher {
        font-size: 32px;
        font-weight: 500;
        color: #333;
        Text {
          color: #fa6c3a;
        }
      }
      .rightImg {
        width: 45px;
        height: 20px;
        margin-left: 24px;
        image {
          width: 100%;
          height: 100%;
        }
      }
    }
    .flex-img {
      width: 556px;
      display: flex;
      margin: 48px auto;
      justify-content: space-between;
      &-item {
        width: 164px;
        height: 164px;
      }
    }
  }
}
