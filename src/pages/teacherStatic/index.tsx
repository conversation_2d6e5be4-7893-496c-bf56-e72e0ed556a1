import { View, Text, Image } from '@tarojs/components';
import haedBg from '@/assets/teacherStatic/index-bg.png';
import teacherHead from '@/assets/teacherStatic/index-teacher-head.png';
import qrcode from '@/assets/teacherStatic/qrcode.png';
import rightImg from '@/assets/midCoupon/rightImg.png';
import leftImg from '@/assets/midCoupon/leftImg.png';
import flexImg1 from '@/assets/teacherStatic/flex-img-1.png';
import flexImg2 from '@/assets/teacherStatic/flex-img-2.png';
import flexImg3 from '@/assets/teacherStatic/flex-img-3.png';
import './index.scss';

const Indexwebview = () => {
  return (
    <View className='content'>
      <Image className='content-bg' src={haedBg}></Image>
      <View className='teacher-info'>
        <Image className='teacher-info-head' src={teacherHead} />
        <View className='teacher-info-name'>小熊美术赠课老师</View>
        <Image
          className='teacher-info-qrcode'
          src={qrcode}
          showMenuByLongpress
        />
        <View className='teacher-info-desc'>长按识别二维码添加老师</View>
        <View className='teacher-info-desc'>领4节创意绘画课</View>
        <View className='line-part'>
          <View className='circle left'></View>
          <View className='line'></View>
          <View className='circle right'></View>
        </View>
        <View className='titleBox'>
          <View className='leftImg'>
            <Image src={leftImg} />
          </View>
          <Text className='teacher'>5天训练 收获满满</Text>
          <View className='rightImg'>
            <Image src={rightImg} />
          </View>
        </View>
        <View className='flex-img'>
          <Image className='flex-img-item' src={flexImg1}></Image>
          <Image className='flex-img-item' src={flexImg2}></Image>
          <Image className='flex-img-item' src={flexImg3}></Image>
        </View>
      </View>
    </View>
  );
};
export default Indexwebview;
