import { useState, useEffect } from 'react';
import Taro, { useShareAppMessage, useRouter } from '@tarojs/taro';
import { View, Image, Button } from '@tarojs/components';
import { getAssistCount } from '@/api/groupbuy';
import sensors from '@/utils/sensors_data';
import WxLogin from '@/components/wxlogin';
import TimeDown from './compontent/countDown';
import './index.scss';
import btn from '../../assets/assist/carve-btn.png';
import btn1 from '../../assets/assist/aq-btn1.png';
import share from '../../assets/assist/share.png';
import share1 from '../../assets/assist/share-aq.png';
import assist from '../../assets/assist/assist-invite.png';
import carveBg from '../../assets/assist/carve-bg.png';
import aqInvite from '../../assets/assist/aq-invite.png';
/**
 * 外部进入邀请助力页面，会传入uid
 */

export default function AssistFriend() {
  const $route = useRouter();
  const { from, uid } = $route.params;
  // 判断是答题还是瓜分助力 1：答题，2：瓜分
  const [isAq] = useState(Number(from) === 1);
  const [assistCount, setAssistCount] = useState(0);
  Taro.hideHomeButton(); //隐藏返回首页
  const setTitle = (title: string) => {
    Taro.setNavigationBarTitle({ title });
  };
  // eslint-disable-next-line @typescript-eslint/no-shadow
  const getCount = uid => {
    getAssistCount({
      uid,
      type: isAq ? 'KNOWLEDGE_ADVENTURE' : 'CARVE_UP_POINTS',
    }).then(res => {
      res.code === 0 && setAssistCount(res.payload);
    });
  };
  useEffect(() => {
    // 设置页面标题
    setTitle(isAq ? '知识大冒险' : '瓜分千万小熊币');
    // 获取助力人数
    uid && getCount(uid);
  }, [uid]);
  useShareAppMessage(res => {
    if (res.from === 'button') {
      // 来自页面内转发按钮
      console.log(res.target);
    }
    const title = isAq
      ? '万分火急！！我正在参加知识大冒险活动，求你帮我助力'
      : '万分火急！！我正在参加瓜分千万活动，求你帮我助力';
    return {
      title,
      path: `/pages/assist/assistResult?uid=${uid}&from=${from}`,
      imageUrl: isAq ? share1 : share,
    };
  });
  return (
    <View className='carve-bg'>
      <WxLogin isIntroduce={false} isFollow={false} />
      <Image
        className='carve-bg-img'
        mode='scaleToFill'
        src={isAq ? aqInvite : carveBg}
      ></Image>
      <View className='carve-content'>
        {!isAq && (
          <>
            <Image className='c-r-img' src={assist}></Image>
            <TimeDown></TimeDown>
          </>
        )}
        <Button
          className={!isAq ? 'c-c-btn ss' : 'c-c-btn'}
          open-type='share'
          onClick={() => {
            sensors.track('xxys_answer_dailyincline_click', {});
          }}
        >
          <Image src={isAq ? btn1 : btn}></Image>
        </Button>
        <View className={isAq ? 'c-c-count white' : 'c-c-count'}>
          本周已邀请({assistCount}/3)
        </View>
      </View>
    </View>
  );
}
