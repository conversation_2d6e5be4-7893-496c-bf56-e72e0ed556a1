.pos {
  position: absolute;
  left: 0;
  right: 0;
}
.carve-result-bg {
  width: 100%;
  height: 1496px;
  background-image: url('https://fe-cdn.xiaoxiongmeishu.com/ai-mp-user/image/result-bg.png');
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  &.aq {
    background-image: url('https://fe-cdn.xiaoxiongmeishu.com/ai-mp-user/image/aq-bg-new.png');
  }
  &.after-aq {
    background-image: url('https://fe-cdn.xiaoxiongmeishu.com/ai-mp-user/image/assist-bg-after.png');
    .c-r-img-new {
      width: 626px;
      height: 54px;
      padding-top: 102px;
      margin: auto;
      display: block;
    }
    .c-r-text-new {
      text-align: center;
      margin-top: 20px;
      font-size: 28px;
      font-weight: 200;
    }
    .c-r-content-new {
      width: 580px;
      height: 618px;
      background-color: #fff;
      border-radius: 40rpx;
      padding: 50rpx 38rpx;
      margin: 0 auto;
      margin-top: 70px;
      box-sizing: border-box;
      text-align: center;
      .c-r-switch-img {
        width: 504px;
        height: 341px;
        border-radius: 24px;
      }
      .c-r-activity-img {
        width: 424px;
        height: 110px;
        margin-top: 56px;
      }
    }
  }
  .c-r-img {
    @extend .pos;
    width: 626px;
    height: 112px;
    margin: auto;
    top: 90px;
  }
  .c-r-lotter {
    @extend .pos;
    top: 428px;
    width: 522px;
    margin: auto;
    .raffle-img {
      width: 174px;
      height: 174px;
    }
  }
  .c-c-btn {
    @extend .pos;
    width: 478px;
    height: 118px;
    top: 1050px;
    margin: auto;
    &.ss {
      width: 440px;
      height: 120px;
      top: 894px;
    }
    image {
      width: 100%;
      height: 100%;
    }
  }
  .c-c-pointer {
    position: absolute;
    width: 133px;
    height: 131px;
    top: 1100px;
    right: 120px;
    animation: pointmove 1s infinite ease-in-out;
    image {
      width: 100%;
      height: 100%;
    }
  }
  @keyframes pointmove {
    0% {
      transform: scale(1) translate(10px, 10px);
    }

    50% {
      transform: scale(1.2) translate(0, 0);
    }

    100% {
      transform: scale(1) translate(10px, 10px);
    }
  }
}
.re-btn {
  position: absolute;
  bottom: 80px;
  left: 0;
  right: 0;
  margin: auto;
  width: 456px;
  height: 84px;
  background: linear-gradient(90deg, #ffb128, #ff5418);
  border-radius: 42px;
  line-height: 84px;
  text-align: center;
  font-size: 34px;
  font-weight: 800;
  color: #fff;
}
.at-modal__container {
  text-align: center;
  .at-btn {
    @extend .re-btn;
  }
  .at-pointer {
    position: absolute;
    width: 133px;
    height: 131px;
    right: 80px;
    bottom: 0px;
    animation: pointmove 1s infinite ease-in-out;
    image {
      width: 100%;
      height: 100%;
    }
  }
}
.at-curtain__btn-close--bottom {
  opacity: 0;
}
.at-modal__overlay {
  background-color: rgba(0, 0, 0, 0.9);
}
.iss .at-modal__container {
  width: 694px;
  height: 806px;
  background: transparent;
  top: 40%;
}
.iss .img {
  width: 694px;
  height: 806px;
}
.at-modal__container {
  width: 620px;
  height: 320px;
  .at-modal__content {
    height: 100%;
  }
  .modal-tips {
    font-size: 36px;
    text-align: center;
    padding-top: 50px;
  }
  .at-btn {
    @extend .re-btn;
    bottom: 62px;
  }
}
.at-curtain__container {
  width: 100%;
  text-align: center;
}
