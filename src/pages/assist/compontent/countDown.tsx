import { useState, useEffect } from 'react';

import { Text, View } from '@tarojs/components';
import { countDownFormat } from '@/utils/index';
import './count.scss';

const TimeDown = () => {
  const [timeText, setTimeText] = useState([]);
  const endTime = () => {
    const date = new Date();
    const week = date.getDay();
    const oneDay = 86400000;
    const newTime = new Date(date.getTime()).setHours(23, 59, 59, 999);
    return date.getTime() + (8 - week) * oneDay + newTime - date.getTime();
  };
  useEffect(() => {
    countDown();
  }, []);
  let timer: any;
  // 瓜分倒计时
  const countDown = () => {
    let invTime: any = endTime() - +new Date();
    timer && clearInterval(timer);
    timer = setInterval(() => {
      invTime -= 1000;
      setTimeText(countDownFormat(invTime, 'dd-hh-mm-ss').split('-'));
      if (invTime <= 0) {
        invTime = 0;
        clearInterval(timer);
      }
    }, 1000);
  };
  const splitStr = str => {
    str = new String(str);
    return [str.charAt(0), str.charAt(1)];
  };
  const d = timeText[0] - 1;
  const h = splitStr(timeText[1]);
  const m = splitStr(timeText[2]);
  const s = splitStr(timeText[3]);
  const timeItem = time => {
    return time.map((item, index) => {
      return (
        <Text className='time' key={item + '-' + index}>
          {item}
        </Text>
      );
    });
  };
  return (
    <View className='c-c-time'>
      {/* d初始值可能为空 */}
      {d && (
        <>
          距本场活动截止:
          <Text className='time'>{d}</Text>天{timeItem(h)}时{timeItem(m)}分
          {timeItem(s)}秒
        </>
      )}
    </View>
  );
};
export default TimeDown;
