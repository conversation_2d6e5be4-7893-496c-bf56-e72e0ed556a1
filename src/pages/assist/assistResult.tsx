import {
  useState,
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
} from 'react';
import sensors from '@/utils/sensors_data';
import Taro, { useRouter, useDidHide } from '@tarojs/taro';
import { View, Image } from '@tarojs/components';
import { AtModal, AtModalContent } from 'taro-ui';
import { useSelector, useDispatch } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import WxLogin from '@/components/wxlogin';
import {
  getWxAssistProps,
  getProgramUserByOenid,
  checkUserBuy,
  getActivityCheck,
} from '@/api/groupbuy';
import store from '@/store/groupbuy/index';
import TimeDown from './compontent/countDown';
import './result.scss';
import btn from '../../assets/assist/result-btn.png';
import btn2 from '../../assets/assist/aq-btn.png';
import pointer from '../../assets/assist/pointer.png';
import one from '../../assets/assist/assist-1.png';
import two from '../../assets/assist/assist-2.png';
import thr from '../../assets/assist/assist-3.png';
import four from '../../assets/assist/assist-4.png';
import write from '../../assets/assist/write.png';
import music from '../../assets/assist/music.png';
import art from '../../assets/assist/art.png';
import newFail from '../../assets/assist/assist-fail.png';
import newSuccess from '../../assets/assist/assist-success.png';
import switchUnjoin from '../../assets/assist/switch-unjoin.png';
import switchjoined from '../../assets/assist/switch-joined.png';
import newJoin from '../../assets/assist/assist-join.png';
import newInvite from '../../assets/assist/assist-invite-new.png';
/**
 * 抽奖部分图片
 */
const requireContext = require.context(
  '@/assets/assist',
  false,
  /lottery-.*\.(png)$/,
);
const lotteryMap = {};
requireContext.keys().forEach((item: any) => {
  const name = item
    .split('/')
    .pop()
    .replace(/\.\w+$/, '');
  lotteryMap[name] = requireContext(item);
});

// 抽奖子组件
const Lottery = forwardRef((_props, ref) => {
  const [gifts, setGifts] = useState([
    { id: 1, name: 'lottery-1', activeName: 'lottery-1-a', sel: false },
    { id: 2, name: 'lottery-2', activeName: 'lottery-2-a', sel: false },
    { id: 3, name: 'lottery-3', activeName: 'lottery-3-a', sel: false },
    { id: 4, name: 'lottery-4', activeName: 'lottery-4-a', sel: false },
    { id: 5, name: 'lottery-5', activeName: 'lottery-5-a', sel: false },
    { id: 6, name: 'lottery-6', activeName: 'lottery-6-a', sel: false },
    { id: 7, name: 'lottery-7', activeName: 'lottery-7-a', sel: false },
    { id: 8, name: 'lottery-8', activeName: 'lottery-8-a', sel: false },
    { id: 9, name: 'lottery-9', activeName: 'lottery-9-a', sel: false },
  ]);
  const [timerClock, setTimerClock] = useState(false);
  useImperativeHandle(ref, () => ({
    handleLottery,
    timerClock,
  }));
  const handleLottery = (lotteryId, fn) => {
    setTimerClock(true);
    const currentGifts = JSON.parse(JSON.stringify(gifts));
    let dynamic_id = 1;
    let inver_timer: any;
    const clearLottery = () => {
      setGifts(() => {
        return currentGifts.map(o => {
          o.sel = false;
          return o;
        });
      });
    };
    const carve = choosenId => {
      let selNum = 1; // 循环次数
      inver_timer = setInterval(() => {
        if (dynamic_id - 1 == choosenId && selNum === 2) {
          clearInterval(inver_timer);
          setTimerClock(false);
          fn();
          // 本地存储已经抽过奖品了
          Taro.setStorageSync('userLottery', true);
          // 清除抽奖状态
          clearLottery();
          return;
        }
        if (dynamic_id > 9) {
          dynamic_id = 1;
          selNum++;
        }
        setGifts(() => {
          return currentGifts.map(o => {
            o.sel = false;
            if (o.id == dynamic_id) {
              o.sel = true;
            }
            return o;
          });
        });
        dynamic_id++;
      }, 200);
    };
    carve(lotteryId);
  };
  return (
    <View className='c-r-lotter'>
      {gifts.map(item => {
        return (
          <Image
            key={item.id}
            className='raffle-img'
            src={item.sel ? lotteryMap[item.activeName] : lotteryMap[item.name]}
          ></Image>
        );
      })}
    </View>
  );
});

export default function AssistFriend() {
  const $route = useRouter();
  const { from, uid } = $route.params;
  const LotteryDom: any = useRef();
  const [visibleAt, setVisibleAt] = useState(false);
  const [visibleModal, setVisibleModal] = useState(false);
  const [isAq] = useState(Number(from) === 1);
  const [assistImg, setAssistImg] = useState(one);
  const [buyStatus, setBuyStatus] = useState('');
  const [modalImg, setModalImg] = useState(art);
  /* *
   * 助力改版后
   * */
  const [assistNewImg, setAssistNewImg] = useState(newFail);
  const [switctImg, setSwitchImg] = useState(switchUnjoin);
  const [newJoinImg, setNewJoinImg] = useState(newJoin);
  const [newJoinText, setNewJoinText] = useState('');
  const [beforeAssist, setBeforeAssist] = useState(true);
  const [isJoinActivity, setIsJoinActivity] = useState(false); // 是否参与本期活动
  const [avatar, setAvatar] = useState(
    Taro.getStorageSync('userInfo').avatarUrl || '',
  );
  const [clickLock, setClickLock] = useState(false);
  const aqUrl = sendId => {
    let param = `${
      isJoinActivity
        ? 'entrance_page=share'
        : 'entrance_page=help&channelId=10574&sendId=' + sendId
    }`;
    return process.env.NODE_ENV === 'online'
      ? encodeURIComponent(
          `https://www.xiaoxiongmeishu.com/h5/bearQuestions?${param}`,
        )
      : encodeURIComponent(
          `https://test.meixiu.mobi/ai-app-h5-test/bearQuestions?${param}`,
        );
  };
  /* *
   * 助力改版后
   * */
  const dispatch = useDispatch();
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  //openid
  const openId = useSelector((state: UserStateType) => state.openid);
  Taro.hideHomeButton(); //隐藏返回首页
  const setTitle = (title: string) => {
    Taro.setNavigationBarTitle({ title });
  };
  // eslint-disable-next-line @typescript-eslint/no-shadow
  const getAssistResult = (uid, openid, _avatar = avatar) => {
    getWxAssistProps({
      uid,
      targetOpenId: openid,
      type: isAq ? 'KNOWLEDGE_ADVENTURE' : 'CARVE_UP_POINTS',
      avatar: _avatar,
    }).then(res => {
      if (res.code === 0) {
        const payload = res.payload;
        const map = {
          0: one,
          1: two,
          2: thr,
          3: four,
        };
        const sensorsMap = {
          0: '助力成功',
          1: '',
          2: '已助力',
          3: '被助力3次',
        };
        const sensorsNewMap = {
          0: `助力成功,本期${isJoinActivity ? '没有' : '已经'}参与答题`,
          1: '不能给自己助力',
          2: '已给好友助力过，不能重复助力',
          3: '被助力对象已完成本周任务',
          4: '今日助力机会用完，明天再助力吧',
        };
        const textNewMap = {
          0: isJoinActivity ? '参与答题闯关赢大奖' : '邀友参与奖励答题次数',
          1: '不能给自己助力哦～',
          2: '已给好友助力过，不能重复助力',
          3: '好友已完成助力任务，无需再助力',
          4: '今日助力机会用完，明天再助力吧',
        };
        if (!isAq) setAssistImg(map[payload]);
        else {
          setAssistNewImg(payload == 0 ? newSuccess : newFail);
          setSwitchImg(!isJoinActivity ? switchUnjoin : switchjoined);
          setNewJoinImg(!isJoinActivity ? newJoin : newInvite);
          setNewJoinText(textNewMap[payload]);
          setBeforeAssist(false);
        }
        setClickLock(false);
        sensors.track(
          Number(from) == 1
            ? 'xxys_answer_assistPage_view'
            : 'xxys_dividecoin_assistPage_view',
          {
            user_status: !isAq ? sensorsMap[payload] : sensorsNewMap[payload],
            refer_id: uid,
          },
        );
      }
    });
  };
  // 根据openid获取uid
  const getUserIdToOpenid = openid => {
    getProgramUserByOenid({
      openId: openid,
      subject: 'ART_APP',
    }).then(res => {
      if (res.code === 0) {
        if (res.payload.uid) {
          dispatch({
            type: 'CHANGE_USERID',
            userid: res.payload.uid,
          });
          !isAq && getUserLottery(res.payload.uid);
          // isAq && getActivityIsJoin(res.payload.uid);
        } else {
          setBuyStatus('art');
        }
      } else {
        setBuyStatus('art');
      }
    });
  };
  const getUserLottery = userid => {
    checkUserBuy({ uid: userid }).then(res => {
      /**
       * 1. 买过美术体验课：转介绍美术体验课小程序
       * 2. 买过音乐体验课：转介绍音乐体验课小程序
       * 3. 买过书法体验课：书法0.1元小程序
       */
      if (res.code === 0) {
        const {
          artBuyStatus,
          writeBuyStatus,
          musicBuyStatus,
          buyWrite,
          buyArt,
          buyMusic,
        } = res.payload;
        const buyTArt = buyArt && artBuyStatus === 'TEST';
        const buyTMusic = buyMusic && musicBuyStatus === 'TEST';
        const buyTWrite = buyWrite && writeBuyStatus === 'TEST';
        if (!buyTArt) {
          setBuyStatus('art');
        } else if (!buyTMusic) {
          setBuyStatus('music');
        } else if (!buyTWrite) {
          setBuyStatus('write');
        } else {
          setBuyStatus('all');
        }
      }
    });
  };
  useEffect(() => {
    // 设置页面标题
    setTitle(isAq ? '知识大冒险' : '瓜分千万小熊币');
    // 获取openID
    if (uid && openId) {
      if (userId) {
        // 获取用户抽奖结果
        !isAq && getUserLottery(userId);
        // isAq && getActivityIsJoin(userId);
      } else {
        getUserIdToOpenid(openId);
      }
      !isAq && getAssistResult(uid, openId);
    }
  }, [uid, openId]);
  // 抽奖
  useEffect(() => {
    // 2s后自动开始抽奖
    const userLottery = Taro.getStorageSync('userLottery');
    setTimeout(() => {
      if (buyStatus && !userLottery) handleLotteryStart();
    }, 2000);
  }, [buyStatus]);
  useDidHide(() => {
    Taro.removeStorageSync('userLottery');
  });
  const handleLotteryStart = () => {
    if (!buyStatus) return;
    // 通知子组件开始抽奖
    const map = {
      art: art,
      music: music,
      write: write,
    };
    buyStatus !== 'all' && setModalImg(map[buyStatus]);
    // resultMap 抽奖的结果
    const resultMap = {
      art: 6,
      music: 8,
      write: 1,
      all: 9,
    };
    const userLottery = Taro.getStorageSync('userLottery');
    if (userLottery) {
      buyStatus === 'all' ? handleOpenModal() : handleOpenAt();
      return;
    }
    // eslint-disable-next-line @typescript-eslint/no-shadow
    const { handleLottery, timerClock } = LotteryDom.current;
    !timerClock &&
      handleLottery(
        resultMap[buyStatus],
        buyStatus === 'all' ? handleOpenModal : handleOpenAt,
      );
  };
  const handleCloseAt = () => {
    setVisibleAt(false);
    const buyStatusMap = {
      art: () => {
        const channelId = isAq ? '10086' : '9923';
        store.dispatch({ type: 'CHANGE_CHANNELID', channelId });
        if (process.env.APP_ENV !== 'weappV1')
          Taro.navigateToMiniProgram({
            appId: 'wx326319ed9257c109',
            path: `/pages/normalGroup/art/index?msChannelId=${channelId}&sendId=${uid}&subject=meishu`,
          });
        else
          Taro.navigateTo({
            url: `/pages/normalGroup/art/index?msChannelId=${channelId}&sendId=${uid}&subject=meishu`,
          });
      },
      music: () => {
        // 打开音乐的小程序
        const channelId = isAq ? '10087' : '10231';
        Taro.navigateToMiniProgram({
          appId: 'wx539e6104d53dcfe6',
          path: `/page/index/index?packageId=522&type=0&channelId=${channelId}&sendId=${uid}`,
        });
      },
      write: () => {
        const channelId = isAq ? '10088' : '10232';
        store.dispatch({ type: 'CHANGE_CHANNELID', channelId });
        Taro.navigateTo({
          url: `/pages/calligraphyExperience/newPage?channelId=${channelId}&sendId=${uid}`,
        });
      },
    };
    buyStatusMap[buyStatus]();
    const map = {
      art: '美术体验课',
      music: '音乐体验课',
      write: '书法体验课',
    };
    sensors.track(
      Number(from) == 1
        ? 'xxys_answer_assistPage_lotteryResult_click'
        : 'xxys_dividecoin_assistPage_lotteryResult_click',
      {
        lottery_type: map[buyStatus],
        refer_id: uid,
      },
    );
  };
  const handleOpenAt = () => {
    const map = {
      art: '美术体验课',
      music: '音乐体验课',
      write: '书法体验课',
    };
    buyStatus !== 'all' &&
      sensors.track(
        Number(from) == 1
          ? 'xxys_answer_assistPage_lotteryResult_view'
          : 'xxys_dividecoin_assistPage_lotteryResult_view',
        {
          lottery_type: map[buyStatus],
          refer_id: uid,
        },
      );
    setVisibleAt(true);
  };
  const handleOpenModal = () => {
    sensors.track(
      Number(from) == 1
        ? 'xxys_answer_assistPage_lotteryResult_view'
        : 'xxys_dividecoin_assistPage_lotteryResult_view',
      {
        lottery_type: '谢谢惠顾',
        refer_id: uid,
      },
    );
    setVisibleModal(true);
  };
  const atClose = () => {
    setVisibleAt(false);
  };
  /* *
   * 助力改版后
   * */
  // 获取头像
  const getUserInfo = () => {
    if (clickLock) return;
    setClickLock(true);
    if (avatar) getAssistResult(uid, openId, avatar);
    else
      Taro.getUserProfile({
        desc: '用于完善会员资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
        success: res => {
          // 开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
          setAvatar(res.userInfo.avatarUrl);
          Taro.setStorageSync('userInfo', res.userInfo);
          getAssistResult(uid, openId, res.userInfo.avatarUrl);
        },
      });
  };
  // 跳转地址
  const getWebView = () => {
    Taro.navigateTo({
      url: `/pages/webview/index?url=${aqUrl(uid)}`,
    });
  };
  // 获取是否参加过本期活动
  const getActivityIsJoin = userid => {
    getActivityCheck({
      uid: userid,
      subject: 'ART_APP',
      type: 'KNOWLEDGE_ADVENTURE',
    }).then(res => {
      setIsJoinActivity(res.payload);
    });
  };
  return (
    <View className='content'>
      <WxLogin isIntroduce={false} isFollow={false} />
      {isAq &&
        ((beforeAssist && (
          <View className='carve-result-bg aq'>
            <View onClick={() => getUserInfo()} className='c-c-btn ss'>
              <Image src={btn2}></Image>
            </View>
          </View>
        )) || (
          <View className='carve-result-bg after-aq'>
            <Image className='c-r-img-new' src={assistNewImg}></Image>
            <View className='c-r-text-new'>{newJoinText}</View>
            <View className='c-r-content-new'>
              <Image src={switctImg} className='c-r-switch-img'></Image>
              <Image
                src={newJoinImg}
                className='c-r-activity-img'
                onClick={() => getWebView()}
              ></Image>
            </View>
          </View>
        ))}
      {!isAq && (
        <View className='carve-result-bg'>
          <View className='carve-content'>
            <Image className='c-r-img' src={assistImg}></Image>
            <TimeDown></TimeDown>
            <Lottery ref={LotteryDom}></Lottery>
            <View
              className={isAq ? 'c-c-btn ss' : 'c-c-btn'}
              onClick={handleLotteryStart}
            >
              <Image src={isAq ? btn2 : btn}></Image>
            </View>
            <View className='c-c-pointer' onClick={handleLotteryStart}>
              <Image src={pointer}></Image>
            </View>
          </View>
          <AtModal isOpened={visibleAt} onClose={atClose} className='iss'>
            <Image src={modalImg} className='img' />
            <View className='at-btn' onClick={handleCloseAt}>
              {buyStatus === 'art' ? '今日体验送画材' : '0.1元领取'}
            </View>
            <View className='at-pointer' onClick={handleCloseAt}>
              <Image src={pointer}></Image>
            </View>
          </AtModal>
          <AtModal isOpened={visibleModal} closeOnClickOverlay={false}>
            <AtModalContent>
              <View className='modal-tips'>谢谢参与，祝你下次好运</View>
              <View
                className='at-btn'
                onClick={() => {
                  sensors.track(
                    'xxys_dividecoin_assistPage_lotteryResult_click',
                    {
                      lottery_type: '谢谢惠顾',
                      refer_id: uid,
                    },
                  );
                  setVisibleModal(false);
                }}
              >
                知道了
              </View>
            </AtModalContent>
          </AtModal>
        </View>
      )}
    </View>
  );
}
