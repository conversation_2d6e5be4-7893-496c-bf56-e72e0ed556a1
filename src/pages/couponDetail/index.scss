page {
  background: #f0f0f0;
}
.content {
  position: relative;
  .view-bg {
    width: 100%;
    position: absolute;
    height: 1448px;
  }
  .b-dy {
    position: absolute;
    top: 1141px;
    width: 100%;
    .effectivity {
      width: 631px;
      height: 88px;
      line-height: 88px;
      color: #fff;
      font-size: 36px;
      text-align: center;
      margin: 0 auto;
      border-radius: 88px;
      background: linear-gradient(to bottom, #ff8369, #ff1919);
      animation: scaleJumpbtn 1s ease-in-out infinite;
    }
  }
  .time {
    font-size: 28px;
    font-weight: 400;
    text-align: center;
    margin-top: 40px;
  }
  @keyframes scaleJumpbtn {
    0% {
      transform: scale(0.93);
    }
    50% {
      transform: scale(1);
    }
    100% {
      transform: scale(0.93);
    }
  }
  .custom-time-main .at-countdown__item:last-child {
    display: inline-flex;
  }
}
.login-modal {
  // .at-modal__contenter {
  //   width: 580px;
  // }
  .at-modal__content {
    padding: 40px;
  }
  .login-title {
    font-size: 40px;
    font-weight: 500;
    color: #333333;
    line-height: 40px;
    text-align: center;
    margin-bottom: 40px;
  }
  .login-main {
    width: 460px;
    height: 340px;
  }
  .login-btn {
    width: 460px;
    height: 84px;
    background: #fa6c3a;
    border-radius: 44px;
    font-size: 32px;
    font-weight: 500;
    color: #ffffff;
    line-height: 84px;
    margin-top: 40px;
  }
}
