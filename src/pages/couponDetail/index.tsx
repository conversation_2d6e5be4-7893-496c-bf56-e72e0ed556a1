import { AtCountdown } from 'taro-ui';
import sensors from '@/utils/sensors_data';
import { useDispatch, useSelector } from 'react-redux';
import { useEffect, useMemo, useState } from 'react';
import { UserStateType } from '@/store/groupbuy/state';
import { View, Button, Image } from '@tarojs/components';
import Taro, { useRouter, useShareAppMessage } from '@tarojs/taro';
// import shareImg from '@/assets/normalGroup/art/share-img-20240708.jpg';
import viewBg from '@/assets/normalGroup/art/view-bg.png';
import viewBgNew from '@/assets/normalGroup/art/view-bg-new.png';
// import loginMainPng from '@/assets/couponDetail/login-main.png';
import {
  getUserCouponByPackageId,
  getProgramUserSubject,
  getUserByUserNum,
} from '@/api/groupbuy';
import WxLogin from '@/components/wxlogin';
import './index.scss';

const Indexwebview = () => {
  const shareImg =
    'https://fe-cdn.xiaoxiongmeishu.com/apph5/video/discount-share-bg.jpg';
  const shareImg2 =
    'https://fe-cdn.xiaoxiongmeishu.com/apph5/video/discount-share-2d9-bg.png';
  const dispatch = useDispatch();
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  //openid
  const openId = useSelector((state: UserStateType) => state.openid);
  const router = useRouter();
  const [sendId, setsendId] = useState('');
  const [endTime, setendTime] = useState('');
  const [couponInfo, setcouponInfo] = useState({
    newCouponId: '',
  });
  // const [loginModalOpend, setLoginModalOpend] = useState(false)

  const _NODE_ENV = process.env.NODE_ENV;
  const disPackagesId = {
    dev: 7698,
    test: 8932,
    online: 1733,
  };
  // 原页面仅318，展示2000+亲友券、现改为318,322,323,324,325,345均展示2000+亲友券
  const NEW_COUPONS_IDS = ['318', '322', '323', '324', '325', '345'];

  useEffect(() => {
    var _sendId;
    if (router.params.scene) {
      _sendId = decodeURIComponent(router.params.scene).split('=');
      setsendId(_sendId[1]);
    }
    if (router.params.sendId) setsendId(router.params.sendId);
    handleUserNum();
  }, []);

  // 支持小程序码打开小程序 scence: s=9324863&c=17455&e=qwbSop
  const handleUserNum = () => {
    if (router.params.s) {
      getUserByUserNum({ userNum: router.params.s }).then((res) => {
        if (res.code === 0) {
          setsendId(res.payload.id);
        }
      });
    }
  };

  const sensorsHandler = (couponId = '') => {
    sensors.track('xxys_couponpage_view', {
      entrance_page: router.params.entrance_page || router.params.e || '',
      channel_id:
        router.params.channelId || process.env.NODE_ENV == 'online'
          ? '15626'
          : process.env.NODE_ENV == 'test'
          ? '7044'
          : '4243',
      couponId: couponId,
      refer_id: sendId,
    });
  };

  const getConponHandler = () => {
    if (!sendId && userId == null) return;
    getUserCouponByPackageId(
      sendId ? sendId : userId,
      disPackagesId[_NODE_ENV],
    ).then((res) => {
      /* res.payload = [
        {
          newCouponId: '343',
          couponId: '871755103575609344',
          couponUserId: '871755103575609344',
          amount: 26.0,
          name: '体验课优惠券',
          status: 'NOACTIVE',
          startDate: '1688009078750',
          endDate: '1693143169000',
          description: '测试',
          trainingCampCouponId: '0',
          stack: 'STACKABLE',
          isMust: 0
        },
        {
          newCouponId: '319',
          couponId: '871755103575609344',
          couponUserId: '871755103575609344',
          amount: 26.0,
          name: '体验课优惠券',
          status: 'NOACTIVE',
          startDate: '1688009078750',
          endDate: '1693143169000',
          description: '测试',
          trainingCampCouponId: '0',
          stack: 'STACKABLE',
          isMust: 0
        }
      ] */
      if (res.payload.length) {
        setendTime(res.payload[0].endDate);
        setcouponInfo(res.payload[0]);
        sensorsHandler(res.payload[0].newCouponId);
      }
      sensorsHandler();
    });
  };

  useEffect(() => {
    sendId && getConponHandler();
    if (!sendId && userId) getConponHandler();
    // setLoginModalOpend(!sendId)
  }, [sendId, userId]);

  useShareAppMessage((res) => {
    if (res.from === 'button') {
      // 来自页面内转发按钮
      console.log(res.target);
    }
    sensors.track('xxys_couponpage_shareclick', {
      operation_type: res.from === 'button' ? '去分享' : '转发给朋友',
    });

    var channel =
      process.env.NODE_ENV == 'online'
        ? '15626'
        : process.env.NODE_ENV == 'test'
        ? '7044'
        : '4243';
    if (router.params.channelId) channel = router.params.channelId;
    if (router.params.c) channel = router.params.c;
    const nowSendId = sendId || userId;
    return nowSendId
      ? {
          title: '送你1张亲友券，9.9元学10节美术创意课，还免费送画材礼包',
          path: `/pages/normalGroup/art/discount9_9/index?sendId=${nowSendId}&channelId=${channel}&msChannelId=${channel}&couponId=${couponInfo.newCouponId}`,
          imageUrl: shareImg,
        }
      : {
          title: '今天可以免费领画材礼包，名额有效，快给宝贝领取！',
          path: `pages/normalGroup/art/index?channelId=13073`,
          imageUrl: shareImg2,
        };
  });

  // 获取用户手机号
  const getPhoneNumber = (res) => {
    if (res.detail.errMsg === 'getPhoneNumber:fail user deny') {
      console.log(res.detail.errMsg);
    } else {
      const { encryptedData, iv } = res.detail;
      // getProgramUserSubject兼容getProgramUser
      getProgramUserSubject({
        openId,
        encryptedData,
        iv,
        subject: 'ART_APP',
      }).then((response) => {
        if (response.code !== 0) return;
        if (response.payload.uid) {
          dispatch({
            type: 'CHANGE_USERID',
            userid: response.payload.uid,
          });
        }
        if (response.payload.token)
          Taro.setStorageSync('appToken', response.payload.token);
        response.payload.mobile &&
          dispatch({
            type: 'CHANGE_MOBILE',
            mobile: response.payload.mobile,
          });
        Taro.showToast({
          title: '登录成功，转发亲友券给好友吧',
          icon: 'none',
          duration: 2000,
        });
      });
    }
  };

  const timeInfo = useMemo(() => {
    const date = new Date().getTime();
    var second = Math.floor((+endTime - date) / 1000);
    // 天数
    var day = Math.floor(second / 3600 / 24);
    // 小时
    var hours = Math.floor((second / 3600) % 24);
    // 分钟
    var minutes = Math.floor((second / 60) % 60);
    // 秒
    var seconds = Math.floor(second % 60);
    return { day, hours, minutes, seconds };
  }, [endTime]);

  return (
    <View className='content'>
      <WxLogin subject='ART_APP' noAskNoaddress />
      <Image
        className='view-bg'
        src={
          NEW_COUPONS_IDS.includes(couponInfo.newCouponId) ? viewBgNew : viewBg
        }
      />
      <View className='b-dy'>
        {sendId || userId ? (
          <Button openType='share' className='effectivity effectivity2'>
            立即赠送
          </Button>
        ) : (
          <Button
            className='effectivity effectivity2'
            open-type='getPhoneNumber'
            onGetPhoneNumber={getPhoneNumber}
          >
            立即赠送
          </Button>
        )}
        {endTime && (
          <View className='time'>
            有效期{' '}
            <AtCountdown
              className='custom-time-main'
              isShowDay
              key={timeInfo.seconds}
              day={timeInfo.day}
              hours={timeInfo.hours}
              minutes={timeInfo.minutes}
              seconds={timeInfo.seconds}
              format={{ day: ':', hours: ':', minutes: ':', seconds: '' }}
            />{' '}
            结束
          </View>
        )}
      </View>

      {/* <AtModal
        isOpened={loginModalOpend}
        // closeOnClickOverlay={false}
        className='login-modal'
      >
        <AtModalContent>
          <View className='login-title'>登录后赠送优惠券</View>
          <Image className='login-main' src={loginMainPng}></Image>
          <Button
            className='login-btn'
            open-type='getPhoneNumber'
            onGetPhoneNumber={getPhoneNumber}
          >
            点击登录
          </Button>
        </AtModalContent>
      </AtModal> */}
    </View>
  );
};
export default Indexwebview;
