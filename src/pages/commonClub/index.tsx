/* eslint-disable */
import Taro, { useRouter } from '@tarojs/taro';
import { useSelector } from 'react-redux';
import { judgeMinienv } from '@/utils/auth';
import { UserStateType } from '@/store/groupbuy/state';
import { useEffect } from 'react';
import { bindUnionId } from '@/api/groupbuy';
import { View, Image } from '@tarojs/components';
import selected from '@/assets/common/club-bt.png';
import selectedTitle from '@/assets/common/club-title-gf.png';
import selectedGf from '@/assets/common/club-bt-gf.png';
import WxLogin from '@/components/wxlogin';
// @ts-ignore

import './index.scss';
// 组件扩展申明
declare global {
  interface PxhRouter {
    path: string;
  }
  namespace JSX {
    interface IntrinsicElements {
      // 扩展 IntrinsicElements 否则无法识别自定义标签
      cell: React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLElement>,
        HTMLElement
      >;
    }
  }
  namespace React {
    interface HTMLAttributes<T> extends AriaAttributes, DOMAttributes<T> {
      // 扩展HTML标签的属性类型
      // 如果需要使用自定义属性的化需要
      onStartmessage?: Function | undefined | { [key: string]: any };
      onCompletemessage?: Function | undefined | { [key: string]: any };
      url?: string | undefined | { [key: string]: any };
      iconUrl?: string | undefined | { [key: string]: any };
      contactText?: string | undefined | { [key: string]: any };
      plugid?: string;
    }
  }
}
// 测试打包打dev包
export default () => {
  const router = useRouter();
  let params = router.params;
  const from = params.from ? +params.from : 1; // 0、瓜分，1 是答题
  const cellUrl =
    process.env.NODE_ENV == 'online'
      ? [
          'https://work.weixin.qq.com/gm/84792943a5bca8f9caf7a03374448cc4',
          'https://work.weixin.qq.com/gm/366cefaa2ab60d256239b28f997d67e4',
        ]
      : [
          'https://work.weixin.qq.com/gm/993527c1409334153284355de0ec66e5',
          'https://work.weixin.qq.com/gm/0cf5c74bfcfa3dd4466d3593cda9e044',
        ];
  const btnImg = [selectedGf, selected];
  const classArr = ['carve', ''];
  const errCode = {
    '-3002': '系统错误,请联系管理员',
    '-3004': '用户信息授权失败',
    '-3005': '系统错误,请联系管理员',
    '-3006': '用户已经加入群聊', //如果notifytype = 1，则为二维码展示方式，此时仍会展示二维码
    '-3009': '群聊已满员',
    '-3010': '群聊已解散',
    '-3011': '用户命中企业群聊黑名单',
    '-3012': '用户已在群里中且群已满员',
  };
  const setTitle = (title: string) => {
    Taro.setNavigationBarTitle({ title });
  };
  const unionId = useSelector((state: UserStateType) => state.unionId);
  useEffect(() => {
    setTitle(from == 1 ? '小熊艺术乐园' : '瓜分千万小熊币');
    unionId &&
      bindUnionId({
        uid: params.uid ? params.uid : '',
        unionid: unionId,
        roomType: from == 1 ? 'QUESTIONS_ROOM' : 'GOLD_ROOM',
      }).then(() => {});
  }, [params, unionId]);
  const startmessage = e => {
    console.log('startmessage', e);
  };
  // 加群操作完成的回调
  const completemessage = e => {
    console.log('completemessage', e);
  };
  return (
    <View
      className={`${judgeMinienv('release') &&
        'index'} container w100 relative ${classArr[from]}`}
    >
      <WxLogin isIntroduce={false} isFollow={false} />
      {/* 头部导航栏 */}
      {!from && (
        <Image src={selectedTitle} mode='widthFix' className='title-img' />
      )}
      {judgeMinienv('release') && (
        <View className='button'>
          <Image src={btnImg[from]} mode='widthFix' className='img' />
          <cell
            onStartmessage={startmessage}
            onCompletemessage={completemessage}
            url={cellUrl[from]}
            iconUrl=''
            contactText=''
          />
        </View>
      )}
    </View>
  );
};
