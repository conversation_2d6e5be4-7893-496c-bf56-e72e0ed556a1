@import '@/theme/groupbuy/common.scss';
cell {
  height: 100%;
  display: block;
}
.index {
  font-size: 0;
  position: relative;
  background: url('https://fe-cdn.xiaoxiongmeishu.com/ai-mp-user/image/club-bg.png?v=1')
    no-repeat 0 0 /100%;
  background-size: 100% 100%;
  width: 100%;
  height: 1496px;
  .button {
    position: relative;
    left: 0;
    right: 0;
    top: 894px;
    margin: 0 auto;
    width: 456px;
    height: 124px;
    overflow: hidden;
    border-radius: 124px;
    .img {
      position: absolute;
      width: 100%;
      pointer-events: none;
      z-index: 99;
    }
  }
  &.carve {
    background: url('https://fe-cdn.xiaoxiongmeishu.com/ai-mp-user/image/club-bg-gf.png')
      no-repeat 0 0 /100%;
    .button {
      top: 968px;
    }
    .title-img {
      position: absolute;
      width: 626px;
      height: 112px;
      left: 0;
      right: 0;
      top: 88px;
      margin: 0 auto;
    }
  }
  .wrapper {
    opacity: 0;
  }
}
