import { View, Image } from '@tarojs/components';
import poster1 from '@/assets/addqynew/poster1.png';
import qr01 from '@/assets/addqynew/qr01.png';
import { useEffect, useState } from 'react';
import sensors from '@/utils/sensors_data';
import Taro, { useRouter } from '@tarojs/taro';
import { getUserOpenIdSubject } from '@/api/groupbuy';
import { getPosterListByIdApi } from '@/api/1v1k8s';

import './index.scss';

const Indexwebview = () => {
  const router = useRouter();
  const onLongPress = () => {
    // sensors.track('xxys_privateDomain_addWorkWxPage_wxLongPress');
  };
  const {
    params: { posterId = '' },
  } = router;

  const [posterInfo, setPosterInfo] = useState({
    posterUrl: '',
    copywriting: '',
    individualizationUrl: '',
  });
  useEffect(() => {
    sensors.track('xxys_privateDomain_ivrAddWorkWXPage_view');
    posterId && sensors.track('xxys_privateDomain_CJAddWorkWXPage_view');

    Taro.login({
      success: function(res) {
        if (res.code) {
          getUserOpenIdSubject({
            code: res.code,
            channel: router.params.channelId || '',
            subject: 'ART_APP',
          }).then(data => {
            if (data.code === 0) {
              if (data.payload.token)
                Taro.setStorageSync('appToken', data.payload.token);
              sensors.init({
                wx_openid: data.payload.openid || '',
                user_id: data.payload.uid || '',
              });
              sensors.track('xxys_privateDomain_addWorkWxPage_view');
            }
          });
        } else {
          console.log('登录失败！' + res.errMsg);
        }
      },
    });
  }, []);

  useEffect(() => {
    Taro.hideHomeButton();
    getPosterListByIdApi(posterId).then(res => {
      const { code, payload } = res;
      if (code === 0) {
        setPosterInfo(payload);
      }
    });
  }, []);

  return (
    <View className='sy-addteacher-wrap'>
      <View className='wrap-bg'>
        <View className='wrap-bg-img'>
          <Image
            src={posterId ? posterInfo.posterUrl : poster1}
            mode='widthFix'
          />
        </View>
        <View
          className={posterId ? 'wrap-bg-btn custom' : 'wrap-bg-btn'}
          onLongPress={onLongPress}
        >
          {posterInfo.copywriting && (
            <View className='tips'>
              {posterInfo.copywriting.split('<>')[0]}
            </View>
          )}
          <Image
            showMenuByLongpress
            src={posterId ? posterInfo.individualizationUrl : qr01}
            mode='widthFix'
          />
          {posterInfo.copywriting && (
            <View className='tips'>
              {posterInfo.copywriting.split('<>')[1]}
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

export default Indexwebview;
