page {
  background-color: #ffffff;
}
.sy-addteacher-wrap {
  width: 100%;
  font-size: 0;
  .wrap-bg {
    width: 100%;
    height: 100%;
    position: relative;
  }
  .wrap-bg-img {
    width: 100%;
    image {
      width: 100%;
      height: 100%;
      display: block;
    }
  }
  .wrap-bg-btn {
    position: absolute;
    top: 1058px;
    left: 50%;
    transform: translateX(-50%);
    image {
      width: 320px;
      height: 320px;
      display: block;
    }
    .tips {
      font-size: 36px;
      font-weight: 600;
      color: #865150;
      text-align: center;
      padding-bottom: 20px;
      &:last-child {
        font-size: 29px;
        font-weight: 400;
        margin-top: 20px;
        padding-bottom: 0px;
      }
    }
  }
  .custom {
    top: 990px;
  }
}
