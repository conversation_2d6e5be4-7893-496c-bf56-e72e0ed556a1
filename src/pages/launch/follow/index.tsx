import Taro, { useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import Undertask from '@/components/undertake';
import WechatWrap from '@/components/wechatWrap';
import sensors from '@/utils/sensors_data';
import Contactshare from './components/contactshare';
import Spellgroup from './components/spellgroup';

export default function FollowIndex() {
  const [iShow, setIShow] = useState(true);
  const router = useRouter();
  let params = router.params;
  const isWrite =
    (params.subject && params.subject === 'WRITE_APP') ||
    (params.type && params.type == '105');
  const isCrafts = params.type && params.type == '601';
  const originalCost = params.originalCost;
  const getTitle = () => {
    if (params.hasOwnProperty('levelName')) {
      return '艺术宝';
    }
    if (isWrite) {
      return '小熊书法';
    }
    if (isCrafts) {
      return '小熊手工艺';
    }
    return '小熊艺术';
  };
  const showEle = () => {
    // soul区分是否是拼团
    const subject = getTitle();
    // 如果从APP过来
    if (params.source == 'appWechat' && params.planCourseId && iShow) {
      return (
        <Undertask
          handleShow={() => setIShow(false)}
          isWriteFollow={params.hasOwnProperty('levelName')}
        />
      );
    }

    if (
      subject === '小熊艺术' &&
      !params.type &&
      !params.soul?.includes('artindex')
    ) {
      return <WechatWrap />;
    }
    if (params.soul?.includes('artindex')) {
      const appId = Taro.getAccountInfoSync().miniProgram.appId;
      // 小熊艺术乐园移除选择微信地址
      if (appId === 'wx326319ed9257c109') {
        return <WechatWrap />;
      } else {
        return <Spellgroup />;
      }
    } else {
      if (iShow) {
        return (
          <Undertask
            handleShow={() => setIShow(false)}
            isWriteFollow={params.hasOwnProperty('levelName')}
          />
        );
      } else {
        return <Contactshare subjectTitle={getTitle()} />;
      }
    }
    // return <WechatWrap />;
  };
  useEffect(() => {
    process.env.TARO_ENV == 'weapp' && Taro.hideHomeButton(); //隐藏返回首页
    if (process.env.TARO_ENV == 'alipay') {
      sensors.track('ai_marketing_AlipayminiAPP_teacherpagebrowse', {
        channel_id: params.channelId,
        urlType: params.urlType,
      });
    }
    if (process.env.TARO_ENV == 'alipay' && originalCost) {
      my.getAuthCode({
        // 订单服务授权：order_service。如需同时获取用户多项授权，可在 scopes 中传入多个 scope 值。
        scopes: ['order_service'],
        success: res => {
          // 订单服务授权成功
          console.log('originalCost，授权成功', res);
        },
        fail: res => {
          // 订单服务授权失败，根据自己的业务场景来进行错误处理
          console.log('originalCost，授权失败', res);
        },
      });
    }
  }, []);

  Taro.setNavigationBarTitle({
    title: getTitle(),
  });
  return <>{showEle()}</>;
}
