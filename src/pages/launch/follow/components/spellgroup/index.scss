.page {
  width: 100%;
  min-height: 100vh;
  background-color: #fafafa;
  &.page-nospell {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &.page-spell {
    background-image: url('../../../../../assets/normalGroup/spell/header.png');
    background-position: 0 0;
    background-repeat: no-repeat;
    background-size: 100% 355px;
    padding-bottom: 20px;
  }
  .title {
    width: 100%;
    height: 67px;
    font-size: 48px;
    font-weight: 500;
    color: #ffffff;
    line-height: 67px;
    padding: 74px 0 53px 0;
    text-align: center;
    position: relative;
    text-indent: 54px;
    Text {
      font-size: 64px;
    }
    // &::after {
    //   content: '';
    //   width: 42px;
    //   height: 42px;
    //   background-image: url('../../../../../assets/normalGroup/spell/header-icon.png');
    //   background-repeat: no-repeat;
    //   background-position: 0 0;
    //   background-size: contain;
    //   position: absolute;
    //   left: 252px;
    //   top: 87px;
    // }
  }
  .desc-img {
    width: 631px;
    height: 340px;
  }
  .share-wrap {
    width: 690px;
    background: #ffffff;
    border-radius: 30px;
    margin: 0 auto;
    padding: 54px 0 46px 0;
    text-align: center;
    .share-title {
      height: 48px;
      font-size: 42px;
      font-weight: 600;
      color: #333;
      line-height: 48px;
      text-align: center;
      margin-bottom: 36px;
      text {
        color: #ff7000;
      }
    }
  }
  .share-btn {
    padding: 0 36px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    .share {
      width: 415px;
      height: 88px;
      background-image: url('../../../../../assets/normalGroup/spell/share-poster.png');
      background-repeat: no-repeat;
      background-size: contain;
      background-position: 0 0;
      &_wx {
        // margin-left: 20px;
        background-image: url('../../../../../assets/normalGroup/spell/share-wx.png');
        animation: breathe 1.2s ease-in-out infinite;
      }
      .sharetype-btn {
        width: 100%;
        height: 100%;
        background-color: transparent;
        &::after {
          border: none;
        }
      }
    }
  }
  @keyframes breathe {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }
  .warm-prompt {
    margin-top: 46px;
    .warm-prompt-title {
      height: 89px;
      line-height: 89px;
      padding-left: 140px;
      text-align: left;
      position: relative;
      margin-bottom: 14px;
      font-size: 34px;
      font-weight: 500;
      color: #666666;
      &::before {
        content: '';
        width: 109px;
        height: 89px;
        background-image: url('../../../../../assets/normalGroup/spell/tips-icon.png');
        background-repeat: no-repeat;
        background-size: contain;
        background-position: 0 0;
        position: absolute;
        left: 31px;
        top: 0;
      }
    }
    .warm-prompt-tips {
      padding: 0 43px;
      font-size: 26px;
      font-weight: 400;
      color: #666666;
    }
  }
  .active-courses-btn {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 116px;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .add-teacher {
    width: 690px;
    background: #ffffff;
    border-radius: 20px;
    margin: 44px auto 20px;
    position: relative;
    padding-top: 97px;
    padding-bottom: 62px;
    position: relative;
    .header-icon {
      width: 372px;
      height: 58px;
      background-repeat: no-repeat;
      background-size: contain;
      background-position: 0 0;
      background-image: url('../../../../../assets/normalGroup/spell/zswx.png');
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      top: -14px;
    }
    .add-teacher-title {
      height: 45px;
      font-size: 32px;
      font-weight: 600;
      color: #3e2d21;
      line-height: 45px;
      text-align: center;
    }
    .wx-qrcode {
      width: 240px;
      height: 240px;
      background: #f0f0f0;
      border-radius: 20px;
      margin: 40px auto;
      image {
        width: 100%;
        height: 100%;
      }
    }
    .add-teacher-sub {
      height: 37px;
      font-size: 26px;
      font-weight: 500;
      color: #333333;
      line-height: 37px;
      text-align: center;
      margin-top: 16px;
    }
    .sub-hr {
      width: 626px;
      height: 1px;
      background: #e8e8e8;
      margin: 54px auto 0;
    }
    .add-introduced {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      margin-top: 50px;
      .introduced-item {
        height: 56px;
        line-height: 56px;
        text-align: center;
        text-indent: 86px;
        position: relative;
        margin-bottom: 20px;
        font-size: 28px;
        font-weight: 400;
        color: #3e2d21;
        &::before {
          content: '';
          width: 56px;
          height: 56px;
          position: absolute;
          left: 0;
          top: 0;
          background-repeat: no-repeat;
          background-size: contain;
          background-position: 0 0;
        }
        &.introduced-item_fd {
          &::before {
            background-image: url('../../../../../assets/normalGroup/spell/fd.png');
          }
        }
        &.introduced-item_dy {
          &::before {
            background-image: url('../../../../../assets/normalGroup/spell/dy.png');
          }
        }
        &.introduced-item_gz {
          &::before {
            background-image: url('../../../../../assets/normalGroup/spell/gz.png');
          }
        }
      }
    }
  }
}
.diversion {
  width: 696px;
  height: 166px;
  position: relative;
  margin: 0 auto;
  margin-bottom: 30px;
  .bg {
    width: 100%;
    height: 100%;
  }
  .btn {
    position: absolute;
    width: 106px;
    height: 40px;
    bottom: 35px;
    right: 19px;
    -webkit-animation-timing-function: ease-in-out;
    -webkit-animation-name: breathe;
    -webkit-animation-duration: 500ms;
    -webkit-animation-iteration-count: infinite;
    -webkit-animation-direction: alternate;
  }
}
@keyframes breathe {
  0% {
    opacity: 1;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
.at-modal__container {
  width: 600px;
  background: transparent;
  .new-group-overly {
    position: relative;
    .count-down {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 160px;
      color: #fff;
      text-align: center;
      .at-countdown__time-box {
        color: #fff;
        font-size: 30px;
        font-family: initial;
        display: inline-block;
      }
    }
    .new-group-overly-main image {
      width: 100%;
    }
  }
}
// 绘本加购
.teacher-info {
  width: 670px;
  margin: 0 auto;
  text-align: center;
  border-radius: 40px;
  padding-bottom: 33px;
  background-color: #fff;
  margin: 60px auto;
  &-photo {
    width: 140px;
    height: 140px;
    margin-top: -65px;
    border-radius: 50%;
    border: 10px solid #fff;
    box-sizing: border-box;
  }
  &-name {
    font-size: 28px;
    color: #666;
    margin-bottom: 4px;
    .span {
      width: 64px;
      height: 34px;
      font-size: 24px;
      line-height: 34px;
      text-align: center;
      border-radius: 6px;
      color: #ffa700;
      padding: 0 8px;
      margin-left: 6px;
      background-color: #fff4e0;
      &.pic {
        color: #5bbeff;
        background-color: #ebf7ff;
      }
    }
  }
  &-tabs {
    margin-bottom: 41px;
    &-item {
      display: inline-block;
      vertical-align: top;
      width: 110px;
      height: 34px;
      margin-right: 5px;
    }
  }
  &-alert {
    font-size: 56px;
    margin: 52px 0;
    font-weight: 600;
  }
  &-qrcode {
    width: 260px;
    height: 260px;
    margin-bottom: 36px;
  }
  &-tips {
    width: 417px;
    height: 55px;
  }
  &-outWx-btn {
    width: 530px;
    height: 96px;
    line-height: 96px;
    font-size: 38px;
    color: #fff;
    font-weight: 600;
    margin: 0 auto;
    background: #ff6d00;
    border-radius: 96px;
    margin-bottom: 22px;
  }
  &-title {
    font-size: 34px;
    display: flex;
    align-items: center;
    font-weight: 600;
    justify-content: center;
    .span {
      display: inline-block;
      width: 83px;
      height: 2px;
      background: linear-gradient(to right, #fff, #989898);
      &:nth-child(2) {
        background: linear-gradient(to left, #fff, #989898);
      }
      margin: 0 16px;
    }
  }
  &-line {
    width: 608px;
    height: 1px;
    margin: 30px auto;
    background: #f0f0f0;
  }
  &-p {
    font-size: 24px;
    color: #999;
  }
  &.isAddPic {
    border-radius: 30px;
    padding-top: 44px;
    .teacher-info-alert {
      margin-top: 0;
      margin-bottom: 15px;
    }
    .teacher-info-photo {
      margin-top: 47px;
    }
    .line-part {
      height: 40px;
      display: flex;
      background: #fff;
      align-items: center;
      justify-content: space-between;
      .circle {
        overflow: hidden;
        width: 40px;
        height: 40px;
        border-radius: 40px;
        background-color: #ffa700;
        &.left {
          margin-left: -20px;
          background-color: #ffaf19;
        }
        &.right {
          margin-right: -20px;
        }
      }
      .line {
        flex: 1;
        border-bottom: 1px dashed rgba(255, 167, 0, 0.16);
      }
    }
  }
}
