import Taro, { useDidShow, useShareAppMessage, useRouter } from '@tarojs/taro';
import { View, Image, Button, Text } from '@tarojs/components';
import { AtModal, AtCountdown } from 'taro-ui';
import activeBtn from '@/assets/normalGroup/spell/active-btn.png';
import teacherHead from '@/assets/unionCombineOrders/commonTeacherHead.jpg';
import imgFirmConfirm from '@/assets/unionCombineOrders/img-firm-confirm.png';
import imgAutonymConfirm from '@/assets/unionCombineOrders/img-autonym-confirm.png';
import imgScanTips from '@/assets/unionCombineOrders/img-scan-tips.png';
import descImf from '@/assets/normalGroup/spell/desc-img.png';
import { Fragment, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import {
  getTeacherInfo,
  getOrderDetailApi,
  findByKey,
  getTeacherInfoByOrderIdsAndSubject,
} from '@/api/groupbuy';
import SharePoster from '@/pages/normalGroup/art/components/sharePoster';
import sensors from '@/utils/sensors_data';
import './index.scss';

export default function Spellgroup() {
  //路由参数
  const router = useRouter();
  //redux
  const orderId =
    router.params.orderId ||
    useSelector((state: UserStateType) => state.orderId);

  //sendId
  const sendId =
    useSelector((state: UserStateType) => state.sendId) || router.params.sendId;
  //uid
  const userId = useSelector((state: UserStateType) => state.userid);
  // 老师微信号二维码
  const [teacherWeChatQrCode, setTeacherWeChatQrCode] = useState('');

  const [visiblePoster, setVisiblePoster] = useState(false);
  const [countdown] = useState<number>(5 * 60 * 60);
  const [tenModal, setTenModal] = useState(false);
  const [isopen, setIsopen] = useState(false);
  const [teacherlist, setteacherlist] = useState<any>([]);
  // 激活课程
  const [activeClick, setActiveClick] = useState(() => {
    const _a = Taro.getStorageSync('spellactive') == '1';
    return _a;
  });
  const [orderInfo, setOrderInfo] = useState({
    buytime: '0',
    packagesId: '62',
  });

  const subjectName = {
    ART_APP: '美术',
    PICTURE_BOOK: '绘本',
    MUSIC_APP: '音乐',
    WRITE_APP: '书法',
  };

  let invTime = useMemo(() => {
    return Number(orderInfo.buytime) + 10800000 - +new Date();
  }, [orderInfo.buytime]);

  const iShow = useMemo(() => {
    return ['771', '770', '7613'].includes(orderInfo.packagesId);
  }, [orderInfo.packagesId]);

  useEffect(() => {
    router.params.isAddPic && getTeacherList();
  }, [router.params.isAddPic]);

  const getTeacherList = () => {
    var orderIds: any =
      //   decodeURIComponent(`${router.params.unionCombineOrders}`) ||
      Taro.getStorageSync('unionCombineOrders') || '';
    console.log(Taro.getStorageSync('unionCombineOrders'));
    getTeacherInfoByOrderIdsAndSubject({
      orderNo: orderIds && orderIds[0].orderid,
      subject: orderIds[0].type,
    }).then(res => {
      var _res = JSON.parse(JSON.stringify(res.payload));
      getTeacherInfoByOrderIdsAndSubject({
        orderNo: orderIds[1].orderid,
        subject: orderIds[1].type,
      }).then(res1 => {
        let _res1 = JSON.parse(JSON.stringify(res1.payload));
        setteacherlist([_res, _res1]);
      });
    });
  };

  const shareTitEl = () => {
    if (invTime <= 0) {
      return (
        <View>
          拼单<Text>成功</Text>，邀请好友一起来画画
        </View>
      );
    }
    return (
      <View>
        还差<Text>1</Text>人，赶快邀请好友来拼单吧
      </View>
    );
  };

  const handleLesson = () => {
    setActiveClick(true);
    Taro.setStorageSync('spellactive', '1');
    sensors.track('xxys_experienceCoursePage_addv_view', {
      entrance_page: '首页状态按钮',
    });
  };
  const handleShare = type => {
    if (!activeClick) {
      sensors.track('xxys_experienceCoursePage_share_click', {
        share_type: type === 'wx' ? '微信好友' : '保存图片',
      });
    } else {
      sensors.track('xxys_experienceCoursePage_addv_click', {
        share_type: type === 'wx' ? '微信好友' : '保存图片',
      });
    }
    handleLesson();
    type == 'poster' && setVisiblePoster(true);
    isopen && type == 'wx' && setTenModal(true);
  };

  useDidShow(() => {
    if (!activeClick) {
      sensors.track('xxys_experienceCoursePage_share_view', {});
    } else {
      sensors.track('xxys_experienceCoursePage_addv_view', {
        entrance_page: '上一页跳转',
      });
    }
  });

  useShareAppMessage(() => {
    return {
      title: '小熊美术体验课降价啦！终于等到了，快来',
      path: `/pages/normalGroup/art/index?sendId=${userId}&channelId=16348`,
      imageUrl:
        'https://fe-cdn.xiaoxiongmeishu.com/ai-mp-user/image/art-share-img.png',
    };
  });

  // 获取订单详情
  const getOrderdetail = () => {
    orderId &&
      getOrderDetailApi(orderId).then(res => {
        if (res.code === 0) {
          setOrderInfo(res.payload.order);
        }
      });
  };
  const jumpWeChat = () => {
    Taro.navigateToMiniProgram({
      appId: 'wxef2b5fd3726b5d22',
      path: `/pages/introduce/index/index?channel=12726&sendId=${sendId}`,
      complete() {},
    });
  };

  const getConfig = () => {
    findByKey({ key: 'add_wx_page_ad_switch' }).then(res => {
      setIsopen(res.payload == '1');
    });
  };

  //调用获取老师微信号接口
  useEffect(() => {
    orderId &&
      getTeacherInfo({
        orderNo: orderId,
        addSource: router.params.addSource || '1',
      }).then(res => {
        if (res.code === 0) {
          setTeacherWeChatQrCode(res.payload.teacherWeChatQrCode);
        }
      });
    getOrderdetail();
    getConfig();
  }, [orderId]);
  return (
    <View className={`page ${iShow ? 'page-spell' : 'page-nospell'}`}>
      {iShow && (
        <Fragment>
          <View className='title'>
            恭喜获得立减<Text>25元</Text>特权
          </View>
          <View className='share-wrap'>
            <View className='share-title'>
              {/* {shareTitEl()} */}
              邀友一起学，<Text>得25元</Text>现金红包
            </View>
            <Image src={descImf} className='desc-img' />
            <View className='share-btn'>
              {/* <View
                className='share'
                onClick={() => handleShare('poster')}
              ></View> */}
              <View
                className='share share_wx'
                onClick={() => handleShare('wx')}
              >
                <Button className='sharetype-btn' openType='share' />
              </View>
            </View>
          </View>
          {/* {isopen && activeClick && (
            <View
              className='diversion'
              onClick={() => {
                jumpWeChat();
                sensors.track(
                  'xxys_experienceCoursePage_addv_banner_click',
                  {},
                );
              }}
            >
              <Image src={diver_bg} className='bg'></Image>
            </View>
          )} */}
          {!activeClick && (
            <Fragment>
              {/* 温馨提示 */}
              {/* <View className='warm-prompt'>
                <View className='warm-prompt-title'>温馨提示</View>
                <View className='warm-prompt-tips'>
                  <View>
                    1、邀请好友拼单可享受立减7元的优惠，原价36元的体验课程只需支付29元
                  </View>
                  <View>2、拼单不再享受「学完课程返现7元」的活动优惠</View>
                  <View>3、最终解释权归小熊艺术所有</View>
                </View>
              </View> */}
              {/* 激活按钮 */}
              <View className='active-courses-btn' onClick={handleLesson}>
                <Image src={activeBtn} />
              </View>
            </Fragment>
          )}
        </Fragment>
      )}

      {/* 添加专属老师 */}
      {(activeClick || !iShow) &&
        (!router.params.isAddPic ? (
          <>
            <View className='add-teacher'>
              <View className='header-icon'></View>
              <View className='add-teacher-title'>
                添加专属老师，激活课程服务
              </View>
              <View className='wx-qrcode'>
                <Image src={teacherWeChatQrCode} showMenuByLongpress />
              </View>
              <View className='add-teacher-sub'>
                {/* 长按二维码识别关注，公众号内添加老师微信 */}
                长按二维码识别关注
              </View>
              <View className='sub-hr'></View>
              <View className='add-introduced'>
                <View className='introduced-item introduced-item_fd'>
                  在线辅导，提升孩子学习效率
                </View>
                <View className='introduced-item introduced-item_dy'>
                  答疑解惑，解决您的各种问题
                </View>
                <View className='introduced-item introduced-item_gz'>
                  全程跟踪，助力孩子全面成长
                </View>
              </View>
            </View>
          </>
        ) : (
          <>
            <View className='teacher-info isAddPic'>
              <View className='teacher-info-alert'>
                请务必添加二维码 <View>否则无法激活课程</View>
              </View>
              {teacherlist.map((o, i) => {
                return (
                  <View key={i}>
                    <Image
                      className='teacher-info-photo'
                      src={o.teacherHead || teacherHead}
                    ></Image>
                    <View className='teacher-info-name'>
                      {o.teacherName}
                      <Text
                        className={`span ${
                          o.subject != 'ART_APP' ? 'pic' : ''
                        }`}
                      >
                        {subjectName[o.subject]}
                        {/* {o.subject == 'PICTURE_BOOK' ? '绘本' : '美术'} */}
                      </Text>
                    </View>
                    <View className='teacher-info-tabs'>
                      <Image
                        className='teacher-info-tabs-item'
                        mode='widthFix'
                        src={imgFirmConfirm}
                      />
                      <Image
                        className='teacher-info-tabs-item'
                        mode='widthFix'
                        src={imgAutonymConfirm}
                      />
                    </View>
                    <Image
                      className='teacher-info-qrcode'
                      src={o.teacherWeChatQrCode}
                      showMenuByLongpress
                    />
                    <Image
                      className='teacher-info-tips'
                      src={imgScanTips}
                      mode='widthFix'
                    />
                    {/* {
                      !i && <View className='line-part' v-if='!index'>
                        <View className='circle left'></View>
                        <View className='line'></View>
                        <View className='circle right'></View>
                      </View>
                    } */}
                    <View className='teacher-info-line'></View>
                    {i == 1 && (
                      <View className='teacher-info-p'>
                        *若添加失败，截图保存二维码在微信中识别打开
                      </View>
                    )}
                  </View>
                );
              })}
            </View>
          </>
        ))}
      {/* 海报分享 */}
      <SharePoster
        visible={visiblePoster}
        closeModal={() => setVisiblePoster(false)}
      />
      <AtModal isOpened={tenModal} onClose={() => setTenModal(false)}>
        <View
          className='new-group-overly'
          onClick={() => {
            setTenModal(false);
            sensors.track('xxys_experienceCoursePage_addv_pop_click', {});
            jumpWeChat();
          }}
        >
          <View className='count-down'>
            领取倒计时：
            <AtCountdown
              seconds={countdown}
              format={{ hours: ':', minutes: ':', seconds: '' }}
            />
          </View>
          {/* <View
            className='new-group-overly-main'
            // onClick={(e: ITouchEvent) => {
            //   e.stopPropagation();
            // }}
          >
            <Image src={diver_poster} mode='widthFix'></Image>
          </View> */}
        </View>
      </AtModal>
    </View>
  );
}
