import Taro, { useRouter } from '@tarojs/taro';
import { useState, useEffect } from 'react';
import qs from 'qs';
import { View, Image, Text, Button } from '@tarojs/components';
import headerLeft from '@/assets/follow/headerLeft.png';
import headerRight from '@/assets/follow/headerRight.png';
import add from '@/assets/follow/addTeacher.gif';
import follow from '@/assets/follow/follow.gif';
import addWxGroup from '@/assets/follow/addWxGroup.gif';
import sensors from '@/utils/sensors_data';
import WxLogin from '@/components/wxlogin';
import './index.scss';

export default function Contactshare(props) {
  const { subjectTitle } = props;
  const [img, setImg] = useState<string>(add);
  const [newImg, setNewImg] = useState<string>(
    'https://fe-cdn.xiaoxiongmeishu.com/ai-mp-user/image/newAddTeacher.png?1',
  );
  const [text, setText] = useState<string>('添加老师微信获取专人服务');
  const [title, setTitle] = useState<string>('添加老师');
  const [desc, setDesc] = useState<string>('');
  const router = useRouter();
  let params = router.params;

  const setType = () => {
    setImg(follow);
    setNewImg(
      'https://fe-cdn.xiaoxiongmeishu.com/ai-mp-user/image/newFollow.png?1',
    );
    setText('关注公众号');
    setTitle('关注公众号');
    setDesc('领取200小熊币 查看物流信息');
  };
  const setGroupType = () => {
    setImg(addWxGroup);
    setNewImg(
      'https://fe-cdn.xiaoxiongmeishu.com/ai-mp-user/image/newAddwxgroup.png',
    );
    setText('激活课程');
    setTitle('添加学习群');
    setDesc('');
  };

  useEffect(() => {
    // if (params.type != '1' && params.type != '104') {
    //   setType();
    // }
    if (params.type && !['1', '104', '105'].includes(params.type)) {
      setType();
    }
    if (params.type == '10') {
      setGroupType();
    }
  }, [params.type]);

  useEffect(() => {
    Taro.setNavigationBarTitle({
      title,
    });
  }, [title]);
  const report = () => {
    if (subjectTitle == '小熊书法') {
      sensors.track('sf_Experiencecourse_addteacherpage_addbuttonclick', {});
    } else {
      if (title === '添加老师')
        sensors.track('xxms_testcourse_appletsfollowpage_addbuttonclick', {});
      else sensors.track('xxms_testcourse_appletsfollowpage_pageclick', {});
    }
  };
  Taro.hideHomeButton(); //隐藏返回首页

  return (
    <Button
      className='allButton'
      openType='contact'
      showMessageCard
      sendMessagePath={qs.stringify(params)}
      sendMessageImg={newImg}
      onClick={() => {
        report();
      }}
    >
      <WxLogin
        subject={subjectTitle === '小熊书法' ? 'WRITE_APP' : 'ART_APP'}
        isIntroduce={false}
        isFollow={false}
      />
      <View className='Index'>
        <View className='content'>
          <View className='header'>
            <Image src={headerLeft} className='headerImage' />
            <Text className='headerText'>{text}</Text>
            <Image src={headerRight} className='headerImage' />
          </View>
          {params.type != '1' ? (
            <View className='header-desc'>{desc}</View>
          ) : null}
          <View className='bodyImage' open-type='contact'>
            <Image src={img} className='addImage' />
          </View>
          <View className='addTeacher'>{title}</View>
        </View>
      </View>
    </Button>
  );
}
// FollowIndex.config = {
//   navigationBarTitleText: '关注老师'
// }
