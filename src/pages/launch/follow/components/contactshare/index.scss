@import '../../../../../theme/groupbuy/common.scss';
page {
  width: 100%;
  height: 100%;
}
.allButton {
  width: 100%;
  height: 100%;
  background: #f2f2f2;
  margin-left: 0px;
  margin-right: 0px;
  padding: 0px;
  border: none;
  border-radius: 0rpx;
}
.allButton::after {
  border: none;
  border-radius: 0rpx;
}

.Index {
  width: 100%;
  height: 100%;
  position: relative;
}
.content {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  width: 686px;
  height: fit-content;
  padding-bottom: 42px;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 40px;
}
.header {
  text-align: center;
  padding-bottom: 56px;
  padding-top: 46px;
  box-sizing: border-box;
}
.headerImage {
  width: 45px;
  height: 20px;
}
.headerText {
  padding: 0 25px;
  box-sizing: border-box;
}
.header-desc {
  height: 46px;
  font-size: 32px;
  color: #666666;
  line-height: 46px;
  text-align: center;
  margin: -36px 0 40px;
}
.bodyImage {
  width: 620px;
  height: 660px;
  border-radius: 25px;
  margin: 0 auto;
  padding-bottom: 40px;
  box-sizing: border-box;
  // background: red;
  .addImage {
    width: 100%;
    height: 100%;
    border-radius: 25px;
  }
}
.addTeacher {
  width: 620px;
  height: 104px;
  margin: 0 auto;
  background: #ff7000;
  border-radius: 52px;
  line-height: 104px;
  text-align: center;
  font-size: 40px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #ffffff;
  animation: scaleTeacher 2s ease-in-out infinite;
}
@keyframes scaleTeacher {
  /* 定义关键帧、scaleTeacher是需要绑定到选择器的关键帧名称 */
  0% {
    transform: scale(0.7);
  }

  25% {
    transform: scale(1.05);
  }

  50% {
    transform: scale(0.7);
  }

  75% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(0.7);
  }
}
