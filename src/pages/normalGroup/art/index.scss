// @import '../../../theme/groupbuy/common.scss';
@import '../../../theme/normalGroup/common.scss';

.container {
  font-size: 0;
  padding-bottom: calc(112rpx + env(safe-area-inset-bottom));
  background: #ffe66d;

  .layout-header {
    padding: 40px 0 16px;
  }

  .fixedTop {
    position: sticky;
  }

  .intro-part {
    .intro-con {
      padding: 10px $font-32;
      background-color: $light-yellow;

      .intro-text {
        font-size: $font-24;
        color: $c_red;
      }

      .icon {
        width: $font-32;
        margin-right: 16px;
      }
    }
  }

  .body-part {
    // margin-top: 80px;
    padding: 69px $font-32;
    background-color: $de_orange;

    &.newArt {
      padding: 30px 0;
      background-color: #ffe161;
      &.pb0 {
        padding-bottom: 0;
      }
    }

    .body-img-part {
      position: relative;

      .overfolw {
        width: 600px;
        margin: 0 auto;
        top: 300px;
        left: 0;
        right: 0;
        height: 746px;
        position: absolute;
        overflow-x: hidden;
        .slide-insert {
          height: 746px;
          overflow-y: visible !important;
          .slide-item {
            overflow-y: visible !important;
            .slide-3 {
              width: 580px !important;
              height: 602px !important;
            }
          }
          .insert-swiper {
            height: 100%;
            text-align: center;
            overflow-y: visible !important;
          }
        }
        &.slide-index-1 {
          height: 1400px;
          top: 350px;
          .slide-insert {
            height: 1350px;
            .slide-1 {
              width: 600px !important;
              height: 1200px !important;
            }
          }
        }
      }
      &.pd {
        padding: 50px 30px;
        swiper .wx-swiper-dots {
          display: none;
        }
      }
      &.mbaround {
        padding: 20px 32px;
      }

      .slide-index-2 {
        height: 1810px;
        box-sizing: border-box;
        .insert-swiper {
          height: 634px !important;
          width: 634px;
          top: 240px;
          left: 60px;
          position: absolute;
          .slide-item,
          .slide-1 {
            height: 634px !important;
            width: 634px !important;
          }
        }
      }
      &:last-child {
        margin: 0;
      }
    }
  }

  .footer-part {
    font-size: $font-24;
    color: #999;
    line-height: $font-38;
    padding: 39px 30px;
    letter-spacing: 1px;
    background-color: $linght-grey;
  }

  .suction-bottom {
    bottom: 0;
    z-index: 101;

    .bg_white {
      height: 116px;
      padding-bottom: env(safe-area-inset-bottom);
      margin-top: $font-20;
      position: relative;
      box-sizing: content-box;

      &.trans-bg {
        // background-color: transparent !important;
      }

      .sel-img {
        margin-top: 10px;
        position: relative;
      }

      .img-btn-box {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        bottom: 0;
        display: flex;
        padding-left: 200px;
        box-sizing: border-box;
        align-items: baseline;
        justify-content: center;
        .thirtySix-btn {
          width: 250px;
          height: 84px;
        }

        .twentyNine-btn {
          width: 500px;
          height: 84px;
        }
        &.old {
          padding-left: 0;
        }
        &.new {
          padding: 0 4px 0 24px;
          justify-content: space-between;
          .twentyNine-btn.new-btn {
            width: 537px;
            height: 108px;
            .sel-img {
              width: 537px;
              height: 108px;
            }
          }
          .left-img {
            width: 133px;
            height: 109px;
          }
          .twentyNine-btn.zero-btn {
            width: 504px;
            height: 114px;
            line-height: 108px;
            text-align: center;
            font-size: 40px;
            font-weight: 500;
            position: absolute;
            right: 0;
            top: 10px;
            z-index: 2;
            .text {
              width: 504px;
              height: 114px;
              line-height: 78px;
              text-align: center;
              color: #fff;
              font-size: 40px;
              font-weight: 500;
              z-index: 3;
              position: absolute;
              .momey {
                font-size: 32px;
              }
              .zero {
                font-size: 54px;
                font-family: DINAlternate, DINAlternate;
              }
              .del {
                font-size: 24px;
                font-weight: 300;
                text-decoration: line-through;
              }
            }
            .right-img {
              width: 100%;
              height: 100%;
              position: absolute;
              left: 0;
              top: 0;
              z-index: inherit;
            }
          }
        }
      }

      .sle-img-part {
        margin-left: 18px;

        .sel-img {
          width: 165px;
          margin-top: -25px;
        }
      }

      .sle-price-part {
        margin-left: 18px;
        font-size: $font-32;

        .dollar,
        .price {
          font-size: $font-36;
          color: $c_red;
        }

        .price {
          font-size: 56px;
        }
      }

      .buy-submit {
        width: 297px;
        height: 88px;
        font-size: $font-32;
        line-height: 88px;
        margin-right: $font-28;
        border-radius: 88px;
        background-color: $c_red;
      }
      .coupon-36 {
        position: absolute;
        width: 270px;
        right: 34px;
        top: -60px;
      }
    }
    .formh5twentynine {
      display: flex;
      justify-content: space-between;
      align-items: center;
      &.blueBtn {
        height: 164px;
        margin-top: 0;
        background-color: transparent !important;
      }
      .left-img {
        width: 100%;
        vertical-align: top;
        pointer-events: none;
      }
      .bntgif {
        position: absolute;
        right: 5px;
        top: 20px;
        bottom: 0;
        width: 340px;
      }
    }
  }

  .at-float-layout .layout-header {
    text-align: center;
    background-color: #fff;
  }

  .giveMask {
    .at-modal__container {
      width: 600px;
      background-color: transparent;
    }

    .giveMaskImg {
      width: 600px;
      height: 800px;
    }
  }

  .giveMask-new {
    .at-modal__container {
      width: 636px;
      text-align: center;
      border-radius: 24px;
      height: 625px;
      padding-top: 60px;
      box-sizing: border-box;
      overflow: visible;
    }
    &-title {
      font-size: 44px;
      font-weight: bold;
      margin-bottom: 33px;
    }
    .coupon-img {
      width: 594px;
      height: 279px;
      margin-bottom: 33px;
    }
    .submit-btn {
      width: 396px;
      height: 88px;
      line-height: 88px;
      text-align: center;
      border-radius: 88px;
      color: #fff;
      margin: 0 auto;
      font-size: 38px;
      background-color: #ffa511;
    }
    .mask-close {
      position: absolute;
      width: 48px;
      height: 48px;
      bottom: -80px;
      left: 0;
      right: 0;
      margin: 0 auto;
    }
  }

  .zero-giveMask {
    .at-modal__container {
      width: 600px;
      height: 800px;
      background: none;
      overflow: visible;
      .coupon-img {
        width: 100%;
        height: 100%;
      }
      .submit-btn {
        position: absolute;
        bottom: 0px;
        width: 340px;
        height: 100px;
        left: 130px;
        z-index: 100;
      }
      .mask-close {
        position: absolute;
        width: 48px;
        height: 48px;
        bottom: -80px;
        left: 0;
        right: 0;
        margin: 0 auto;
        z-index: 100;
      }
    }
  }
}

.at-modal__overlay {
  background-color: rgba(0, 0, 0, 0.7);
}

.viewoview {
  position: fixed;
  right: 24px;
  bottom: 230px;
  width: 160px;
  height: 160px;
  background: url('../../../assets/normalGroup/newArt/show-video-btn.gif')
    no-repeat 0 0/100%;
}
swiper .wx-swiper-dots {
  margin-top: 40px;
  bottom: 30px;
  .wx-swiper-dot {
    width: 28rpx;
    height: 28rpx;
    border-radius: 28rpx;
    background: #f5f5f5 !important;
    box-shadow: 0px 0px 16px 0px inset #d6d6d6 !important;
  }
  .wx-swiper-dot-active {
    box-shadow: none !important;
    background: linear-gradient(to bottom, #ffdf00, #ffb300) !important;
  }
}

.formh5twentynine-btn-img {
  position: absolute;
  right: 18px;
  top: 42px;
  bottom: -6px;
  width: 271px;
  height: 128px;
  animation-name: scaleDraw; /*关键帧名称*/
  animation-timing-function: ease-in-out; /*动画的速度曲线*/
  animation-iteration-count: infinite; /*动画播放的次数*/
  animation-duration: 1.2s; /*动画所花费的时间*/
  img {
    width: 100%;
    height: 100%;
  }
}

@keyframes scaleDraw {
  /*定义关键帧、scaleDrew是需要绑定到选择器的关键帧名称*/
  0% {
    transform: scale(1); /*开始为原始大小*/
  }
  25% {
    transform: scale(1.06); /*放大1.1倍*/
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(1.06);
  }
}
