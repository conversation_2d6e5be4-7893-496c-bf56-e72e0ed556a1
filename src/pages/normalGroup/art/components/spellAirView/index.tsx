import Taro, { useRouter } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { useState, useEffect, useMemo } from 'react';
import { countDownFormat } from '@/utils/index';
import sensors from '@/utils/sensors_data';
import './index.scss';

export default function SpellAirView(props) {
  const router = useRouter();
  const [timeText, setTimeText] = useState<Array<string>>(['00', '00', '00']);
  const [isAct, setIsAct] = useState(false);
  let timer = null as any;
  let invTime = useMemo(() => {
    return Number(props.orderInfo.buytime) + 10800000 - +new Date();
  }, [props.orderInfo.buytime]);

  const iShow = useMemo(() => {
    return (
      ['771', '770', '7613'].includes(props.orderInfo.packagesId) &&
      props.orderInfo.buytime > 0
    );
  }, [props.orderInfo.packagesId]);

  const countDown = () => {
    timer && clearInterval(timer);
    timer = setInterval(() => {
      invTime -= 1000;
      setTimeText(() => {
        return countDownFormat(invTime, 'hh-mm-ss').split('-');
      });
      if (invTime <= 0) {
        invTime = 0;
        clearInterval(timer);
        setIsAct(true);
      }
    }, 1000);
  };

  const handleJump = () => {
    sensors.track('xxys_experienceCoursePage_status', {
      status: invTime <= 0 ? '拼单成功' : '拼单中',
    });
    // soul区分是否是拼团
    // form=1代表是从艺术宝app跳转过来到购买页的
    Taro.setStorageSync('spellactive', '1');
    Taro.navigateTo({
      url: `/pages/launch/follow/index?soul=artindex${
        router.params.from == '1' ? '&from=1' : ''
      }`,
    });
  };

  useEffect(() => {
    iShow && countDown();
    return () => {
      clearInterval(timer);
    };
  }, [iShow]);

  return (
    <>
      {iShow && (
        <View className='air-view' onClick={handleJump}>
          <View
            className={`air-view-wrap ${
              invTime <= 0 || isAct ? 'air-view-wrap_success' : ''
            }`}
          >
            {invTime > 0 && !isAct && (
              <View className='view-countdown'>
                <Text>{timeText[0] || '00'}:</Text>
                <Text>{timeText[1] || '00'}:</Text>
                <Text>{timeText[2] || '00'}</Text>
              </View>
            )}
          </View>
        </View>
      )}
    </>
  );
}
