import { getPosterListApi } from '@/api/1v1k8s';
import { View, Image } from '@tarojs/components';
import { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import { saveImage } from '@/utils/auth';
import Taro from '@tarojs/taro';
import './index.scss';

export default function SharePoster(props) {
  const NODE_ENV = process.env.NODE_ENV;
  //uid
  const userId = useSelector((state: UserStateType) => state.userid);
  const posterTypeId = NODE_ENV === 'online' ? 17 : 52;
  const [poster, setPoster] = useState('');
  const iShow = useMemo(() => {
    return props.visible;
  }, [props.visible]);
  const getQrCode = () => {
    let url = '';
    let tmp = new Date().valueOf().toString();
    tmp = tmp.substr(tmp.length - 2, tmp.length);
    const baseUrl = {
      development: 'https://dev.meixiu.mobi/ai-app-h5-activity-dev/b',
      dev: 'https://dev.meixiu.mobi/ai-app-h5-activity-dev/b',
      test: 'https://test.meixiu.mobi/ai-app-h5-activity-test/b',
      gray: '',
      online: 'https://www.xiaoxiongmeishu.com/activity/b',
    };

    url = `${baseUrl[NODE_ENV]}?sendId=${userId}&msChannelId=10954&t=${tmp}`;
    return url;
  };
  const handleClose = () => {
    props.closeModal();
  };

  const handleSave = () => {
    saveImage(poster).then(() => {
      Taro.showToast({
        title: `保存成功`,
        icon: 'none',
      });
    });
  };

  useEffect(() => {
    userId &&
      getPosterListApi({
        id: posterTypeId,
        qrCode: getQrCode(),
        uid: userId,
      }).then(res => {
        if (res.code === 0) {
          const payload: any = res.payload;
          // 标准海报随机
          let randomNum = Math.floor(Math.random() * payload.length);
          payload.map(v => (v.pType = 'art'));
          setPoster(payload[randomNum].posterUrl);
        } else {
          Taro.showToast({
            title: res.errors || '服务器开小差了',
            icon: 'none',
          });
        }
      });
  }, [userId]);

  return (
    <View className={`m-modal ${iShow ? '' : 'm-hiden'}`} onClick={handleClose}>
      <View className='m-layer'></View>
      <View className='m-section'>
        <View className='m-content'>
          <View className='m-poster'>
            <Image src={poster} onLongPress={handleSave} />
          </View>
          <View className='m-word'>
            <View>长按保存图片 分享好友一起学 </View>
            {/* <View>赶快邀请好友来拼单吧 </View> */}
          </View>
        </View>
      </View>
    </View>
  );
}
