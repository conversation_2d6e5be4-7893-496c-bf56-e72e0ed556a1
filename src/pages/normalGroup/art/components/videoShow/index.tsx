import { View, Video, Image, CoverView, CoverImage } from '@tarojs/components';
import Taro, { useDidShow } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import videobtn from '@/assets/normalGroup/newArt/videobtn01.png';
import './index.scss';

export default function videoShow(props) {
  const { isShow, close, click } = props;
  const VideoContext = Taro.createVideoContext('videoId');
  const [showVideo, setShowVideo] = useState(false);

  const onFullscreenChange = e => {
    if (!e.detail.fullScreen) {
      setShowVideo(false);
      VideoContext.pause();
      close();
    }
  };

  useEffect(() => {
    if (isShow) {
      setShowVideo(true);
      VideoContext.requestFullScreen({ direction: 0 });
      VideoContext.play();
    }
  }, [isShow]);

  useDidShow(() => {
    const ishow = Taro.getStorageSync('vshow');
    if (ishow == '1') {
      setShowVideo(true);
      VideoContext.play();
    } else if (isShow == '2') {
      setShowVideo(false);
      VideoContext.pause();
    }
  });

  return (
    <View
      className='video_modal_wrap'
      style={{ display: showVideo ? '' : 'none' }}
    >
      <View className='video_modal_show'>
        <Video
          id='videoId'
          autoplay={showVideo}
          loop
          src='https://s2.xiaoxiongmeishu.com/zjs/DA957FB0-623B-4D72-9D78-126F8EF0C18E.MP4'
          direction={0}
          onFullscreenChange={onFullscreenChange}
        >
          <CoverView
            className='videoBtn'
            onClick={() => {
              VideoContext.exitFullScreen();
              VideoContext.pause();
              setShowVideo(false);
              click();
            }}
          >
            <CoverImage src={videobtn} />
          </CoverView>
        </Video>
      </View>
    </View>
  );
}
