.video_modal_wrap {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1001;
  .video_modal_show {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #000000;
    position: relative;
    .videoBtn {
      width: 163px;
      height: 161px;
      position: fixed;
      right: 17px;
      top: 1100px;
      animation: scaleJumpbtn 0.8s ease-in-out infinite;
      z-index: 999;
      image {
        width: 100%;
        height: 100%;
      }
    }
    .video_b {
      width: 56px;
      height: 56px;
      position: absolute;
      top: 180px;
      right: 40px;
      z-index: 99;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #ffffff;
      .video_b_icon {
        width: 28px;
        height: 28px;
        background: url('../../../../../assets/normalGroup/newArt/close.png')
          no-repeat 0 0;
        background-size: contain;
      }
    }
    // video {
    //   position: relative;
    //   z-index: 10;
    // }
  }
  @keyframes scaleJumpbtn {
    0% {
      transform: scale(0.9);
    }
    50% {
      transform: scale(1);
    }
    100% {
      transform: scale(0.9);
    }
  }
}
