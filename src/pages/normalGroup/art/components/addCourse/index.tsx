import Taro from '@tarojs/taro';
import { useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import { useEffect, forwardRef, useState, useImperativeHandle } from 'react';
import {
  getBuyOrderByUserId,
  getManagementByTypeAndCategory,
  queryOrderByUserId,
  supSwitch,
} from '@/api/groupbuy';

const ChildComponent = forwardRef((props: any, ref) => {
  const { setAttachSubjectHandler, selPicBookHandler } = props;
  useImperativeHandle(ref, () => ({
    getSubjectBuyType,
  }));
  //   书法音乐加购开关
  const [muawrswitch, setmuawrswitch] = useState('0');
  const userId = useSelector((state: UserStateType) => state.userid);
  const channelId = useSelector((state: UserStateType) => state.channelId);
  const isLive =
    process.env.NODE_ENV == 'online' || process.env.NODE_ENV == 'prod';
  useEffect(() => {
    supSwitch({
      key: 'xcxjiagou',
    }).then(res => {
      setmuawrswitch(res.payload + '');
    });
  }, []);

  useEffect(() => {
    // const accountInfo = Taro.getAccountInfoSync();
    // if (accountInfo.miniProgram.appId != 'wx817df83e561f7921') return;
    userId && getSubjectBuyType();
  }, [userId]);

  const getSubjectBuyType = () => {
    selPicBookHandler(false);
    if (userId) {
      setTimeout(() => {
        if (['1', '3'].includes(muawrswitch)) checkBuyWrite();
        else if (muawrswitch == '2') checkBuyMusic();
      }, 1500);
      //   else {
      //     if (pciswitch) getUserType();
      //   }
    }
  };

  // 当前用户是否购买过绘本
  const getUserType = async () => {
    getBuyOrderByUserId({
      userId,
      orderRegType: 'FIRST',
      subjects: 'PICTURE_BOOK',
    }).then(res => {
      let { payload = [] } = res;
      if (payload.length > 0) {
        if (muawrswitch) checkBuyWrite();
      } else {
        getBuyOrderByUserId({
          userId,
          orderRegType: 'EXPERIENCE',
          subjects: 'PICTURE_BOOK',
          includeRefund: true,
        }).then(res1 => {
          if (res1.payload.length > 0) {
            if (muawrswitch) checkBuyWrite();
          } else {
            getManagementByTypeAndCategory(channelId).then(res2 => {
              const { status } = res2;
              if (status == 'OK') {
                setAttachSubjectHandler({
                  packagesId: isLive ? 1582 : 7670,
                  stage: res2.payload.period,
                  sup: 'S1',
                  topicId: 1,
                  channel: '15815',
                  title: '小熊阅读绘本创作',
                  desc: '限时免费加赠4节课',
                  subject: 'PICTURE_BOOK',
                  name: '绘本',
                });
              } else setAttachSubjectHandler(null);
            });
          }
        });
      }
    });
  };
  // 校验是否能买书法课
  const checkBuyWrite = () => {
    if (!muawrswitch) return;
    Taro.showLoading();
    queryOrderByUserId({
      userId: userId,
      channels: isLive ? '17097' : '4245',
      subjects: 'WRITE_APP',
      packageId: isLive ? 910 : process.env.NODE_ENV == 'test' ? 8915 : 7659,
    })
      .then(res => {
        const { experienceCheckMap } = res.payload;
        // experienceCheckMap.WRITE_APP为true 表示之前已经买过体验课了
        if (experienceCheckMap.WRITE_APP) {
          if (muawrswitch == '1') checkBuyMusic();
          setAttachSubjectHandler(null);
          return;
        } else
          setAttachSubjectHandler({
            packagesId: isLive
              ? 910
              : process.env.NODE_ENV == 'test'
              ? 8915
              : 7659,
            stage: 0,
            sup: 'S1',
            topicId: 7,
            channel: isLive ? '17097' : '4245',
            title: '在线书法互动硬笔书法课',
            desc: '限时特惠4节课',
            subject: 'WRITE_APP',
            name: '书法',
          });
      })
      .finally(() => {
        Taro.hideLoading();
      });
  };
  // 校验是否能买音乐课
  const checkBuyMusic = () => {
    Taro.showLoading();
    queryOrderByUserId({
      userId: userId,
      channels: isLive ? '17096' : '3515',
      subjects: 'MUSIC_APP',
      packageId: 762,
    })
      .then(res => {
        const { experienceCheckMap } = res.payload;
        // experienceCheckMap.MUSIC_APP为true 表示之前已经买过体验课了
        if (experienceCheckMap.MUSIC_APP) {
          setAttachSubjectHandler(null);
          if (muawrswitch == '2') checkBuyWrite();
        } else
          setAttachSubjectHandler({
            packagesId: 762,
            stage: 0,
            sup: 'S3',
            topicId: 4,
            channel: isLive ? '17096' : '3515',
            title: '0基础也能学的音乐弹唱课',
            desc: '限时特惠4节课',
            subject: 'MUSIC_APP',
            name: '音乐',
          });
      })
      .finally(() => {
        Taro.hideLoading();
      });
  };

  return <></>;
});
export default ChildComponent;
