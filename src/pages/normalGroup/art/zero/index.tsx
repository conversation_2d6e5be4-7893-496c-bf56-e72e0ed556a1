import { useEffect, useRef, useState } from 'react';
import Taro, { useDidShow, useRouter, useShareAppMessage } from '@tarojs/taro';
import { AtFloatLayout, AtModal, AtModalContent } from 'taro-ui';
import { Image, Swiper, SwiperItem, View, Text } from '@tarojs/components';
import sensors from '@/utils/sensors_data';
import store from '@/store/groupbuy/index';
import { UserStateType } from '@/store/groupbuy/state';
import { levelV1, levelV2 } from '@/common/data.config';
import '@/theme/custom-taro-ui.scss';
/** 图片部分   **/
import modalCloseImg from '@/assets/groupbuy/thirtySix/close.png';
import redeemImg from '@/assets/normalGroup/newArt/retain-fbb.gif';
import artBanner from '@/assets/normalGroup/newArt/banner-new.png';
import artBannerOnly from '@/assets/normalGroup/artnew/header-banner-only.png';
import artBanner36 from '@/assets/normalGroup/thirtySix/header-banner-36.png';
import artPriceFull from '@/assets/normalGroup/newArt/price.png';
import artPriceFull36 from '@/assets/normalGroup/thirtySix/reback-price-36.png';
import selIcon from '@/assets/groupbuy/index/gift-new.jpg';
import selIconV1 from '@/assets/groupbuy/index/gift-new-v2.png';
import shareImg from '@/assets/normalGroup/art/share-img-20240708.jpg';
import rebackBtn from '@/assets/normalGroup/newArt/reback-btn.png';
import SlideS4 from '@/assets/normalGroup/newArt/slide-s4.png';
import SlideIndex1 from '@/assets/normalGroup/newArt/slide-index-1.png';
import SlideIndex2 from '@/assets/normalGroup/newArt/slide-index-2.png';
import leftImg from '@/assets/normalGroup/newArt/left-img.png';
import SlideS1To3 from '@/assets/normalGroup/newArt/slide-s1-3.png';
import rebackBtn36 from '@/assets/normalGroup/thirtySix/bottom-img-36.png';
import giveMaskImg from '@/assets/normalGroup/newArt/giveMask.gif';
import zeroModal from '@/assets/normalGroup/newArt/zeroModal.png';
import zeroIntro from '@/assets/normalGroup/newArt/zeroIntro.png';
import zeroBanner from '@/assets/normalGroup/newArt/zeroBanner.png';
import zeroBtn from '@/assets/normalGroup/newArt/zeroBtn.png';
import Orderdetailmp from '@/components/orderDetailMp';
import { useSelector } from 'react-redux';
import {
  getChannelListApi,
  getExpUserApi,
  getOrderDetailApi,
  // getWeixinByUnionid,
  reportScanNum,
  getUserCouponByPackageId,
} from '@/api/groupbuy';
// @ts-ignore
import CommonTop from '../../../../components/commonTop';
import '../index.scss';
import LayoutLevel from '../../../../components/level';
import LayoutOrderV1 from '../../../../components/orderV1';
import WxLogin from '../../../../components/wxlogin';
import SpellAirView from '../components/spellAirView';
import VideoShow from '../components/videoShow';
import AddCourse from '../components/addCourse';
// 中间部分图片
// // 有spreadId
const artRequireContext = require.context(
  '@/assets/normalGroup/newArt',
  true,
  /^\.\/.*zjs-img0[123457]\.png$/,
);
// 周周分享引用图片
const artFromWeRequireContext = require.context(
  '@/assets/normalGroup/artnew',
  true,
  /^\.\/.*zjs-img0[123457]-we\.png$/,
);

// 0元赠课券id，不区分环境
const zeroCouponPackageId = '1888';
// 0元券id，不区分环境
const couponId = '351';

/** 图片部分完 **/
export default () => {
  const router = useRouter();

  const orderRef: any = useRef(null);
  const addCourseRef: any = useRef(null);

  const [onlyshow36, setOnlyshow36] = useState(false);

  const [fromWeekly, setFromWeekly] = useState(false);

  /** 常量 */
  // const artIntroText = ['仅¥2.9元/节', '1对1辅导', '课程永久回放'];
  const footerText = [
    '客服电话：400-002-8080  浙ICP备20001038号-3',
    '地址：北京市朝阳区朝来高科技产业园区36号院10号楼',
    '杭州小伴熊科技有限公司 版权所有',
  ];
  const artBodyPartImages = artRequireContext.keys().map(artRequireContext);
  const artWeBodyPartImages = artFromWeRequireContext
    .keys()
    .map(artFromWeRequireContext);

  /** 常量结束 */
  //redux
  const orderId = useSelector((state: UserStateType) => state.orderId);
  const [orderInfo, setOrderInfo] = useState({
    buyTime: '0',
    packageId: 62,
  });

  /** 订单浮窗 */
  // 显示套餐隐藏浮窗
  const [isOpenWind, setIsOpenWind] = useState<boolean>(false);
  // 显示隐藏订单浮窗
  const [isShowOrder, setIsShowOrder] = useState<boolean>(false);
  // 订单支付页面的数据
  const [payPageData, setPayPageData] = useState<any | null>();
  // 新增29元拼单增加变量区分36元
  // 拼团倒计时需要获取订单详情
  const [payOrderData, setPayOrderData] = useState({
    orderType: '36',
    packagesId: '62',
  });
  //  0元订单不能加购！！ 加购订单数据
  const [attachSubject, setAttachSubject] = useState<any>(null);
  const [selectPicBookType, setSelectPicBookType] = useState(false);
  // 显示video
  const [showVideo, setShowVideo] = useState(false);
  // 用户放弃付款再次确认弹窗
  const [showRedeemModal, setShowRedeemModal] = useState<boolean>(false);
  // 是否显示挽留窗口（只第一次点击关闭）
  const [needShowRedeem, setNeedShowRedeem] = useState<boolean>(true);
  // 级别类型
  //   const [levelType] = useState<number>(Math.floor(Math.random() * 3));
  const [levelType] = useState<number>(1);
  //私域二级渠道列表
  const [sYChannel, setSYChannel] = useState<any[]>([]);
  const [zeroPackageModalVis, setZeroPackageModalVis] = useState(false); // 0元赠课弹窗
  //  是否领取优惠券
  const [isReceive, setIsreceive] = useState(false); // 是否领取了36元优惠券，然后展示0元支付
  //   绘本课程购买开关是否开启
  //   const [pciswitch, setpciswitch] = useState(false);
  // 关闭挽留弹窗
  const hideRedeemHandle = () => {
    setShowRedeemModal(false);
  };
  /** 传给子页面的参数 **/

  //openid
  const openId = useSelector((state: UserStateType) => state.openid);
  const spreadId = useSelector((state: UserStateType) => state.spreadId);
  const channelId = useSelector((state: UserStateType) => state.channelId);

  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  const unionId = useSelector((state: UserStateType) => state.unionId);

  // 判断是否展示0元赠课弹窗
  useEffect(() => {
    handleZeroPackageVis();
  }, []);

  useEffect(() => {
    if (openId && spreadId && channelId === '6634') {
      const data = {
        openid: openId,
        spreadUserId: spreadId,
      };
      reportScanNum(data).then((res) => {
        console.log(res);
      });
    }
  }, [openId, spreadId, channelId]);

  //   美术音乐联报开关
  //   const [artMusicSwitch, setArtMusicSwitch] = useState<boolean>(false);

  const handleClickZero = () => {
    setZeroPackageModalVis(false);
    setIntroImg(zeroIntro);
    setBanner(zeroBanner);
    // setPayOrderData({
    //   orderType: '0',
    //   packagesId: zeroCouponPackageId,
    // });
    setAttachSubject(null);
    setIsreceive(true);
    // sensors.track('xxys_experienceCoursePage_buy_click', {
    //   user_role: store.getState().userRole,
    //   channel_id: store.getState().channelId,
    //   button_position: '0元',
    //   buy_model: 'model_4',
    //   sendId: store.getState().sendId || '',
    // });
    // sensors.track('xxys_Landingpage_couponpop_click', {
    //   operation_type: '立即领取',
    // });
  };

  const handleZeroPackageVis = async () => {
    const res = await disCouponHandler();
    if (res) {
      setZeroPackageModalVis(true);
    }
  };

  // 判断是否还有0元支付的券
  const disCouponHandler = () => {
    return new Promise((resolve) => {
      const sendId = router.params['sendId'];
      getUserCouponByPackageId(sendId, zeroCouponPackageId).then((res) => {
        const { payload } = res;
        let hasNoUse = false;
        if (payload?.length) {
          for (let item of payload) {
            if (item.status == 'NOACTIVE') hasNoUse = true;
          }
        }
        if (!hasNoUse) {
          Taro.showToast({
            title: '抱歉，此券已被抢用，您可29元拼团购课体验',
            icon: 'none',
            duration: 4000,
          });
          setPayOrderData({
            orderType: '29',
            packagesId:
              process.env.NODE_ENV == 'online'
                ? '771'
                : process.env.NODE_ENV == 'test'
                ? '770'
                : '7613',
          });
        }
        resolve(hasNoUse);
      });
    });
  };

  // 监听订单浮窗的打开和关闭
  const watchShowOrder = (state, pageData) => {
    console.log('【state,pageData 】', state, pageData);
    if (!state && needShowRedeem) {
      setShowRedeemModal(true);
      setNeedShowRedeem(false);
      return;
    }
    setNeedShowRedeem(true);
    // setIsShowOrder(state);
    setIsOpenWind(false);
    pageData && setPayPageData(pageData);
    /** 神策埋点
     * 用户点击选择套餐时触发 **/
    pageData &&
      Object.keys(pageData).length > 0 &&
      !isShowOrder &&
      sensors.track('xxys_experienceCoursePage_courseSup_click', {
        course_sup: pageData.sup,
        channel_id: store.getState().channelId,
        user_role: store.getState().userRole,
        buy_model: 'model_4',
        abtest: levelType == 0 ? '单年龄' : '年龄&介绍',
        sendId: store.getState().sendId || '',
      });
    /** 神策埋点 **/
  };

  // 自有流量（一级渠道）>> 私域（二级渠道461）渠道下的，右上角转发时带原渠道
  const getChannelDetail = () => {
    getChannelListApi({
      appsubject: 'ART_APP',
      channel_class_ids: process.env.NODE_ENV == 'online' ? '461' : '347',
      page: 1,
      size: 100,
    }).then((res) => {
      if (res.code === 0) {
        const data = res.payload.content;
        const ids = Object.keys(data).map((v) => data[v].channelInfo.id);
        setSYChannel(ids);
      }
    });
  };

  // 联投音乐开关接口
  useEffect(() => {
    // 0 新版  1 老板
    if (router.params['fromWeekly'] == '1') {
      setFromWeekly(true);
      setBanner(artBannerOnly);
    }

    getOrderdetail();
  }, []);

  useEffect(() => {
    userId && getChannelDetail();
    if (userId) {
      typeHandle(userId);
      return;
    }
    // unionId &&
    //   getWeixinByUnionid(unionId).then(result => {
    //     console.log(result, 'res');
    //     typeHandle(result.payload.uid);
    //   });
  }, [unionId, userId]);
  // 随材赠品展示
  const [giveaway] = useState({
    img: selIconV1,
    sImg: selIcon,
    detail: [
      '小熊马克笔',
      '小熊勾线笔',
      'AR涂色卡',
      '各类作品纸',
      '绘画成长手册',
      '学习图谱等',
    ],
    notes: '该图仅为展示，具体以实物为主，每期根据课程不同收到的随材会有所差异',
  });

  // 判断周周分享渠道进入
  const [introImg, setIntroImg] = useState(artPriceFull);
  const [showGiveMask, setShowGiveMask] = useState(false);
  const [banner, setBanner] = useState(artBanner);
  const country = useRef(false);

  useEffect(() => {
    if (isOpenWind || isShowOrder) {
      country.current = true;
    }
  }, [isOpenWind, isShowOrder]);

  useEffect(() => {
    console.log(router.params, 'params====>');
    if (router.params.pzd) {
      console.log(router.params['msChannelId'], '天天领mschannelId');
      store.dispatch({
        type: 'CHANGE_CHANNELID',
        channelId: router.params['msChannelId'],
      });
    }
  }, [router.params]);

  // 显示36、29
  const typeHandle = (uid: string) => {
    return;
    getExpUserApi(uid).then((res) => {
      if (res.code == 0) {
        setOnlyshow36(res.payload);
        if (res.payload) {
          setBanner(artBanner36);
          setIntroImg(artPriceFull36);
        }
      }
    });
  };

  // 赠送弹框点击事件
  const handleClickGive = () => {
    setShowGiveMask(false);
    setIsOpenWind(true);
    setPayOrderData({
      orderType: '29',
      packagesId:
        process.env.NODE_ENV == 'online'
          ? '771'
          : process.env.NODE_ENV == 'test'
          ? '770'
          : '7613',
    });
    sensors.track('xxys_experienceCoursePage_buy_click', {
      user_role: store.getState().userRole,
      channel_id: store.getState().channelId,
      button_position: '29元',
      buy_model: 'model_4',
      sendId: store.getState().sendId || '',
    });
  };

  const handleCloneGive = () => {
    setShowGiveMask(false);
    sensors.track('xxys_experienceCoursePage_bagpop_close', {});
  };
  // 获取订单详情
  const getOrderdetail = () => {
    orderId &&
      getOrderDetailApi(orderId).then((res) => {
        if (res.code === 0) {
          setOrderInfo(res.payload.order);
        }
      });
  };

  const videoclick = () => {
    sensors.track('xxys_experienceCoursePage_buy_click', {
      button_position: '视频购买按钮',
      buy_model: 'model_4',
      sendId: store.getState().sendId || '',
    });
    setPayOrderData({
      orderType: '29',
      packagesId:
        process.env.NODE_ENV == 'online'
          ? '771'
          : process.env.NODE_ENV == 'test'
          ? '770'
          : '7613',
    });
    Taro.setStorageSync('vshow', '2');
    setShowVideo(false);
    setIsOpenWind(true);
  };

  // 打开级别、购买弹窗
  const openHandle = (price: string) => {
    if (price === '0') {
      setPayOrderData({
        orderType: '0',
        packagesId: zeroCouponPackageId,
      });
    } else if (price === '29') {
      setPayOrderData({
        orderType: '29',
        packagesId:
          process.env.NODE_ENV == 'online'
            ? '771'
            : process.env.NODE_ENV == 'test'
            ? '770'
            : '7613',
      });
    } else {
      setPayOrderData({
        orderType: '36',
        packagesId: '62',
      });
    }

    sensors.track('xxys_experienceCoursePage_buy_click', {
      user_role: store.getState().userRole,
      channel_id: store.getState().channelId,
      button_position: price == '29' ? '29元' : '36元',
      buy_model: 'model_4',
      sendId: store.getState().sendId || '',
    });

    if (levelType == 2) {
      watchShowOrder(true, {});
      setIsShowOrder(true);
      return;
    }
    setIsOpenWind(true);
  };

  useDidShow(() => {
    getOrderdetail();

    const params: any = router.params;
    let _params = {
      course_subject: 'ART_APP-小熊美术',
      poster_id: params['poster_id'] || '',
      channel_id: params.msChannelId,
      xz_channel_id: params.xzChannelId,
      yy_channel_id: params.yyChannelId,
      user_role: store.getState().userRole,
      Use_equipment: params['equipment'] == 1 ? 'ipad' : '非ipad',
      buy_model: 'model_4',
      sendId: params['sendId'] || '',
      entrance_page: params['entrance_page'] || '',
    };
    if (params['isToWf']) {
      let _json = {
        '1': '好友',
        '2': '朋友圈',
        '3': '保存本地',
        '4': '微信分享',
        '5': '中台生成长',
        '6': '中台生成短',
      };
      _params['share_from'] = _json[params['isToWf']];
    }
    if (params['sharetype']) _params['sharetype'] = params['sharetype'];
    if (_params['msChannelId'] == '2079')
      _params['abtest'] = router.params['fromWeekly'] == '0' ? '老版' : '新版';
    if (params['sText']) _params['invitation_content'] = params['sText'];
    _params['scene'] = Taro.getLaunchOptionsSync().scene;
    sensors.track('xxys_experienceCoursePage_view', _params);
  });

  useShareAppMessage(() => {
    const _channelId = router.params.msChannelId || router.params.channelId;
    let shareChannelId = '13073';
    if (_channelId && sYChannel.includes(_channelId)) {
      shareChannelId = _channelId;
    }
    return {
      title: '今天可以免费领画材礼包，名额有限，快给宝贝领取！',
      path: '/pages/normalGroup/art/index?channelId=' + shareChannelId,
      imageUrl: shareImg,
    };
  });

  const payConfirmHandler = () => {
    if (!payPageData) {
      Taro.showToast({
        title: '请先选择级别',
        icon: 'none',
      });
    }

    setTimeout(() => {
      orderRef.current.payConfirm();
    }, 500);
  };

  const getPhoneNumberHandler = (res) => {
    orderRef.current.getPhoneNumber(res);
  };

  const selPicBookHandler = (type) => {
    setSelectPicBookType(type);
  };
  /* 订单弹窗结束 */
  /** 传给子页面的参数结束 **/
  return (
    <View className='container w100 relative'>
      {/* 微信登录组件 */}
      <WxLogin subject='ART_APP' />
      <AddCourse
        ref={addCourseRef}
        setAttachSubjectHandler={(data) =>
          !isReceive ? setAttachSubject(data) : null
        }
        selPicBookHandler={selPicBookHandler}
      />
      {/* 头部导航栏 */}
      <CommonTop currentName='小熊美术' isIntroduce={false} />
      {/* banner图 */}
      <View className='w100 banner-con'>
        <Image className='banner w100' mode='widthFix' src={banner}></Image>
      </View>
      {/* // 视频播放 */}

      {/* banner图下小介绍框 */}
      {!fromWeekly && (
        <View className='intro-part'>
          <View className='intro-img'>
            <Image className='w100' src={introImg} mode='widthFix'></Image>
          </View>
          {/* <View className='flex intro-con font_blod'>
         {artIntroText.map((item, index) => {
         return (
         <View
         className='flexitem intro-text flex_only'
         key={`intro-${index}`}
         >
         <Image className='icon' mode='widthFix' src={bingoIcon}></Image>
         <Text>{item}</Text>
         </View>
         );
         })}
         </View> */}
        </View>
      )}
      {/* 图片列表 */}
      <View className='body-part newArt'>
        {!fromWeekly
          ? artBodyPartImages.map((item, index) => {
              return (
                <View className='body-img-part' key={`body-img-${index}`}>
                  <Image
                    className='body-img w100'
                    mode='widthFix'
                    src={`${item}`}
                  ></Image>
                  {index === 1 && (
                    <View className='overfolw slide-index-1'>
                      <View className='slide-insert'>
                        <Swiper
                          className='insert-swiper'
                          circular
                          indicatorDots
                          autoplay
                        >
                          <SwiperItem className='slide-item'>
                            <Image
                              src={SlideIndex1}
                              className='slide-1'
                            ></Image>
                          </SwiperItem>
                          <SwiperItem className='slide-item'>
                            <Image
                              src={SlideIndex2}
                              className='slide-1'
                            ></Image>
                          </SwiperItem>
                        </Swiper>
                      </View>
                    </View>
                  )}
                  {index === 3 && (
                    <View className='overfolw'>
                      <View className='slide-insert'>
                        <Swiper
                          className='insert-swiper'
                          circular
                          indicatorDots
                          autoplay
                        >
                          <SwiperItem className='slide-item'>
                            <Image src={SlideS1To3} className='slide-3'></Image>
                          </SwiperItem>
                          <SwiperItem className='slide-item'>
                            <Image src={SlideS4} className='slide-3'></Image>
                          </SwiperItem>
                        </Swiper>
                      </View>
                    </View>
                  )}
                </View>
              );
            })
          : artWeBodyPartImages.map((item, index) => {
              return (
                <View
                  className={`body-img-part ${index != 5 ? 'pd' : ''}`}
                  key={`body-img-${index}`}
                >
                  <Image
                    className='body-img w100'
                    mode='widthFix'
                    src={`${item}`}
                  ></Image>
                  {index === 3 && (
                    <View className='overfolw'>
                      <View className='slide-insert'>
                        <Swiper
                          className='insert-swiper'
                          circular
                          indicatorDots
                          autoplay
                        >
                          <SwiperItem className='slide-item'>
                            <Image src={SlideS1To3} className='slide-3'></Image>
                          </SwiperItem>
                          <SwiperItem className='slide-item'>
                            <Image src={SlideS4} className='slide-3'></Image>
                          </SwiperItem>
                        </Swiper>
                      </View>
                    </View>
                  )}
                </View>
              );
            })}
      </View>
      <Orderdetailmp />

      {/* 底部文字 */}
      <View className='footer-part'>
        {footerText.map((item, index) => {
          return (
            <View className='text_al' key={`text-${index}`}>
              {item}
            </View>
          );
        })}
      </View>
      {/* 购买部分*/}
      <View className='suction-bottom fixed'>
        <View className={`bg_white flex w100 ${onlyshow36 ? '' : 'trans-bg'}`}>
          {onlyshow36 ? (
            <Image
              className='sel-img w100'
              src={rebackBtn36}
              mode='widthFix'
              onClick={() => openHandle('36')}
            ></Image>
          ) : isReceive ? (
            <>
              <View className='img-btn-box new'>
                <Image
                  className='left-img'
                  src={leftImg}
                  mode='widthFix'
                ></Image>
                <View
                  className='twentyNine-btn zero-btn'
                  data-type='twentyNine'
                  onClick={() => openHandle('0')}
                >
                  <Image className='right-img' src={zeroBtn}></Image>
                  <View className='text'>
                    亲友专享
                    <Text className='money'>￥</Text>
                    <Text className='zero'>0</Text>
                    <Text className='del'> 原价36元</Text>
                  </View>
                </View>
              </View>
            </>
          ) : (
            <>
              <View className='img-btn-box old'>
                <Image
                  className='sel-img w100'
                  src={rebackBtn}
                  mode='widthFix'
                ></Image>
                <View className='img-btn-box'>
                  {/* <View className='twentyNine-btn'></View> */}
                  {/* <View
                    className='thirtySix-btn'
                    data-type='thirtySix'
                    onClick={() => openHandle('36')}
                  ></View> */}
                  <View
                    className='twentyNine-btn'
                    data-type='twentyNine'
                    onClick={() => openHandle('29')}
                  ></View>
                </View>
              </View>
            </>
          )}
        </View>
      </View>
      {/* 弹窗部分 */}
      {/* 套餐 */}
      <AtFloatLayout
        className={`${
          attachSubject != null ? 'addOther' : ''
        } custom-float-layout`}
        isOpened={isOpenWind}
        onClose={() => {
          setIsOpenWind(false);
        }}
        title='选择级别'
      >
        <LayoutLevel
          oldLevelArray={levelType == 1 ? levelV2 : levelV1}
          levelType={levelType}
          isOpenWind={isOpenWind}
          pType='art'
          payOrderData={payOrderData}
          newLevelType
          payConfirmHandler={payConfirmHandler}
          getPhoneNumberHandler={getPhoneNumberHandler}
          watchShowOrder={watchShowOrder}
          attachSubject={attachSubject}
          selectPicBookType={selectPicBookType}
          selPicBook={selPicBookHandler}
        />
      </AtFloatLayout>
      {/* 订单 */}
      <AtFloatLayout
        className='order-layout custom-float-layout'
        isOpened={isShowOrder}
        onClose={() => {
          watchShowOrder(false, null);
        }}
      >
        {payPageData && (
          <LayoutOrderV1
            isShowOrder={isShowOrder}
            watchCloseOrder={watchShowOrder}
            payPageData={payPageData}
            orderType={payOrderData.orderType}
            subject='ART_APP'
            packagesId={payOrderData.packagesId}
            classNum={10}
            topicId={3}
            isReceive={isReceive}
            disCoupon={disCouponHandler}
            pType='art'
            pName='美术'
            ref={orderRef}
            giveaway={giveaway}
            levelType={levelType}
            payConfirmHandler={payConfirmHandler}
            delayPay
            attachSubject={attachSubject}
            // pciswitch={pciswitch}
            selectPicBook={selectPicBookType}
            // 0元优惠券不能加购，29元可以0元加购
            afterCreateZeroOrder={() =>
              !isReceive ? addCourseRef.current.getSubjectBuyType() : null
            }
            // 0元获取手机号后不再弹选择等级
            addPicShwoLevel={() => (!isReceive ? setIsOpenWind(true) : null)}
          />
        )}
      </AtFloatLayout>
      {/* 退出确认框 */}
      <AtModal
        className='redeem-modal'
        isOpened={showRedeemModal}
        closeOnClickOverlay={false}
      >
        <Image
          className='close-img'
          src={modalCloseImg}
          onClick={hideRedeemHandle}
        ></Image>
        <AtModalContent>
          <Image className='redeem-img' src={redeemImg} mode='widthFix'></Image>
          <View className='redeem-btn' onClick={hideRedeemHandle}></View>
          {/* <View className='footer'>
           <View className='btn' onClick={hideRedeemHandle}>
           36元10节课包邮送画材
           </View>
           </View> */}
        </AtModalContent>
      </AtModal>
      {/** 周周渠道赠送框 */}
      {!onlyshow36 && (
        <AtModal
          isOpened={showGiveMask}
          className='giveMask'
          onClose={handleCloneGive}
        >
          <Image
            className='giveMaskImg'
            src={giveMaskImg}
            onClick={handleClickGive}
          ></Image>
        </AtModal>
      )}
      {!onlyshow36 && (
        <AtModal
          isOpened={zeroPackageModalVis}
          className='zero-giveMask'
          closeOnClickOverlay={false}
          onClose={handleCloneGive}
        >
          <Image className='coupon-img' src={zeroModal} />
          <View className='submit-btn' onClick={handleClickZero}>
            立即领取
          </View>
          <Image
            className='mask-close'
            src={modalCloseImg}
            onClick={() => {
              setZeroPackageModalVis(false);
              // setShowGiveMask(false);
              // setIsColseReceive(true);
              // sensors.track('xxys_Landingpage_couponpop_click', {
              //   operation_type: '关闭弹窗',
              // });
              // store.dispatch({
              //   type: 'CHANGE_CHANNELID',
              //   channelId: _NODE_ENV == 'online' ? '15637' : '7959',
              // });
            }}
          />
        </AtModal>
      )}
      {!onlyshow36 && <SpellAirView orderInfo={orderInfo} />}
      {!onlyshow36 && (!orderInfo.buyTime || orderInfo.buyTime == '0') && (
        <View
          className='viewoview'
          onClick={() => {
            sensors.track('xxys_experienceCoursePage_video_click', {});
            setShowVideo(true);
            Taro.setStorageSync('vshow', '1');
          }}
        ></View>
      )}
      <VideoShow
        isShow={showVideo}
        close={() => {
          Taro.setStorageSync('vshow', '2');
          setShowVideo(false);
        }}
        click={() => videoclick()}
      />
    </View>
  );
};
