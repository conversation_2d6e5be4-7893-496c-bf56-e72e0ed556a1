import { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import Taro, { useRouter, useShareAppMessage } from '@tarojs/taro';
import { AtFloatLayout, AtModal, AtModalContent } from 'taro-ui';
import { Image, View } from '@tarojs/components';
import sensors from '@/utils/sensors_data';
import store from '@/store/groupbuy/index';
import { UserStateType } from '@/store/groupbuy/state';
import { levelV1, levelV2 } from '@/common/data.config';
import '@/theme/custom-taro-ui.scss';
/** 图片部分   **/
import modalCloseImg from '@/assets/groupbuy/thirtySix/close.png';
import redeemImgOld from '@/assets/normalGroup/newArt/retain-fbb.gif';
import selIconV1 from '@/assets/groupbuy/index/gift-new-v2.png';
import selIcon from '@/assets/groupbuy/index/gift-new.jpg';

import shareImg from '@/assets/normalGroup/art/share-img-20240708.jpg';

import BtnL from '@/assets/normalGroup/formh5twentynine/ten-img-l.png';
import BtnR from '@/assets/normalGroup/formh5twentynine/ten-img-r.gif';
// 14953
import BtnBlueL from '@/assets/normalGroup/formh5twentynineBlue/620-bottom-price.png';
import BtnBLueR from '@/assets/normalGroup/formh5twentynineBlue/620-bottom-price1.png';

import Orderdetailmp from '@/components/orderDetailMp';
import { unionSwitch, getPackageId } from '@/api/groupbuy';
// @ts-ignore
import CommonTop from '@/components/commonTop';
import BannerCom from './components/bannerCom/index';
import DetailCom from './components/detailCom/index';
import '../index.scss';
import LayoutLevel from '../../../../components/level';
import LayoutOrderV1 from '../../../../components/orderV1';
import WxLogin from '../../../../components/wxlogin';
import AddCourse from '../components/addCourse';

/** 图片部分完 **/
export default () => {
  const router = useRouter();

  const orderRef: any = useRef(null);
  const addCourseRef: any = useRef(null);
  const channelId = useSelector((state: UserStateType) => state.channelId);
  const [btnLeftImg] = useState(
    router.params.channelId === '14953' ? BtnBlueL : BtnL,
  );
  const [btnRightImg] = useState(
    router.params.channelId === '14953' ? BtnBLueR : BtnR,
  );
  // 随材赠品展示
  const [giveaway] = useState({
    img: selIconV1,
    sImg: selIcon,
    detail: [
      '小熊马克笔',
      '小熊勾线笔',
      'AR涂色卡',
      '各类作品纸',
      '绘画成长手册',
      '学习图谱等',
    ],
    notes: '该图仅为展示，具体以实物为主，每期根据课程不同收到的随材会有所差异',
  });
  const country = useRef(false);
  /** 常量 */
  const footerText = [
    '客服电话：400-002-8080  浙ICP备20001038号-3',
    '地址：北京市朝阳区朝来高科技产业园区36号院10号楼',
    '杭州小伴熊科技有限公司 版权所有',
  ];
  /** 常量结束 */
  /** 订单浮窗 */
  // 显示套餐隐藏浮窗
  const [isOpenWind, setIsOpenWind] = useState<boolean>(false);
  const [packagesIdObj, setPackagesIdObj] = useState<object>({});
  // 显示隐藏订单浮窗
  const [isShowOrder, setIsShowOrder] = useState<boolean>(false);
  // 订单支付页面的数据
  const [payPageData, setPayPageData] = useState<any>();
  // 新增29元拼单增加变量区分36元
  // 拼团倒计时需要获取订单详情
  const [payOrderData, setPayOrderData] = useState({
    orderType: '36',
    packagesId: '62',
  });
  // 监听订单浮窗的打开和关闭
  const watchShowOrder = (state, pageData) => {
    if (!state && needShowRedeem) {
      setShowRedeemModal(true);
      setNeedShowRedeem(false);
      return;
    }
    setNeedShowRedeem(true);
    // setIsShowOrder(state);
    setIsOpenWind(false);
    pageData && setPayPageData(pageData);
    /** 神策埋点
     * 用户点击选择套餐时触发 **/
    pageData &&
      Object.keys(pageData).length > 0 &&
      !isShowOrder &&
      sensors.track('ai_ArtSystemCourse_appletcourseSup_click', {
        course_sup: pageData.sup,
        channel_id: router.params.channelId || store.getState().channelId,
        user_role: store.getState().userRole,
        buy_model: 'model_4',
        abtest: Number(router.params.abtest) === 1 ? '是' : '否',
        sendId: store.getState().sendId || '',
        clickID: router.params.clickid,
        userType: 1,
      });
    /** 神策埋点 **/
  };
  // 用户放弃付款再次确认弹窗
  const [showRedeemModal, setShowRedeemModal] = useState<boolean>(false);
  // 是否显示挽留窗口（只第一次点击关闭）
  const [needShowRedeem, setNeedShowRedeem] = useState<boolean>(true);
  // 级别类型
  const [levelType] = useState<number>(1);
  //   加购订单数据
  const [attachSubject, setAttachSubject] = useState<any>(null);
  const [selectPicBookType, setSelectPicBookType] = useState(false);
  // 关闭挽留弹窗
  const hideRedeemHandle = () => {
    setShowRedeemModal(false);
  };
  // 美术音乐联报开关
  const [, setArtMusicSwitch] = useState<boolean>(false);

  useEffect(() => {
    sensors.track('ai_ArtSystemCourse_appletbuypagebrowse', {
      open_source: router.params.openSource === 'H5' ? 2 : 1,
      channel_id: router.params.channelId || channelId,
    });
    // h5，打开小程序后埋点
    sensors.track('market_adverts_enterpage_wp_route', {
      channel: router.params.channelId || channelId,
      clickId: router.params.clickid,
      route: router.path,
    });

    unionSwitch({
      propertiesKey: 'ARTDANCESWITCH',
    }).then((res) => {
      if (res.code === 0) {
        let { payload } = res;
        setArtMusicSwitch(Boolean(Number(payload)));
      }
    });
  }, []);

  useEffect(() => {
    if (isOpenWind || isShowOrder) {
      country.current = true;
    }
  }, [isOpenWind, isShowOrder]);

  useEffect(() => {
    if (router.params.channelId) {
      getPackageId({
        channel: router.params.channelId,
      }).then((res: any) => {
        setPackagesIdObj(res.payload);
        console.log('???????', res);
      });
      store.dispatch({
        type: 'CHANGE_CHANNELID',
        channelId: router.params['channelId'],
      });
    }
    if (router.params.sup) {
      openHandle();
    }
  }, [router.params]);

  // 打开级别、购买弹窗
  const openHandle = () => {
    setPayOrderData({
      orderType: '29',
      packagesId: '610',
    });

    if (levelType == 2) {
      watchShowOrder(true, {});
      setIsShowOrder(true);
      return;
    }
    sensors.track('ai_ArtSystemCourse_appletbuypageclick', {
      open_source: router.params.openSource === 'H5' ? 2 : 1,
      channel_id: router.params.channelId || channelId,
    });
    setIsOpenWind(true);
    sensors.track('ai_ArtSystemCourse_appletcourseSup_browse', {
      open_source: router.params.openSource === 'H5' ? 2 : 1,
      user_role: store.getState().userRole,
      channel_id: router.params.channelId || channelId,
      clickID: router.params.clickid,
      userType: 1,
      abtest: Number(router.params.abtest) === 1 ? '是' : '否',
    });
  };

  useShareAppMessage(() => {
    return {
      title: '【特价秒杀】1节在线儿童美术课只要几块钱！',
      path: window.location.href,
      imageUrl: shareImg,
    };
  });

  const payConfirmHandler = () => {
    setTimeout(() => {
      orderRef.current.payConfirm();
    }, 500);
  };
  const getPhoneNumberHandler = (res) => {
    orderRef.current.getPhoneNumber(res);
  };

  //   加购
  const selPicBookHandler = (type) => {
    setSelectPicBookType(type);
  };

  /* 订单弹窗结束 */
  /** 传给子页面的参数结束 **/
  return (
    <View className='container w100 relative'>
      {/* 微信登录组件 */}
      <WxLogin subject='ART_APP' />
      <AddCourse
        ref={addCourseRef}
        setAttachSubjectHandler={(data) => setAttachSubject(data)}
        selPicBookHandler={selPicBookHandler}
      />
      {/* 头部导航栏 */}
      <CommonTop currentName='小熊美术' isIntroduce={false} />
      {/* banner图 */}
      <BannerCom />
      {/* 图片列表 */}
      <DetailCom />
      {/* 订单列表 */}
      <Orderdetailmp />
      {/* 底部文字 */}
      <View className='footer-part'>
        {footerText.map((item, index) => {
          return (
            <View className='text_al' key={`text-${index}`}>
              {item}
            </View>
          );
        })}
      </View>
      {/* 购买部分*/}
      <View className='suction-bottom fixed'>
        <View
          className={`bg_white img-btn-box formh5twentynine ${
            router.params.channelId === '14953' ? 'blueBtn' : ''
          }`}
          onClick={openHandle}
        >
          <Image className='left-img' src={btnLeftImg} mode='widthFix'></Image>
          <Image
            className={`btn-img ${
              router.params.channelId === '14953'
                ? 'formh5twentynine-btn-img'
                : 'bntgif'
            }`}
            src={btnRightImg}
            mode='widthFix'
          ></Image>
        </View>
      </View>
      {/* 套餐 */}
      <AtFloatLayout
        className={`${
          attachSubject != null ? 'addOther' : ''
        } custom-float-layout`}
        isOpened={isOpenWind}
        onClose={() => {
          setIsOpenWind(false);
        }}
        title='选择级别'
      >
        <LayoutLevel
          oldLevelArray={levelType == 1 ? levelV2 : levelV1}
          levelType={levelType}
          pType='art'
          newLevelType
          payOrderData={payOrderData}
          payConfirmHandler={payConfirmHandler}
          getPhoneNumberHandler={getPhoneNumberHandler}
          watchShowOrder={watchShowOrder}
          attachSubject={attachSubject}
          selectPicBookType={selectPicBookType}
          selPicBook={selPicBookHandler}
        />
      </AtFloatLayout>
      {/* 订单 */}
      <AtFloatLayout
        className='order-layout custom-float-layout'
        isOpened={isShowOrder}
        onClose={() => {
          watchShowOrder(false, null);
        }}
      >
        {payPageData && (
          <LayoutOrderV1
            isShowOrder={isShowOrder}
            watchCloseOrder={watchShowOrder}
            payPageData={payPageData}
            orderType={payOrderData.orderType}
            subject='ART_APP'
            packagesId={
              packagesIdObj[payPageData.sup] || payOrderData.packagesId
            }
            classNum={10}
            topicId={3}
            pType='art'
            pName='美术'
            payConfirmHandler={payConfirmHandler}
            ref={orderRef}
            levelType={levelType}
            giveaway={giveaway}
            attachSubject={attachSubject}
            sourcePageType='H5'
            // pciswitch={pciswitch}
            selectPicBook={selectPicBookType}
            afterCreateZeroOrder={() =>
              addCourseRef.current.getSubjectBuyType()
            }
            addPicShwoLevel={() => setIsOpenWind(true)}
          />
        )}
      </AtFloatLayout>
      {/* 退出确认框 */}
      <AtModal
        className='redeem-modal new'
        isOpened={showRedeemModal}
        closeOnClickOverlay={false}
      >
        <Image
          className='close-img'
          src={modalCloseImg}
          onClick={hideRedeemHandle}
        ></Image>
        <AtModalContent>
          <Image
            className='redeem-img'
            src={redeemImgOld}
            mode='widthFix'
          ></Image>
          <View className='redeem-btn' onClick={hideRedeemHandle}></View>
        </AtModalContent>
      </AtModal>
    </View>
  );
};
