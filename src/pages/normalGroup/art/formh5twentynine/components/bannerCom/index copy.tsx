import React, { useState, useEffect } from 'react';
import Taro, { useRouter } from '@tarojs/taro';
import { Image, Swiper, SwiperItem, View } from '@tarojs/components';

import banner1 from '@/assets/normalGroup/formh5twentynine/banner1.png';
import banner2 from '@/assets/normalGroup/formh5twentynine/banner2.png';
import banner3 from '@/assets/normalGroup/formh5twentynine/banner3.png';

import bannerListBg from '@/assets/normalGroup/formh5twentynineBlue/620-topbg.png';
import bannerList1 from '@/assets/normalGroup/formh5twentynineBlue/620-top1.png';
import bannerList2 from '@/assets/normalGroup/formh5twentynineBlue/620-top2.png';
import bannerList3 from '@/assets/normalGroup/formh5twentynineBlue/620-top3.png';

import artPriceFull from '@/assets/normalGroup/formh5twentynine/decorationForTen.png';
import artPriceFull1 from '@/assets/normalGroup/formh5twentynine/decorationForTen1.png';
import artPriceFull2 from '@/assets/normalGroup/formh5twentynineBlue/620-decoration.png';

import styles from './index.module.scss';

export default function BannerCom() {
  const router = useRouter();

  const [banner, setBanner] = useState();
  const [channelId] = useState(router.params.channelId);

  const [bannerList] = useState([bannerList1, bannerList2, bannerList3]);
  const [introImg, setIntroImg] = useState(artPriceFull);

  useEffect(() => {
    if (channelId === '14953') {
      setIntroImg(artPriceFull2);
    } else {
      if (router.params['age'] == '1') {
        setBanner(banner2);
      } else {
        setBanner(banner1);
      }
      if (router.params['pageType'] == '1') {
        setIntroImg(artPriceFull1);
        setBanner(banner3);
      }
    }
  }, []);

  return (
    <View>
      {channelId === '14953' ? (
        <View
          className={styles.swiperTop}
          style={{ backgroundImage: 'url(' + bannerListBg + ')' }}
        >
          <View className={styles.bannerSlideInsert}>
            <Swiper
              className={styles.bannerSlideSwiper}
              circular
              indicatorDots
              autoplay
            >
              {bannerList.map((SlideItem, SlideIndex) => {
                return (
                  <SwiperItem
                    key={'SlideItem' + SlideIndex}
                    className={styles.bannerSlidev}
                  >
                    <Image
                      src={SlideItem}
                      className={styles.bannerSlideImg}
                    ></Image>
                  </SwiperItem>
                );
              })}
            </Swiper>
          </View>
        </View>
      ) : (
        <>
          <View className='w100 banner-con'>
            <Image className='banner w100' mode='widthFix' src={banner}></Image>
          </View>
        </>
      )}
      <View className='intro-part'>
        <View className='intro-img'>
          <Image className='w100' src={introImg} mode='widthFix'></Image>
        </View>
      </View>
    </View>
  );
}
