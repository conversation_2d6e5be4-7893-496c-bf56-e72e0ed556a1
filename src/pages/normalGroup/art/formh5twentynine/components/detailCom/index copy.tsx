import React, { useState, useEffect } from 'react';
import Taro, { useRouter } from '@tarojs/taro';
import { Image, Swiper, SwiperItem, View } from '@tarojs/components';

export default function DtailCom() {
  const router = useRouter();

  const [channelId] = useState(router.params.channelId);
  const [artBodyPartImages, setartBodyPartImages] = useState<string[]>([]);

  useEffect(() => {
    let artBodyPartImagesArr: string[] = [];
    if (channelId === '14953') {
      artBodyPartImagesArr = [
        require('@/assets/normalGroup/formh5twentynineBlue/620-img01.png'),
        require('@/assets/normalGroup/formh5twentynineBlue/620-img02.png'),
        require('@/assets/normalGroup/formh5twentynineBlue/620-img03.png'),
        require('@/assets/normalGroup/formh5twentynineBlue/620-img04.png'),
        require('@/assets/normalGroup/formh5twentynineBlue/620-img05.png'),
      ];
    } else {
      artBodyPartImagesArr = [
        require('@/assets/normalGroup/formh5twentynine/ten-img-2.png'),
        require('@/assets/normalGroup/formh5twentynine/ten-img-6.png'),
        require('@/assets/normalGroup/formh5twentynine/ten-img-3.png'),
        {
          bgImg: require('@/assets/normalGroup/formh5twentynine/ten-img-4.png'),
          Swipers: [
            require('@/assets/normalGroup/formh5twentynine/ten-img-4-1.png'),
            // require('@/assets/images/thirtySixForTenDays/ten-img-4-2.png')
          ],
        },
        require('@/assets/normalGroup/formh5twentynine/ten-img-5.png'),
      ];
      if (router.params['pageType'] == '1') {
        artBodyPartImagesArr.unshift(
          require('@/assets/normalGroup/formh5twentynine/ten-img-1-new.png'),
        );
      } else {
        artBodyPartImagesArr.unshift(
          require('@/assets/normalGroup/formh5twentynine/ten-img-1.png'),
        );
      }
    }
    setartBodyPartImages(artBodyPartImagesArr);
  }, []);

  return (
    <View>
      {channelId === '14953' ? (
        <View className='body-part newArt pb0'>
          {artBodyPartImages.map((item, index) => {
            return (
              <View
                className='body-img-part mbaround'
                key={`body-img-${index}`}
              >
                <Image
                  className='body-img w100 '
                  mode='widthFix'
                  src={`${item}`}
                ></Image>
              </View>
            );
          })}
        </View>
      ) : (
        <View className='body-part newArt pb0'>
          {artBodyPartImages.map((item, index) => {
            return (
              <View
                className='body-img-part mbaround'
                key={`body-img-${index}`}
              >
                {index === 4 ? (
                  <View className='slide-index-2'>
                    <Image
                      className='body-img w100 '
                      mode='widthFix'
                      src={item.bgImg}
                    ></Image>
                    <View className='slide-insert'>
                      <Swiper
                        className='insert-swiper'
                        circular
                        indicatorDots
                        autoplay
                      >
                        {item.Swipers.map((SlideItem, SlideIndex) => {
                          return (
                            <SwiperItem
                              key={'SlideItem' + SlideIndex}
                              className='slide-item'
                            >
                              <Image
                                src={SlideItem}
                                className='slide-1'
                              ></Image>
                            </SwiperItem>
                          );
                        })}
                      </Swiper>
                    </View>
                  </View>
                ) : (
                  <Image
                    className='body-img w100 '
                    mode='widthFix'
                    src={`${item}`}
                  ></Image>
                )}
              </View>
            );
          })}
        </View>
      )}
    </View>
  );
}
