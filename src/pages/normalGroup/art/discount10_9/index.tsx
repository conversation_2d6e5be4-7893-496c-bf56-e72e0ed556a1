import { useEffect, useRef, useState } from 'react';
import Taro, { useDidShow, useRouter, useShareAppMessage } from '@tarojs/taro';
import { AtFloatLayout, AtModal, AtModalContent } from 'taro-ui';
import { Image, Swiper, SwiperItem, View } from '@tarojs/components';
import sensors from '@/utils/sensors_data';
import store from '@/store/groupbuy/index';
import { UserStateType } from '@/store/groupbuy/state';
import { levelV1, levelV2 } from '@/common/data.config';
import '@/theme/custom-taro-ui.scss';
/** 图片部分   **/
import modalCloseImg from '@/assets/groupbuy/thirtySix/close.png';
import redeemImg from '@/assets/normalGroup/newArt/retain-fbb.gif';
import artBanner from '@/assets/normalGroup/newArt/banner-only.png';
import artBanner36 from '@/assets/normalGroup/thirtySix/header-banner-36.png';
import artPriceFull from '@/assets/normalGroup/newArt/price.png';
import artPriceFull36 from '@/assets/normalGroup/thirtySix/reback-price-36.png';
import selIcon from '@/assets/groupbuy/index/gift-new.jpg';
import selIconV1 from '@/assets/groupbuy/index/gift-new-v2.png';
import shareImg from '@/assets/normalGroup/art/share-img-only.png';
import rebackBtn from '@/assets/normalGroup/art/discount10_9/btn.gif';
import rebackBtn36 from '@/assets/normalGroup/thirtySix/bottom-img-36.png';

import banner_1_new from '@/assets/normalGroup/art/discount10_9/banner-1-new.png';
import decoration_news_one from '@/assets/normalGroup/art/discount10_9/decoration-news-one.png';
import SlideIndex5 from '@/assets/normalGroup/art/discount10_9/s-1.png';
import SlideIndex6 from '@/assets/normalGroup/art/discount10_9/s-2.png';
import SlideIndex7 from '@/assets/normalGroup/art/discount10_9/s-3.png';

import Orderdetailmp from '@/components/orderDetailMp';
import { useSelector } from 'react-redux';
import {
  getChannelListApi,
  getExpUserApi,
  getOrderDetailApi,
  getWeixinByUnionid,
  reportScanNum,
  unionSwitch,
} from '@/api/groupbuy';
// @ts-ignore
import CommonTop from '@/components/commonTop';
import LayoutLevel from '@/components/level';
import LayoutOrderV1 from '@/components/orderV1';
import WxLogin from '@/components/wxlogin';
import './index.scss';
// 中间部分图片
// // 有spreadId
const artRequireContext = require.context(
  '@/assets/normalGroup/art/discount10_9',
  true,
  /^\.\/.*FourteenPiontNine-img-[123456]\.png$/,
);

/** 图片部分完 **/
export default () => {
  const router = useRouter();

  const orderRef: any = useRef(null);

  const [onlyshow36, setOnlyshow36] = useState(false);
  /** 常量 */
  // const artIntroText = ['仅¥2.9元/节', '1对1辅导', '课程永久回放'];
  const footerText = [
    '客服电话：400-002-8080  浙ICP备20001038号-3',
    '地址：北京市朝阳区朝来高科技产业园区36号院10号楼',
    '杭州小伴熊科技有限公司 版权所有',
  ];
  const artBodyPartImages = artRequireContext.keys().map(artRequireContext);
  /** 常量结束 */
  //redux
  const orderId = useSelector((state: UserStateType) => state.orderId);
  const [orderInfo, setOrderInfo] = useState({
    buyTime: '0',
    packageId: 62,
  });

  /** 订单浮窗 */
  // 显示套餐隐藏浮窗
  const [isOpenWind, setIsOpenWind] = useState<boolean>(false);
  // 显示隐藏订单浮窗
  const [isShowOrder, setIsShowOrder] = useState<boolean>(false);
  // 订单支付页面的数据
  const [payPageData, setPayPageData] = useState<object | null>();
  // 新增29元拼单增加变量区分36元
  // 拼团倒计时需要获取订单详情
  const [payOrderData, setPayOrderData] = useState({
    orderType: '36',
    packagesId: '1907',
  });
  // 显示video
  const [showVideo, setShowVideo] = useState(false);
  // 监听订单浮窗的打开和关闭
  const watchShowOrder = (state, pageData) => {
    if (!state && needShowRedeem) {
      setShowRedeemModal(true);
      setNeedShowRedeem(false);
      return;
    }
    setNeedShowRedeem(true);
    // setIsShowOrder(state);
    pageData && setPayPageData(pageData);
    /** 神策埋点
     * 用户点击选择套餐时触发 **/
    pageData &&
      Object.keys(pageData).length > 0 &&
      !isShowOrder &&
      sensors.track('xxys_experienceCoursePage_courseSup_click', {
        course_sup: pageData.sup,
        channel_id: channelId,
        user_role: store.getState().userRole,
        buy_model: 'model_4',
        abtest: levelType == 0 ? '单年龄' : '年龄&介绍',
        sendId: store.getState().sendId || '',
      });
    /** 神策埋点 **/
  };
  // 用户放弃付款再次确认弹窗
  const [showRedeemModal, setShowRedeemModal] = useState<boolean>(false);
  // 是否显示挽留窗口（只第一次点击关闭）
  const [needShowRedeem, setNeedShowRedeem] = useState<boolean>(true);
  // 级别类型
  //   const [levelType] = useState<number>(Math.floor(Math.random() * 3));
  const [levelType] = useState<number>(1);
  //私域二级渠道列表
  const [sYChannel, setSYChannel] = useState<any[]>([]);
  // 关闭挽留弹窗
  const hideRedeemHandle = () => {
    setShowRedeemModal(false);
  };
  /** 传给子页面的参数 **/

  //openid
  const openId = useSelector((state: UserStateType) => state.openid);
  const spreadId = useSelector((state: UserStateType) => state.spreadId);
  const channelId = useSelector((state: UserStateType) => state.channelId);

  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  const unionId = useSelector((state: UserStateType) => state.unionId);
  useEffect(() => {
    if (openId && spreadId && channelId === '6634') {
      const data = {
        openid: openId,
        spreadUserId: spreadId,
      };
      reportScanNum(data).then((res) => {
        console.log(res);
      });
    }
  }, [openId, spreadId, channelId]);

  // 美术音乐联报开关
  const [artMusicSwitch, setArtMusicSwitch] = useState<boolean>(false);

  // 自有流量（一级渠道）>> 私域（二级渠道461）渠道下的，右上角转发时带原渠道
  const getChannelDetail = () => {
    getChannelListApi({
      appsubject: 'ART_APP',
      channel_class_ids: process.env.NODE_ENV == 'online' ? '461' : '347',
      page: 1,
      size: 100,
    }).then((res) => {
      console.log(res);
      if (res.code === 0) {
        const data = res.payload.content;
        const ids = Object.keys(data).map((v) => data[v].channelInfo.id);
        setSYChannel(ids);
      }
    });
  };

  // 联投音乐开关接口
  useEffect(() => {
    unionSwitch({
      propertiesKey: 'ARTDANCESWITCH',
    }).then((res) => {
      if (res.code === 0) {
        let { payload } = res;
        setArtMusicSwitch(Boolean(Number(payload)));
      }
    });
    handleWeeklySharing();
    getOrderdetail();
    getChannelDetail();
  }, []);

  useEffect(() => {
    if (userId) {
      // typeHandle(userId);
      return;
    }
    unionId &&
      getWeixinByUnionid(unionId).then((result) => {
        console.log(result, 'res');
        // typeHandle(result.payload.uid);
      });
  }, [unionId]);

  // 随材赠品展示
  const [giveaway] = useState({
    img: selIconV1,
    sImg: selIcon,
    detail: [
      '小熊马克笔',
      '小熊勾线笔',
      'AR涂色卡',
      '各类作品纸',
      '绘画成长手册',
      '学习图谱等',
    ],
    notes: '该图仅为展示，具体以实物为主，每期根据课程不同收到的随材会有所差异',
  });

  const limtTime = () => {
    if (process.env.NODE_ENV == 'online')
      return new Date().getTime() > 1675180800000;
    else return new Date().getTime() > 1675180800000;
  };

  // 判断周周分享渠道进入
  const [introImg, setIntroImg] = useState(artPriceFull);
  const [showGiveMask, setShowGiveMask] = useState(false);
  const [banner, setBanner] = useState(artBanner);
  const country = useRef(false);

  useEffect(() => {
    if (isOpenWind || isShowOrder) {
      country.current = true;
    }
  }, [isOpenWind, isShowOrder]);

  const handleWeeklySharing = () => {
    setTimeout(() => {
      if (!country.current && !showGiveMask) {
        setShowGiveMask(true);
      }
    }, 10000);
  };

  useEffect(() => {
    console.log(router.params, 'params====>');
    if (router.params.pzd) {
      console.log(router.params['msChannelId'], '天天领mschannelId');
      store.dispatch({
        type: 'CHANGE_CHANNELID',
        channelId: router.params['msChannelId'],
      });
    }
  }, [router.params]);

  // 显示36、29
  const typeHandle = (uid: string) => {
    getExpUserApi(uid).then((res) => {
      if (res.code == 0) {
        setOnlyshow36(res.payload);
        if (res.payload) {
          setBanner(artBanner36);
          setIntroImg(artPriceFull36);
        }
      }
    });
  };

  // 赠送弹框点击事件
  const handleClickGive = () => {
    setShowGiveMask(false);
    setIsOpenWind(true);
    setPayOrderData({
      orderType: '29',
      packagesId:
        process.env.NODE_ENV == 'online'
          ? '1907'
          : process.env.NODE_ENV == 'test'
          ? '1907'
          : '1907',
    });
    sensors.track('xxys_experienceCoursePage_buy_click', {
      user_role: store.getState().userRole,
      channel_id: channelId,
      button_position: '29元',
      buy_model: 'model_4',
      sendId: store.getState().sendId || '',
    });
  };

  const handleCloneGive = () => {
    setShowGiveMask(false);
    sensors.track('xxys_experienceCoursePage_bagpop_close', {});
  };
  // 获取订单详情
  const getOrderdetail = () => {
    orderId &&
      getOrderDetailApi(orderId).then((res) => {
        if (res.code === 0) {
          setOrderInfo(res.payload.order);
        }
      });
  };

  const videoclick = () => {
    sensors.track('xxys_experienceCoursePage_buy_click', {
      button_position: '视频购买按钮',
      buy_model: 'model_4',
      sendId: store.getState().sendId || '',
    });
    setPayOrderData({
      orderType: '29',
      packagesId:
        process.env.NODE_ENV == 'online'
          ? '1907'
          : process.env.NODE_ENV == 'test'
          ? '1907'
          : '1907',
    });
    Taro.setStorageSync('vshow', '2');
    setShowVideo(false);
    setIsOpenWind(true);
  };

  // 打开级别、购买弹窗
  const openHandle = (price: string) => {
    if (price === '29') {
      setPayOrderData({
        orderType: '29',
        packagesId:
          process.env.NODE_ENV == 'online'
            ? '1907'
            : process.env.NODE_ENV == 'test'
            ? '1907'
            : '1907',
      });
    } else {
      setPayOrderData({
        orderType: '36',
        packagesId: '1907',
      });
    }

    sensors.track('xxys_experienceCoursePage_buy_click', {
      user_role: store.getState().userRole,
      channel_id: channelId,
      button_position: price == '29' ? '29元' : '36元',
      buy_model: 'model_4',
      sendId: store.getState().sendId || '',
    });

    if (levelType == 2) {
      watchShowOrder(true, {});
      setIsShowOrder(true);
      return;
    }
    setIsOpenWind(true);
  };

  useDidShow(() => {
    getOrderdetail();

    const params: any = router.params;
    let _params = {
      course_subject: 'ART_APP-小熊美术',
      poster_id: params['poster_id'] || '',
      channel_id: params.msChannelId,
      xz_channel_id: params.xzChannelId,
      yy_channel_id: params.yyChannelId,
      user_role: store.getState().userRole,
      Use_equipment: params['equipment'] == 1 ? 'ipad' : '非ipad',
      buy_model: 'model_4',
      sendId: params['sendId'] || '',
      entrance_page: params['entrance_page'] || '',
    };
    if (params['isToWf']) {
      let _json = {
        '1': '好友',
        '2': '朋友圈',
        '3': '保存本地',
        '4': '微信分享',
        '5': '中台生成长',
        '6': '中台生成短',
      };
      _params['share_from'] = _json[params['isToWf']];
    }
    _params['scene'] = Taro.getLaunchOptionsSync().scene;
    sensors.track('xxys_experienceCoursePage_view', _params);
  });

  useShareAppMessage(() => {
    const _channelId = router.params.channelId || router.params.msChannelId;
    let shareChannelId = '13073';
    if (_channelId && sYChannel.includes(_channelId)) {
      shareChannelId = _channelId;
    }
    return {
      title: '今天可以免费领画材礼包，名额有限，快给宝贝领取！',
      path:
        '/pages/normalGroup/art/discount10_9/index?channelId=' + shareChannelId,
      imageUrl: shareImg,
    };
  });

  const payConfirmHandler = () => {
    setTimeout(() => {
      orderRef.current.payConfirm();
    }, 500);
  };
  const getPhoneNumberHandler = (res) => {
    orderRef.current.getPhoneNumber(res);
  };

  /* 订单弹窗结束 */
  /** 传给子页面的参数结束 **/
  return (
    <View className='container w100 relative'>
      {/* 微信登录组件 */}
      <WxLogin subject='ART_APP' />
      {/* 头部导航栏 */}
      <CommonTop currentName='小熊美术' isIntroduce={false} />
      {/* banner图 */}
      <View className='w100 banner-con'>
        <Image
          className='banner w100'
          mode='widthFix'
          src={banner_1_new}
        ></Image>
      </View>
      {/* // 视频播放 */}

      {/* banner图下小介绍框 */}
      <View className='intro-part'>
        <View className='intro-img'>
          <Image
            className='w100'
            src={decoration_news_one}
            mode='widthFix'
          ></Image>
        </View>
        {/* <View className='flex intro-con font_blod'>
         {artIntroText.map((item, index) => {
         return (
         <View
         className='flexitem intro-text flex_only'
         key={`intro-${index}`}
         >
         <Image className='icon' mode='widthFix' src={bingoIcon}></Image>
         <Text>{item}</Text>
         </View>
         );
         })}
         </View> */}
      </View>
      {/* 图片列表 */}
      <View className='body-part newArt newArt1'>
        {artBodyPartImages.map((item, index) => {
          return (
            <View className='body-img-part' key={`body-img-${index}`}>
              {index === 1 ? (
                <View className='overfolw slide-index-1 new'>
                  <View className='slide-insert'>
                    <Swiper
                      className='insert-swiper'
                      circular
                      indicatorDots
                      autoplay
                    >
                      <SwiperItem className='slide-item'>
                        <Image
                          src={SlideIndex5}
                          className='slide-1'
                          mode='widthFix'
                        ></Image>
                      </SwiperItem>
                      <SwiperItem className='slide-item'>
                        <Image
                          src={SlideIndex6}
                          className='slide-1'
                          mode='widthFix'
                        ></Image>
                      </SwiperItem>
                      <SwiperItem className='slide-item'>
                        <Image
                          src={SlideIndex7}
                          className='slide-1'
                          mode='widthFix'
                        ></Image>
                      </SwiperItem>
                    </Swiper>
                  </View>
                </View>
              ) : (
                <Image
                  className='body-img w100'
                  mode='widthFix'
                  src={`${item}`}
                ></Image>
              )}
            </View>
          );
        })}
      </View>
      <Orderdetailmp />

      {/* 底部文字 */}
      <View className='footer-part'>
        {footerText.map((item, index) => {
          return (
            <View className='text_al' key={`text-${index}`}>
              {item}
            </View>
          );
        })}
      </View>
      {/* 购买部分*/}
      <View className='suction-bottom fixed'>
        <View className={`bg_white flex w100 ${onlyshow36 ? '' : 'trans-bg'}`}>
          {onlyshow36 ? (
            <Image
              className='sel-img w100'
              src={rebackBtn36}
              mode='widthFix'
              onClick={() => openHandle('36')}
            ></Image>
          ) : (
            <>
              {/* <Image className='coupon-36' src={coupon} mode='widthFix'></Image> */}
              <Image
                className='sel-img w100'
                src={rebackBtn}
                mode='widthFix'
              ></Image>
              <View className='img-btn-box' onClick={() => openHandle('29')}>
                <View className='twentyNine-btn'></View>
              </View>
            </>
          )}
        </View>
      </View>
      {/* 弹窗部分 */}
      {/* 套餐 */}
      <AtFloatLayout
        className='custom-float-layout'
        isOpened={isOpenWind}
        onClose={() => {
          setIsOpenWind(false);
        }}
        title='选择级别'
      >
        <LayoutLevel
          oldLevelArray={levelType == 1 ? levelV2 : levelV1}
          levelType={levelType}
          pType='art'
          newLevelType
          payConfirmHandler={payConfirmHandler}
          getPhoneNumberHandler={getPhoneNumberHandler}
          watchShowOrder={watchShowOrder}
        />
      </AtFloatLayout>
      {/* 订单 */}
      <AtFloatLayout
        className='order-layout custom-float-layout'
        isOpened={isShowOrder}
        onClose={() => {
          watchShowOrder(false, null);
        }}
      >
        {payPageData && (
          <LayoutOrderV1
            isShowOrder={isShowOrder}
            watchCloseOrder={watchShowOrder}
            payPageData={payPageData}
            orderType={payOrderData.orderType}
            subject='ART_APP'
            packagesId={payOrderData.packagesId}
            payConfirmHandler={payConfirmHandler}
            classNum={10}
            topicId={3}
            pType='art'
            pName='美术'
            ref={orderRef}
            giveaway={giveaway}
            levelType={levelType}
          />
        )}
      </AtFloatLayout>
      {/* 退出确认框 */}
      <AtModal
        className='redeem-modal'
        isOpened={showRedeemModal}
        closeOnClickOverlay={false}
      >
        <Image
          className='close-img'
          src={modalCloseImg}
          onClick={hideRedeemHandle}
        ></Image>
        <AtModalContent>
          <Image className='redeem-img' src={redeemImg} mode='widthFix'></Image>
          <View className='redeem-btn' onClick={hideRedeemHandle}></View>
          {/* <View className='footer'>
           <View className='btn' onClick={hideRedeemHandle}>
           36元10节课包邮送画材
           </View>
           </View> */}
        </AtModalContent>
      </AtModal>
      {/** 周周渠道赠送框 */}
      {/* {!onlyshow36 && (
        <AtModal
          isOpened={showGiveMask}
          className='giveMask'
          onClose={handleCloneGive}
        >
          <Image
            className='giveMaskImg'
            src={giveMaskImg}
            onClick={handleClickGive}
          ></Image>
        </AtModal>
      )}
      {!onlyshow36 && <SpellAirView orderInfo={orderInfo} />}
      {!onlyshow36 && (!orderInfo.buyTime || orderInfo.buyTime == '0') && (
        <View
          className='viewoview'
          onClick={() => {
            sensors.track('xxys_experienceCoursePage_video_click', {});
            setShowVideo(true);
            Taro.setStorageSync('vshow', '1');
          }}
        ></View>
      )}
      <VideoShow
        isShow={showVideo}
        close={() => {
          Taro.setStorageSync('vshow', '2');
          setShowVideo(false);
        }}
        click={() => videoclick()}
      /> */}
    </View>
  );
};
