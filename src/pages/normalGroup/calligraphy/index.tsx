import { useRouter } from '@tarojs/taro';
import { useState } from 'react';
import { AtFloatLayout, AtModal, AtModalContent } from 'taro-ui';
import { View, Image, Block } from '@tarojs/components';
import sensors from '@/utils/sensors_data';
import { decodeUrl } from '@/utils';
import store from '@/store/groupbuy/index';
/** 图片部分   **/
import banner from '@/assets/normalGroup/calligraphy/banner.png';
import priceFull from '@/assets/normalGroup/calligraphy/price_full.png';
import submit from '@/assets/normalGroup/calligraphy/submit.png';
import selIcon from '@/assets/normalGroup/calligraphy/sel_icon.png';
import redeemImg from '@/assets/normalGroup/calligraphy/detention.png';
import modalCloseImg from '@/assets/groupbuy/thirtySix/close.png';
// @ts-ignore
import CommonTop from '../../../components/commonTop';
import './index.scss';
import LayoutLevel from '../../../components/level';
import LayoutOrder from '../../../components/order';
import WxLogin from '../../../components/wxlogin';
// 中间部分图片
const requireContext = require.context(
  '@/assets/normalGroup/calligraphy',
  true,
  /^\.\/.*body_part_\d\.png$/,
);
const bodyPartImages = requireContext.keys().map(requireContext);
/** 图片部分完 **/

export default () => {
  const router = useRouter();
  const depar = decodeUrl(decodeURIComponent(`${router.params.q}`));
  if (depar['subject'] !== 'xiezi' || !Object.keys(depar)[0]) return null;
  /** 订单浮窗 */
  // 显示套餐隐藏浮窗
  const [isOpenWind, setIsOpenWind] = useState<boolean>(false);
  // 显示隐藏订单浮窗
  const [isShowOrder, setIsShowOrder] = useState<boolean>(false);
  // 订单支付页面的数据
  const [payPageData, setPayPageData] = useState<object | null>(null);
  // 监听订单浮窗的打开和关闭
  const watchShowOrder = (state, pageData) => {
    if (!state && needShowRedeem) {
      setShowRedeemModal(true);
      setNeedShowRedeem(false);
      return;
    }
    setNeedShowRedeem(true);
    setIsShowOrder(state);
    pageData && setPayPageData(pageData);
    /** 神策埋点
     * 用户点击选择套餐时触发 **/
    pageData &&
      sensors.track('xxys_experienceCoursePage_courseSup_click', {
        course_sup: pageData.sup,
        channel_id: store.getState().channelId,
        user_role: store.getState().userRole,
        sendId: store.getState().sendId || '',
      });
    /** 神策埋点 **/
  };
  // 用户放弃付款再次确认弹窗
  const [showRedeemModal, setShowRedeemModal] = useState<boolean>(false);
  // 是否显示挽留窗口
  const [needShowRedeem, setNeedShowRedeem] = useState<boolean>(false);
  // 关闭挽留弹窗
  const hideRedeemHandle = () => {
    setShowRedeemModal(false);
  };
  /** 传给子页面的参数 **/
  /* 套餐弹窗 */
  const oldLevelArray = [
    {
      bgcolor: '#FF9C00',
      label: '基础',
      fit: '适合幼小衔接 + 1年级孩子',
      range: '初步感受汉字的形体美，学习正确的坐姿及握笔姿势培养良好的书写习惯',
      courseday: '0',
      sup: 'S1',
      period: 0,
    },
    {
      bgcolor: '#91dc4b',
      label: '高阶',
      fit: '适合2–3年级孩子',
      range: '提高汉字鉴赏能力，独立分析字形及间架结构，熟练掌握合体字书写技巧',
      courseday: '0',
      sup: 'S2',
      period: 0,
    },
  ];
  /* 套餐弹窗结束 */
  /* 订单页 */
  // 随材赠品展示
  const [giveaway] = useState({
    img: selIcon,
    detail: [
      '控笔练习卡',
      '美术宝写字作品纸',
      '趣味小印章',
      '写字专用练习本',
      '学习规划图谱',
      '其他材料若干',
    ],
    notes: '该图仅为展示，具体以实物为主，每期根据课程不同收到的随材会有所差异',
  });
  /** 传给子页面的参数结束 **/
  return (
    // 避免从写字跳转到美术页面（app.ts中逻辑）造成的组件重复渲染从而导致的接口重复请求，进行判断(二维码跳转落地页为写字，或者不是二维码跳转直接进入)
    (depar['subject'] === 'xiezi' || !Object.keys(depar)[0]) && (
      <View className='container w100 relative'>
        {/* 头部导航栏 */}
        <CommonTop currentName='小熊书法' />
        {/* banner图 */}
        <View className='w100 banner-con'>
          <Image className='banner w100' mode='widthFix' src={banner}></Image>
        </View>
        {/* banner图下小介绍框 */}
        <View className='intro-part'>
          <View className='intro-img'>
            <Image className='w100' src={priceFull} mode='widthFix'></Image>
          </View>
        </View>
        {/* 图片列表 */}
        <View className='body-part'>
          {bodyPartImages.map((item, index) => {
            return (
              <View className='body-img-part' key={`body-img-${index}`}>
                <Image
                  className='body-img w100'
                  mode='widthFix'
                  src={`${item}`}
                ></Image>
              </View>
            );
          })}
        </View>
        {/* 购买部分*/}
        <View className='suction-bottom fixed'>
          <View
            className='bg_white w100'
            onClick={() => {
              setIsOpenWind(true);
              sensors.track('xxys_experienceCoursePage_buy_click', {
                user_role: store.getState().userRole,
                channel_id: store.getState().channelId,
                sendId: store.getState().sendId || '',
              });
            }}
          >
            <Image
              className='sel-img w100'
              src={submit}
              mode='widthFix'
            ></Image>
          </View>
        </View>
        {/* 弹窗部分 */}
        <Block>
          {/* 登陆 */}
          <WxLogin subject='WRITE_APP' />
          {/* 套餐 */}
          <AtFloatLayout
            isOpened={isOpenWind}
            onClose={() => {
              setIsOpenWind(false);
            }}
            title='选择级别'
          >
            <LayoutLevel
              // 美术有两种套餐，所以其他只有一种套餐的就传两个一样的
              new_array={oldLevelArray}
              oldLevelArray={oldLevelArray}
              pType='calligraphy' // 类型类型
              watchShowOrder={watchShowOrder}
            />
          </AtFloatLayout>
          {/* 订单 */}
          <AtFloatLayout
            className='order-layout'
            isOpened={isShowOrder}
            onClose={() => {
              watchShowOrder(false, null);
            }}
          >
            {payPageData && (
              <LayoutOrder
                isShowOrder={isShowOrder}
                watchCloseOrder={watchShowOrder}
                payPageData={payPageData}
                pType='calligraphy'
                packagesId={63}
                subject='WRITE_APP'
                topicId={1}
                pName='书法'
                classNum={5}
                orderType='9.9'
                giveaway={giveaway}
              />
            )}
          </AtFloatLayout>
        </Block>
        {/* 退出确认框 */}
        <AtModal
          className='redeem-modal'
          isOpened={showRedeemModal}
          closeOnClickOverlay={false}
        >
          <Image
            className='close-img'
            src={modalCloseImg}
            onClick={hideRedeemHandle}
          ></Image>
          <AtModalContent>
            <Image className='redeem-img' src={redeemImg}></Image>
            <View className='footer'>
              <View className='btn' onClick={hideRedeemHandle}>
                9.9元5节课包邮送礼盒
              </View>
            </View>
          </AtModalContent>
        </AtModal>
      </View>
    )
  );
};
