import { useState, useEffect } from 'react';
import { View } from '@tarojs/components';
import { createSortMethodByPropInArr } from '@/utils';
import './index.scss';

export default function Index() {
  // 当前选中的tab
  const [problemTab, setProblemTab] = useState<number>(0);
  // 当前展示的问答章节的序列
  const [section, setSection] = useState<Array<number>>([
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
  ]);
  // 问答类型
  const [sectionArr] = useState<Array<Array<number>>>([
    [1, 2, 3, 4, 5, 6, 7, 8],
    [1],
    [3, 4],
    [5, 6],
    [2, 7, 8],
  ]);
  // 标签
  const [tabs] = useState<Array<string>>([
    '全部',
    '效果',
    '时间',
    '礼品',
    '上课',
  ]);
  // 当前问答章节的数据
  const [currentProblem, setCurrentProblem] = useState<Array<any>>([]);
  // 所有的问答数据
  const [problem] = useState<Array<any>>([
    {
      id: 1,
      ask: '有家长买过吗？孩子学习效果如何？',
      answer: [
        '看得见的进步！我家孩子已经学了一个月了，首先以前经常画一会就坐不住就不想画了， 现在每次都能安静的坐着完成一幅完整的作品，以前只有见他看动画片的时候这么专注过。总之，我作为家长来说很欣慰！为孩子选了一条对的路！',
        '孩子已经学了半个月啦，能够明显的看出来孩子的长进。之前报过线下班，但是据说是代笔情况严重，感觉对孩子成长并没有什么太大的用途。现在学习小熊美术，每天都能看到孩子的作品，我还是很满意的。',
        '以前孩子也喜欢画画，但是每天都是乱涂乱画。也想过让孩子系统的学习美术，但是外面报班一是贵，还没时间接送。现在在小熊美术已经学了一个月了，总的来说体验还是不错的，每天孩子自己在家就能跟着画，期待后面的学习。',
        '谢谢小熊美术，也谢谢小熊美术的李老师，现在每天孩子除了完成课上的作品，还会自己多完成一幅画，每次李老师都能给出耐心的点评，谢谢李老师的肯定。孩子很喜欢你，谢谢。',
        '我家两个孩子都在学小熊美术，姐姐弟弟两个人每天都会按时一起学画。弟弟有的时候画的进度慢了，姐姐还会在旁边带着弟弟一起，两个孩子都很喜欢。',
      ],
    },
    {
      id: 2,
      ask: '上课形式是怎样的？靠谱吗？',
      answer: [
        '用手机或者平板就可以，说是智能AI互动美术课，引导孩子自由画画。',
        '挺靠谱的，上完课有专门的老师跟进孩子学习情况，有问题可以随时提问。',
        '只要孩子有时间，都能随时上课的。',
        '购课后安装小熊美术App，在App里面上课学习，专业老师会提供辅导。',
      ],
    },
    {
      id: 3,
      ask: '上课时间如何安排呢？都是固定时间吗？',
      answer: [
        '上课时间自由安排，看你时间。',
        '几点上都行，我和老公平时忙，顾不上。工作日都是奶奶带孩子，这个课很能调动孩子积极性，我家宝贝就很喜欢上课，奶奶只要负责打开iPad孩子自己就会学。',
      ],
    },
    {
      id: 4,
      ask: '包邮赠送的礼包是真的吗？多久能收到？',
      answer: [
        '我的礼包3天就收到了，老师在群里说是开课前都能收到。',
        '当然是真的，而且是包邮的。',
        '是真的，礼包特别好看，里面东西很多，而且和课程是配套的，给我很大惊喜。',
      ],
    },
    {
      id: 5,
      ask: '如何查询礼盒的物流单号呢？',
      answer: ['下载小熊美术App可在【我-订单物流】中 查看快递寄出状态。'],
    },
    {
      id: 6,
      ask: '如何添加老师微信？',
      answer: [
        '买完课后会有老师二维码，直接扫码添加即可；很快就能通过。',
        '下载小熊美术App，用买课的手机号登陆，在我的课程-如何上课那里可以查老师微信号。',
        '买完课之后会有短信通知老师微信号的，及时添加就行。',
      ],
    },
    {
      id: 7,
      ask: '为什么要添加老师微信号？',
      answer: [
        '老师会拉你进入微信学习群，日常的通知、学习辅导、学习小任务和活动都会发布到微信群中。',
        '购课后都是由你添加的老师服务，有任何问题老师帮解决。',
      ],
    },
    {
      id: 8,
      ask: '这个课程能回放吗？忘了上课怎么办？',
      answer: ['没有限制，可以随时回看。', '放心吧，课程长期有效的。'],
    },
  ]);
  const changeTab = (tab, sectionType) => {
    if (problemTab === tab) return;
    setProblemTab(tab);
    setSection(sectionType);
    const data = problem
      .sort(createSortMethodByPropInArr('id', sectionType))
      .slice(-sectionType.length);
    setCurrentProblem(data);
  };

  useEffect(() => {
    const data = problem.sort(createSortMethodByPropInArr('id', section));
    setCurrentProblem(data);
  }, [problem, section]);

  return (
    <View className='problem-component'>
      <View className='tabs'>
        {tabs.map((item, index) => (
          <View
            className={`tab ${problemTab === index ? 'active' : ''}`}
            key={`tab-${index}`}
            onClick={() => changeTab(index, sectionArr[index])}
          >
            {item}
          </View>
        ))}
      </View>
      <View className='content'>
        {currentProblem.map((qa, k) => (
          <View className='qa' key={`qa-${k}`}>
            <View className='title'>{qa.ask}</View>
            {qa.answer.map((item, v) => (
              <View className='answer' key={`answer-${v}`}>
                {item}
              </View>
            ))}
          </View>
        ))}
      </View>
    </View>
  );
}
