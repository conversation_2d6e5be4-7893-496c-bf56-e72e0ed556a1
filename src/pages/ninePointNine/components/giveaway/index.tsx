import { useState } from 'react';
import giftImg from '@/assets/groupbuy/index/gift-img.png';
import giftBox from '@/assets/groupbuy/thirtySix/giftBox.png';
import { View, Image } from '@tarojs/components';
import './index.scss';

export default function Index() {
  // 随材赠品展示
  const [giveaway] = useState({
    img: giftImg,
    detail: [
      '小熊马克笔',
      '小熊勾线笔',
      'AR涂色卡',
      '各类作品纸',
      '绘画成长手册',
      '学习图谱等',
    ],
    notes: '该图仅为展示，具体以实物为主，每期根据课程不同收到的随材会有所差异',
  });
  return (
    <View className='giveaway'>
      <View className='row'>
        <View className='gift-box'>
          <Image src={giftBox} mode='widthFix' className='img' />
        </View>
        <View className='label'>
          {giveaway.detail.map((item, index) => {
            return (
              <View className='label-item' key={`label-${index}`}>
                {item}
              </View>
            );
          })}
        </View>
      </View>
      <View className='notes'>{giveaway.notes}</View>
    </View>
  );
}
