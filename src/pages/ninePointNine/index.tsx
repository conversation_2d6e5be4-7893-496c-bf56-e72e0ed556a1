import { useEffect, useLayoutEffect, useState } from 'react';
import { AtFloatLayout, AtModal, AtModalContent } from 'taro-ui';
import { Image, Swiper, SwiperItem, View } from '@tarojs/components';
import { useDispatch, useSelector } from 'react-redux';
import { levelV1, levelV2 } from '@/common/data.config';
import { useRouter } from '@tarojs/taro';
// @ts-ignore
import CommonTop from '@/components/commonTop';

import '@/theme/custom-taro-ui.scss';
import sensors from '@/utils/sensors_data';
import { UserStateType } from '@/store/groupbuy/state';

import selIcon from '@/assets/ninePointNine/gift-9_9-v1.png';
import redeemImg from '@/assets/groupbuy/thirtySix/redeem-img2.png';
import modalCloseImg from '@/assets/groupbuy/thirtySix/close.png';
import CONFIG from './config';

import LayoutGiveaway from './components/giveaway';
import LayoutPromise from './components/promise';
import LayoutProblem from './components/problem';

import LayoutLevel from '../../components/level';
import LayoutOrderV1 from '../../components/orderV1';
import WxLogin from '../../components/wxlogin';
import './index.scss';

/**
 *  @param popupType 浮窗类型
 *  0、随材赠品展示
 *  1、郑重承若
 *  2、大家都关心的问题
 *  3、选择级别（年龄）
 * */

/**
 * @param schemaType 模式类型 (预留参数)
 * 4、模式4
 * 5、模式5（待定）
 * */

/**
 *  @param levelType 级别类型
 *  0、选择级别（年龄）
 *  1、选择级别（写死描述）
 *  2、支付和级别选择一起
 * */

export default () => {
  const router = useRouter();
  const { age } = router.params;
  const configData = CONFIG[1];
  let bannerbg = '';
  if (age) {
    bannerbg = configData.ageList[0];
  } else {
    bannerbg = configData.defList[0];
  }

  function getGroupClass(index) {
    return (
      (configData.imgGroupsClass[index] &&
        configData.imgGroupsClass[index]['class']) ||
      ''
    );
  }

  const dispatch = useDispatch();

  // 显示隐藏浮窗
  const [isOpened, setIsOpened] = useState<boolean>(false);
  // 显示隐藏订单浮窗
  const [isShowOrder, setIsShowOrder] = useState<boolean>(false);
  // 浮窗类型
  const [popupType, setPopupType] = useState<number>(0);
  // 级别类型
  //   const [levelType] = useState<number>(Math.floor(Math.random() * 3));
  const [levelType] = useState<number>(1);

  // 支付页面的数据
  const [payPageData, setPayPageData] = useState<object | null>(null);

  const [showRedeemModal, setShowRedeemModal] = useState<boolean>(false);
  const [needShowRedeem, setNeedShowRedeem] = useState<boolean>(true);

  // 浮窗标题
  const [popupTitle] = useState([
    '随材赠品展示',
    '郑重承若',
    '大家都关心的问题',
    '选择级别',
  ]);

  let channelId = useSelector((state: UserStateType) => state.channelId);
  let userRole = useSelector((state: UserStateType) => state.userRole);

  // 改变弹窗类型
  const changePopupType = newPopupType => {
    if (levelType == 2) {
      watchShowOrder(true, {});
      setIsShowOrder(true);
      return;
    }
    setIsOpened(true);
    setPopupType(newPopupType);
  };

  // 监听订单浮窗的打开和关闭
  const watchShowOrder = (state, pageData) => {
    if (!state && needShowRedeem) {
      setShowRedeemModal(true);
      setNeedShowRedeem(false);
      return;
    }
    setNeedShowRedeem(true);
    setIsShowOrder(state);
    pageData && setPayPageData(pageData);

    pageData &&
      Object.keys(pageData).length > 0 &&
      !isShowOrder &&
      sensors.track('xxys_experienceCoursePage_courseSup_click', {
        course_sup: pageData.sup,
        channel_id: channelId,
        user_role: userRole,
        buy_model: 'model_4',
        abtest: levelType == 0 ? '单年龄' : '年龄&介绍',
      });
  };
  // 关闭挽留弹窗
  const hideRedeemHandle = () => {
    setShowRedeemModal(false);
  };

  useLayoutEffect(() => {
    // setCountDown(getOverTime());
  }, []);

  useEffect(() => {
    channelId &&
      sensors.track('xxms_testcourse_applethomepage_view', {
        buy_model: 'model_4',
        channel_id: channelId,
        page_source: '9.9元体验包小程序',
      });
  }, [channelId]);

  useEffect(() => {
    //区分类型
    dispatch({
      type: 'PAY_TYPE',
      payType: '36',
    });
  }, [dispatch]);

  // 随材赠品展示
  const [giveaway] = useState({
    img: selIcon,
    sImg: selIcon,
    detail: [
      '小熊模切',
      '超轻粘土',
      '小熊作品纸',
      '黑色勾线笔',
      '小熊马克笔',
      '重彩油画棒',
      '手指画颜料',
      '其他材料若干',
    ],
    notes: '该图仅为展示，具体以实物为主，每期根据课程不同收到的随材会有所差异',
  });
  return (
    <View className='index container w100 relative'>
      {/* <CodeSnippet /> */}
      {/* 微信登录组件 */}
      <WxLogin subject='ART_APP' isIntroduce={false} />
      {/* 头部导航栏 */}
      <CommonTop currentName='小熊美术' isIntroduce={false} />
      <View className='banner-box'>
        <Image mode='widthFix' src={bannerbg} className='swipe-img'></Image>
      </View>
      <View>
        <Image
          mode='widthFix'
          src={configData.decoration}
          className='payText'
        ></Image>
        <View className='bannerContent'>
          <View className='bannerList'>
            {configData.imgGroups.map((e, i, arr) => {
              if (Array.isArray(e)) {
                return (
                  <View className={`content-swiper-box ${getGroupClass(i)}`}>
                    <View className='swiper-bg'>
                      <Image
                        mode='widthFix'
                        src={arr[i - 1]}
                        className='swiper-bg-img'
                      ></Image>
                    </View>
                    <Swiper
                      className='swiper-content'
                      circular
                      autoplay
                      indicatorDots
                      indicatorActiveColor='linear-gradient(180deg, #FFDF00 0%, #FFB300 100%)'
                    >
                      {e.map((v, index) => (
                        <SwiperItem key={index} className='swiper-content-item'>
                          <Image
                            mode='widthFix'
                            src={v}
                            className='swipe-conent-img'
                          ></Image>
                        </SwiperItem>
                      ))}
                    </Swiper>
                  </View>
                );
              }
              if (Array.isArray(arr[i + 1])) {
                return null;
              }
              return (
                <View className='bannerDiv' key={i}>
                  <Image mode='widthFix' src={e} className='bannerImg'></Image>
                </View>
              );
            })}
          </View>
        </View>
        {/* <View>
         <Image mode='widthFix' src={sign} className='payText'></Image>
         </View> */}
      </View>

      <View className='suction-bottom' onClick={() => changePopupType(3)}>
        <Image
          mode='widthFix'
          src={configData.bottomBtn[0]}
          className='pay-img'
        />

        <Image
          mode='widthFix'
          src={configData.bottomBtn[1]}
          className='pay-fixed'
        />
      </View>
      {levelType !== 2 && (
        <AtFloatLayout
          className={`'custom-float-layout' ${
            popupType === 2 && isOpened ? 'problem-layout' : ''
          }`}
          isOpened={isOpened}
          onClose={() => {
            setIsOpened(false);
          }}
          title={popupTitle[popupType]}
        >
          {popupType === 0 && <LayoutGiveaway />}
          {popupType === 1 && <LayoutPromise />}
          {popupType === 2 && <LayoutProblem />}
          {popupType === 3 && (
            <LayoutLevel
              oldLevelArray={levelType == 1 ? levelV2 : levelV1}
              levelType={levelType}
              pType='art'
              orderType='9.9'
              regtype='EXPERIENCE_ONE_WEEK'
              watchShowOrder={watchShowOrder}
            />
          )}
        </AtFloatLayout>
      )}
      <AtFloatLayout
        className='order-layout custom-float-layout'
        isOpened={isShowOrder}
        onClose={() => {
          watchShowOrder(false, null);
        }}
      >
        {payPageData && (
          <LayoutOrderV1
            isShowOrder={isShowOrder}
            watchCloseOrder={watchShowOrder}
            payPageData={payPageData}
            orderType='9.9'
            subject='ART_APP'
            packagesId={process.env.NODE_ENV == 'online' ? 1150 : 58}
            classNum={10}
            topicId=''
            pType='art'
            isIntroduce={false}
            pName='美术'
            giveaway={giveaway}
            levelType={levelType}
            regtype='EXPERIENCE_ONE_WEEK'
            timing={300000}
            isUseByType
          />
        )}
      </AtFloatLayout>

      <AtModal
        className='redeem-modal'
        isOpened={showRedeemModal}
        closeOnClickOverlay={false}
      >
        <Image
          className='close-img'
          src={modalCloseImg}
          onClick={hideRedeemHandle}
        ></Image>
        <AtModalContent>
          <Image className='redeem-img' src={redeemImg}></Image>
          <View className='footer'>
            <View className='btns' onClick={hideRedeemHandle}>
              继续支付
            </View>
          </View>
        </AtModalContent>
      </AtModal>
    </View>
  );
};
