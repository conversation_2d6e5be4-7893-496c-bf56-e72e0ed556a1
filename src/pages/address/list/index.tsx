import Taro, { useRouter, useDidShow } from '@tarojs/taro';
import React, { useState } from 'react';
import { View, Image, Text } from '@tarojs/components';
import { AtToast } from 'taro-ui';

import { useSelector, useDispatch } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import { getAddressList } from '@/api/groupbuy';

import { addressListTypes, ToastStausTypes } from '@/types/types';

// 图片资源
import editImg from '@/assets/address/edit.png';
import choose from '@/assets/address/choose.png';
import unChoose from '@/assets/address/unChoose.png';

import './index.scss';

export default () => {
  const dispatch = useDispatch();
  const params = useRouter().params;
  // toast 提示
  const [ToastStaus, setToastStaus] = useState<ToastStausTypes>({
    status: undefined,
    text: '',
    duration: 1000,
    isOpened: false,
  });
  const userId = useSelector((state: UserStateType) => state.userid);
  const chooseAddressId = useSelector(
    (state: UserStateType) => state.chooseAddressId,
  );
  // 地址列表
  const [addressLists, setAddressLists] = useState<Array<addressListTypes>>([]);
  // 本次选中项Id
  const [selectAddressId, setselectAddressId] = useState<string>('');
  // 上级页面
  const [enterPage] = useState<string>(params.enterPage || '');

  useDidShow(() => {
    getUserAddress();
  });
  const getUserAddress = () => {
    if (userId) {
      getAddressList({
        userId,
        subject: 'ART_APP',
      })
        .then(res => {
          const { payload = [] } = res;
          if (payload.length) {
            // 如果有上次选择的地址，优先选择上次地址
            payload.forEach(element => {
              const {
                province,
                city,
                area,
                street,
                addressDetail,
                countryName,
                countryCode,
              } = element;
              element.addressStr = `${
                countryCode === 'cn' ? '' : countryName
              } ${province} ${
                city === province ? '' : city
              } ${area} ${street} ${addressDetail}`;
            });

            if (chooseAddressId) {
              setselectAddressId(chooseAddressId);
            } else {
              // 找到默认地址, 如果默认地址不是cn 则选中第一个cn地址
              let defaultAddress = payload.find(item => item.isDefault);
              if (defaultAddress.countryCode !== 'cn') {
                defaultAddress = payload.find(
                  item => item.countryCode === 'cn',
                );
              }
              setselectAddressId(defaultAddress.id);
            }
            setAddressLists(payload);
          }
        })
        .catch(rej => {
          setToastStaus({
            ...ToastStaus,
            status: 'error',
            text: `errors:${rej}`,
          });
        });
    }
  };

  const goAddressOperation = (item: any = null) => {
    let path = '/pages/address/edit/index?';
    if (item) {
      path = path + 'editCode=' + item.id;
    }
    Taro.navigateTo({
      url: path,
    });
  };
  const useAndGoBack: Function = async () => {
    dispatch({
      type: 'CHOOSEADDRESSID',
      chooseAddressId: selectAddressId,
    });
    setTimeout(() => {
      Taro.navigateBack();
    }, 20);
  };

  return (
    // 地址列表
    <View className='manage-container'>
      {/* 上方列表区域 */}
      <View className='manage-body'>
        {/* 列表外层盒子 */}
        <View className='manage-body-card'>
          {/* 列表内层盒子 */}
          <View className='manage-list-card'>
            {/* 单条地址记录 */}
            {addressLists.length !== 0 ? (
              addressLists.map((item, index) => {
                return (
                  <View
                    className='manage-list-card-item'
                    key={item.id}
                    onClick={() => {
                      setselectAddressId(item.id);
                    }}
                  >
                    {/* 地址信息区域 */}
                    <View className='manage-list-card-item-info'>
                      {enterPage === 'chooseAddress' ? (
                        <View>
                          {selectAddressId == item.id ? (
                            <Image className='choose' src={choose} />
                          ) : (
                            <Image className='choose' src={unChoose} />
                          )}
                        </View>
                      ) : (
                        ''
                      )}
                      <View className='manage-info'>
                        <View className='manage-info-top'>
                          <View className='info-left'>
                            <Text className='name'>{item.receiptName}</Text>
                            <Text className='phone'>{item.receiptTel}</Text>
                          </View>
                          <View
                            className='info-right'
                            onClick={() => {
                              goAddressOperation(item);
                            }}
                          >
                            <Image className='edit-img-style' src={editImg} />
                            <Text className='edit-img-text'> 修改</Text>
                          </View>
                        </View>
                        <View className='manage-info-bottom'>
                          {item.isDefault ? (
                            <Text className='default-add'>默认</Text>
                          ) : null}
                          <Text className='address-info'>
                            地址：{item.addressStr}
                          </Text>
                        </View>
                      </View>
                    </View>
                  </View>
                );
              })
            ) : (
              <View className='lottie-box'>
                <Text>暂无收货地址~</Text>
              </View>
            )}
          </View>
        </View>
      </View>
      <View className='manage-btn'>
        {enterPage === 'chooseAddress' ? (
          <View className='add-and-save-btn'>
            <button
              type='button'
              className={`$'small-button' $'delete-button'`}
              onClick={() => {
                goAddressOperation();
              }}
            >
              添加
            </button>
            <button
              type='button'
              className={`$'small-button' $'change-button'`}
              onClick={() => {
                useAndGoBack();
              }}
            >
              确认
            </button>
          </View>
        ) : (
          <button
            type='button'
            className='add-button'
            onClick={() => goAddressOperation()}
          >
            新增收货地址
          </button>
        )}
      </View>
      <AtToast {...ToastStaus}></AtToast>
    </View>
  );
};
