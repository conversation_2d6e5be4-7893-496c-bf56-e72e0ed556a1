// 地址管理
page {
  background: #fff;
}
.manage-container {
  font-family: PingFang SC;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: #fff;
  // 上方地址内容区域
  .manage-body {
    display: flex;
    justify-content: center;
    flex: 1;
    padding-bottom: 128px;
    padding-bottom: calc(128px + constant(safe-area-inset-bottom));
    padding-bottom: calc(128px + env(safe-area-inset-bottom));

    // 外层盒子
    &-card {
      display: flex;
      flex-direction: column;
      width: 100%;

      // 内层盒子
      .manage-list-card {
        flex: 1;
        background: #ffffff;

        // 单条地址
        &-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 30px;
          border-bottom: 1px solid #f5f5f5;
          font-size: 28px;

          &-info {
            display: flex;
            align-items: center;
            width: 100%;
            // 左侧选中icon
            .choose {
              display: flex;
              align-items: center;
              margin-right: 30px;
              display: block;
              width: 30px;
              height: 30px;
            }

            // 地址信息
            .manage-info {
              flex: 1;
              display: flex;
              width: 100%;
              flex-direction: column;

              .manage-info-top {
                display: flex;
                align-items: center;
                justify-content: space-between;

                .info-left {
                  .name {
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                    margin-right: 20px;
                  }

                  .phone {
                    margin-right: 10px;
                  }
                }

                .info-right {
                  display: flex;
                  align-items: center;

                  .edit-img-style {
                    width: 23px;
                    height: 23px;
                    margin-right: 9px;
                  }

                  .edit-img-text {
                    white-space: nowrap;
                    font-size: 28px;
                    color: #222222;
                  }
                }
              }

              .manage-info-bottom {
                width: 600px;
                margin-top: 14px;
                font-size: 24px;
                word-wrap: break-word;

                .default-add {
                  display: inline-block;
                  height: 40px;
                  line-height: 40px;
                  white-space: nowrap;
                  color: #ff9c00;
                  padding: 2px 16px;
                  background: rgba(255, 170, 24, 0.2);
                  border-radius: 21px;
                  margin-right: 10px;
                }

                .address-info {
                  height: 40px;
                  line-height: 40px;
                  width: 100%;
                  color: #999999;
                }
              }
            }
          }
        }
      }
    }

    .lottie-box {
      padding-top: 26vh;
      display: flex;
      align-items: center;
      flex-direction: column;
      margin-top: 28px;
      font-size: 28px;
      color: #666666;
    }
  }

  // 底部按钮
  .manage-btn {
    box-sizing: border-box;
    position: fixed;
    bottom: 0;
    background: #fff;
    width: 100vw;
    padding: 16px 30px;
    padding-bottom: 16px;
    padding-bottom: calc(16px + constant(safe-area-inset-bottom));
    padding-bottom: calc(16px + env(safe-area-inset-bottom));
    .add-and-save-btn {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .small-button {
        border-radius: 88px;
        width: 328px;
        outline: none;
        height: 96px;
        font-size: 36px;
      }
      .delete-button {
        color: #ffbc00;
        border: 1px solid #ffbc00;
        margin-right: 34px;
        background: transparent;
      }
      .change-button {
        background: #ffbc00;
        border: 1px solid transparent;
        color: #fff;
      }
    }

    .add-button {
      width: 100%;
      color: #fff;
      font-size: 32px;
      font-weight: 500;
      height: 96px;
      background: #ffbc00;
      border: 1px solid transparent;
      border-radius: 88px;
      outline: none;
    }
  }
}
