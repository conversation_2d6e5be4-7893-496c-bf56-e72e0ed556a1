import Taro, { useRouter } from '@tarojs/taro';
import React, { useState, useEffect } from 'react';
import { View, Image, Input, Text, Textarea, Switch } from '@tarojs/components';
import { AtToast } from 'taro-ui';

import { useSelector, useDispatch } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';

import {
  getCenterAddressList,
  getAddressTownList,
  getCenterCountryList,
  getCenterOSAddressList,
} from '@/api/groupbuy';

import {
  GetAddressTownListRes,
  GetCenterCountryItem,
  ToastStausTypes,
} from '@/types/types';
import chooseArea from '@/assets/address/chooseArea.png';

import './index.scss';

interface Props {
  openArea: Function;
}
interface AreaData {
  telAreaCode?: string;
  provinceName?: string;
  cityName?: string;
  countyName?: string;
  townName?: string;
  countyCode?: string;
  provinceEdit?: string;
  cityEdit?: string;
  areaEdit?: string;
  streetEdit?: string;
  citys?: Array<ArrayData>;
  countys?: Array<ArrayData>;
  countryName?: string;
  countryCode?: string;
  postCode?: string;
  countryNameEdit?: string;
  countryType?: number;
}
interface ArrayData {}
interface AreaList {
  map: Function;
  find: Function;
  length: number;
}
interface backToProp {
  state: boolean;
  content: object;
  isBackShow?: boolean;
}

interface AreaObj {
  homeList: AreaList;
  abroadList: Array<GetCenterCountryItem>;
}
export default (props: Props) => {
  const [tabList] = useState<Array<string>>(['请选择']);

  const [addressObj, setAddressObj] = useState<AreaObj>({
    homeList: [],
    abroadList: [],
  });

  const [cityList, setCityList] = useState<AreaList>([]);
  const [county, setCounty] = useState<AreaList>([]);
  const [town, setTown] = useState<GetAddressTownListRes[]>([]);

  const [currentTab, setCurrentTab] = useState(0);

  const [province, setProvince] = useState('');
  const [city, setCity] = useState('');
  const [area, setArea] = useState('');
  const [street, setStreet] = useState('');
  const [telAreaCode, setTelAreaCode] = useState('');
  const [zipCode, setZipCode] = useState('');
  const [countryCode, setCountryCode] = useState('');
  const [countryName, setCountryName] = useState('');

  // 0为国内
  const [countryType, setCountryType] = useState<number>(0);

  const [isChange, setIsChange] = useState<boolean>(false);
  const [queryCountryCode] = useState(['cn']);
  // 修改单条地址项(自定义hooks)
  // const [changeItem] = useChangeAddressItem()
  const [changeItem, setchangeItem] = useState('');
  const { openArea = () => {} } = props;
  // 获取地址栏参数
  const params = useRouter().params;
  const [editCode] = useState<string>(!params.editCode ? 'add' : '');
  const [type] = useState<string>(!params.editCode ? 'add' : '');

  // toast 提示
  const [ToastStaus, setToastStaus] = useState<ToastStausTypes>({
    status: undefined,
    text: '',
    duration: 1000,
    isOpened: false,
  });
  // 获取海外和国内的地址
  useEffect(() => {
    getCenterAddressList().then(homeRes => {
      getCenterCountryList().then(abroadRes => {
        let homeList = homeRes.payload.data;
        let abroadList = abroadRes.payload.data;
        if (!!queryCountryCode.length) {
          // 不可以选择更改国内和海外
          setIsChange(true);
          if (queryCountryCode.includes('cn')) {
            setCountryType(0);
          } else {
            setCountryType(1);
            // 如果链接上有countryCode且不是cn，只展示对应的海外国家
            if (abroadRes.payload && abroadRes.payload.data.length !== 0) {
              abroadList = abroadRes.payload.data.filter(item =>
                queryCountryCode.includes(item.countryCode),
              );
            }
          }
        }
        setAddressObj({
          homeList,
          abroadList,
        });
      });
    });
  }, []);

  useEffect(() => {
    let backData = {
      id: '',
      userId: '',
      receiptName: '',
      receiptTel: '',
      province: '',
      city: '',
      area: '',
      street: '',
      addressDetail: '',
      isDefault: 0,
      type: 0,
      telAreaCode: '',
      zipCode: '',
      countryCode: '',
      countryName: '',
    };
    // if (changeItem.id) {
    //   backData = changeItem
    // } else if (sessionStorage.getItem('chooseEditAddress')) {
    //   backData = JSON.parse(sessionStorage.getItem('chooseEditAddress') || '')
    // }
    const {
      province: provinceEdit,
      city: cityEdit,
      area: areaEdit,
      street: streetEdit,
      telAreaCode: telAreaCodeEdit,
      type: typeEdit,
      zipCode: zipCodeEdit,
      countryCode: countryCodeEdit,
      countryName: countryNameEdit,
    } = backData;
    if (typeEdit === 0) {
      if (addressObj.homeList.length === 0) return;
      if (provinceEdit && cityEdit && areaEdit) {
        // 修改的时候，国内和国外的地址不能互相改
        setIsChange(true);
        setCountryType(0);
        echoAddress({
          countryNameEdit,
          provinceEdit,
          cityEdit,
          areaEdit,
          streetEdit: streetEdit || '暂不选择',
          countryType: 0,
        });
      }
    } else {
      if (addressObj.abroadList.length === 0) return;
      if (countryNameEdit && provinceEdit && cityEdit) {
        // 修改的时候，国内和国外的地址不能互相改
        setIsChange(true);
        setCountryType(1);
        echoAddress({
          countryNameEdit,
          provinceEdit,
          cityEdit,
          areaEdit,
          streetEdit: streetEdit || '暂不选择',
          countryType: 1,
        });
      }
    }

    closePopUp({
      state: false,
      isBackShow: true,
      content: {
        showProvince: provinceEdit,
        showCity: cityEdit,
        showArea: areaEdit,
        showStreet: streetEdit === '暂不选择' ? '' : streetEdit,
        showTelAreaCode: telAreaCodeEdit,
        showCountryType: typeEdit,
        showZipCode: zipCodeEdit,
        showCountryCode: countryCodeEdit,
        showCountryName: countryNameEdit,
      },
    });
  }, [type === 'change' && changeItem && addressObj.homeList.length !== 0]);

  const echoAddress: Function = async (params: AreaData) => {
    const {
      countryNameEdit,
      provinceEdit,
      cityEdit,
      areaEdit,
      streetEdit,
      countryType,
    } = params;
    if (countryType === 0) {
      // 国内
      // 回显省
      let provinceEcho = addressObj.homeList.find(
        (item: AreaData) => item.provinceName === provinceEdit,
      );
      provinceEcho && handleClickProvince(provinceEcho, 0);
      // 回显城市
      let cityEcho = provinceEcho?.citys.find(
        (item: AreaData) => item.cityName === cityEdit,
      );
      cityEcho && handleClickCity(cityEcho, 0);
      // 回显地区
      let areaEcho = cityEcho?.countys.find(
        (item: AreaData) => item.countyName === areaEdit,
      );
      areaEcho && handleClickRegion(areaEcho, 0);
      // 回显街道
      let getTowns: GetAddressTownListRes =
        (await handleClickRegion(areaEcho, 0)) || [];
      let streetEcho =
        getTowns &&
        getTowns.find!((item: AreaData) => item.townName === streetEdit);
      streetEcho && handleClickTown(streetEcho, 0);
    } else {
      // 海外
      // 回显国家
      let countryEcho;
      if (queryCountryCode) {
        countryEcho = addressObj.abroadList[0];
      } else {
        countryEcho = addressObj.abroadList.find(
          (item: AreaData) => item.countryName === countryNameEdit,
        );
      }
      countryEcho && handleClickProvince(countryEcho, 1);
      // 回显省
      const provinceData =
        (await getCenterOSAddressList({
          countryCode: countryEcho?.countryCode || '',
        })) || [];
      let provinceEcho = provinceData.payload.data.find(
        (item: AreaData) => item.provinceName === provinceEdit,
      );
      provinceEcho && handleClickCity(provinceEcho, 1);
      // 回显城市
      let cityEcho = provinceEcho?.citys.find(
        (item: AreaData) => item.cityName === cityEdit,
      );
      cityEcho && handleClickRegion(cityEcho, 1);
      // 回显地区
      let areaEcho = cityEcho?.countys.find(
        (item: AreaData) => item.countyName === areaEdit,
      );
      areaEcho && handleClickTown(areaEcho, 1);
    }
  };

  const handleClickTab = (value: number) => {
    setCurrentTab(value);
  };

  const handleClickProvince = (item: AreaData, selCountryType: number) => {
    tabList[1] = '请选择';
    tabList[2] = '';
    tabList[3] = '';
    setTelAreaCode(item.telAreaCode || '');
    setCurrentTab(1);

    if (selCountryType === 0) {
      // 国内
      tabList[0] = item.provinceName || '';
      setProvince(item.provinceName || '');
      setCityList(item.citys || []);
    } else {
      // 海外
      tabList[0] = item.countryName || '';
      setCountryName(item.countryName || '');
      setCountryCode(item.countryCode || '');
      getCenterOSAddressList({ countryCode: item.countryCode || '' }).then(
        res => {
          setCityList(res.payload.data);
        },
      );
    }
  };
  const handleClickCity = (item: AreaData, selCountryType: number) => {
    tabList[2] = '请选择';
    tabList[3] = '';
    setCurrentTab(2);
    if (selCountryType === 0) {
      // 国内
      tabList[1] = item.cityName || '';
      setCity(item.cityName || '');
      setCounty(item.countys || []);
    } else {
      // 海外
      tabList[1] = item.provinceName || '';
      setProvince(item.provinceName || '');
      setCounty(item.citys || []);
    }
  };
  const handleClickRegion = async (item: AreaData, selCountryType: number) => {
    tabList[3] = '请选择';
    setCurrentTab(3);

    if (selCountryType === 0) {
      // 国内
      tabList[2] = item.countyName || '';
      setArea(item.countyName || '');
      let datas = await getAddressTownList({
        code: item.countyCode || '',
      }).then(res => {
        let data = res.payload;
        data.unshift({ townName: '暂不选择' });
        setTown(data);
        return data;
      });
      return datas;
    } else {
      // 海外
      tabList[2] = item.cityName || '';
      setCity(item.cityName || '');
      let data = item.countys || [];
      data.unshift({ countyName: '暂不选择' });
      setTown(data);
      setZipCode(item.postCode || '');
    }
  };
  const handleClickTown = (item: AreaData, selCountryType: number) => {
    let showStreet;
    if (selCountryType === 0) {
      // 国内
      tabList[3] = item.townName || '';
      setStreet(item.townName || '');
      showStreet = item.townName;
    } else {
      // 海外
      tabList[3] = item.countyName || '';
      setArea(item.countyName || '');
      showStreet = item.countyName;
    }

    // 关闭弹框 回传选中四级地址
    closePopUp({
      state: false,
      content: {
        showProvince: province,
        showTelAreaCode: telAreaCode,
        showCity: city,
        showArea: area,
        showStreet: showStreet === '暂不选择' ? '' : showStreet,
        showCountryType: selCountryType,
        showZipCode: zipCode,
        showCountryCode: countryCode,
        showCountryName: countryName,
      },
    });
  };

  const closePopUp: Function = (params: backToProp) => {
    const { state, content, isBackShow = false } = params;
    openArea({
      state,
      isBackShow,
      content: content || {},
    });
  };
  const clickNationTabs = (index: number) => {
    if (countryType === index) return;
    if (isChange) {
      setToastStaus({
        ...ToastStaus,
        text: `当前收货号码不可选择其他国家地址`,
        isOpened: true,
      });
      return;
    }
    tabList[0] = '请选择';
    tabList[1] = '';
    tabList[2] = '';
    tabList[3] = '';
    setProvince('');
    setCity('');
    setArea('');
    setStreet('');
    setTelAreaCode('');
    setCountryType(index);
    setCurrentTab(0);
  };

  return (
    <View className='area-box'>
      <View className='area-tabs-box'>
        <View className='area-country'>
          <Text
            className={`area-country-item ${countryType === 0 ? 'active' : ''}`}
            onClick={() => {
              clickNationTabs(0);
            }}
          >
            中国
          </Text>
          <Text
            className={`area-country-item ${countryType === 1 ? 'active' : ''}`}
            onClick={() => {
              clickNationTabs(1);
            }}
          >
            海外国家
          </Text>
        </View>
        <View className='area-tabs'>
          {tabList.map((item, index: number) => {
            return (
              <View
                className={`area-tabs-item ${
                  currentTab === index ? 'active' : ''
                }`}
                key={`tab-${index}`}
                onClick={() => handleClickTab(index)}
              >
                {item}
              </View>
            );
          })}
        </View>
      </View>
      {countryType === 0 ? (
        <View className='area-list'>
          <View
            className={`area-province ${currentTab === 0 ? 'active' : 'hide'}`}
          >
            {addressObj.homeList.map((item: AreaData, index: number) => {
              return (
                <View
                  className='area-list-item'
                  key={`area-province${index}`}
                  onClick={() => handleClickProvince(item, 0)}
                >
                  {item.provinceName === province && (
                    <Image src={chooseArea} className='choose-right' />
                  )}
                  {item.provinceName}
                </View>
              );
            })}
          </View>
          <View className={`area-city ${currentTab === 1 ? 'active' : 'hide'}`}>
            {cityList.map((item: AreaData, index: number) => {
              return (
                <View
                  className='area-list-item'
                  key={`area-city${index}`}
                  onClick={() => handleClickCity(item, 0)}
                >
                  {item.cityName === city && (
                    <Image src={chooseArea} className='choose-right' />
                  )}
                  {item.cityName}
                </View>
              );
            })}
          </View>
          <View
            className={`area-region ${currentTab === 2 ? 'active' : 'hide'}`}
          >
            {county.map((item: AreaData, index: number) => {
              return (
                <View
                  className='area-list-item'
                  key={`area-region${index}`}
                  onClick={() => handleClickRegion(item, 0)}
                >
                  {item.countyName === area && (
                    <Image src={chooseArea} className='choose-right' />
                  )}
                  {item.countyName}
                </View>
              );
            })}
          </View>
          <View
            className={`area-street ${currentTab === 3 ? 'active' : 'hide'}`}
          >
            {town.map((item: GetAddressTownListRes, index: number) => {
              return (
                <View
                  className='area-list-item'
                  key={`area-street${index}`}
                  onClick={() => handleClickTown(item, 0)}
                >
                  {item.townName === street && (
                    <Image src={chooseArea} className='choose-right' />
                  )}
                  {item.townName}
                </View>
              );
            })}
          </View>
        </View>
      ) : (
        <View className='area-list'>
          <View
            className={`area-province ${currentTab === 0 ? 'active' : 'hide'}`}
          >
            {addressObj.abroadList.map(
              (item: GetCenterCountryItem, index: number) => {
                return (
                  <View
                    className='area-list-item'
                    key={`area-province${index}`}
                    onClick={() => handleClickProvince(item, 1)}
                  >
                    {item.countryName === countryName && (
                      <Image src={chooseArea} className='choose-right' />
                    )}
                    {item.countryName}
                  </View>
                );
              },
            )}
          </View>
          <View className={`area-city ${currentTab === 1 ? 'active' : 'hide'}`}>
            {cityList.map((item: AreaData, index: number) => {
              return (
                <View
                  className='area-list-item'
                  key={`area-city${index}`}
                  onClick={() => handleClickCity(item, 1)}
                >
                  {item.provinceName === province && (
                    <Image src={chooseArea} className='choose-right' />
                  )}
                  {item.provinceName}
                </View>
              );
            })}
          </View>
          <View
            className={`area-region ${currentTab === 2 ? 'active' : 'hide'}`}
          >
            {county.map((item: AreaData, index: number) => {
              return (
                <View
                  className='area-list-item'
                  key={`area-region${index}`}
                  onClick={() => handleClickRegion(item, 1)}
                >
                  {item.cityName === city && (
                    <Image src={chooseArea} className='choose-right' />
                  )}
                  {item.cityName}
                </View>
              );
            })}
          </View>
          <View
            className={`area-street ${currentTab === 3 ? 'active' : 'hide'}`}
          >
            {town.map((item: AreaData, index: number) => {
              return (
                <View
                  className='area-list-item'
                  key={`area-street${index}`}
                  onClick={() => handleClickTown(item, 1)}
                >
                  {item.countyName === area && (
                    <Image src={chooseArea} className='choose-right' />
                  )}
                  {item.countyName}
                </View>
              );
            })}
          </View>
        </View>
      )}
      <AtToast {...ToastStaus}></AtToast>
    </View>
  );
};
