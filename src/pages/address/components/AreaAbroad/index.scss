.area-box {
  width: 100%;
  background: #ffffff;
  font-size: 28px;
  padding-top: 120px;
  .area-tabs-box {
    transform: translateZ(0);
    -webkit-overflow-scrolling: touch;
    position: fixed;
    z-index: 99;
    top: 110px;
    width: 100%;
    border-bottom: 1px solid #f5f5f5;
    background-color: #fff;
    box-sizing: border-box;

    .area-country {
      padding: 0 30px 30px;
      display: flex;
      justify-content: space-around;
      .active {
        color: #ffbc00;
      }
    }
    .area-tabs {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding-left: 30px;

      .active {
        color: #ffbc00;
      }
      .area-tabs-item {
        margin: 10px 0 20px 30px;
        &:first-child {
          margin-left: 0;
        }
      }
    }
  }
  .area-list {
    .choose-right {
      width: 25px;
      height: 18px;
      margin-right: 10px;
    }
    .active {
      display: block;
    }
    .hide {
      display: none;
    }
    .area-list-item {
      margin-top: 40px;
      margin-left: 30px;
      &:last-child {
        margin-bottom: 40px;
      }
    }
  }
}
