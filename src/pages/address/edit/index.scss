page {
  background: #fff;
}
// 操作地址
.container {
  background: #ffffff;
  height: 100vh;
  // 上方提示
  .tip {
    background: #f2f2f2;
    color: #999999;
    font-size: 24px;
    font-weight: 400;
    padding: 20px 30px;
  }
  .InputFrom {
    padding: 24px 0;
    margin: 0 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ebedf0;
    .InputLabel {
      font-size: 32px;
      font-family: PingFang SC;
      color: #222222;
      width: 180px;
    }
    .setDefault {
      width: 260px;
    }
    .InputItem {
      flex: 1;
      font-size: 28px;
    }
    .TextareaItem {
      flex: 1;
      background: #fff;
      width: 100%;
      height: 120px;
      padding: 0 30px;
      font-size: 28px;
    }
    .telAreaCodeText {
      color: #969799;
      font-size: 32px;
      margin-left: 12px;
    }
    .rightIcon {
      width: 32px;
      height: 32px;
    }
  }
  // 无地址新建状态文案
  .no-address {
    font-size: 24px;
    margin: 20px 0 0;
    padding: 0 30px;
    color: #ff9c31;
  }
  // 底部按钮样式
  .fixed-bottom {
    z-index: 2;
    background: #fff;
    position: fixed;
    bottom: 40px;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    width: 100%;
    .sub-btn-style {
      display: flex;
      justify-content: center;
      padding: 0 30px;
      .small-button {
        border-radius: 88px;
        width: 328px;
        outline: none;
        height: 96px;
        font-size: 36px;
      }
      .delete-button {
        color: #ffbc00;
        border: 1px solid #ffbc00;
        margin-right: 34px;
        background: transparent;
      }
      .change-button {
        background: #ffbc00;
        border: 1px solid transparent;
        color: #fff;
      }
      .add-button {
        width: calc(100% - 60px);
        height: 96px;
        color: #fff;
        font-size: 32px;
        font-weight: 500;
        background: #ffbc00;
        border: 1px solid transparent;
        border-radius: 88px;
        outline: none;
      }
    }
  }
  // 弹窗
  .service-description {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.7);
    z-index: 6;
    &.hide {
      visibility: hidden;
    }
    // :global {
    //   .am-drawer-sidebar {
    //     background: #fff;
    //     height: 60vh;
    //   }
    // }
  }
  .address-area-float {
    .layout {
      height: 80%;
    }

    .at-float-layout__container {
      .layout-body {
        .layout-body__content {
          max-height: 840px;
        }
      }
    }
  }
}
