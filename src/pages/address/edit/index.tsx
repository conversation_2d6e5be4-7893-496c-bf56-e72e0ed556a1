import Taro, { useRouter } from '@tarojs/taro';
import { View, Image, Input, Text, Textarea, Switch } from '@tarojs/components';
import { AtFloatLayout, AtModal } from 'taro-ui';
import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';

import {
  deleteAddress,
  createUserAddress,
  updateUserAddress,
  getAddressList,
  getAddressById,
} from '@/api/groupbuy';

import chooseAddress from '@/assets/pay/arrow-right.png';
import { areaAddressType } from '@/types/types';

import AreaChoose from '@/pages/address/components/AreaAbroad/index';
import './index.scss';

export default () => {
  const userId = useSelector((state: UserStateType) => state.userid);
  const dispatch = useDispatch();
  const params = useRouter().params;
  const [editCode] = useState<string>(params.editCode || '');
  const [receiptName, setReceiptName] = useState<string>('');
  const [receiptTel, setReceiptTel] = useState<string>('');
  const [telAreaCode, setTelAreaCode] = useState<string>('+86');
  // 拼接后的地址
  const [areaPopState, setAreaPopState] = useState<boolean>(false);
  const [pieceAddress, setPieceAddress] = useState<string>('');
  // 收货人、手机号、省、市、区、街道、详细地址、是否默认、页面单选框是否默认、获取用户地址数量
  const [province, setProvince] = useState<string>('');
  const [city, setCity] = useState<string>('');
  const [area, setArea] = useState<string>('');
  const [street, setStreet] = useState<string>('');
  const [addressDetail, setAddressDetail] = useState<string>('');
  const [isDefault, setIsDefault] = useState<boolean>(false);
  const [isPageDefault, setIsPageDefault] = useState<boolean>(false);
  const [addressNum, setAddressNum] = useState<number>(10);
  // 删除弹窗
  const [delModal, setdelModal] = useState(false);

  const chooseFourLevelAddress = () => {
    setAreaPopState(true);
  };

  // 获取当前要修改的地址
  useEffect(() => {
    editCode &&
      getAddressById({ addressId: editCode, userId }).then(res => {
        const { payload } = res;
        setReceiptName(payload.receiptName);
        setReceiptTel(payload.receiptTel);
        setProvince(payload.province);
        setCity(payload.city);
        setArea(payload.area);
        setStreet(payload.street);
        setAddressDetail(payload.addressDetail);
        setIsDefault(payload.isDefault);
        setIsPageDefault(payload.isDefault);
        setPieceAddress(
          `${payload.province} ${
            payload.city === payload.province ? '' : payload.city
          } ${payload.area} ${payload.street}`,
        );
      });
  }, [editCode]);

  useEffect(() => {
    getAddressNumber();
  }, []);
  // 获取当前用户地址数量
  const getAddressNumber: Function = async () => {
    try {
      let res = await getAddressList({
        userId,
        subject: 'ART_APP',
      });
      const { payload = [] } = res;
      payload.length && setAddressNum(payload.length);
    } catch (error) {
      Taro.showToast({
        title: `error:${error}`,
        icon: 'none',
      });
    }
  };
  const changeAddress = () => {
    if (!formChecked(5)) return;
    updateUserAddress({
      isDefault: isPageDefault ? '1' : '0',
      addressId: editCode,
      userId,
      receiptName,
      receiptTel,
      telAreaCode,
      province,
      city,
      area,
      street,
      areaCode: '',
      idCode: '',
      addressDetail,
    }).then(res => {
      if (res.code === 0) {
        Taro.showToast({
          title: '修改成功',
          icon: 'none',
        });
        setTimeout(() => {
          Taro.navigateBack();
        }, 2000);
        return;
      }
      Taro.showToast({
        title: '修改失败',
        icon: 'none',
      });
    });
  };
  // 新增地址按钮
  const addAddress: Function = () => {
    if (!formChecked(5)) return;
    // 正常添加地址
    // 国内
    createUserAddress({
      subject: 'ART_APP',
      isDefault: isPageDefault ? '1' : '0',
      userId,
      receiptName,
      receiptTel,
      telAreaCode,
      province,
      city,
      area,
      street,
      areaCode: '',
      idCode: '',
      addressDetail,
    }).then(res => {
      if (res.code === 0) {
        Taro.showToast({
          title: '新增成功',
          icon: 'none',
        });
        setTimeout(() => {
          Taro.navigateBack();
        }, 2000);
        return;
      }
      Taro.showToast({
        title: '新增失败',
        icon: 'none',
      });
    });
  };
  // 删除
  const deleteAddressFn = () => {
    deleteAddress({
      addressId: editCode,
      isDefault: isPageDefault,
      userId,
    }).then(res => {
      if (res.code === 0) {
        dispatch({
          type: 'CHOOSEADDRESSID',
          chooseAddressId: '',
        });
        Taro.showToast({
          title: '删除成功',
          icon: 'none',
        });
        setTimeout(() => {
          Taro.navigateBack();
        }, 2000);
        return;
      }
      Taro.showToast({
        title: '删除失败',
        icon: 'none',
      });
    });
  };

  // 表单校验
  const formChecked: Function = (index: number) => {
    switch (index) {
      case 1:
        if (!receiptName) {
          Taro.showToast({
            title: '收货人不能为空',
            icon: 'none',
          });
        }
        return false;
      case 2:
        if (telAreaCode === '+86') {
          if (
            !(
              /^1[3456789]\d{9}$/.test(receiptTel) ||
              /^1[3456789]\d{1}[*]{4}\d{4}$/.test(receiptTel)
            )
          ) {
            if (!receiptTel) {
              Taro.showToast({
                title: '手机号不能为空',
                icon: 'none',
              });
              return false;
            }
            Taro.showToast({
              title: '手机号格式错误',
              icon: 'none',
            });
            return false;
          }
        }
        break;
      case 3:
        if (!pieceAddress) {
          Taro.showToast({
            title: '所在地区不能为空',
            icon: 'none',
          });
        }
        return false;
      case 4:
        if (!addressDetail) {
          Taro.showToast({
            title: '详细地址不能为空',
            icon: 'none',
          });
        }
        return false;
      default:
        if (!receiptName) {
          Taro.showToast({
            title: '收货人不能为空',
            icon: 'none',
          });
          return false;
        }

        if (telAreaCode === '+86') {
          if (
            !(
              /^1[3456789]\d{9}$/.test(receiptTel) ||
              /^1[3456789]\d{1}[*]{4}\d{4}$/.test(receiptTel)
            )
          ) {
            if (!receiptTel) {
              Taro.showToast({
                title: '手机号不能为空',
                icon: 'none',
              });
              return false;
            }
            Taro.showToast({
              title: '手机号格式错误',
              icon: 'none',
            });
            return false;
          }
        }
        if (!pieceAddress) {
          Taro.showToast({
            title: '所在地区不能为空',
            icon: 'none',
          });
          return false;
        }

        if (!addressDetail) {
          Taro.showToast({
            title: '详细地址不能为空',
            icon: 'none',
          });
          return false;
        }
        return true;
    }
  };

  // 修改抽屉状态、获取数据
  const closeDrawer: Function = (item: areaAddressType) => {
    const { state = false, content } = item;
    setAreaPopState(state);
    const {
      showProvince,
      showTelAreaCode,
      showCity,
      showArea,
      showStreet,
      showCountryType,
    } = content;

    if (showCountryType === 0) {
      if (!(showProvince && showCity && showArea)) return;
      setPieceAddress(
        `${showProvince} ${
          showCity === showProvince ? '' : showCity
        } ${showArea} ${showStreet}`,
      );
    }
    setProvince(showProvince);
    setTelAreaCode(showTelAreaCode);
    setCity(showCity);
    setArea(showArea);
    setStreet(showStreet);
  };
  return (
    <View className='container'>
      <View className='tip'>
        为了保证快递能及时送达，请确保地址信息准确，收货电话畅通
      </View>
      <View className='InputFrom'>
        <Text className='InputLabel'>收货人：</Text>
        <Input
          placeholder='请填写收货人姓名'
          maxlength={50}
          className='InputItem'
          type='text'
          value={receiptName}
          onInput={value => {
            setReceiptName(value.detail.value);
          }}
        ></Input>
      </View>
      <View className='InputFrom'>
        <Text className='InputLabel'>手机号码：</Text>
        <Input
          placeholder='请填写收货人手机号'
          maxlength={50}
          type='number'
          className='InputItem'
          value={receiptTel}
          onInput={value => {
            setReceiptTel(value.detail.value);
          }}
        ></Input>
        <Text className='telAreaCodeText'>{telAreaCode}</Text>
      </View>
      <View className='InputFrom' onClick={chooseFourLevelAddress}>
        <Text className='InputLabel'>所在地区：</Text>
        <View className='InputItem'>{pieceAddress || ''}</View>
        <Image className='rightIcon' src={chooseAddress} />
      </View>

      <View className='InputFrom'>
        <Text className='InputLabel'>详细地址：</Text>
        <Textarea
          placeholder='街道门牌、楼房房间号等信息'
          className='TextareaItem'
          value={addressDetail}
          onInput={value => {
            setAddressDetail(value.detail.value);
          }}
        ></Textarea>
      </View>

      {addressNum && !isDefault ? (
        <>
          <View className='InputFrom'>
            <Text className='InputLabel setDefault'>设为默认地址：</Text>
            <Switch
              onChange={res => {
                setIsPageDefault(res.detail.value);
              }}
              checked={isPageDefault}
              color='rgb(255, 188, 0)'
            />
          </View>
          <View className='no-address'>
            {addressNum
              ? '*设置为默认地址后，未发货的物流订单会使用该地址'
              : '*此地址为默认地址，未发货的订单均使用此地址'}
          </View>
        </>
      ) : (
        <View className='no-address'>*此地址为默认地址</View>
      )}
      {/* 关联订单的新增、修改、删除 */}
      <View className='fixed-bottom'>
        {editCode ? (
          <View className='sub-btn-style'>
            {/* 默认地址不可删除 */}
            {isDefault ? (
              ''
            ) : (
              <button
                type='button'
                className={`$'small-button' $'delete-button'`}
                onClick={() => {
                  setdelModal(true);
                }}
              >
                删除
              </button>
            )}
            <button
              type='button'
              className={`$'small-button' $'change-button'`}
              onClick={() => {
                changeAddress();
              }}
            >
              保存
            </button>
          </View>
        ) : (
          <View className='sub-btn-style'>
            <button
              type='button'
              className='add-button'
              onClick={() => {
                addAddress();
              }}
            >
              保存
            </button>
          </View>
        )}
      </View>

      <AtFloatLayout
        className='address-area-float'
        isOpened={areaPopState}
        title='请选择所在地区'
        onClose={() => setAreaPopState(false)}
      >
        <AreaChoose openArea={closeDrawer} />
      </AtFloatLayout>

      <AtModal
        isOpened={delModal}
        title='温馨提示'
        cancelText='取消'
        confirmText='确认'
        onConfirm={deleteAddressFn}
        content='确认删除该地址吗?'
      />
    </View>
  );
};
