.orderDetail {
  .atTopThe {
    height: 172px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding-left: 40px;
    .logo {
      width: 72px;
      height: 72px;
      background-image: url('../../../assets/orderDetail/logo.png');
      background-repeat: no-repeat;
      background-size: cover;
      background-position: 0 0;
    }
    .title {
      height: 45px;
      font-size: 32px;
      font-weight: 700;
      color: #333333;
      margin-left: 30px;
    }
  }
  .toShowGoods {
    height: 258px;
    display: flex;
    background: #fff7e7;
    .showBoxImg {
      width: 240px;
      height: 210px;
      margin-left: 40px;
      margin-top: 24px;
      .img {
        width: 100%;
        height: 100%;
      }
    }
    .showBoxAbout {
      flex: 1;
      margin-left: 18px;
      padding-top: 28px;
      .showBoxAboutTitle {
        text-align: left;
        height: 42px;
        font-size: 30px;
        font-weight: 700;
        color: #1a1a1a;
        line-height: 42px;
        display: flex;
        .titleLe {
          margin-right: 44px;
        }
      }
      .showBoxAboutTips {
        display: flex;
        align-items: center;
        height: 33px;
        margin-top: 11px;
        .gifts {
          width: 100px;
          font-size: 24px;
          font-weight: 700;
          color: #ff9b00;
        }
        .tips {
          flex: 1;
          font-size: 24px;
          font-weight: 400;
          color: #727272;
        }
      }
      .payPrice {
        display: flex;
        align-items: center;
        margin-top: 70px;
        margin-left: 150px;
        .words {
          font-size: 28px;
          font-weight: 700;
          color: #1a1a1a;
        }
        .price {
          font-size: 34px;
          font-weight: 700;
          color: #ff7009;
          margin-left: 12px;
          display: flex;
          align-items: flex-end;
          .unit {
            font-size: 22px;
            margin-bottom: 4px;
          }
        }
        .tips {
          font-size: 28px;
          font-weight: 400;
          color: #666666;
        }
      }
    }
  }
  .directionsForUse {
    position: relative;
    margin-top: 60px;
    .tipsImg {
      width: 240px;
      height: 71px;
      background: linear-gradient(180deg, #ffcb00 0%, #ff9b00 100%);
      border-radius: 1px 100px 100px 1px;
      line-height: 71px;
      font-size: 36px;
      font-weight: 600;
      color: #ffffff;
      text-align: center;
      margin-bottom: 36px;
    }
    .directionsItem {
      padding: 0 40px;
      margin-bottom: 48px;
      .directionTitle {
        height: 45px;
        font-size: 32px;
        font-weight: 700;
        color: #333333;
      }
      .directionWords {
        font-size: 28px;
        font-weight: 400;
        color: #727272;
        margin-top: 23px;
      }
    }
  }
}
