import { View, Image } from '@tarojs/components';
import { useRouter } from '@tarojs/taro';
import showBox from '@/assets/orderDetail/thirtyBox.png';
import './index.scss';

export default function Index() {
  const { params } = useRouter();
  const { orderId } = params;
  console.log(orderId);

  return (
    <View className='orderDetail'>
      <View className='atTopThe'>
        <View className='logo'></View>
        <View className='title'>小熊美术 - 用画画认识世界</View>
      </View>
      <View className='toShowGoods'>
        <View className='showBoxImg'>
          <Image className='img' src={showBox} mode='widthFix' />
        </View>
        <View className='showBoxAbout'>
          <View className='showBoxAboutTitle'>
            <View className='titleLe'>10日互动绘本故事体验包</View>
            <View className='titleRi'>x1</View>
          </View>
          <View className='showBoxAboutTips'>
            <View className='gifts'>【赠品】</View>
            <View className='tips'>创意绘画手工礼盒</View>
          </View>
          <View className='payPrice'>
            <View className='words'>实付:</View>
            <View className='price'>
              <View className='unit'>￥</View>
              <View className='num'>36</View>
            </View>
            <View className='tips'>（免运费）</View>
          </View>
        </View>
      </View>
      <View className='directionsForUse'>
        <View className='tipsImg'>使用说明</View>
        <View className='directionsItem'>
          <View className='directionTitle'>体验流程</View>
          <View className='directionWords'>
            通过手机/平板“应用商店”或者“AppStroe”免费下载安装小熊艺术APP。使用购买时的手机号登陆，点击【我的学习】，开始体验。
          </View>
        </View>
        <View className='directionsItem'>
          <View className='directionTitle'>物流查询</View>
          <View className='directionWords'>
            打开“小熊艺术”APP，使用购买时的手机号登陆后，在【我的】-【订单物流】中查询。
          </View>
        </View>
        <View className='directionsItem'>
          <View className='directionTitle'>可以反复观看吗？</View>
          <View className='directionWords'>购买后，支持随时随地反复观看</View>
        </View>
      </View>
    </View>
  );
}
