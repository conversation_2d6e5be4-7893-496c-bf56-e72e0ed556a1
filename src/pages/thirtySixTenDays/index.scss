@import '@/theme/groupbuy/common.scss';

.index {
  font-size: 0;
  position: relative;
  padding-bottom: calc(112rpx + env(safe-area-inset-bottom));
  .fixedTop {
    position: sticky;
  }
  .divider {
    width: 100%;
    height: 16rpx;
    background: #f7f7f7;
  }
  .commodity-price {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 147rpx;
    .unit-price {
      display: flex;
      align-items: center;
      width: 480rpx;
      height: 100%;
      box-sizing: border-box;
      padding: 0 32rpx;
      background: $red-to-orange;
      .price-symbol {
        color: $white;
        font-size: $font-40;
        margin-top: 40rpx;
      }
      .price {
        color: $white;
        font-size: $font-96;
      }
      .price-details {
        margin-top: 6rpx;
        margin-left: 20rpx;
        .price-detailed {
          margin-top: 4rpx;
          color: $white;
          font-size: $font-24;
        }
      }
    }
    .assemble-price {
      display: flex;
      align-items: flex-start;
      justify-content: center;
      flex-direction: column;
      width: calc(100% - 480rpx);
      height: 100%;
      background: $orange-to-yellow;
      position: relative;
      padding-left: 70rpx;
      &::before {
        content: '';
        display: block;
        position: absolute;
        top: 0;
        bottom: 0;
        left: -5%;
        width: 0;
        height: 0;
        margin: auto 0;
        border-top: 20rpx solid transparent;
        border-right: 20rpx solid #fd880d;
        border-bottom: 20rpx solid transparent;
      }
      .groupbuy-reduction {
        font-size: $font-36;
        color: $white;
      }
      .reduce-price {
        font-size: $font-36;
        color: #fcd850;
        // text-decoration: line-through;
      }
    }
  }
  .introduce {
    display: flex;
    justify-content: space-between;
    padding: 32rpx 24rpx;
    .product-introduction {
      width: calc(100% - 100rpx);
      font-size: $font-28;
      color: $dim-gray;
      text-align: justify;
      font-weight: bold;
    }
  }
  .promise-introduce {
    width: 100%;
    padding: 24rpx 32rpx;
    box-sizing: border-box;
    .row {
      display: flex;
      width: 100%;
      justify-content: space-between;
      padding: 10rpx 0;
      font-size: $font-26;
      .title {
        color: $light-grey;
      }
      .content {
        flex: 1;
        color: $dim-gray;
        padding-left: 24rpx;
        .promotion-row {
          margin-bottom: 20rpx;
        }
        .promotion-text {
          margin-left: 16rpx;
          font-size: $font-26;
          color: $dim-gray;
        }
      }
      .view {
        display: flex;
        align-items: center;
        color: $light-grey;
        .arrow {
          width: 12rpx;
          height: 22rpx;
          margin-left: 16rpx;
        }
      }
    }
  }
  .groupbuy-detail {
    background: $white;
    padding: 14rpx 32rpx;
    .title {
      font-size: $font-28;
      color: $dim-gray;
      font-weight: bold;
      margin-bottom: 26rpx;
    }
    .content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .swiper-head {
        width: 330rpx;
        height: 60rpx;
        .head {
          display: flex;
          align-items: center;
          .head-box {
            position: relative;
            width: 60rpx;
            height: 60rpx;
            border-radius: 30rpx;
            overflow: hidden;
            background: $light-grey;
            .head-img {
              max-width: 100%;
              max-height: 100%;
              position: absolute;
              left: 0;
              right: 0;
              bottom: 0;
              top: 0;
              margin: auto;
            }
          }
          .nickname {
            width: 254rpx;
            font-size: $font-28;
            color: $dim-gray;
            margin-left: 16rpx;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }
      .clustering-info {
        display: flex;
        .clustering-info-box {
          display: flex;
          flex-direction: column;
          .number {
            text-align: right;
            font-size: $font-24;
            color: $dim-gray;
            .highlight {
              color: $red;
            }
          }
          .countdown {
            color: $light-grey;
            font-size: $font-24;
            .at-countdown {
              width: 110rpx;
              text-align: right;
            }
            .at-countdown__time-box {
              min-width: 0;
              font-family: '黑体';
              color: $light-grey;
              font-size: $font-26;
            }
            .at-countdown__separator {
              font-size: $font-26;
              padding: 0;
            }
            .at-countdown__item,
            .at-countdown__time {
              color: $light-grey;
            }
          }
        }
        .groupbuy-btn {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 144rpx;
          height: 60rpx;
          background: $red-to-yellow;
          font-size: $font-28;
          color: $white;
          border-radius: 10rpx;
          margin-left: 20rpx;
          .arrow {
            width: 12rpx;
            height: 21rpx;
            margin-left: 6rpx;
          }
        }
      }
    }
  }
  .groupbuy-procedure {
    .title {
      padding: 16rpx 0 0 32rpx;
      color: #252525;
      font-size: $font-32;
      font-weight: bold;
    }
    .img {
      width: 100%;
    }
  }
  .qa {
    padding: 0 32rpx 20rpx 32rpx;
    .all {
      display: flex;
      justify-content: space-between;
      padding: 24rpx 0;
      .number {
        font-size: $font-26;
        color: $light-grey;
        font-weight: bold;
      }
      .view {
        display: flex;
        align-items: center;
        color: $light-grey;
        font-size: $font-26;
        .arrow {
          width: 12rpx;
          height: 22rpx;
          margin-left: 16rpx;
        }
      }
    }
    .problem-row {
      display: flex;
      justify-content: space-between;
      padding-bottom: 20rpx;
      .problem-icon {
        width: 35rpx;
        height: 35rpx;
      }
      .problem {
        flex: 1;
        font-size: $font-24;
        font-weight: bold;
        color: $dim-gray;
        margin-left: 24rpx;
      }
      .answer {
        font-size: $font-24;
        color: $light-grey;
      }
    }
  }
  .big-img {
    width: 100%;
  }
  .pay-img {
    width: 100%;
  }
  .suction-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 112rpx;
    background: $white;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    padding-bottom: env(safe-area-inset-bottom);
    .pay-fixed {
      position: absolute;
      width: 320px;
      right: 13px;
      top: 13px;
      height: 90px;
      animation: btnAnimate 2s ease-in-out infinite;
    }
    .synopsis {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 310rpx;
      height: 112rpx;
      .course {
        display: flex;
        justify-content: center;
        align-items: center;
        .number {
          color: #ff6c00;
          font-size: $font-50;
          text-align: end;
          font-family: DINAlternate-Bold, DINAlternate;
        }
        .section {
          color: $dim-gray;
          font-size: $font-34;
          margin-left: 5rpx;
        }
        .icon {
          font-size: $font-22;
          color: $white;
          font-weight: bold;
          padding: 5rpx 8rpx;
          margin-left: 18rpx;
          border-radius: 0rpx 10rpx 10rpx 10rpx;
          background: $orange-to-yellow;
        }
      }
      .copywriting {
        color: $silver;
        font-size: $font-24;
      }
    }
    .origin-price {
      width: 180rpx;
      height: 100%;
      background: $orange;
    }
    .groupbuy-price {
      flex: 1;
      height: 100%;
      background: $red;
    }
    .origin-price,
    .groupbuy-price {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      color: $white;
      .price {
        font-size: $font-36;
        font-weight: bold;
      }
      .text {
        font-size: $font-26;
      }
    }
  }
  .layout-header {
    background: $white;
    padding: 40rpx 0;
    .layout-header__title {
      text-align: center;
      font-size: $font-30;
      padding-left: 80rpx;
    }
  }
  .problem-layout {
    .layout {
      height: 70%;
    }
  }
  .order-layout {
    .layout {
      height: 500px;
      max-height: 500px;
      min-height: 500px;
      .layout-body {
        height: 480px;
        max-height: 480px;
        min-height: 480px;
      }
      .layout-body__content {
        max-height: 480px;
        min-height: 480px;
      }
    }
  }
  .order-layout-auto {
    .layout {
      height: 1020px;
      max-height: 1020px;
      .layout-body {
        height: 1020px;
        max-height: 1020px;
      }
      .layout-body__content {
        max-height: 1020px;
      }
    }
  }
  .modal-content {
    text-align: center;
    .modal-tel {
      color: blue;
    }
  }
}

.at-modal__footer .at-modal__action > button:last-child {
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  &::after {
    border: 0;
  }
  .button {
    font-size: 28px;
    color: #6190e8;
    background-color: transparent;
    &::after {
      border: 0;
    }
  }
}

@keyframes btnAnimate {
  0% {
    transform: scale(0.85);
  }

  25% {
    transform: scale(1.05);
  }

  50% {
    transform: scale(0.85);
  }

  75% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(0.85);
  }
}
