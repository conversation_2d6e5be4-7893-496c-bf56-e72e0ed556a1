import { Button } from '@tarojs/components';
import { getProgramUserSubject } from '@/api/groupbuy';
import { useDispatch, useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';

interface Paybtnprops {
  className?: string;
  authError?: (res: any) => void;
  authSuccess: (res: any) => void;
  btnName?: string;
  children?: any;
  subject?: string;
}

const Paybtn = ({
  className = 'button',
  authError,
  authSuccess,
  btnName,
  children,
  subject,
}: Paybtnprops) => {
  const dispatch = useDispatch();
  //openid
  const openId = useSelector((state: UserStateType) => state.openid);
  // 获取用户手机号
  const getPhoneNumber = res => {
    if (res.detail.errMsg === 'getPhoneNumber:fail user deny') {
      authError && authError(res);
    } else {
      const { encryptedData, iv } = res.detail;
      getProgramUserSubject({
        openId,
        encryptedData,
        iv,
        subject,
      }).then(phone => {
        phone.payload.uid &&
          dispatch({
            type: 'CHANGE_USERID',
            userid: phone.payload.uid,
          });
        if (phone.payload.token)
          Taro.setStorageSync('appToken', phone.payload.token);
        phone.payload.mobile &&
          dispatch({
            type: 'CHANGE_MOBILE',
            mobile: phone.payload.mobile,
          });
        authSuccess(phone);
      });
    }
  };

  return (
    <Button
      className={className}
      open-type='getPhoneNumber'
      onGetPhoneNumber={getPhoneNumber}
    >
      {btnName || children || '确认支付'}
    </Button>
  );
};

export default Paybtn;
