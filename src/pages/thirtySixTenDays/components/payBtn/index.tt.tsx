import Taro from '@tarojs/taro';
import { Button } from '@tarojs/components';
import { useDispatch, useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import { ttInitzjProgramApi } from '@/api/groupbuy';
import sensors from '@/utils/sensors_data';

interface Paybtnprops {
  className?: string;
  authError?: (res: any) => void;
  authSuccess: (res: any) => void;
  btnName?: string;
  children?: any;
}

const Paybtn = ({
  className = 'button',
  authError,
  authSuccess,
  btnName,
  children,
}: Paybtnprops) => {
  const sessionkey = useSelector((state: UserStateType) => state.sessionkey);
  const openid = useSelector((state: UserStateType) => state.openid);
  const dispatch = useDispatch();
  // 获取用户手机号
  const getPhoneNumber = res => {
    if (
      res.detail.errMsg === 'getPhoneNumber:fail user deny' ||
      res.detail.errMsg === 'getPhoneNumber:fail auth deny'
    ) {
      authError && authError(res);
    }
    if (res.detail.errMsg === 'getPhoneNumber:fail no permission') {
      // 未授权
      Taro.showToast({ title: '授权失败!请绑定手机号', icon: 'none' });
    } else {
      if (res.detail.errMsg === 'getPhoneNumber:ok') {
        const { encryptedData, iv } = res.detail;
        ttInitzjProgramApi({
          sessionKey: sessionkey,
          encryptedData,
          iv,
          appid: 'tt979ce12108a4d5f701',
          openid,
          appsubject: 'ART_APP',
        }).then(result => {
          if (result.code === 0) {
            const payload = result.payload;
            payload.uid &&
              dispatch({
                type: 'CHANGE_USERID',
                userid: payload.uid,
              });
            payload.mobile &&
              dispatch({
                type: 'CHANGE_MOBILE',
                mobile: payload.mobile,
              });
            sensors.login(payload.sensorsId);
            authSuccess(result);
          } else {
            Taro.showToast({
              title: res.errors || '授权失败!请绑定手机号',
              icon: 'none',
            });
          }
        });
      } else {
        Taro.showToast({
          title: '授权失败!请绑定手机号',
          icon: 'none',
        });
      }
    }
  };

  return (
    <Button
      className={className}
      open-type='getPhoneNumber'
      onGetPhoneNumber={getPhoneNumber}
    >
      {btnName || children || '确认支付'}
    </Button>
  );
};

export default Paybtn;
