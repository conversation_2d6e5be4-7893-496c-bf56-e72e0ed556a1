import Taro from '@tarojs/taro';
import { Button } from '@tarojs/components';
import { initAliprogram, decryptAliUserApi } from '@/api/groupbuy';
import { useDispatch } from 'react-redux';

interface Paybtnprops {
  className?: string;
  authError: (res: any) => void;
  authSuccess: (res: any) => void;
  btnName?: string;
  children?: any;
}
const Paybtn = ({
  className = 'button',
  authError,
  authSuccess,
  btnName,
  children,
}: Paybtnprops) => {
  // 获取用户手机号
  const dispatch = useDispatch();
  const onGetAuthorize = () => {
    my.getAuthCode({
      scopes: ['auth_base'],
      success: resCode => {
        initAliprogram(resCode.authCode).then(resInit => {
          my.getPhoneNumber({
            success: res => {
              const { sign, response } = JSON.parse(res.response);
              decryptAliUserApi({
                sign,
                response,
                subject: 'ART_APP',
                aliUserId: resInit.payload.aliUserId,
              }).then(result => {
                const payload = result.payload;
                payload.uid &&
                  dispatch({
                    type: 'CHANGE_USERID',
                    userid: payload.uid,
                  });
                payload.mobile &&
                  dispatch({
                    type: 'CHANGE_MOBILE',
                    mobile: payload.mobile,
                  });
                authSuccess(res);
                if (resInit.payload.token)
                  Taro.setStorageSync('appToken', resInit.payload.token);
                if (result.payload.token)
                  Taro.setStorageSync('appToken', result.payload.token);
              });
            },
            fail: res => {
              Taro.hideLoading();
              Taro.showModal({
                content: res.errorMessage,
                showCancel: false,
              });
            },
          });
        });
      },
    });
  };

  return (
    <Button
      className={className}
      openType='getAuthorize'
      scope='phoneNumber'
      onError={authError}
      onGetAuthorize={onGetAuthorize}
    >
      {btnName || children || '确认支付'}
    </Button>
  );
};

export default Paybtn;
