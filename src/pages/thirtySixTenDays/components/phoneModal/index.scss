@mixin layoutH {
  height: 400px;
  max-height: 400px;
  min-height: 400px;
}

.phone-layout {
  width: 100%;
  height: 100vh;
  position: fixed;
  left: 0;
  bottom: 0;
  right: 0;
  top: 0;
  background-color: rgba($color: #000000, $alpha: 0.3);
  transition: opacity 150ms ease-in;
  z-index: 101;
}
.receive {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 640px;
  background-color: #ffffff;
  border-radius: 32px 32px 2px 2px;
  padding: 72px 60px 0 60px;
  box-sizing: border-box;
  &.receive_m {
    height: 400px;
  }
  .receiveTitle {
    height: 50px;
    font-size: 36px;
    font-weight: 700;
    color: #222222;
  }
  .receivePhone {
    margin-top: 50px;
    display: flex;
    align-items: center;
    .icon {
      width: 42px;
      height: 52px;
      background-image: url('../../../../assets/thirtySix/index/telicon.png');
      background-position: 0 0;
      background-repeat: no-repeat;
      background-size: contain;
      margin-left: 36px;
      padding-right: 36px;
      border-right: 1px solid #e6e6e6;
    }
    .at-input {
      width: 514px;
      margin-left: 20px;
    }
    .telinput {
      width: 100%;
      height: 100%;
      font-size: 32px;
      color: #999999;
      padding-left: 20px;
    }
  }
  .receiveCode {
    margin-top: 40px;
    .telinput {
      width: 100%;
      height: 100%;
      font-size: 32px;
      color: #999999;
      padding-left: 20px;
    }
  }
  .receiveForm {
    .receivePhone,
    .receiveCode {
      height: 96px;
      background: #f7f7f7;
      border-radius: 16px;
      .at-input {
        height: 100%;
        background-color: transparent;
        padding: 0;
        margin-bottom: 0;
        input {
          height: 96px;
          line-height: 96px;
        }
      }
    }
    .receiveCode {
      position: relative;
      margin-top: 30px;
    }
    .receiveObtain {
      position: absolute;
      top: 26px;
      right: 28px;
      width: 160px;
      height: 44px;
      color: #ffbc00;
      font-weight: 400;
      font-size: 32px;
      line-height: 44px;
      text-align: center;
      z-index: 100;
      padding-left: 28px;
      border-left: 1px solid #e6e6e6;
    }
    .receiveBtn {
      display: flex;
      height: 96px;
      align-items: center;
      justify-content: space-around;
      margin-top: 40px;

      .cancel {
        width: 48%;
        height: 96px;
        line-height: 96px;
        font-size: 32px;
        text-align: center;
        border-radius: 16px;
        background-color: #e9e9e9;
        color: #4a4a4a;
      }
      .okbtn {
        width: 48%;
        height: 96px;
        line-height: 96px;
        font-size: 32px;
        text-align: center;
        border-radius: 16px;
        color: #ffffff;
        background: #ffbc00;
        font-weight: 400;
      }
    }
  }
}
