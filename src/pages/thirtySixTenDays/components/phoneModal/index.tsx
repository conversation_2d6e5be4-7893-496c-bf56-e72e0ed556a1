import { getLogin, getVerificationCode } from '@/api/groupbuy';
import { saveMobileApi } from '@/api/umsapi';
import { UserStateType } from '@/store/groupbuy/state';
import sensors from '@/utils/sensors_data';
import { Input, View } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
// import { AtInput } from 'taro-ui';

import './index.scss';

interface PhoneModalProps {
  authPhoneCancel: () => void;
  authPhoneSuccess: (res: any) => void;
  iVisibleModal: boolean;
}

export default function PhoneModal(props: PhoneModalProps) {
  const { authPhoneSuccess, authPhoneCancel, iVisibleModal = false } = props;

  const { params } = useRouter();
  const [newMobile, setNewMobile] = useState<string>('');
  const [newCode, setNewCode] = useState('');
  const dispatch = useDispatch();
  const openid = useSelector((state: UserStateType) => state.openid);
  const [codeSendStatus, setCodeSendStatus] = useState(true);
  const [codeText, setCodeText] = useState<string>('获取验证码');
  const [codeStatus, setCodeStatus] = useState(true);
  const [isShow, setIsShow] = useState(false);

  const reg_tel = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/;

  // 填写验证码
  const codeChange = val => {
    const value = String(val);
    setNewCode(value);
    // if (value.length == 4 && newMobile.length == 11) {
    //   login(value);
    // }
  };
  // 获取验证码
  const newSendCode = () => {
    sensors.track('xxms_testcoursedescription_code', {});
    if (!reg_tel.test(newMobile)) {
      Taro.showToast({ title: '请输入正确的手机号', icon: 'none' });
      return false;
    }
    if (newMobile == '') {
      Taro.showToast({ title: '请输入手机号', icon: 'none' });
      return false;
    }
    if (newMobile.length != 11) {
      Taro.showToast({ title: '手机号格式不正确', icon: 'none' });
      return false;
    }
    if (!codeStatus) {
      return false;
    }
    let timer_num = 60;
    const timeClock: any = setInterval(() => {
      timer_num--;
      setCodeText(`${timer_num}秒`);
      setCodeStatus(false);
      if (timer_num == 0) {
        clearInterval(timeClock);
        setCodeText('获取验证码');
        setCodeStatus(true);
      }
    }, 1000);
    // 发送验证码
    getVerificationCode(newMobile).then((res: any) => {
      res.code == 0
        ? Taro.showToast({ title: '发送成功', icon: 'none' })
        : Taro.showToast({ title: '发送失败', icon: 'none' });
    });
  };

  const login = () => {
    if (newMobile == '') {
      Taro.showToast({ title: '请输入手机号', icon: 'none' });
      return false;
    }
    if (newMobile.length != 11) {
      Taro.showToast({ title: '手机号格式不正确', icon: 'none' });
      return false;
    }
    if (!reg_tel.test(newMobile)) {
      Taro.showToast({ title: '请输入正确的手机号', icon: 'none' });
      return false;
    }
    if (newCode == '') {
      Taro.showToast({ title: '请输入验证码', icon: 'none' });
      return false;
    }
    if (!codeSendStatus) {
      return false;
    }
    setCodeSendStatus(false);
    const defChannel = Taro.getStorageSync('defChannel');
    const DATA = {
      mobile: newMobile,
      code: newCode,
      channel: params.channel || defChannel,
      sendId: params.sendId || '',
    };
    getLogin(DATA).then((res: any) => {
      setCodeSendStatus(true);
      if (res.code === 0) {
        sensors.login(res.payload.user.sensorsId);
        sensors.track('xxms_testcourse_loginsignupresult', {
          is_success: '是',
          is_first: res.payload.type === 'REGIST' ? '是' : '否',
        });
        dispatch({
          type: 'CHANGE_USERID',
          userid: res.payload.id,
        });
        dispatch({
          type: 'CHANGE_MOBILE',
          mobile: newMobile,
        });
        saveMobileApi({
          mobile: newMobile,
          appid: 'tt979ce12108a4d5f701',
          openid: openid,
        }).then(() => {
          setIsShow(false);
          authPhoneSuccess(res);
        });
      } else {
        sensors.track('xxms_testcourse_loginsignupresult', {
          is_success: '否',
          is_first: '',
        });
        Taro.showToast({ title: '请输入正确信息' || res.errors, icon: 'none' });
      }
    });
  };

  useEffect(() => {
    setIsShow(iVisibleModal);
  }, [iVisibleModal]);

  return (
    <>
      {isShow && (
        <View className='phone-layout'>
          <View className='receive'>
            <View className='receiveForm'>
              <View className='receiveTitle'>绑定手机号</View>
              <View className='receivePhone'>
                <View className='icon'></View>
                <Input
                  className='telinput'
                  type='number'
                  placeholder='请输入手机号'
                  value={newMobile}
                  cursorSpacing={10}
                  maxlength={11}
                  disabled={false}
                  onInput={(e: any) => {
                    const value = e.detail.value;
                    setNewMobile(value);
                  }}
                />
              </View>
              <View className='receiveCode'>
                {/* <AtInput
                  name='code'
                  type='number'
                  placeholder='请输入验证码'
                  maxlength={4}
                  value={newCode}
                  border={false}
                  cursorSpacing={10}
                  onChange={val => codeChange(val)}
                /> */}
                <Input
                  className='telinput'
                  type='number'
                  placeholder='请输入验证码'
                  value={newCode}
                  cursorSpacing={10}
                  maxlength={4}
                  onInput={(e: any) => {
                    const value = e.detail.value;
                    codeChange(value);
                  }}
                />
                <View className='receiveObtain' onClick={newSendCode}>
                  {codeText}
                </View>
              </View>
              <View className='receiveBtn'>
                <View
                  className='cancel'
                  onClick={() => {
                    setIsShow(false);
                    authPhoneCancel();
                  }}
                >
                  取消
                </View>
                <View className='okbtn' onClick={login}>
                  确定
                </View>
              </View>
            </View>
          </View>
        </View>
      )}
    </>
  );
}
