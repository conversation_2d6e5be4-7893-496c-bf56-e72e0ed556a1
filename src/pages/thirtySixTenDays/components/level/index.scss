@import '../../../../theme/groupbuy/common.scss';

.level {
  padding: 0 32px 20px 32px;
  .card {
    display: flex;
    align-items: center;
    padding: 32px 10px 32px 0;
    border: 1px solid #cccccc;
    border-radius: 10px;
    margin-bottom: 20px;
    .label {
      width: 80px;
      height: 80px;
      line-height: 80px;
      color: $white;
      font-size: $font-40;
      font-weight: bold;
      border-radius: 0 100px 100px 0;
      padding-left: 11px;
      margin-right: 30px;
      box-sizing: border-box;
    }
    .info {
      flex: 1;
      .title {
        display: flex;
        align-items: center;
        height: 50px;
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        .fit {
          font-size: $font-32;
          color: #222222;
          font-weight: bold;
        }
        .range {
          font-size: $font-24;
          color: #222222;
          font-weight: bold;
        }
      }
      .desc {
        font-size: $font-28;
        color: $silver;
      }
    }
  }
  .active {
    border: 1px solid #ff9c00;
    background: #fffbf4;
  }
  .level-tip {
    font-size: 26px;
    color: #888888;
    text-align: center;
    margin-bottom: 30px;
  }
}
