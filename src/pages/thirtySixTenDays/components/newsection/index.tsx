import { View, Image } from '@tarojs/components';
import banner from '@/assets/thirtySix/newsection/banner.png';
import bannerPay from '@/assets/thirtySix/newsection/price01.png';
import content1 from '@/assets/thirtySix/newsection/c1.png';
import content2 from '@/assets/thirtySix/newsection/c2.png';

import './index.scss';

interface sectionProps {
  headImg: string;
}

const Section = ({ headImg }: sectionProps) => {
  const imgList = [content1, content2];
  return (
    <>
      <View className='banner'>
        <Image mode='widthFix' src={banner} className='banner'></Image>
      </View>
      <View>
        <View>
          <Image mode='widthFix' src={bannerPay} className='payText'></Image>
        </View>
        <View className='bannerContent'>
          <View className='bannerList'>
            {imgList.map((e, i) => {
              return (
                <View className='bannerDiv' key={i}>
                  <Image mode='widthFix' src={e} className='bannerImg'></Image>
                </View>
              );
            })}
          </View>
        </View>
      </View>
    </>
  );
};

export default Section;
