import Taro from '@tarojs/taro';
import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { View, Text, Image, Button } from '@tarojs/components';
import { UserStateType } from '@/store/groupbuy/state';
import { getSupManagements } from '@/api/groupbuy';
import { useAuthPay } from '@/hooks/authPayhook/useAuthPay';
import Paybtn from '@/pages/thirtySixTenDays/components/payBtn';

import './index.scss';

export default function Index(props) {
  const {
    watchCloseOrder,
    payPageData,
    orderType,
    giveaway,
    pType,
    packagesId,
    topicId,
    subject,
  } = props;

  //userid
  const userId = useSelector((state: UserStateType) => state.userid);

  const { authSuccess, setPeriod, payConfirm, redeemFlag } = useAuthPay({
    topicId,
    packagesId,
    subject,
    pType,
    payPageData,
  });

  const authError = () => {
    Taro.hideLoading();
  };
  const payConfirmHandle = () => {
    payConfirm(payPageData.sup);
  };

  const authSuccessHandle = res => {
    res.sup = payPageData.sup;
    authSuccess(res);
  };

  useEffect(() => {
    payPageData &&
      getSupManagements({
        type: 'TESTCOURSE',
        sup: payPageData.sup,
        subject,
      }).then(res => {
        const result = res.payload && res.payload;
        if (result) {
          setPeriod(result.period);
        } else {
          Taro.showToast({
            title: res.errors,
            icon: 'none',
          });
        }
      });
  }, [payPageData, subject, setPeriod]);

  return (
    <>
      <View className='order'>
        <View className='title'>
          <View
            className='close'
            onClick={() => watchCloseOrder(false, null, redeemFlag)}
          ></View>
        </View>
        <View className='subTitle'>
          <View className='level'>10日互动绘本故事体验包 </View>
        </View>
        <View className='packages'>
          <View className='row'>
            <View className='label'>【优惠】</View>
            <View className='desc'>限购一次，新人专享{orderType}元</View>
          </View>
          <View className='row'>
            <View className='label'>【赠品】</View>
            <View className='desc'>
              创意绘画手工礼盒
              <Text className='label'>（收货信息将在付款后填写）</Text>
            </View>
          </View>
          <View className='row'>
            <View className='label'>【提醒】</View>
            <View className='desc'>不同级别的礼盒略有差异，请您以实物为准</View>
          </View>
        </View>
        {/* <View className='gift-box-row'> */}
        {/* <View className={pType + ' gift-box'}>
            <Image src={giveaway.img} mode='widthFix' className='img' />
          </View>
          <View className={pType + ' label'}>
            {giveaway.detail.map((item, index) => {
              return (
                <View className={pType + ' label-item'} key={`label-${index}`}>
                  {item}
                </View>
              );
            })}
          </View> */}
        {/* </View> */}
        <View className='pay'>
          <View className='price-box'>
            <View className='symbol'>¥</View>
            <View className='price'>{orderType}</View>
          </View>
          {userId ? (
            <Button className='button' onClick={payConfirmHandle}>
              确认支付
            </Button>
          ) : (
            <Paybtn
              className='button'
              authError={authError}
              authSuccess={authSuccessHandle}
            />
          )}
        </View>
      </View>
    </>
  );
}
