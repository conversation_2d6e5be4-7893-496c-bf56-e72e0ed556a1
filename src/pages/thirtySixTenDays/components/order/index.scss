@import '@/theme/groupbuy/common.scss';
.order {
  // height: 850px;
  padding: 48px 14px 0 14px;
  box-sizing: border-box;
  .title {
    display: flex;
    justify-content: center;
    margin-bottom: 48px;
    padding: 0 120px;
    position: relative;
    .close {
      width: 100px;
      height: 100px;
      position: absolute;
      top: -32px;
      right: -32px;
      &::before,
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        display: inline-block;
        width: 36px;
        height: 2px;
        border-radius: 1px;
        background: #ccc;
      }
      &::before {
        transform: translate3d(-50%, -50%, 0) rotate(45deg);
      }
      &::after {
        transform: translate3d(-50%, -50%, 0) rotate(-45deg);
      }
    }
    .quota,
    .time {
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      .highlight {
        color: $red;
        font-size: $font-44;
        font-weight: 600;
      }
      .text {
        color: $silver;
        font-size: $font-28;
      }
    }
  }
  .subTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 18px;
    border-bottom: 1px solid #e6e6e6;
    margin-bottom: 24px;
    .level {
      color: $dim-gray;
      font-size: $font-32;
    }
    .start-time {
      color: $grey;
      font-size: $font-24;
    }
  }
  .packages {
    margin: 24px 0 32px 0;
    display: flex;
    flex-direction: column;
    .row {
      width: 100%;
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      .label {
        align-self: flex-start;
        color: $orange;
        font-size: $font-24;
      }
      .desc {
        flex: 1;
        color: $silver;
        font-size: $font-24;
        .text {
          margin-bottom: 12px;
        }
      }
    }
  }
  .gift-box-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 20px;
    border: 3px dashed $light-grey;
    padding: 20px 0 20px 15px;
    margin-bottom: 40px;
    .gift-box {
      width: 202px;
      box-sizing: border-box;
      &.art {
        margin-left: 22px;
      }
      .img {
        max-width: 100%;
        max-height: 100%;
      }
    }
    .label {
      width: 420px;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      &.calligraphy {
        width: 465px;
      }
      .label-item {
        width: 48%;
        height: 40px;
        font-size: $font-26;
        color: $light-grey;
        position: relative;
        overflow: hidden;
        &.calligraphy {
          width: 54%;
        }
        &:nth-child(2n-1).calligraphy {
          width: 45%;
        }
        &::before {
          display: inline-block;
          content: '·';
          font-size: $font-38;
          vertical-align: middle;
          margin-right: 8px;
        }
      }
    }
  }
  .pay {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 28px;
    .price-box {
      display: flex;
      align-items: baseline;
      color: #ff7000;
      font-weight: bold;
      .symbol {
        font-size: $font-40;
        margin-right: 15px;
      }
      .price {
        font-size: $font-68;
      }
    }
    .button {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 216px;
      height: 80px;
      border-radius: 40px;
      background: $orange-to-yellow;
      color: $white;
      font-size: $font-32;
      font-weight: bold;
      margin-right: 0;
      &::after {
        border: 0;
      }
    }
  }
  .paySection {
    .pay-title {
      width: 100%;
      height: 45px;
      line-height: 45px;
      font-size: 32px;
      font-weight: 500;
      color: #1a1a1a;
      text-align: left;
      padding-bottom: 18px;
      border-bottom: 1px solid #e6e6e6;
      margin-bottom: 24px;
    }
    .type-section {
      display: flex;
      align-items: center;
      justify-content: space-around;
      .pay-box {
        width: 330px;
        height: 88px;
        border-radius: 14px;
        border: 2px solid #e6e6e6;
        display: flex;
        align-items: center;
        justify-items: center;
        position: relative;
        box-sizing: border-box;
        &.activity {
          border: 2px solid #ff9b00;
          &::after {
            content: '';
            width: 32px;
            height: 32px;
            background-image: url('../../../../assets/thirtySix/index/chose-icon.png');
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
            position: absolute;
            right: 0px;
            top: 0px;
          }
        }
      }
      .pay-wrod {
        flex: 1;
        margin-left: 24px;
      }
    }
    .pay-btn-tt {
      width: 686px;
      height: 96px;
      line-height: 96px;
      text-align: center;
      background: linear-gradient(270deg, #ffa300 0%, #ff6a00 100%);
      border-radius: 48px;
      margin: 60px auto 20px;
      font-size: 42px;
      font-weight: 600;
      color: #ffffff;
    }
    .payb {
      padding: 0;
      background-color: transparent;
      &::after {
        border: 0;
      }
    }
  }
}
