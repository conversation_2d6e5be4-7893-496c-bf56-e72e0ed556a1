import Taro, {
  useDidHide,
  useDidShow,
  useRouter,
  useShareAppMessage,
} from '@tarojs/taro';
import { useState, useEffect } from 'react';
import {
  AtFloatLayout,
  AtModal,
  AtModalHeader,
  AtModalContent,
  AtModalAction,
} from 'taro-ui';
import { View, Image, Button } from '@tarojs/components';
import { useDispatch, useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
// import { getOrderDetailApi } from '@/api/groupbuy';
import { getOrderId } from '@/common/order';

import bottomPay from '@/assets/thirtySix/newsection/bottombg.png';
import buy from '@/assets/thirtySix/newsection/bottomBtn.png';
import selIcon from '@/assets/thirtySix/index/material.png';
import redeemImg from '@/assets/thirtySix/index/fedeemalipay.png';

// @ts-ignore
import CommonTop from '@/components/commonTop';

import Paybtn from '@/pages/thirtySixTenDays/components/payBtn';

import sensors from '@/utils/sensors_data';

import { useLogin } from '@/hooks/loginhook/useLogin';
import LayoutOrder from './components/order';
import RetainModal from './components/retainModal';
import Section from './components/newsection';
import PhoneModal from './components/phoneModal';

import './index.scss';

export default () => {
  const { params } = useRouter();
  const { headImg } = params;
  const defChannel = {
    weapp: {
      development: '',
      dev: '',
      test: '',
      gray: '7732',
      online: '7732',
    },
    alipay: {
      development: '',
      dev: '',
      test: '',
      gray: '7732',
      online: '7732',
    },
    tt: {
      development: '7765',
      dev: '7765',
      test: '7765',
      gray: '8580',
      online: '8580',
    },
  };

  Taro.setStorageSync(
    'defChannel',
    defChannel[process.env.TARO_ENV][process.env.NODE_ENV],
  );
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  // const orderid = useSelector((state: UserStateType) => state.orderId);
  //redux
  const dispatch = useDispatch();

  // 显示隐藏订单浮窗
  const [isShowOrder, setIsShowOrder] = useState<boolean>(false);
  // 字节小程序显示modal
  const [isShowModal, setIsShowModal] = useState<boolean>(false);
  // 授权手机号
  const [iPhoneModal, setIPhoneModal] = useState(false);

  // 支付页面的数据
  const [payPageData, setPayPageData] = useState<object | null>({
    bgcolor: '#FF9C00',
    label: 'DEFAULT',
    sup: 'DEFAULT',
    fit: `刚刚会拿画笔、初步认识颜色`,
    range: '学习重点：点线涂鸦 | 兴趣探究 | 趣味手工',
    courseday: '0',
    period: 0,
  });

  const [showRedeemModal, setShowRedeemModal] = useState<boolean>(false);
  const [needShowRedeem, setNeedShowRedeem] = useState<boolean>(true);

  const { iLoading } = useLogin({ subject: 'ART_APP', isIntroduce: false });
  // 挽留弹窗
  const retainModalProps = {
    ishow: showRedeemModal,
    btnName: '继续支付',
    closeModal: type => {
      if (type === 'btn') {
        sensors.track(
          'xxms_testcourse_registrationpaypopup_paybuttonclick',
          {},
        );
      }
      setShowRedeemModal(false);
    },
    redeemImg: redeemImg,
  };

  // 底部购买按钮
  const handlePayClick = () => {
    if (iLoading && process.env.TARO_ENV === 'tt') {
      return;
    }
    sensors.track('xxms_testcourse_home_buybuttonclick', {});
    if (process.env.TARO_ENV === 'tt') {
      if (userId) {
        setIsShowOrder(true);
      } else {
        setIsShowModal(true);
      }
    } else {
      setIsShowOrder(true);
    }
    // if (orderid) {
    //   getOrderDetailApi(orderid).then(res => {
    //     if (
    //       res.code === 0 &&
    //       res.payload &&
    //       res.payload.order &&
    //       res.payload.order.status === 'COMPLETED'
    //     ) {
    //       Taro.showToast({
    //         title: '您已购买体验课，不支持再次购买',
    //         icon: 'none',
    //       });
    //     } else {
    //       if (process.env.TARO_ENV === 'tt') {
    //         if (userId) {
    //           setIsShowOrder(true);
    //         } else {
    //           setIsShowModal(true);
    //         }
    //       } else {
    //         setIsShowOrder(true);
    //       }
    //     }
    //   });
    // } else {
    //   if (process.env.TARO_ENV === 'tt') {
    //     if (userId) {
    //       setIsShowOrder(true);
    //     } else {
    //       setIsShowModal(true);
    //     }
    //   } else {
    //     setIsShowOrder(true);
    //   }
    // }
  };

  // 监听订单浮窗的打开和关闭
  const watchShowOrder = (state, pageData, redeemFlag = true) => {
    if (!state && needShowRedeem && redeemFlag) {
      setShowRedeemModal(true);
      setNeedShowRedeem(false);
      return;
    }
    setNeedShowRedeem(true);
    setIsShowOrder(state);
    pageData && setPayPageData(pageData);
  };

  useEffect(() => {
    //区分类型
    dispatch({
      type: 'PAY_TYPE',
      payType: '36',
    });
  }, [dispatch]);

  useEffect(() => {
    userId && getOrderId(userId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId]);

  useDidShow(() => {
    sensors.track('xxms_testcourse_homepage_view', {
      page_source: '36元字节小程序',
      channel_id:
        params.channel ||
        defChannel[process.env.TARO_ENV][process.env.NODE_ENV],
    });
    // userId && getOrderId(userId);
  });

  /* 套餐弹窗结束 */
  /* 订单弹窗 */
  // 随材赠品展示
  const [giveaway] = useState({
    img: selIcon,
    detail: [
      '小熊模切',
      '超轻粘土',
      '小熊作品纸',
      '黑色勾线笔',
      '小熊马克笔',
      '重彩油画棒',
      '手指画颜料',
      '其他材料若干',
    ],
    notes: '该图仅为展示，具体以实物为主，每期根据课程不同收到的随材会有所差异',
  });

  useShareAppMessage(() => {
    return {
      title: '小熊美术',
      path: `/pages/thirtySixTenDays/index${
        params.channelId ? `?channelId=${params.channelId}` : ''
      }`,
      success() {
        console.log('分享成功');
      },
      fail() {
        console.log('分享失败');
      },
    };
  });

  return (
    <View className='index container w100 relative'>
      {/* 头部导航栏 */}
      <CommonTop currentName='小熊美术' isIntroduce={false} />
      {/* 图片区 */}
      <Section headImg={headImg || '1'} />
      {/* 购买按钮 */}
      <View className='suction-bottom' onClick={handlePayClick}>
        <Image mode='widthFix' src={bottomPay} className='pay-img' />
        <Image mode='widthFix' src={buy} className='pay-fixed' />
      </View>

      <AtFloatLayout
        className='order-layout'
        isOpened={isShowOrder}
        onClose={() => {
          watchShowOrder(false, null);
        }}
      >
        {payPageData && (
          <LayoutOrder
            isShowOrder={isShowOrder}
            watchCloseOrder={watchShowOrder}
            payPageData={payPageData}
            orderType='36'
            subject='ART_APP'
            packagesId={62}
            classNum={10}
            topicId={3}
            pType='art'
            isIntroduce={false}
            pName='美术'
            giveaway={giveaway}
          />
        )}
      </AtFloatLayout>

      <RetainModal {...retainModalProps} />

      {/* 字节手机号未审核前 处理 */}
      <AtModal
        isOpened={isShowModal}
        onClose={() => {
          setIsShowModal(false);
          setIPhoneModal(false);
          setIsShowOrder(false);
        }}
      >
        <AtModalHeader>授权手机号</AtModalHeader>
        <AtModalContent>
          <View className='modal-content'>
            <View>购买需要授权手机号或绑定手机号</View>
            <View>请授权或绑定</View>
            <View
              className='modal-tel'
              onClick={() => {
                setIsShowModal(false);
                setIPhoneModal(true);
              }}
            >
              绑定手机号 &gt;&gt;
            </View>
          </View>
        </AtModalContent>
        <AtModalAction>
          <Button
            onClick={() => {
              setIsShowModal(false);
              setIPhoneModal(false);
              setIsShowOrder(false);
            }}
          >
            取消
          </Button>
          <Button
            className='authbutton'
            onClick={() => {
              setIsShowModal(false);
            }}
          >
            <Paybtn
              className='button'
              authError={() => {}}
              authSuccess={() => {
                setIsShowOrder(true);
              }}
              btnName='去授权'
            />
          </Button>
        </AtModalAction>
      </AtModal>

      {/* 字节手机号未审核前 填写手机号 */}
      <PhoneModal
        iVisibleModal={iPhoneModal}
        authPhoneCancel={() => {
          setIPhoneModal(false);
          setIsShowModal(false);
        }}
        authPhoneSuccess={() => {
          setIPhoneModal(false);
          setIsShowOrder(true);
        }}
      />
    </View>
  );
};
