.content {
  width: 100%;
  height: 100vh;
  background: #fff;
  position: relative;
  .bg {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }
  video {
    width: 100%;
    height: calc(100% - 152px);
  }
  .cover {
    width: 100%;
    height: calc(100% - 152px);
    position: absolute;
    left: 0;
    top: 0;
    z-index: 10;
    .play {
      width: 120px;
      height: 120px;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      z-index: 15;
    }
    .img {
      width: 100%;
      height: 100%;
    }
  }
}
.footer {
  width: 100%;
  height: 88px;
  padding: 24px 0 40px;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  position: fixed;
  bottom: 0;
  left: 0;
  background: #fff;
  .share {
    width: 88px;
    height: 88px;
    padding: 0;
    margin: 0;
    border-radius: 50%;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .button {
    width: 574px;
    height: 88px;
    background: linear-gradient(180deg, #ffac4e 0%, #ff4519 100%);
    border-radius: 48px;
    border: 1px solid #fffcc9;
    font-size: 32px;
    font-weight: 500;
    text-align: center;
    color: #ffffff;
    line-height: 88px;
  }
  .only-share {
    width: 440px;
  }
}
