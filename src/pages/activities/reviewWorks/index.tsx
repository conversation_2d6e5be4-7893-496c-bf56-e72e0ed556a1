import { useEffect, useState, useRef } from 'react';
import { useSelector } from 'react-redux';
import { View, Video, Image, Button } from '@tarojs/components';
// import VideoBorderPng from '@/assets/activities/reviewWorks/video-border.png';
import SharePng from '@/assets/activities/reviewWorks/share.png';
// import PlayPng from '@/assets/activities/reviewWorks/play.png';
import sensors from '@/utils/sensors_data';
import WxLogin from '@/components/wxlogin';
import Taro, { useRouter, useShareAppMessage } from '@tarojs/taro';
import { selVideo, getBuyOrderByUserId } from '@/api/groupbuy';
import shareImg from '@/assets/activities/reviewWorks/share-cover.png';
import { decodeUrl } from '@/utils';
import './index.scss';

const ReviewWorks = () => {
  const router: any = useRouter();
  const [info, setInfo] = useState({
    iceTaskVideo: '',
    studentId: '',
    coverImgSrc: '',
  });
  // const [played, setPlayed] = useState(false);
  const videoRef = useRef<any>();
  const userId = useSelector((state: any) => state.userid);
  const [canBuy, setCanBuy] = useState(-1);

  // 生成小程序码 参数会转码
  const {
    params: { scene },
  } = router;
  // c渠道 s视频任务id
  const query: any = decodeUrl(decodeURIComponent(scene));
  const { s, c } = query;

  useEffect(() => {
    // 是否购买过美术体验课
    if (userId) {
      getBuyOrderByUserId({
        userId: userId,
        orderRegType: 'EXPERIENCE',
        subjects: 'ART_APP',
        includeRefund: true,
      }).then(res => {
        if (res.code === 0) {
          setCanBuy(res.payload.length);
        }
      });
      if (s) {
        selVideo({ syncTaskId: s }).then(res => {
          if (res.code === 0) {
            const { taskUrls } = res.payload;
            const imgList = Object.values(
              JSON.parse(taskUrls),
            ).filter((v: any) => v.startsWith('https://'));
            setInfo({
              ...res.payload,
              coverImgSrc: imgList.pop() || '',
            });
            sensors.track('xxys_2023growthReview_playPage_view', {
              refer_id: info.studentId,
            });
          }
        });
      }
    }
  }, [userId]);

  useShareAppMessage(() => {
    sensors.track('xxys_2023growthReview_playPage_view', {
      button_name: '分享',
      refer_id: info.studentId,
    });

    return {
      title: '快来看我在小熊美术2023成长回顾',
      path: `/pages/activities/reviewWorks/index?scene=${scene}`,
      imageUrl: shareImg,
    };
  });

  const clickBuy = () => {
    if (canBuy === -1) return;
    sensors.track('xxys_2023growthReview_playPage_click', {
      button_name: '购买',
      refer_id: info.studentId,
    });
    if (canBuy > 0) {
      Taro.showToast({
        title: '您已购买过该课程',
        icon: 'none',
        duration: 2000,
      });
      return;
    }
    Taro.navigateTo({
      url: `/pages/normalGroup/art/index?sendId=${info.studentId}&channelId=${c}`,
    });
  };

  // const clickPlay = () => {
  //   console.log('【videoRef】', videoRef.current);
  //   setPlayed(true);
  //   videoRef.current.play();
  // };

  return (
    <View className='wrap'>
      <WxLogin isIntroduce={false} isFollow={false} />
      <View className='content'>
        {/* <Image className='bg' src={VideoBorderPng} /> */}
        <Video
          ref={videoRef}
          src={info.iceTaskVideo}
          autoplay
          loop={false}
          // poster={info.coverImgSrc}
        />
        {/* {
          !played ?  <View className='cover'>
          <Image className='play' src={PlayPng} onClick={clickPlay} />
          <Image className='img' src={info.coverImgSrc} />
        </View> : null
        } */}
      </View>
      <View className='footer'>
        {canBuy === 0 ? (
          <>
            <Button openType='share' className='share'>
              <Image src={SharePng} />
            </Button>
            <View className='button' onClick={clickBuy}>
              购买29元体验课 送画材大礼包
            </View>
          </>
        ) : (
          <Button openType='share' className='button only-share'>
            分&nbsp;&nbsp;&nbsp;享
          </Button>
        )}
      </View>
    </View>
  );
};

export default ReviewWorks;
