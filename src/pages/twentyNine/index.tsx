import Taro, {
  requirePlugin,
  useRouter,
  useShareAppMessage,
} from '@tarojs/taro';
import { useEffect, useState } from 'react';
import { Button, Image, View } from '@tarojs/components';
import { useDispatch, useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import { getOrderId } from '@/common/order';

// import bottomPay from '@/assets/groupbuy/twentyNine/purchase-bg.png';
import buy from '@/assets/groupbuy/twentyNine/purchase-bg-v2.0.png';

// @ts-ignore
import CommonTop from '@/components/commonTop';
import { useLogin } from '@/hooks/loginhook/useLogin';
import { AtFloatLayout, AtModal, AtModalContent } from 'taro-ui';
import LayoutOrderV1 from '@/components/alipay/AlipayOrderV1';
import modalCloseImg from '@/assets/groupbuy/thirtySix/close.png';
import redeemImg from '@/assets/groupbuy/thirtySix/redeem-img2.png';
import selIcon from '@/assets/groupbuy/thirtySix/sel_icon.png';

import { decryptAliUserApi } from '@/api/groupbuy';
import sensors from '@/utils/sensors_data';
import Section from './components/section';
//@ts-ignore
import Paybtn from './components/payBtn/index';
import './index.scss';

export default () => {
  const { params } = useRouter();
  const defChannelList = {
    alipay: {
      development: '4243',
      dev: '4243',
      test: '7044',
      gray: '7732',
      online: '7732',
    },
  };
  const def_channelId =
    params.channelId ||
    defChannelList[process.env.TARO_ENV][process.env.NODE_ENV];

  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  const aliUserId = useSelector((state: UserStateType) => state.aliUserId);

  //redux
  const dispatch = useDispatch();
  const [urlType] = useState('twentyNine');
  const { iLoading } = useLogin({ subject: 'ART_APP' });

  // 显示隐藏订单浮窗
  const [isShowOrder, setIsShowOrder] = useState<boolean>(false);
  const canReport = params.bizScenario == 'msyq';
  // 支付页面的数据
  const [payPageData, setPayPageData] = useState<object | null>({
    sup: 'default',
  });
  const [showRedeemModal, setShowRedeemModal] = useState<boolean>(false);
  const [needShowRedeem, setNeedShowRedeem] = useState<boolean>(true);
  const plugin = requirePlugin('tradePay');
  const [discountResults, setDiscountResults] = useState<object | null>(null);
  const [activityConsultId, setActivityConsultId] = useState<string>('');

  // 随材赠品展示
  const [giveaway] = useState({
    img: selIcon,
    detail: [
      '小熊模切',
      '超轻粘土',
      '小熊作品纸',
      '黑色勾线笔',
      '小熊马克笔',
      '重彩油画棒',
      '手指画颜料',
      '其他材料若干',
    ],
    notes: '该图仅为展示，具体以实物为主，每期根据课程不同收到的随材会有所差异',
  });

  useEffect(() => {
    //区分类型
    dispatch({
      type: 'PAY_TYPE',
      payType: '36',
    });
  }, [dispatch]);

  useEffect(() => {
    userId &&
      !iLoading &&
      getOrderId(userId, 'ART_APP', true)
        .then((res) => {
          if (res) {
            // @ts-ignore
            const _payload = res.payload[0];
            if (_payload && _payload.isRefund !== 'REFUNDEND') {
              Taro.redirectTo({
                url: `/pages/thirtySix/addAddress/index?vType=${
                  _payload.sup == 'DEFAULT' ? '1' : '3'
                }&sup=${
                  _payload.sup == 'DEFAULT' ? '' : _payload.sup
                }&orderId=${_payload.id}&packagesId=${
                  _payload.packagesId
                }&channelId=${def_channelId}&urlType=${urlType}`,
              });
            }
          }
        })
        .catch((err) => {
          console.log('asd', err);
        });
  }, [userId, iLoading]);

  useEffect(() => {
    sensors.track('ai_marketing_AlipayminiAPP_buypagebrowse', {
      channel_id: params.channelId,
      urlType: urlType,
    });
    getOrderPageDiscountInfo();
  }, []);

  useEffect(() => {
    if (discountResults?.success) {
      setActivityConsultId(
        discountResults?.orderDiscountDetailInfo?.activityConsultId || '',
      );
    }
  }, [discountResults]);

  const getOrderPageDiscountInfo = async () => {
    /* const ali_params = {
      // 仅为代码说明，具体参数参考入参表格！
      itemDetailInfo: {
        outItemId: '2023103022000404872134',
        outSkuId: '17141',
        price: '36',
      },
    };
    const discountResult = await plugin.getOrderPageDiscountInfo(ali_params) || {}; */
    const ali_params = {
      orderDetailInfo: {
        amount: '29', // 订单总金额
        itemDetailInfoList: [
          {
            outItemId: '2023103022000404872134',
            outSkuId: '17141',
            price: '29', // 商品价格
            quantity: 100000, // 数量
          },
        ],
      },
    };
    const discountResult = await plugin.getOrderPageDiscountInfo(ali_params);
    const { success } = discountResult;
    if (!success) {
      console.error('商详前置优惠接口失败', discountResult);
    } else {
      console.log('商详前置优惠接口成功', discountResult);
      setDiscountResults(discountResult);
    }
  };

  useShareAppMessage(() => {
    return {
      title: '小熊美术',
      path: `/pages/twentyNine/index${
        params.channelId ? `?channelId=${params.channelId}` : ''
      }`,
      success() {
        console.log('分享成功');
      },
      fail() {
        console.log('分享失败');
      },
    };
  });

  // 监听订单浮窗的打开和关闭
  const watchShowOrder = (state, pageData) => {
    if (!state && needShowRedeem) {
      setShowRedeemModal(true);
      setNeedShowRedeem(false);
      return;
    }
    setNeedShowRedeem(true);
    setIsShowOrder(state);
    pageData && setPayPageData(pageData);
  };

  // 关闭挽留弹窗
  const hideRedeemHandle = () => {
    setShowRedeemModal(false);
  };

  const authSuccess = () => {
    my.getPhoneNumber({
      success: (res) => {
        const { sign, response } = JSON.parse(res.response);
        decryptAliUserApi({
          sign,
          response,
          subject: 'ART_APP',
          aliUserId: aliUserId,
        }).then((result) => {
          if (result.code == 0) {
            const payload = result.payload;
            payload.uid &&
              dispatch({
                type: 'CHANGE_USERID',
                userid: payload.uid,
              });
            payload.mobile &&
              dispatch({
                type: 'CHANGE_MOBILE',
                mobile: payload.mobile,
              });
            if (result.payload.token)
              Taro.setStorageSync('appToken', result.payload.token);
            sensors.track('xxms_testcourse_loginsignupresult', {
              is_success: '是',
            });
            bottonBtnClick();
          } else {
            sensors.track('xxms_testcourse_loginsignupresult', {
              is_success: '否',
            });
          }
        });
      },
      fail: (res) => {
        Taro.showModal({
          content: res.errorMessage,
          showCancel: false,
        });
      },
    });
  };

  const bottonBtnClick = () => {
    // 小程序购买页立即购买点击
    sensors.track('ai_marketing_AlipayminiAPP_buypageclick', {
      channel_id: params.channelId,
      urlType: urlType,
    });
    // 小程序选择级别浏览
    sensors.track('ai_marketing_AlipayminiAPP_selectionlevelbrowse', {
      channel_id: params.channelId,
      urlType: urlType,
    });
    !iLoading && setIsShowOrder(true);
  };
  return (
    <View className='index container w100 relative'>
      {/* 头部导航栏 */}
      <CommonTop
        currentName='小熊美术'
        isIntroduce={false}
        channelId={def_channelId}
      />
      {/* 图片区 */}
      <Section />
      <View className='suction-bottom'>
        <View className='fix-bg'></View>
        <View>
          <order-discount-card
            className='pay-fixed'
            discountResponse={discountResults}
          />
          {/* <detail-discount-card discountResponse={discountResults} /> */}
        </View>
        {/* <Image mode='widthFix' src={bottomPay} className='pay-img' /> */}
        {userId ? (
          <Button className='pay-btn' onClick={bottonBtnClick}>
            <Image mode='widthFix' src={buy} className='pay-fixed' />
          </Button>
        ) : (
          <Paybtn className='pay-btn' authSuccess={authSuccess}>
            <Image mode='widthFix' src={buy} className='pay-fixed' />
          </Paybtn>
        )}
      </View>
      {/* 弹窗 */}
      <AtFloatLayout
        className='order-layout'
        isOpened={isShowOrder}
        onClose={() => {
          watchShowOrder(false, null);
        }}
      >
        {payPageData && (
          <LayoutOrderV1
            isShowOrder={isShowOrder}
            watchCloseOrder={watchShowOrder}
            payPageData={payPageData}
            orderType='29'
            subject='ART_APP'
            packagesId={610}
            classNum={10}
            topicId={3}
            pType='art'
            canReport={canReport}
            isIntroduce={false}
            pName='10日趣味美术体验包'
            giveaway={giveaway}
            model={4}
            activityConsultId={activityConsultId}
          />
        )}
      </AtFloatLayout>
      <AtModal
        className='redeem-modal'
        isOpened={showRedeemModal}
        closeOnClickOverlay={false}
      >
        <Image
          className='close-img'
          src={modalCloseImg}
          onClick={hideRedeemHandle}
        ></Image>
        <AtModalContent>
          <Image className='redeem-img' src={redeemImg}></Image>
          <View className='footer'>
            <View className='btns' onClick={hideRedeemHandle}>
              继续支付
            </View>
          </View>
        </AtModalContent>
      </AtModal>
    </View>
  );
};
