/**
 * 小熊书法，新增0元小程序页
 */
import Taro, { useShareAppMessage, useRouter } from '@tarojs/taro';
import { useState, useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { AtModal } from 'taro-ui';
import banner from '@/assets/zeroWrite/trial-class-img0.png';
import Btn from '@/assets/zeroWrite/trial-class-btn.png';
import packet from '@/assets/zeroWrite/packet.png';
import packetReceive from '@/assets/zeroWrite/packet-receive.png';
import { View, Image, Button } from '@tarojs/components';
import WxLogin from '@/components/wxlogin';
import Modal from './components/payModal';
import './nineWrite.scss';

const artRequireContext = require.context(
  '@/assets/zeroWrite/',
  true,
  /^\.\/.*trial-class-img\d|dd\.png$/,
);

const imgList = artRequireContext.keys().map(artRequireContext);

export default function CalligraphyExperience() {
  const params = useRouter().params;
  const country = useRef(false);
  const dispatch = useDispatch();
  const [showGiveMask, setShowGiveMask] = useState(false);
  const [isPacketReceive, setIsPacketReceive] = useState(false);
  useEffect(() => {
    params.channelId &&
      dispatch({
        type: 'CHANGE_CHANNELID',
        channelId: params.channelId,
      });
    params.sendId &&
      dispatch({
        type: 'CHANGE_SENDID',
        sendId: params.sendId,
      });
  }, [params]);

  const [modalStatus, setModalStatus] = useState(false);
  const [isBuy, setIsBuy] = useState(true);

  useEffect(() => {
    setTimeout(() => {
      if (!country.current && !showGiveMask) {
        setShowGiveMask(true);
      }
    }, 0);
  }, []);
  useEffect(() => {
    if (showGiveMask) {
      country.current = true;
    }
  }, [showGiveMask]);

  useShareAppMessage(() => {
    return {
      title: '小熊书法帮助孩子轻松写好字',
      path: `/pages/writeExperience/nineWrite?channelId=${params.channelId ||
        ''}`,
      imageUrl: banner,
    };
  });

  const openModal = () => {
    country.current = true;
    setModalStatus(true);
  };
  const closeModal = () => {
    setModalStatus(false);
  };
  const packetHandler = () => {
    if (!isPacketReceive) setIsPacketReceive(true);
    else {
      setModalStatus(true);
      setShowGiveMask(false);
    }
  };
  Taro.setNavigationBarTitle({
    title: '小熊书法',
  });
  return (
    <View className='newCalligraphyExperience'>
      {imgList.map((e: any, i) => {
        return (
          <View key={i}>
            <Image src={e} className='banner' mode='widthFix'></Image>
          </View>
        );
      })}
      <View className='none'></View>
      <View className='bottomPay'>
        <View
          onClick={() => {
            isBuy && openModal();
          }}
          className='payButton'
        >
          {isBuy ? (
            <Image className='button-img' src={Btn} />
          ) : (
            <Button className='button' open-type='share'>
              分享好友一起学
            </Button>
          )}
        </View>
      </View>
      <Modal
        status={modalStatus}
        modalEvent={closeModal}
        price='9.9'
        packagesId={process.env.NODE_ENV === 'online' ? '911' : '8916'}
        setIsBuy={() => {
          setIsBuy(false);
          country.current = true;
        }}
      />
      <WxLogin subject='WRITE_APP' isIntroduce={false} />
      <AtModal
        className='giveMask'
        isOpened={showGiveMask}
        // closeOnClickOverlay={false}
        onClose={() => {
          // setShowGiveMask(false);
        }}
      >
        <Image
          className='giveMaskImg'
          src={!isPacketReceive ? packetReceive : packet}
          onClick={packetHandler}
        ></Image>
      </AtModal>
    </View>
  );
}
