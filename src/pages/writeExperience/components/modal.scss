.modal {
  padding-bottom: 20px;
  .layout-header__title {
    text-align: left;
    padding: 0;
    font-size: 36px;
    font-weight: 500;
    color: #333333;
  }
  .at-float-layout .layout-body {
    padding: 0;
  }
  .at-float-layout .layout-body__content {
    padding-bottom: 10px;
    overflow: hidden;
  }
  .modalCenter {
    width: 670px;
    height: 500px;
    padding-top: 80px;
    box-sizing: border-box;
    margin: 0px auto;
    > View {
      float: left;
      &:nth-child(2n) .modalLevel {
        margin-right: 0 !important;
      }
    }
  }
  .modalTitle {
    padding: 20px 0 35px 0;
    font-size: 26px;
    color: #666666;
    display: flex;
    .modalSubTitle {
      color: #fd2205;
    }
  }
  .modalLevel {
    width: 300px;
    height: 92px;
    border-radius: 8px;
    border: 1px solid #cccccc;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 32px;
    color: #666666;
    margin-right: 60px;
    margin-bottom: 30px;
  }

  .hasSelected {
    .modalLevelSelect,
    .modalLevel,
    .modalSeniorSelect,
    .modalLeveSenior {
      border: 1px solid #eaeaea;
      box-shadow: 0px 0px 13px 0px rgba(177, 177, 177, 0.26);
      position: relative;
    }
    .selected-mark {
      width: 109px;
      height: 92px;
      position: absolute;
      top: -2px;
      right: 0;
      background-image: url('../../../assets/thirtySix/chooseLevel/hasbuy.png');
      background-repeat: no-repeat;
      background-position: left top;
      background-size: contain;
    }
    .modalLevelTitle,
    .modalLeveSeniorTitle {
      background: #eaeaea;
      color: #ffffff;
    }
    .modalLevelFit,
    .modalLeveSeniorFit {
      color: #eaeaea;
    }
  }
  .modalLevelSelect {
    width: 300px;
    height: 92px;
    border: 2px solid #ffa82d;
    box-shadow: 0px 6px 15px 1px rgba(39, 39, 39, 0.13);
    display: flex;
    align-items: center;
    position: relative;
    font-size: 32px;
    color: #ffa82d;
    background: #fff5ef;
  }
  .ModalSelect {
    width: 55px;
    height: 41px;
    border: 2px solid #ffa82d;
    position: absolute;
    top: 0;
    right: 0;
    background: #ffa82d;
    border-radius: 0 20px 0px 20px;
  }
  .ModalSelect::after {
    content: '';
    position: absolute;
    right: 12px;
    top: 1px;
    width: 25px;
    height: 12px;
    border: 6px solid #ffffff;
    border-radius: 2px;
    border-top: none;
    border-right: none;
    background: transparent;
    transform: rotate(-45deg);
  }

  .modalLevelTitle {
    width: 183px;
    height: 85px;
    background: linear-gradient(0deg, #ffa92d, #ff7c00);
    font-size: 40px;
    font-weight: 500;
    color: #ffffff;
    display: flex;
    align-items: center;
    border-radius: 0 50px 50px 0;
  }
  .modalLevelFit {
    color: #333333;
    margin: 0 auto;
  }

  .modalLeveSenior {
    width: 670px;
    height: 157px;
    border: 2px solid #f6f6f6;
    box-shadow: 0px 6px 15px 1px rgba(39, 39, 39, 0.13);
    border-radius: 30px;
    display: flex;
    align-items: center;
    margin-top: 32px;
  }
  .modalSeniorSelect {
    width: 670px;
    height: 157px;
    border: 2px solid #ffa82d;
    box-shadow: 0px 6px 15px 1px rgba(39, 39, 39, 0.13);
    border-radius: 30px;
    display: flex;
    align-items: center;
    margin-top: 32px;
    position: relative;
  }
  .modalLeveSeniorTitle {
    width: 183px;
    height: 85px;
    background: linear-gradient(0deg, #93e749, #7dc83c);
    font-size: 40px;
    font-weight: 500;
    color: #ffffff;
    display: flex;
    align-items: center;
    border-radius: 0 50px 50px 0;
  }
  .modalLeveSeniorFit {
    font-size: 40px;
    font-weight: 500;
    color: #333333;
    padding-left: 31px;
  }

  .modaPay {
    width: 100%;
    height: 108px;
    margin-top: 60px;
    border: 2px solid #f6f6f6;
    box-shadow: 0px 6px 15px 1px rgba(39, 39, 39, 0.13);
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    bottom: 0;
    .modaPayText {
      display: flex;
      font-weight: 500;
      color: hsl(0, 0%, 20%);
      padding-left: 16px;
      align-items: baseline;
      .modaPayBigText {
        font-size: 50px;
        font-family: PingFang SC;
        font-weight: 500;
        color: #ff8004;
      }
      .modaPayBigWord {
        font-size: 40px;
        font-family: PingFang SC;
        font-weight: 200;
        color: #ff8004;
      }
    }
    .modaPayButton {
      width: 289px;
      height: 88px;
      background: linear-gradient(90deg, #ff9c31, #ff5d31);
      border-radius: 44px;
      font-size: 36px;
      color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
