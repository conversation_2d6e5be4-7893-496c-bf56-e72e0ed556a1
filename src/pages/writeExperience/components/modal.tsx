import Taro from '@tarojs/taro';
import { useEffect, useState } from 'react';
import CheckLevel from '@/utils/checkLevel';
import { useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import sensors from '@/utils/sensors_data';
import { View } from '@tarojs/components';
import { useThirtySixPay } from '@/hooks/payhook/useThirtySixPay';
import { AtFloatLayout } from 'taro-ui';
import Paybtn from '../../thirtySix/components/payBtn/index.weapp';

import './modal.scss';

export default function Modal(props: any) {
  const [sup, SetSup] = useState('');
  const [isZero] = useState(true);
  const [payPageData] = useState<object | null>({
    bgcolor: '#FF9C00',
    label: 'DEFAULT',
    sup,
    fit: `刚刚会拿画笔、初步认识颜色`,
    range: '学习重点：点线涂鸦 | 兴趣探究 | 趣味手工',
    courseday: '0',
    period: 0,
    addTeacher: true,
  });
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  //channelId
  const channelId = useSelector((state: UserStateType) => state.channelId);
  const [hasBuyLevel, setHasBuyLevel] = useState<any[]>([]);
  const [levelListArr] = useState<Array<any>>([
    { level: '学龄前', sup: 'S1', couldBuy: true },
    { level: '一年级', sup: 'S3', couldBuy: true },
    { level: '二年级', sup: 'S2', couldBuy: true },
    { level: '三年级', sup: 'S4', couldBuy: true },
  ]);

  const payMessage = useThirtySixPay({
    topicId: '3',
    packagesId: ['dev', 'development'].includes(process.env.NODE_ENV)
      ? '7659'
      : '910',
    isIntroduce: false,
    subject: 'WRITE_APP',
    pType: 'calligraphy',
    payPageData,
    isZero,
  });
  const authSuccess = res => {
    if (!sup) {
      Taro.showToast({
        title: `请选择级别`,
        icon: 'none',
      });
      return;
    }
    sensors.track('sf_Experiencecourse_authorizedlayer_buttonclick', {
      is_authorization: '允许',
    });
    res.sup = sup;
    if (sup == 'S4') res.sup = 'S2';
    payMessage.authSuccess(res);
  };

  const authError = () => {
    sensors.track('sf_Experiencecourse_authorizedlayer_buttonclick', {
      is_authorization: '拒绝',
    });
    Taro.showToast({
      title: `${isZero ? '领取失败' : '支付失败'}`,
      icon: 'none',
    });
  };

  const filterBuyLevel = usup => {
    return (
      hasBuyLevel &&
      hasBuyLevel.length > 0 &&
      hasBuyLevel.findIndex(v => v == usup) > -1
    );
  };

  useEffect(() => {
    new CheckLevel({
      userId,
      channelId,
      orderId: '', //先选等级没有orderId
      regtype: 'EXPERIENCE', //书法不分单双周 默认EXPERIENCE
      subjects: 'WRITE_APP',
      packageId: ['dev', 'development'].includes(process.env.NODE_ENV)
        ? '7659'
        : '910',
    })
      .initCheck()
      .then((res: any[]) => {
        if (res.length > 0) props.setIsBuy();
        setHasBuyLevel(res);
        if (res.length === 1) {
          if (res.indexOf('S1') > -1) {
            SetSup('S2');
          }
        } else if (res.length == 2) {
          SetSup('');
        }
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId, channelId]);

  return (
    <View className='modal'>
      <AtFloatLayout
        isOpened={props.status}
        title='请选择'
        onClose={() => {
          props.modalEvent();
        }}
      >
        <View className='modalCenter'>
          {levelListArr.map((o, i) => {
            return (
              <View
                className={!!filterBuyLevel(o.sup) ? 'hasSelected' : ''}
                key={i}
              >
                <View
                  className={
                    sup == o.sup ? 'modalLevelSelect modalLevel' : 'modalLevel'
                  }
                  onClick={() => {
                    if (!!filterBuyLevel(o.sup)) {
                      return;
                    }
                    SetSup(o.sup);
                  }}
                >
                  <View className='modalLevelFit'>{o.level}</View>
                </View>
              </View>
            );
          })}
        </View>
        <View className='modaPay'>
          <View className='modaPayText'>
            <View className='modaPayBigWord'>￥</View>
            <View className='modaPayBigText'>0</View>
          </View>
          <View>
            <Paybtn
              className='modaPayButton'
              authError={authError}
              authSuccess={authSuccess}
              btnName='确认领取'
            ></Paybtn>
          </View>
        </View>
      </AtFloatLayout>
    </View>
  );
}
