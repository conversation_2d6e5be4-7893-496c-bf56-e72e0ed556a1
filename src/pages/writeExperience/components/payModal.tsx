import Taro from '@tarojs/taro';
import { useEffect, useState } from 'react';
import CheckLevel from '@/utils/checkLevel';
import { useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import sensors from '@/utils/sensors_data';
import { View } from '@tarojs/components';
import { useThirtySixPay } from '@/hooks/payhook/useThirtySixPay';
import { AtFloatLayout } from 'taro-ui';
import Paybtn from '../../thirtySix/components/payBtn/index.weapp';

import './payModal.scss';

export default function Modal(props: any) {
  const [sup, SetSup] = useState<any>(null);
  const [payPageData] = useState<object | null>({
    bgcolor: '#FF9C00',
    label: 'DEFAULT',
    sup,
    fit: `刚刚会拿画笔、初步认识颜色`,
    range: '学习重点：点线涂鸦 | 兴趣探究 | 趣味手工',
    courseday: '0',
    period: 0,
    addTeacher: true,
  });
  const [levels] = useState([
    {
      key: 'S1',
      tag: '启蒙',
      title: '适合学龄前孩子',
      bgColor: '#FD9C23',
    },
    {
      key: 'S3',
      tag: '基础',
      title: '适合1年级孩子',
      bgColor: '#96D855',
    },
    {
      key: 'S2',
      tag: '进阶',
      title: '适合2年级孩子',
      bgColor: '#40BBE5',
    },
    {
      key: 'S4',
      tag: '高阶',
      title: '适合3年级孩子',
      bgColor: '#B840E5',
    },
  ]);
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  //channelId
  const channelId = useSelector((state: UserStateType) => state.channelId);
  const [hasBuyLevel, setHasBuyLevel] = useState<any[]>([]);

  const payMessage = useThirtySixPay({
    topicId: '3',
    packagesId: props.packagesId,
    isIntroduce: false,
    subject: 'WRITE_APP',
    pType: 'calligraphy',
    payPageData,
  });
  const authSuccess = res => {
    if (!sup) {
      Taro.showToast({
        title: `请选择适合宝贝的级别`,
        icon: 'none',
      });
      return;
    }
    sensors.track('sf_Experiencecourse_authorizedlayer_buttonclick', {
      is_authorization: '允许',
    });
    let _sup = sup;
    if (_sup == 'S4') _sup = 'S2';
    res.sup = _sup;
    payMessage.authSuccess(res);
  };

  const authError = () => {
    sensors.track('sf_Experiencecourse_authorizedlayer_buttonclick', {
      is_authorization: '拒绝',
    });
    Taro.showToast({
      title: `支付失败`,
      icon: 'none',
    });
  };

  const filterBuyLevel = usup => {
    return (
      hasBuyLevel &&
      hasBuyLevel.length > 0 &&
      hasBuyLevel.findIndex(v => v == usup) > -1
    );
  };

  useEffect(() => {
    new CheckLevel({
      userId,
      channelId,
      orderId: '', //先选等级没有orderId
      regtype: 'EXPERIENCE', //书法不分单双周 默认EXPERIENCE
      subjects: 'WRITE_APP',
    })
      .initCheck()
      .then((res: any[]) => {
        setHasBuyLevel(res);
        if (res.length === 1) {
          if (res.indexOf('S1') > -1) {
            SetSup('S2');
          }
        } else if (res.length == 2) {
          SetSup('');
        }
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId, channelId]);

  return (
    <View className='modal'>
      <AtFloatLayout
        isOpened={props.status}
        title='选择级别'
        onClose={() => {
          props.modalEvent();
        }}
      >
        <View className='modalCenter'>
          <View className='modalTitle'>
            *为了保证孩子的学习体验，请您正确选择级别
            {/* <View className='modalSubTitle'>(适合6岁以上年龄孩子)</View> */}
          </View>

          {levels.map((o, i) => {
            return (
              <View
                className={!!filterBuyLevel(o.key) ? 'hasSelected' : ''}
                key={i}
              >
                <View
                  className={sup == o.key ? 'modalLevelSelect' : 'modalLevel'}
                  onClick={() => {
                    if (!!filterBuyLevel(o.key)) {
                      return;
                    }
                    SetSup(o.key);
                  }}
                >
                  {!!filterBuyLevel(o.key) && (
                    <View className='selected-mark'></View>
                  )}
                  <View
                    className='modalLevelTitle'
                    style={`background:${o.bgColor};`}
                  >
                    {o.tag}
                  </View>
                  <View className='modalLevelFit'>{o.title}</View>
                  {/* {sup == o.key && !filterBuyLevel(o.key) ? (
                      <View className='ModalSelect'></View>
                    ) : null} */}
                </View>
              </View>
            );
          })}
        </View>
        <View className='modaPay'>
          <View className='modaPayText'>
            <View className='modaPayBigText'>{props.price}</View>
            <View className='modaPayBigWord'>元/</View>
            <View className='modaPayBigText'>5</View>
            <View className='modaPayBigWord'>日</View>
          </View>
          <View>
            <Paybtn
              className='modaPayButton'
              authError={authError}
              authSuccess={authSuccess}
              btnName='确认支付'
            ></Paybtn>
          </View>
        </View>
      </AtFloatLayout>
    </View>
  );
}
