/**
 * 小熊书法，新增0元小程序页
 */
import Taro, { useShareAppMessage, useRouter } from '@tarojs/taro';
import { useState, useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { AtModal } from 'taro-ui';
import banner from '@/assets/zeroWrite/experienceCourse-1.png';
import Btn from '@/assets/zeroWrite/experienceCourse-btn.png';
import nowBuyPopGIF from '@/assets/zeroWrite/nowBuyPopGIF.gif';
import { View, Image, Button } from '@tarojs/components';
import WxLogin from '@/components/wxlogin';
import Modal from './components/modal';
import './index.scss';

const artRequireContext = require.context(
  '@/assets/zeroWrite/',
  true,
  /^\.\/.*experienceCourse-\d|dd\.png$/,
);

const imgList = artRequireContext.keys().map(artRequireContext);

export default function CalligraphyExperience() {
  const params = useRouter().params;
  const country = useRef(false);
  const dispatch = useDispatch();
  const [showGiveMask, setShowGiveMask] = useState(false);
  const [modalStatus, setModalStatus] = useState(false);
  const [isBuy, setIsBuy] = useState(true);

  useEffect(() => {
    setTimeout(() => {
      if (!country.current && !showGiveMask) {
        setShowGiveMask(true);
      }
    }, 10000);
  }, []);

  useEffect(() => {
    dispatch({
      type: 'CHANGE_CHANNELID',
      channelId: params.channelId || 14119,
    });

    dispatch({
      type: 'CHANGE_SENDID',
      sendId: params.sendId || '0',
    });
  }, [params]);

  useEffect(() => {
    if (showGiveMask) {
      country.current = true;
    }
  }, [showGiveMask]);

  useShareAppMessage(() => {
    return {
      title: '小熊书法帮助孩子轻松写好字',
      path: `/pages/writeExperience/index?channelId=${params.channelId || ''}`,
      imageUrl: banner,
    };
  });

  const openModal = () => {
    country.current = true;
    setModalStatus(true);
  };
  const closeModal = () => {
    setModalStatus(false);
  };
  Taro.setNavigationBarTitle({
    title: '小熊书法',
  });
  return (
    <View className='newCalligraphyExperience'>
      {imgList.map((e: any, i) => {
        return i == 1 ? (
          <View key={i}>
            <Image src={e} className='bannerTop' mode='widthFix'></Image>
          </View>
        ) : (
          <View key={i}>
            <Image src={e} className='banner' mode='widthFix'></Image>
          </View>
        );
      })}
      <View className='none'></View>
      <View className='bottomPay'>
        <View
          onClick={() => {
            openModal();
          }}
          className='payButton'
        >
          {/* {isBuy ? ( */}
          <Image className='button-img' src={Btn} />
          {/* ) : (
            <Button className='button' open-type='share'>
              分享好友一起学
            </Button>
          )} */}
        </View>
      </View>
      <Modal
        status={modalStatus}
        modalEvent={closeModal}
        type='zero'
        setIsBuy={() => {
          setIsBuy(false);
          country.current = true;
        }}
      />
      <WxLogin subject='WRITE_APP' isIntroduce={false} />
      <AtModal
        className='giveMask'
        isOpened={showGiveMask}
        // closeOnClickOverlay={false}
        onClose={() => {
          setShowGiveMask(false);
        }}
      >
        <Image
          className='giveMaskImg'
          src={nowBuyPopGIF}
          onClick={() => {
            setModalStatus(true);
            setShowGiveMask(false);
          }}
        ></Image>
      </AtModal>
    </View>
  );
}
