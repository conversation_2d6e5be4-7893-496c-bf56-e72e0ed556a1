/**
 * 小熊书法，新增0.1元小程序页
 */
import Taro, { useRouter, useShareAppMessage } from '@tarojs/taro';
import { useState, useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import shareWriteIcon from '@/assets/invitetask/share-wirte.png';
import { View, Image, Button } from '@tarojs/components';
import WxLogin from '@/components/wxlogin';
import Modal from './components/payModal';
import './zeroNine.scss';

const artRequireContext = require.context(
  '@/assets/zeroWrite/',
  true,
  /^\.\/.*trial-class-img1-v\d|dd\.png$/,
);

const imgList = artRequireContext.keys().map(artRequireContext);

export default function CalligraphyExperience() {
  const params = useRouter().params;
  const country = useRef(false);
  const dispatch = useDispatch();
  const [showGiveMask, setShowGiveMask] = useState(false);
  const [modalStatus, setModalStatus] = useState(false);
  const [isBuy, setIsBuy] = useState(true);

  useEffect(() => {
    setTimeout(() => {
      if (!country.current && !showGiveMask) {
        setShowGiveMask(true);
      }
    }, 0);
  }, []);

  useEffect(() => {
    params.channelId &&
      dispatch({
        type: 'CHANGE_CHANNELID',
        channelId: params.channelId,
      });
    params.sendId &&
      dispatch({
        type: 'CHANGE_SENDID',
        sendId: params.sendId,
      });
  }, [params]);

  useEffect(() => {
    if (showGiveMask) {
      country.current = true;
    }
  }, [showGiveMask]);

  const openModal = () => {
    country.current = true;
    setModalStatus(true);
  };
  const closeModal = () => {
    setModalStatus(false);
  };

  Taro.setNavigationBarTitle({
    title: '小熊书法',
  });

  // 分享
  useShareAppMessage(() => {
    return {
      title: '发现宝藏了！在家5天打好写字基本功，让你家宝贝也来试试！',
      path: `/pages/writeExperience/zeroNine?channelId=${params.channelId ||
        ''}&come=${params.come || ''}`,
      imageUrl: shareWriteIcon,
      success() {
        console.log('分享成功');
      },
      fail() {
        console.log('分享失败');
      },
    };
  });
  return (
    <View className='newCalligraphyExperience'>
      {imgList.map((e: any, i) => {
        return (
          <View key={i}>
            <Image src={e} className='banner' mode='widthFix'></Image>
          </View>
        );
      })}
      <View className='none'></View>
      <View className='bottomPay'>
        <View
          onClick={() => {
            isBuy && openModal();
          }}
          className='payButton'
        >
          {isBuy ? (
            <View className='button'>0.1元 立即购买</View>
          ) : (
            <Button className='button' open-type='share'>
              分享好友一起学
            </Button>
          )}
        </View>
      </View>
      <Modal
        status={modalStatus}
        modalEvent={closeModal}
        price='0.1'
        packagesId={
          process.env.NODE_ENV === 'online'
            ? '909'
            : process.env.NODE_ENV === 'test'
            ? '8914'
            : '7660'
        }
        setIsBuy={() => {
          setIsBuy(false);
          country.current = true;
        }}
      />
      <WxLogin subject='WRITE_APP' isIntroduce={false} />
    </View>
  );
}
