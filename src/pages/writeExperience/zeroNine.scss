.calligraphyExperience {
  width: 100%;
  height: 100%;
  background: #76cd58;
  .banner {
    width: 100%;
  }
  .payButton {
    position: fixed;
    bottom: 0;
    z-index: 10;
    width: 100%;
  }
  .none {
    width: 100%;
    height: 120px;
  }
}

.newCalligraphyExperience {
  width: 100%;
  height: 100%;
  font-size: 0;
  background-color: #ffffff;
  .banner {
    width: 100%;
  }
  .bannerTop {
    width: 100%;
    margin-top: -40px;
  }
  .none {
    width: 100%;
    height: 120px;
  }
  .bottomPay {
    width: 100%;
    height: 155px;
    background: #ffffff;
    box-shadow: 0px 0px 13px 0px rgba(136, 136, 136, 0.2);
    position: fixed;
    bottom: 0;
    z-index: 2;
    .button {
      width: 90%;
      height: 88px;
      background: linear-gradient(180deg, #ffb51a 0%, #ff7e0b 100%) !important;
      border-radius: 44px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #fff;
      font-size: 38px;
      font-family: PingFangSC-Medium, PingFang SC;
      margin: 40px auto;
      animation: scaleJumpbtn 0.9s ease-in-out infinite;
    }
  }
  @keyframes scaleJumpbtn {
    0% {
      transform: scale(0.95);
    }
    50% {
      transform: scale(1.02);
    }
    100% {
      transform: scale(0.95);
    }
  }
  .bottomPayNew {
    width: 100%;
    height: 155px;
    background: #ffffff;
    box-shadow: 0px 0px 13px 0px rgba(136, 136, 136, 0.2);
    position: fixed;
    bottom: 0;
    z-index: 2;

    .payButton {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    image {
      display: block;
      width: 584x;
      height: 82px;
      transform: scale(0.8);
    }
  }
  .giveMask {
    .at-modal__container {
      width: 750px;
      height: 100%;
      background-color: transparent;
    }
    .giveMaskImg {
      width: 750px;
      height: 1334px;
      margin-top: -100px;
    }
  }
}
