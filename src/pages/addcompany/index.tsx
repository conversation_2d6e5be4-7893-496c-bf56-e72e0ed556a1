import { WebView } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useEffect } from 'react';

const Indexwebview = () => {
  const url = `https://mp.weixin.qq.com/s?__biz=MzkwODI2MjQyNg==&mid=100000065&idx=1&sn=45c170daa69c6900a593a0cfe16d4118&scene=19#wechat_redirect`;
  // const url = `http://www.baidu.com`;
  const handleOnLoad = () => {
    Taro.hideLoading();
  };

  useEffect(() => {
    Taro.showLoading({ title: '加载中...' });
  }, []);

  return <WebView src={url} onLoad={handleOnLoad} />;
};

export default Indexwebview;
