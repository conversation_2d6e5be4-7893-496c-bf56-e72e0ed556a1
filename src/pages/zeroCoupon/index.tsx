import { AtCountdown, AtModal } from 'taro-ui';

import sensors from '@/utils/sensors_data';
import { useDispatch, useSelector } from 'react-redux';
import { useEffect, useMemo, useState } from 'react';
import { UserStateType } from '@/store/groupbuy/state';
import { View, Button, Image } from '@tarojs/components';
import Taro, { useRouter, useShareAppMessage } from '@tarojs/taro';
import viewBg from '@/assets/zeroCoupon/index-bg.png';
import finishBg from '@/assets/zeroCoupon/index-bg-finish.png';
import mainPng from '@/assets/zeroCoupon/main.png';
import buttonBg from '@/assets/zeroCoupon/button-bg.png';
import failPng from '@/assets/zeroCoupon/fail.png';
import successPng from '@/assets/zeroCoupon/success.png';
import couponPng from '@/assets/zeroCoupon/coupon.png';
import mainFinishPng from '@/assets/zeroCoupon/main-finish.png';
import {
  getUserCouponByPackageId,
  getProgramUserSubject,
  getUser,
  getFriendCoupon1888,
} from '@/api/groupbuy';
import WxLogin from '@/components/wxlogin';
import closeImg from '@/assets/pictureBookSimple/icon_close.png';
import Rule from './rule';
import './index.scss';

const Indexwebview = () => {
  const shareImg =
    'https://fe-cdn.xiaoxiongmeishu.com/apph5/video/1705479047845_share-cover.png';
  const defaultHead = 'https://s1.xiaoxiongmeishu.com/image/bear_head.png';
  const dispatch = useDispatch();
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  //openid
  const openId = useSelector((state: UserStateType) => state.openid);
  const router = useRouter();
  const [sendId, setsendId] = useState('');
  const [endTime, setendTime] = useState('');
  const [userInfo, setuserInfo] = useState<any>({});
  const [showGiveMask, setShowGiveMask] = useState(false);
  const [isFull, setIsfull] = useState(false);
  const [ruleVis, setRuleVis] = useState(false);
  const [recevied, setRecevied] = useState(false);
  const [errorText, setErrorText] = useState('');
  // const _NODE_ENV = process.env.NODE_ENV;
  // 0元套餐id，不区分环境
  const packagesId = '1888';
  // 0元券id，不区分环境
  const couponId = '351';

  useEffect(() => {
    var _sendId;
    if (router.params.scene) {
      _sendId = decodeURIComponent(router.params.scene).split('=');
      setsendId(_sendId[1]);
    }
    if (router.params.sendId) setsendId(router.params.sendId);
    sensors.track('xxys_presentCouponPage_view', {
      entrance_page: router.params.entrance_page,
    });
  }, []);

  const getConponHandler = () => {
    if (!sendId && userId == null) return;
    getUserCouponByPackageId(sendId ? sendId : userId, packagesId).then(res => {
      if (res.payload.length) {
        let i = res.payload.findIndex(o => o.newCouponId == couponId);
        if (i > -1 && res.payload[i].endDate > new Date().getTime()) {
          setendTime(res.payload[i].endDate);
          setIsfull(true);
        }
      } else {
        // setIsfull(false);
        setendTime('');
      }
    });
  };

  useEffect(() => {
    if (sendId || (!sendId && userId)) {
      getConponHandler();
      getUserInfo();
    }
  }, [sendId, userId]);

  const getUserInfo = async () => {
    if (!sendId && userId == null) return;
    const { payload } = await getUser({ userId: sendId || userId });
    setuserInfo(payload);
  };

  useShareAppMessage(res => {
    if (res.from === 'button') {
      // 来自页面内转发按钮
      console.log(res.target);
    } else
      sensors.track('xxys_presentCouponPage_shareClick', {
        operation_type: '右上角转发给朋友',
      });

    var channel = '17863';
    if (router.params.channelId) channel = router.params.channelId;
    const title = '送你1张0元券，小熊美术免费学，还送画材包邮哦';
    return {
      title,
      path: `/pages/normalGroup/art/zero/index?sendId=${
        userId ? userId : sendId
      }&channelId=${channel}&msChannelId=${channel}`,
      imageUrl: shareImg,
    };
  });

  // 获取用户手机号
  const getPhoneNumber = res => {
    if (res.detail.errMsg === 'getPhoneNumber:fail user deny') {
      console.log(res.detail.errMsg);
    } else {
      const { encryptedData, iv } = res.detail;
      // getProgramUserSubject兼容getProgramUser
      getProgramUserSubject({
        openId,
        encryptedData,
        iv,
        subject: 'ART_APP',
      }).then(phone => {
        if (phone.payload.uid) {
          dispatch({
            type: 'CHANGE_USERID',
            userid: phone.payload.uid,
          });
        }
        if (phone.payload.token)
          Taro.setStorageSync('appToken', phone.payload.token);
        achieveHandler(phone.payload.uid);
        phone.payload.mobile &&
          dispatch({
            type: 'CHANGE_MOBILE',
            mobile: phone.payload.mobile,
          });
      });
    }
  };
  const timeInfo = useMemo(() => {
    const date = new Date().getTime();
    var second = Math.floor((+endTime - date) / 1000);
    // 天数
    var day = Math.floor(second / 3600 / 24);
    // 小时
    var hours = Math.floor((second / 3600) % 24);
    // 分钟
    var minutes = Math.floor((second / 60) % 60);
    // 秒
    var seconds = Math.floor(second % 60);
    return { day, hours, minutes, seconds };
  }, [endTime]);

  const achieveHandler = (uid = userId) => {
    sensors.track('xxys_presentCouponPage_receiveClick', {});
    getFriendCoupon1888({
      userId: uid,
    }).then(res => {
      if (!res.code) {
        setIsfull(true);
        setRecevied(true);
        setShowGiveMask(true);
        getConponHandler();
      } else {
        setIsfull(false);
        setErrorText(res.errors || '');
        setRecevied(false);
        setShowGiveMask(true);
      }
    });
  };

  const toStudy = () => {
    Taro.showToast({
      title: '我知道了',
      icon: 'none',
      duration: 2000,
    });
    setShowGiveMask(false);
  };

  return (
    <View className='content'>
      <WxLogin subject='ART_APP' noAskNoaddress />
      <Image
        className='view-bg'
        mode='scaleToFill'
        src={!isFull ? viewBg : finishBg}
      />
      <View className='rule-span' onClick={() => setRuleVis(true)}>
        活动规则
      </View>
      {!isFull ? (
        <View className='index'>
          <View className='head-part'>
            <View className='head-title'>
              <View className='head-img'>
                <Image
                  src={userInfo && userInfo.head ? userInfo.head : defaultHead}
                  className='img'
                />
              </View>
              {userInfo && userInfo.username ? userInfo.username : '小熊用户'}
            </View>
            <View className='main'>
              <Image src={mainPng} className='main-img' />
              {userId ? (
                <View className='submit' onClick={() => achieveHandler(userId)}>
                  <Image src={buttonBg} className='submit-img' />
                  <View className='text'>立即领取</View>
                </View>
              ) : (
                <Button
                  className='submit'
                  open-type='getPhoneNumber'
                  onGetPhoneNumber={getPhoneNumber}
                >
                  <Image src={buttonBg} className='submit-img' />
                  <View className='text'>立即赠送</View>
                </Button>
              )}
            </View>
          </View>
        </View>
      ) : (
        <View className='finish'>
          <View className='main'>
            <Image className='mainFinishPng' src={mainFinishPng}></Image>
            {endTime && (
              <View className='time'>
                有效期{' '}
                <AtCountdown
                  className='custom-time-main'
                  isShowDay
                  key={timeInfo.seconds}
                  day={timeInfo.day}
                  hours={timeInfo.hours}
                  minutes={timeInfo.minutes}
                  seconds={timeInfo.seconds}
                  format={{ day: ':', hours: ':', minutes: '', seconds: '' }}
                />{' '}
                结束
              </View>
            )}
            <Button
              className='submit'
              open-type='share'
              onClick={() => {
                sensors.track('xxys_testcoursecouponpage_shareclick', {
                  operation_type: '页面上立即赠送',
                });
              }}
            >
              <Image className='submit-img' src={buttonBg} />
              <View className='text'>立即赠送</View>
            </Button>
          </View>
        </View>
      )}
      <AtModal
        className='giveMask'
        isOpened={showGiveMask}
        closeOnClickOverlay={false}
        onClose={() => {
          setShowGiveMask(false);
        }}
      >
        <View className='popup-box'>
          {recevied ? (
            <View className='recevied'>
              <View className='popup-title'>
                <Image className='img' src={successPng} />
                <View>领取成功</View>
              </View>
              <Image className='coupon' src={couponPng} />
              <Button
                openType='share'
                className='btn'
                onClick={() => {
                  setShowGiveMask(false);
                  sensors.track('xxys_presentCouponPage_shareClick', {
                    operation_type: '弹窗上立即赠送',
                  });
                }}
              >
                <Image className='img' src={buttonBg} />
                <View className='text'>立即赠送</View>
              </Button>
            </View>
          ) : (
            <View>
              <View className='noreceive'>
                <Image className='time' src={failPng} />
                <View className='tip'>{errorText}</View>
                <View className='btn' onClick={toStudy}>
                  <Image className='img' src={buttonBg} />
                  <View className='text'>去学习</View>
                </View>
              </View>
            </View>
          )}
          <Image
            src={closeImg}
            className='close'
            onClick={() => setShowGiveMask(false)}
          />
        </View>
      </AtModal>
      <Rule
        visible={ruleVis}
        close={() => {
          setRuleVis(false);
        }}
      />
    </View>
  );
};
export default Indexwebview;
