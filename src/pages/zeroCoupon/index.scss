page {
  background: #f0f0f0;
}
.content {
  position: relative;
  overflow: hidden;
  height: 100vh;
  min-height: 1448px;
  .view-bg {
    width: 100%;
    position: absolute;
    height: 100vh;
    min-height: 1448px;
    z-index: -1;
  }
  .submit {
    width: 586px;
    height: 116px;
    position: absolute;
    top: 502px;
    left: 18px;
    z-index: 6;
    font-size: 40px;
    font-weight: 600;
    color: #fff;
    line-height: 88px;
    text-align: center;
    margin: 0;
    background-color: none;
    padding: 0;
    animation: breathe 1s linear infinite;
    .submit-img {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 5;
    }
    .text {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 6;
    }
  }
  .rule-span {
    width: 128px;
    height: 40px;
    background: rgba(0, 0, 0, 0.3);
    box-shadow: 0px 4px 8px 0px rgba(185, 65, 2, 0.12);
    border-radius: 23px 0px 0px 23px;
    position: fixed;
    right: 0;
    top: 16px;
    font-size: 24px;
    font-weight: 400;
    color: #ffffff;
    line-height: 40px;
    text-align: center;
  }
  .index {
    width: 100%;
    height: 100%;
    text-align: center;
    overflow: hidden;

    .head-part {
      width: 686px;
      height: 820px;
      box-sizing: border-box;
      border-radius: 24px;
      margin: 376px auto 0;
      text-align: center;
      font-size: 36px;
      background: #fff;
      padding: 148px 32px 0;
      position: relative;
      .head-title {
        height: 60px;
        text-align: center;
        position: absolute;
        left: 50%;
        top: -75px;
        transform: translateX(-50%);
        .head-img {
          width: 150px;
          height: 150px;
          margin: 0 auto;
          padding: 5px;
          background: #fff;
          border-radius: 50%;
          .img {
            width: 140px;
            height: 140px;
            border-radius: 50%;
            margin-top: 6px;
          }
        }
      }
      .main {
        width: 622px;
        height: 640px;
        position: relative;
        overflow: hidden;
        .main-img {
          position: absolute;
          left: 0;
          bottom: 0;
          width: 100%;
          height: 100%;
          z-index: 1;
        }
      }
    }
  }
  .finish {
    .main {
      width: 686px;
      height: 1106px;
      margin: 246px auto 0px;
      position: relative;
      overflow: hidden;
      .mainFinishPng {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        bottom: 0;
        z-index: -1;
      }
      .time {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 890px auto 0;
        .span {
          font-size: 28px;
          margin: 8px;
        }
      }
      .submit {
        top: 970px;
        left: 50px;
      }
      .submit-img {
        // left: 32px;
        // top: 900px;
      }
    }
  }
  .popup-box {
    position: relative;
    .btn {
      width: 544px;
      height: 100px;
      position: absolute;
      bottom: 30px;
      left: 25px;
      .text {
        width: 100%;
        height: 100%;
        left: 0;
        position: absolute;
        z-index: 10;
        font-size: 36px;
        font-weight: 600;
        color: #fff;
        line-height: 78px;
        text-align: center;
      }
      .img {
        width: 100%;
        height: 100%;
        left: 0;
        position: absolute;
        z-index: 1;
      }
    }
    .recevied {
      width: 600px;
      height: 528px;
      background: linear-gradient(180deg, #fee8c6 0%, #ffffff 100%);
      border-radius: 24px;
      text-align: center;
      .popup-title {
        width: 100%;
        font-weight: 600;
        color: #a34929;
        line-height: 48px;
        font-size: 48px;
        text-align: center;
        position: absolute;
        top: -80px;
        .img {
          width: 160px;
          height: 160px;
          margin-bottom: 30px;
        }
      }
      .coupon {
        width: 520px;
        height: 148px;
        margin: 206px auto 0;
      }
    }
    .noreceive {
      width: 600px;
      height: 488px;
      background: #ffffff;
      border-radius: 24px;
      text-align: center;
      .time {
        width: 160px;
        height: 160px;
        margin-top: 32px;
      }
      .tip {
        font-size: 40px;
        font-weight: 500;
        color: #333333;
        line-height: 56px;
        padding: 0 60px;
      }
      // .text {
      //   font-size: 32px;
      //   font-weight: 400;
      //   color: #666666;
      //   line-height: 44px;
      //   margin: 6px 0 10px;
      //   display: inline-block;
      // }
    }
    .close {
      position: absolute;
      top: 550px;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 80px;
    }
  }
}
@keyframes breathe {
  0% {
    transform: scale(0.95);
  }
  50% {
    transform: scale(1);
  }
  100% {
    transform: scale(0.95);
  }
}
