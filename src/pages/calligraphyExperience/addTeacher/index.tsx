/**
 * 写字添加老师页
 */

import { View, Image } from '@tarojs/components';
import xzHeader from '@/assets/calligraphyExperience/addteacher/xz-header.png';
import xzKt from '@/assets/calligraphyExperience/addteacher/xz-kt.png';
import Taro, { useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import { getWriteTeacherInfo } from '@/api/groupbuy';
import sensors from '@/utils/sensors_data';
import './index.scss';

export default function Addteacher() {
  const router = useRouter();
  let params: any = router.params;
  const orderId = useSelector((state: UserStateType) => state.orderId);
  let orderid = params.outTradeNo || params.orderId || orderId;
  // 二维码
  const [qrcodeNo, setQrcodeNo] = useState('');
  const [wechat, setWechat] = useState('');
  const getWriteTeacher = () => {
    const subject = 'WRITE_APP';
    orderid &&
      getWriteTeacherInfo({
        orderNo: orderid,
        subject,
      }).then(res => {
        if (res.code === 0) {
          setQrcodeNo(res.payload.teacherWeChatQrCode);
          setWechat(res.payload.teacherWeChat);
        }
      });
  };
  // 长按关注公众号二维码
  const onLongPresshandle = () => {
    sensors.track('sf_Experiencecourse_addteacherpage_longpresscode', {});
  };

  // 复制
  const copyWechat = () => {
    Taro.setClipboardData({
      data: wechat || '',
      success: function() {
        Taro.showToast({
          title: '您已复制老师微信号,打开微信首页-右上角添加好友',
          icon: 'none',
          duration: 2000,
        });
      },
    });
  };

  useEffect(() => {
    sensors.track('sf_Experiencecourse_addteacherpage_view', {});
    getWriteTeacher();
  }, []);

  return (
    <View className='addteacher-box'>
      <View className='header-view'>
        <Image src={xzHeader} mode='widthFix' />
        <Image className='xz-kt' src={xzKt} mode='widthFix' />
      </View>
      <View className='contain-view'>
        <View className='contain-title'>
          <View>长按添加老师激活课程</View>
          <View>为宝贝写字提供点评指导</View>
        </View>
        <View className='contain-qrcode'>
          <Image
            src={qrcodeNo}
            mode='widthFix'
            onLongPress={() => onLongPresshandle()}
          />
        </View>
        <View className='contain-copy'>
          <View className='wx-code'>老师微信号：{wechat}</View>
          <View className='wx-btn' onClick={copyWechat}>
            复制
          </View>
        </View>
      </View>
    </View>
  );
}
