.addteacher-box {
  width: 100%;
  height: 100vh;
  background: #24ae00;
  position: relative;
  image {
    width: 100%;
    height: 100%;
    display: block;
  }
}
.header-view {
  width: 100%;
  height: 440px;
  overflow: hidden;
  font-size: 0;
  position: relative;
  .xz-kt {
    width: 262px;
    height: 413px;
    position: absolute;
    top: 20px;
    right: 52px;
    z-index: 1000;
  }
}
.contain-view {
  width: 662px;
  height: 768px;
  position: absolute;
  left: 50%;
  top: 358px;
  transform: translate(-50%);
  background-color: #ffffff;
  border-radius: 30px;
  padding-top: 97px;
  box-sizing: border-box;
  .contain-title {
    width: 100%;
    text-align: center;
    font-size: 34px;
    font-weight: 400;
    color: #24ae00;
  }
  .contain-qrcode {
    width: 368px;
    height: 368px;
    background: #ffffff;
    border-radius: 32px;
    margin: 30px auto 0;
  }
  .contain-copy {
    width: 564px;
    height: 80px;
    background: #eaf4e7;
    border-radius: 20px;
    margin: 50px auto 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 14px;
    box-sizing: border-box;
    .wx-code {
      flex: 1;
      font-size: 28px;
      font-weight: 400;
      color: #333333;
    }
    .wx-btn {
      width: 129px;
      height: 52px;
      border-radius: 26px;
      border: 1px solid #24ae00;
      font-size: 28px;
      font-weight: 600;
      color: #24ae00;
      line-height: 52px;
      text-align: center;
    }
  }
}
