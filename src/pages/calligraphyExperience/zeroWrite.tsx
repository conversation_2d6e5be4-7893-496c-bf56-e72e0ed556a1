/**
 * 小熊书法，新增0元小程序页
 */

import Taro, { useShareAppMessage, useRouter } from '@tarojs/taro';
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
// import sensors from '@/utils/sensors_data';
import banner from '@/assets/calligraphyExperience/newPage/banner.png';
import experience from '@/assets/calligraphyExperience/zero/experience0.png';
import harvest from '@/assets/calligraphyExperience/newPage/harvest.png';
import why from '@/assets/calligraphyExperience/newPage/why.png';
import animation from '@/assets/calligraphyExperience/newPage/animation.png';
import major from '@/assets/calligraphyExperience/newPage/major.png';
import saveWorry from '@/assets/calligraphyExperience/newPage/saveWorry.png';
import problem from '@/assets/calligraphyExperience/zero/problem.png';
import address from '@/assets/calligraphyExperience/newPage/address.png';
import payButton from '@/assets/calligraphyExperience/zero/payButton0.png';
import { View, Image } from '@tarojs/components';
import WxLogin from '@/components/wxlogin';
import Modal from './components/modal';
import './index.scss';

export default function CalligraphyExperience() {
  const router = useRouter();
  const dispatch = useDispatch();
  const [modalStatus, setModalStatus] = useState(false);
  const [imgList] = useState([
    banner,
    experience,
    harvest,
    why,
    animation,
    major,
    saveWorry,
    problem,
    address,
  ]);
  dispatch({
    type: 'CHANGE_CHANNELID',
    channelId: router.params.channelId || '2838',
  });

  useShareAppMessage(() => {
    return {
      title: '小熊书法帮助孩子轻松写好字',
      path: `/pages/calligraphyExperience/index?channelId=${router.params.channelId}`,
      imageUrl: banner,
    };
  });
  // useEffect(() => {
  //   sensors.track('sf_Experiencecourse_descriptionpage_view', {
  //     channel_id: router.params.channelId,
  //   });
  // }, []);
  const openModal = () => {
    setModalStatus(true);
    // sensors.track('sf_Experiencecourse_descriptionpage_buybuttonclick', {});
  };
  const closeModal = () => {
    setModalStatus(false);
  };
  Taro.setNavigationBarTitle({
    title: '小熊书法',
  });
  return (
    <View className='newCalligraphyExperience'>
      {imgList.map((e, i) => {
        return i == 1 ? (
          <View key={i}>
            <Image src={e} className='bannerTop' mode='widthFix'></Image>
          </View>
        ) : (
          <View key={i}>
            <Image src={e} className='banner' mode='widthFix'></Image>
          </View>
        );
      })}
      <View className='none'></View>
      <View className='bottomPay'>
        <View
          onClick={() => {
            openModal();
          }}
          className='payButton'
        >
          <Image src={payButton} className='banner' mode='widthFix'></Image>
        </View>
      </View>
      <Modal status={modalStatus} modalEvent={closeModal} type='zero' />
      <WxLogin subject='WRITE_APP' isIntroduce={false} />
    </View>
  );
}
