import { useShareAppMessage, useRouter } from '@tarojs/taro';
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import sensors from '@/utils/sensors_data';
import banner from '@/assets/calligraphyExperience/banner.png';
import learnWrite from '@/assets/calligraphyExperience/learnWrite.png';
import harvest from '@/assets/calligraphyExperience/harvest.png';
import choice from '@/assets/calligraphyExperience/choice.png';
import pattern from '@/assets/calligraphyExperience/pattern.png';
import team from '@/assets/calligraphyExperience/team.png';
import arrange from '@/assets/calligraphyExperience/arrange.png';
import stage from '@/assets/calligraphyExperience/stage.png';
import problem from '@/assets/calligraphyExperience/problem.png';
import payButton from '@/assets/calligraphyExperience/payButton.png';
import { View, Image } from '@tarojs/components';
import WxLogin from '@/components/wxlogin';
import Modal from './components/modal';
import './index.scss';

export default function CalligraphyExperience() {
  const router = useRouter();
  const dispatch = useDispatch();
  const [modalStatus, setModalStatus] = useState(false);
  const [imgList, setImgList] = useState([
    banner,
    learnWrite,
    harvest,
    choice,
    pattern,
    team,
    arrange,
    stage,
    problem,
  ]);
  dispatch({
    type: 'CHANGE_CHANNELID',
    channelId: router.params.channelId || '2838',
  });

  useShareAppMessage(() => {
    return {
      title: '小熊书法帮助孩子轻松写好字',
      path: `/pages/calligraphyExperience/index?channelId=${router.params.channelId}`,
      imageUrl: banner, //自定义图片路径，可以是本地文件路径、代码包文件路径或者网络图片路径，支持PNG及JPG，不传入 imageUrl 则使用默认截图。显示图片长宽比是 5:4
      success: function(res) {
        // 转发成功之后的回调
        if (res.errMsg == 'shareAppMessage:ok') {
        }
      },
      fail: function() {},
    };
  });
  useEffect(() => {
    sensors.track('sf_Experiencecourse_descriptionpage_view', {
      channel_id: router.params.channelId,
    });
  }, []);
  const openModal = () => {
    setModalStatus(true);
    sensors.track('sf_Experiencecourse_descriptionpage_buybuttonclick', {});
  };
  const closeModal = () => {
    setModalStatus(false);
  };
  return (
    <View className='calligraphyExperience'>
      {imgList.map((e, i) => {
        return (
          <View key={i}>
            <Image src={e} className='banner' mode='widthFix'></Image>
          </View>
        );
      })}
      <View className='none'></View>
      <View
        className='payButton'
        onClick={() => {
          openModal();
        }}
      >
        <Image src={payButton} className='banner' mode='widthFix'></Image>
      </View>
      <Modal status={modalStatus} modalEvent={closeModal} />
      <WxLogin subject='WRITE_APP' isIntroduce={false} />
    </View>
  );
}
