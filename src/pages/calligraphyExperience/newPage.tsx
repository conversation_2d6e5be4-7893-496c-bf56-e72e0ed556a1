import { useShareAppMessage, useRouter } from '@tarojs/taro';
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import sensors from '@/utils/sensors_data';
import banner from '@/assets/calligraphyExperience/newPage01/banner.png';
import experience from '@/assets/calligraphyExperience/newPage01/experience.png';
import harvest from '@/assets/calligraphyExperience/newPage01/harvest.png';
import why from '@/assets/calligraphyExperience/newPage01/why.png';
import animation from '@/assets/calligraphyExperience/newPage01/animation.png';
import major from '@/assets/calligraphyExperience/newPage01/major.png';
import saveWorry from '@/assets/calligraphyExperience/newPage01/saveWorry.png';
import problem from '@/assets/calligraphyExperience/newPage01/problem.png';
import address from '@/assets/calligraphyExperience/newPage01/address.png';
import payButton from '@/assets/calligraphyExperience/newPage01/payButton.gif';
import { View, Image } from '@tarojs/components';
import WxLogin from '@/components/wxlogin';
import Modal from './components/modal';
import './index.scss';

export default function CalligraphyExperience() {
  const router = useRouter();
  const dispatch = useDispatch();
  const [modalStatus, setModalStatus] = useState(false);
  const [imgList] = useState([
    banner,
    experience,
    harvest,
    why,
    animation,
    major,
    saveWorry,
    problem,
    address,
  ]);
  const channel = process.env.NODE_ENV === 'online' ? 11691 : 7801;
  dispatch({
    type: 'CHANGE_CHANNELID',
    channelId: router.params.channelId || router.params.channel || channel,
  });

  useShareAppMessage(() => {
    return {
      title: '小熊书法帮助孩子轻松写好字',
      path: `/pages/calligraphyExperience/index?channelId=${router.params
        .channelId || channel}`,
      imageUrl: banner, //自定义图片路径，可以是本地文件路径、代码包文件路径或者网络图片路径，支持PNG及JPG，不传入 imageUrl 则使用默认截图。显示图片长宽比是 5:4
      success: function(res) {
        // 转发成功之后的回调
        if (res.errMsg == 'shareAppMessage:ok') {
        }
      },
      fail: function() {},
    };
  });
  useEffect(() => {
    sensors.track('sf_Experiencecourse_descriptionpage_view', {
      channel_id: router.params.channelId,
    });
  }, []);
  const openModal = () => {
    setModalStatus(true);
    sensors.track('sf_Experiencecourse_descriptionpage_buybuttonclick', {});
  };
  const closeModal = () => {
    setModalStatus(false);
  };
  return (
    <View className='newCalligraphyExperience'>
      {imgList.map((e, i) => {
        return i == 1 ? (
          <View key={i}>
            <Image src={e} className='bannerTop' mode='widthFix'></Image>
          </View>
        ) : (
          <View key={i}>
            <Image src={e} className='banner' mode='widthFix'></Image>
          </View>
        );
      })}
      <View className='none'></View>
      <View className='bottomPayNew'>
        <View
          onClick={() => {
            openModal();
          }}
          className='payButton'
        >
          <Image src={payButton} className='banner' mode='widthFix'></Image>
        </View>
      </View>
      <Modal status={modalStatus} modalEvent={closeModal} />
      <WxLogin subject='WRITE_APP' isIntroduce={false} isFollow={false} />
    </View>
  );
}
