import Taro from '@tarojs/taro';
import { useEffect, useState } from 'react';
import CheckLevel from '@/utils/checkLevel';
import { useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import sensors from '@/utils/sensors_data';
import { View } from '@tarojs/components';
import { useThirtySixPay } from '@/hooks/payhook/useThirtySixPay';
import { AtFloatLayout } from 'taro-ui';
import Paybtn from '../../thirtySix/components/payBtn/index.weapp';

import './modal.scss';

export default function Modal(props: any) {
  const [sup, SetSup] = useState('S1');
  const [isZero] = useState(props.type === 'zero');
  const [payPageData] = useState<object | null>({
    bgcolor: '#FF9C00',
    label: 'DEFAULT',
    sup,
    fit: `刚刚会拿画笔、初步认识颜色`,
    range: '学习重点：点线涂鸦 | 兴趣探究 | 趣味手工',
    courseday: '0',
    period: 0,
    addTeacher: true,
  });
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  //channelId
  const channelId = useSelector((state: UserStateType) => state.channelId);
  const [hasBuyLevel, setHasBuyLevel] = useState<any[]>([]);

  const payMessage = useThirtySixPay({
    topicId: '3',
    packagesId: '124',
    isIntroduce: false,
    subject: 'WRITE_APP',
    pType: 'calligraphy',
    payPageData,
    isZero,
  });
  const authSuccess = res => {
    if (!sup) {
      return;
    }
    sensors.track('sf_Experiencecourse_authorizedlayer_buttonclick', {
      is_authorization: '允许',
    });
    res.sup = sup;
    payMessage.authSuccess(res);
  };

  const authError = () => {
    sensors.track('sf_Experiencecourse_authorizedlayer_buttonclick', {
      is_authorization: '拒绝',
    });
    Taro.showToast({
      title: `${isZero ? '领取失败' : '支付失败'}`,
      icon: 'none',
    });
  };

  const filterBuyLevel = usup => {
    return (
      hasBuyLevel &&
      hasBuyLevel.length > 0 &&
      hasBuyLevel.findIndex(v => v == usup) > -1
    );
  };

  useEffect(() => {
    new CheckLevel({
      userId,
      channelId,
      orderId: '', //先选等级没有orderId
      regtype: 'EXPERIENCE', //书法不分单双周 默认EXPERIENCE
      subjects: 'WRITE_APP',
    })
      .initCheck()
      .then((res: any[]) => {
        setHasBuyLevel(res);
        if (res.length === 1) {
          if (res.indexOf('S1') > -1) {
            SetSup('S2');
          }
        } else if (res.length == 2) {
          SetSup('');
        }
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId, channelId]);

  return (
    <View className='modal'>
      <AtFloatLayout
        isOpened={props.status}
        title='选择级别'
        onClose={() => {
          props.modalEvent();
        }}
      >
        <View className='modalCenter'>
          <View className='modalTitle'>
            为保证本产品使用体验，请您根据下方提示正确选择类别
            {/* <View className='modalSubTitle'>(适合6岁以上年龄孩子)</View> */}
          </View>
          <View className={!!filterBuyLevel('S1') ? 'hasSelected' : ''}>
            <View
              className={sup == 'S1' ? 'modalLevelSelect' : 'modalLevel'}
              onClick={() => {
                if (!!filterBuyLevel('S1')) {
                  return;
                }
                SetSup('S1');
              }}
            >
              {!!filterBuyLevel('S1') && (
                <View className='selected-mark'></View>
              )}
              <View className='modalLevelTitle'>基础版</View>
              <View className='modalLevelFit'>适合幼小衔接-1年级孩子</View>
              {sup == 'S1' && !filterBuyLevel('S1') ? (
                <View className='ModalSelect'></View>
              ) : null}
            </View>
          </View>
          <View className={!!filterBuyLevel('S2') ? 'hasSelected' : ''}>
            <View
              className={sup == 'S2' ? 'modalSeniorSelect' : 'modalLeveSenior'}
              onClick={() => {
                if (!!filterBuyLevel('S2')) {
                  return;
                }
                SetSup('S2');
              }}
            >
              {!!filterBuyLevel('S2') && (
                <View className='selected-mark'></View>
              )}
              <View className='modalLeveSeniorTitle'>高阶版</View>
              <View className='modalLeveSeniorFit'>适合2-3年级孩子</View>
              {sup == 'S2' && !filterBuyLevel('S2') ? (
                <View className='ModalSelect'></View>
              ) : null}
            </View>
          </View>
        </View>
        <View className='modaPay'>
          <View className='modaPayText'>
            <View className='modaPayBigText'>{isZero ? 0 : 0.1} </View>
            <View className='modaPayBigWord'>元/</View>
            <View className='modaPayBigText'>5</View>
            <View className='modaPayBigWord'>节</View>
          </View>
          <View>
            <Paybtn
              className='modaPayButton'
              authError={authError}
              authSuccess={authSuccess}
              btnName='确认支付'
            ></Paybtn>
          </View>
        </View>
      </AtFloatLayout>
    </View>
  );
}
