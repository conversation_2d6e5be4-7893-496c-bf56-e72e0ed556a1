.modal {
  padding-bottom: 20px;
  .layout-header__title {
    text-align: left;
    padding: 0;
    font-size: 36px;
    font-weight: 500;
    color: #333333;
  }
  .at-float-layout .layout-body {
    padding: 0;
  }
  .at-float-layout .layout-body__content {
    padding-bottom: 10px;
    overflow: hidden;
  }
  .modalCenter {
    width: 670px;
    margin: 0 auto;
  }
  .modalTitle {
    padding: 20px 0 35px 0;
    font-size: 26px;
    color: #666666;
    display: flex;
    .modalSubTitle {
      color: #fd2205;
    }
  }
  .modalLevel {
    width: 670px;
    height: 157px;
    border: 2px solid #f6f6f6;
    box-shadow: 0px 6px 15px 1px rgba(39, 39, 39, 0.13);
    border-radius: 30px;
    display: flex;
    align-items: center;
  }

  .hasSelected {
    .modalLevelSelect,
    .modalLevel,
    .modalSeniorSelect,
    .modalLeveSenior {
      border: 1px solid #eaeaea;
      box-shadow: 0px 0px 13px 0px rgba(177, 177, 177, 0.26);
      position: relative;
    }
    .selected-mark {
      width: 109px;
      height: 92px;
      position: absolute;
      top: -2px;
      right: 0;
      background-image: url('../../../assets/thirtySix/chooseLevel/hasbuy.png');
      background-repeat: no-repeat;
      background-position: left top;
      background-size: contain;
    }
    .modalLevelTitle,
    .modalLeveSeniorTitle {
      background: #eaeaea;
      color: #ffffff;
    }
    .modalLevelFit,
    .modalLeveSeniorFit {
      color: #eaeaea;
    }
  }
  .modalLevelSelect {
    width: 670px;
    height: 157px;
    border: 2px solid #ffa82d;
    box-shadow: 0px 6px 15px 1px rgba(39, 39, 39, 0.13);
    border-radius: 30px;
    display: flex;
    align-items: center;
    position: relative;
  }
  .ModalSelect {
    width: 55px;
    height: 41px;
    border: 2px solid #ffa82d;
    position: absolute;
    top: 0;
    right: 0;
    background: #ffa82d;
    border-radius: 0 20px 0px 20px;
  }
  .ModalSelect::after {
    content: '';
    position: absolute;
    right: 12px;
    top: 1px;
    width: 25px;
    height: 12px;
    border: 6px solid #ffffff;
    border-radius: 2px;
    border-top: none;
    border-right: none;
    background: transparent;
    transform: rotate(-45deg);
  }

  .modalLevelTitle {
    width: 183px;
    height: 85px;
    background: linear-gradient(0deg, #ffa92d, #ff7c00);
    font-size: 40px;
    font-weight: 500;
    color: #ffffff;
    display: flex;
    align-items: center;
    border-radius: 0 50px 50px 0;
  }
  .modalLevelFit {
    font-size: 40px;
    font-weight: 500;
    color: #333333;
    padding-left: 31px;
  }

  .modalLeveSenior {
    width: 670px;
    height: 157px;
    border: 2px solid #f6f6f6;
    box-shadow: 0px 6px 15px 1px rgba(39, 39, 39, 0.13);
    border-radius: 30px;
    display: flex;
    align-items: center;
    margin-top: 32px;
  }
  .modalSeniorSelect {
    width: 670px;
    height: 157px;
    border: 2px solid #ffa82d;
    box-shadow: 0px 6px 15px 1px rgba(39, 39, 39, 0.13);
    border-radius: 30px;
    display: flex;
    align-items: center;
    margin-top: 32px;
    position: relative;
  }
  .modalLeveSeniorTitle {
    width: 183px;
    height: 85px;
    background: linear-gradient(0deg, #93e749, #7dc83c);
    font-size: 40px;
    font-weight: 500;
    color: #ffffff;
    display: flex;
    align-items: center;
    border-radius: 0 50px 50px 0;
  }
  .modalLeveSeniorFit {
    font-size: 40px;
    font-weight: 500;
    color: #333333;
    padding-left: 31px;
  }

  .modaPay {
    width: 100%;
    height: 108px;
    margin-top: 60px;
    border: 2px solid #f6f6f6;
    box-shadow: 0px 6px 15px 1px rgba(39, 39, 39, 0.13);
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .modaPayText {
      display: flex;
      font-weight: 500;
      color: hsl(0, 0%, 20%);
      padding-left: 16px;
      align-items: baseline;
      .modaPayBigText {
        font-size: 70px;
        font-family: PingFang SC;
        font-weight: 500;
        color: #ff8004;
      }
      .modaPayBigWord {
        font-size: 40px;
        font-family: PingFang SC;
        font-weight: 200;
        color: #ff8004;
      }
    }
    .modaPayButton {
      width: 290px;
      height: 110px;
      background: linear-gradient(0deg, #ff8d2d, #f76332);
      border-radius: 40px 0 0 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 40px;
      font-weight: 500;
      color: #ffffff;
      padding: 0 !important;
      margin: 0 !important;
    }
  }
}
