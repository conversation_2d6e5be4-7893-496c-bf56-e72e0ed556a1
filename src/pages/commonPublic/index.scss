@import '@/theme/groupbuy/common.scss';

.index {
  font-size: 0;
  position: relative;
  background: url('https://fe-cdn.xiaoxiongmeishu.com/ai-mp-user/image/public-bg.png')
    no-repeat 0 0 /100%;
  width: 100%;
  height: 100vh;
  &.carve {
    background: url('https://fe-cdn.xiaoxiongmeishu.com/ai-mp-user/image/account-01.png')
      no-repeat 0 0 /100%;
  }
  .c-r-img {
    position: absolute;
    left: 0;
    right: 0;
    width: 626px;
    height: 112px;
    margin: auto;
    top: 90px;
  }
  .qrcode {
    width: 336px;
    height: 336px;
    background-color: #ccc;
    position: absolute;
    top: 498px;
    left: 0;
    right: 0;
    margin: 0 auto;
    border-radius: 20px;
    overflow: hidden;
    &.carve-code {
      top: 590px;
    }
    image {
      width: 100%;
      height: 100%;
    }
  }
}
