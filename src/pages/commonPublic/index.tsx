import { judgeMinienv } from '@/utils/auth';
import { useEffect, useState } from 'react';
import Taro, { useRouter } from '@tarojs/taro';
import { getWeixinParamQrcodeImageUrlApi } from '@/api/groupbuy';
import { View, Image } from '@tarojs/components';
import TimeDown from '../assist/compontent/countDown';
// import account from '../../assets/assist/account-t.png';
// @ts-ignore

import './index.scss';

export default () => {
  const router = useRouter();
  let params = router.params;
  // 区分瓜分还是答题  1答题
  const from = params.from ? +params.from : 1; // 0、瓜分，1 是答题
  const [isAq] = useState(Number(params.from) === 1);
  // 二维码
  const [qrcodeNo, setQrcodeNo] = useState('');
  const setTitle = (title: string) => {
    Taro.setNavigationBarTitle({ title });
  };

  const getWeixinParamQrcodeImageUrl = () => {
    getWeixinParamQrcodeImageUrlApi({
      uid: params.uid || '',
      type: params.type ? +params.type : 5,
      appsubject: 'ART_APP',
    }).then(res => {
      if (res.code === 0) {
        setQrcodeNo(res.payload);
      }
    });
  };
  useEffect(() => {
    getWeixinParamQrcodeImageUrl();
    setTitle(isAq ? '小熊艺术乐园' : '瓜分千万小熊币');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params]);
  return (
    <View
      className={
        isAq
          ? 'index container w100 relative'
          : 'carve index container w100 relative'
          ? `${judgeMinienv('release') && 'index'} container w100 relative`
          : `carve ${judgeMinienv('release') &&
              'index'} container w100 relative`
      }
    >
      {!isAq && (
        <>
          {/* <Image className='c-r-img' src={account}></Image> */}
          <TimeDown></TimeDown>
        </>
      )}

      {/* 头部导航栏 */}
      <View className={isAq ? 'qrcode' : 'qrcode carve-code'}>
        <Image src={qrcodeNo} mode='widthFix' showMenuByLongpress />
      </View>
    </View>
  );
};
