import { WebView } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { useEffect } from 'react';

const Indexwebview = () => {
  const router = useRouter();
  var url;
  url = router.params.url;
  url = decodeURIComponent(url);
  const handleOnLoad = () => {
    Taro.hideLoading();
  };

  useEffect(() => {
    Taro.showLoading({ title: '加载中...' });
  }, []);

  return <WebView src={url} onLoad={handleOnLoad} />;
};

export default Indexwebview;
