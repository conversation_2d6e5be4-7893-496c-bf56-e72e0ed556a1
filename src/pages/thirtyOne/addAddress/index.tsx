import Taro, { useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import { View, Image } from '@tarojs/components';
import DefaultAddress from '@/components/defaultAddress';
import { AtForm, AtInput, AtButton, AtTextarea, AtFloatLayout } from 'taro-ui';
import { useSelector } from 'react-redux';
import {
  getCenterAddressList,
  getAddressTownList,
  createUserAddress,
  updateUserAddress,
  createAddressForExpressAndOrder,
} from '@/api/groupbuy';
import sensors from '@/utils/sensors_data';
import arrowRight from '@/assets/groupbuy/index/arrow-right.png';
import chooseRight from '@/assets/groupbuy/addAddress/chooseCity.png';
import { UserStateType } from '@/store/groupbuy/state';
import './index.scss';

export default function AddAddress() {
  // 获取路由挂参
  const router = useRouter();
  let params = router.params;
  // orderId 或是传过来 或是redux中存储获取
  const { orderId } = params;
  const orderid = useSelector((state: UserStateType) => state.orderId);
  const userId = useSelector((state: UserStateType) => state.userid);
  const storeMobile = useSelector((state: UserStateType) => state.mobile);
  const [receiver, setReceiver] = useState<string>('');
  const [mobile, setMobile] = useState<string>(storeMobile);
  const [address, setAddress] = useState<string>('');
  const [details, setDetails] = useState<string>('');
  const [province, setProvince] = useState('');
  const [telAreaCode, setTelAreaCode] = useState('');
  const [city, setCity] = useState('');
  const [area, setArea] = useState('');
  const [street, setStreet] = useState('');
  const [currentTab, setcurrentTab] = useState(0);
  const [tabList] = useState(['请选择']);
  const [addressList, setAddressList] = useState<any>([]);
  const [cityList, setCityList] = useState<any>([]);
  const [county, setCounty] = useState<any>([]);
  const [town, setTown] = useState<any>([]);
  const [isOpen, setIsOpen] = useState(false);
  // 添加默认地址弹窗
  const [addressId, setAddressId] = useState('');
  const [defaultAddressModal, setDefaultAddressModal] = useState({
    show: false,
    type: '1',
    isCheck: true,
    isChange: false,
    newAddress: false,
  });
  const [defaultAddressData, setDefaultAddressData] = useState({
    addressId: '',
    telAreaCode: '',
    province: '',
    city: '',
    area: '',
    street: '',
    addressData: '',
    addressDetail: '',
    name: '',
    phone: '',
  });
  let timer: any = null;

  let lock = true;

  useEffect(() => {
    getCenterAddressList().then(res => {
      const { data } = res.payload;
      setAddressList(data);
    });
    return () => {
      timer && clearTimeout(timer);
    };
  }, [timer]);
  //保存地址接口
  function onSubmit() {
    sensors.track('xxms_testcourse_addresspage_Confirmsubmitbuttonclick', {});

    const flag = vuerify();
    if (vuerifyaddress() && flag) {
      setDefaultAddressModal({
        show: true,
        type: '2',
        isCheck: true,
        isChange: false,
        newAddress: false,
      });
    } else {
      if (flag) {
        if (lock) {
          lock = false;
          Taro.showLoading({ title: '加载中...' });
          createNewAddress().then((resAddress: any) => {
            createAddressForExpressAndOrder({
              userId,
              orderId: orderId || orderid || '',
              addressId: resAddress.addressId,
            })
              .then(res => {
                lock = true;
                console.log(res);
                Taro.hideLoading();
                if (res.code === 0) {
                  let _url = '';
                  if (process.env.TARO_ENV === 'weapp') {
                    _url = `/pages/groupbuy/addTeacher/index?uid=${userId}&type=1`;
                    // 非groupbuy下的落地页，跳转follow下的引导页，type=1是添加老师，非1是关注公众号
                    if (params.type && params.type === 'new')
                      _url = `/pages/launch/follow/index?uid=${userId}&type=1`;
                  } else if (['alipay', 'tt'].includes(process.env.TARO_ENV)) {
                    _url = `/pages/thirtyOne/addNewTeacher/index`;
                  }

                  Taro.reLaunch({
                    url: _url,
                  });
                } else {
                  Taro.showToast({
                    title: res.errors || '服务器开小差了',
                    icon: 'none',
                    duration: 2000,
                  });
                }
              })
              .catch(() => {
                lock = true;
                Taro.hideLoading();
              });
          });
        }
      }
    }
  }
  useEffect(() => {
    if (street) {
      province !== city
        ? setAddress(`${province}/${city}/${area}/${street}`)
        : setAddress(`${province}/${area}/${street}`);
    } else if (province && city && area) {
      province !== city
        ? setAddress(`${province}/${city}/${area}`)
        : setAddress(`${province}/${area}`);
    }
  }, [province, city, area, street]);

  function onReceiver(res) {
    clearTimeout(timer);
    timer = setTimeout(() => {
      setReceiver(res);
    }, 400);
  }
  function onMobile(res) {
    clearTimeout(timer);
    timer = setTimeout(() => {
      setMobile(res);
    }, 400);
  }
  function onDetails(res) {
    clearTimeout(timer);
    timer = setTimeout(() => {
      setDetails(res);
    }, 400);
  }
  function onAddressInput() {}

  function vuerify() {
    const errors: Array<String> = [];

    if (!receiver) {
      errors.push('Name required.');
      Taro.showToast({
        title: '请输入收货人姓名',
        icon: 'none',
        duration: 2000,
      });
    } else if (!mobile) {
      errors.push('Phone required.');
      Taro.showToast({
        title: '请输入手机号码',
        icon: 'none',
        duration: 2000,
      });
    } else if (!validPhone(mobile)) {
      errors.push('Valid phone required.');
      Taro.showToast({
        title: '请输入正确的手机号码',
        icon: 'none',
        duration: 2000,
      });
    } else if (!address) {
      errors.push('province required.');
      Taro.showToast({
        title: '请选择省市区',
        icon: 'none',
        duration: 2000,
      });
    } else if (!details) {
      errors.push('detail required.');
      Taro.showToast({
        title: '请输入详细地址',
        icon: 'none',
        duration: 2000,
      });
    }

    if (!errors.length) {
      return true;
    }
  }
  function validPhone(phone) {
    if (phone.indexOf('*') > -1) {
      return phone.length == 11;
    }
    const re = /^1[3456789]\d{9}$/;
    return re.test(phone);
  }
  const handleClick = value => {
    setcurrentTab(value);
  };
  const openArea = () => {
    setIsOpen(true);
  };
  const closeArea = () => {
    setIsOpen(false);
  };
  const handleClickProvince = item => {
    tabList[0] = item.provinceName;
    tabList[1] = '请选择';
    tabList[2] = '';
    tabList[3] = '';
    setTelAreaCode(item.telAreaCode);
    setProvince(item.provinceName);
    setcurrentTab(1);
    setCityList(item.citys);
  };
  const handleClickCity = item => {
    tabList[1] = item.cityName;
    tabList[2] = '请选择';
    tabList[3] = '';
    setCity(item.cityName);
    setcurrentTab(2);
    setCounty(item.countys);
  };
  const handleClickRegion = item => {
    tabList[2] = item.countyName;
    tabList[3] = '请选择';
    setArea(item.countyName);
    setcurrentTab(3);
    getAddressTownList({ code: item.countyCode }).then(res => {
      let data = res.payload;
      data.unshift({ townName: '暂不选择' });
      setTown(data);
    });
  };
  const handleClickTown = item => {
    setcurrentTab(4);
    if (item.townName !== '暂不选择') {
      tabList[3] = item.townName;
      setStreet(item.townName);
    } else {
      tabList[3] = '请选择';
      setStreet('');
    }
    setcurrentTab(3);
    setIsOpen(false);
  };

  // 默认地址与页面数据关联
  function variableAssignment(onlysetId: boolean, data: any) {
    if (!onlysetId) {
      setReceiver(data.name);
      setMobile(data.phone);
      setAddress(data.addressData);
      setDetails(data.addressDetail);
      setProvince(data.province);
      setTelAreaCode(data.telAreaCode);
      setCity(data.city);
      setArea(data.area);
      setStreet(data.street);
      setDefaultAddressData(data);
    }
    setAddressId(data.addressId);
  }

  //校验默认地址是否修改 或者未使用默认地址
  function vuerifyaddress() {
    if (!addressId) {
      return false;
    }
    let isNeedCheck = true;
    if (defaultAddressModal.isCheck) {
      if (
        receiver === defaultAddressData.name &&
        mobile === defaultAddressData.phone &&
        details === defaultAddressData.addressDetail &&
        province === defaultAddressData.province &&
        city === defaultAddressData.city &&
        area === defaultAddressData.area &&
        street === defaultAddressData.street &&
        telAreaCode === defaultAddressData.telAreaCode
      ) {
        isNeedCheck = false;
      }
    } else {
      isNeedCheck = false;
    }

    return isNeedCheck;
  }
  // 新建地址
  function createNewAddress() {
    return new Promise((resolve, reject) => {
      // 判断是否有默认地址或者修改了默认地址
      if (!addressId || defaultAddressModal.newAddress) {
        createUserAddress({
          subject: 'ART_APP',
          userId: userId,
          receiptName: receiver,
          receiptTel: mobile,
          province: province,
          city: city,
          area: area,
          street: street,
          addressDetail: details,
          telAreaCode: telAreaCode,
          areaCode: '',
          idCode: '',
          isDefault: addressId ? '0' : '1',
        })
          .then(res => {
            if (res.code === 0) {
              resolve({ addressId: res.payload.id });
            } else {
              reject(res);
            }
          })
          .catch(error => {
            reject(error);
          });
      } else if (defaultAddressModal.isChange) {
        updateUserAddress({
          addressId: addressId,
          userId: userId,
          receiptName: receiver,
          receiptTel: mobile,
          province: province,
          city: city,
          area: area,
          street: street,
          addressDetail: details,
          telAreaCode: telAreaCode,
          areaCode: '',
          idCode: '',
          isDefault: '1',
        }).then(res => {
          if (res.code === 0) {
            resolve({ addressId: res.payload.id });
          } else {
            setDefaultAddressModal({
              ...defaultAddressModal,
              isCheck: true,
              isChange: false,
            });
            reject(res);
          }
        });
      } else {
        // 使用默认地址可以直接关联订单生成物流信息
        resolve({ addressId: addressId });
      }
    });
  }

  useEffect(() => {
    defaultAddressModal.isChange && onSubmit();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultAddressModal.isChange]);

  return (
    <View className='add-address'>
      <DefaultAddress
        setDefaultAddressModal={setDefaultAddressModal}
        defaultAddressModal={defaultAddressModal}
        variableAssignment={variableAssignment}
      />
      <View className='head-tips'>
        <View>恭喜您购课成功</View>
      </View>

      <View className='form-card'>
        <View className='form-title'>填写收货地址</View>
        <View className='form-desc'>确保孩子学习前能收到随材礼盒！</View>
        <AtForm onSubmit={onSubmit}>
          <AtInput
            required
            name='receiver'
            title='收货人'
            type='text'
            placeholder='请输入收货人姓名'
            value={receiver}
            onChange={onReceiver}
          />
          <AtInput
            required
            name='mobile'
            type='phone'
            title='手机号码'
            maxlength={11}
            placeholder='请输入手机号码'
            value={mobile}
            onChange={onMobile}
          ></AtInput>
          <View className='region'>
            <AtInput
              required
              name='address'
              type='text'
              title='地区'
              placeholder='省市区'
              value={address}
              editable={false}
              onChange={onAddressInput}
              onClick={openArea}
            >
              <View>
                <Image src={arrowRight} className='arrow' />
              </View>
            </AtInput>
          </View>
          <View className='addr-textarea'>
            <View className='addr-left'>详细地址</View>
            <AtTextarea
              className={isOpen ? 'textareaHide' : ''}
              count={false}
              value={details}
              onChange={onDetails}
              maxLength={200}
              height={160}
              placeholder='如街道、小区门牌号等'
            />
          </View>

          <View className='foot-box'>
            <AtButton className='foot-btn' formType='submit' onClick={onSubmit}>
              确认提交
            </AtButton>
          </View>
        </AtForm>
        {/* </View> */}
      </View>

      {isOpen && (
        <AtFloatLayout
          isOpened={isOpen}
          onClose={closeArea}
          title='请选择所在地区'
        >
          <View className='area-box'>
            <View className='area-tabs'>
              {tabList.map((item, index) => {
                return (
                  <View
                    className={`area-tabs-item ${
                      currentTab === index ? 'active' : ''
                    }`}
                    key={`tab-${index}`}
                    onClick={() => handleClick(index)}
                  >
                    {item}
                  </View>
                );
              })}
            </View>
            <View className='area-list'>
              <View
                className={`area-province ${
                  currentTab === 0 ? 'active' : 'hide'
                }`}
              >
                {addressList.map((item, index) => {
                  return (
                    <View
                      className='area-list-item'
                      key={`area-province${index}`}
                      onClick={() => handleClickProvince(item)}
                    >
                      {item.provinceName === province && (
                        <Image src={chooseRight} className='choose-right' />
                      )}
                      {item.provinceName}
                    </View>
                  );
                })}
              </View>
              <View
                className={`area-city ${currentTab === 1 ? 'active' : 'hide'}`}
              >
                {cityList.map((item, index) => {
                  return (
                    <View
                      className='area-list-item'
                      key={`area-city${index}`}
                      onClick={() => handleClickCity(item)}
                    >
                      {item.cityName === city && (
                        <Image src={chooseRight} className='choose-right' />
                      )}
                      {item.cityName}
                    </View>
                  );
                })}
              </View>
              <View
                className={`area-region ${
                  currentTab === 2 ? 'active' : 'hide'
                }`}
              >
                {county.map((item, index) => {
                  return (
                    <View
                      className='area-list-item'
                      key={`area-region${index}`}
                      onClick={() => handleClickRegion(item)}
                    >
                      {item.countyName === area && (
                        <Image src={chooseRight} className='choose-right' />
                      )}
                      {item.countyName}
                    </View>
                  );
                })}
              </View>
              <View
                className={`area-street ${
                  currentTab === 3 ? 'active' : 'hide'
                }`}
              >
                {town.map((item, index) => {
                  return (
                    <View
                      className='area-list-item'
                      key={`area-street${index}`}
                      onClick={() => handleClickTown(item)}
                    >
                      {item.townName === street && (
                        <Image src={chooseRight} className='choose-right' />
                      )}
                      {item.townName}
                    </View>
                  );
                })}
              </View>
            </View>
          </View>
        </AtFloatLayout>
      )}

      <View className='tips-bottom'>
        <View className='title'>温馨提示</View>
        <View className='content'>
          礼盒会在3-5个工作日内邮寄到货，发出后会短信通知，您可以下载小熊艺术APP，用购课手机号登录，在【我的-订单物流】中查询物流进度。
        </View>
      </View>
    </View>
  );
}
AddAddress.config = {
  navigationBarTitleText: '购课成功',
};
