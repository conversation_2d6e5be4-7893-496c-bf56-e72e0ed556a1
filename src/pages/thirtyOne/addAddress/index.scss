@import '../../../theme/groupbuy/common.scss';

.add-address {
  min-height: calc(100vh - 112px - env(safe-area-inset-bottom));
  padding-bottom: calc(112px + env(safe-area-inset-bottom));
  font-family: PingFangSC-Medium, PingFang SC;
  background: #f7f7f7;
  overflow-x: hidden;
  .layout-header {
    background: $white;
    padding: 40px 0;
    .layout-header__title {
      font-size: $font-30;
      padding-left: 30px;
      font-weight: 400;
    }
  }
  .layout {
    height: 80%;
  }
  .textareaHide {
    display: none;
  }
  .area-box {
    padding-top: 60px;
    .area-tabs {
      width: 100%;
      position: fixed;
      top: 130px;
      padding-left: 15px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      border-bottom: 1px solid #f5f5f5;
      background-color: #fff;
      transform: translateZ(0);
      -webkit-overflow-scrolling: touch;
      .active {
        color: #ff9c00;
      }
      .area-tabs-item {
        margin: 10px 0 20px 30px;
        &:first-child {
          margin-left: 0;
        }
      }
    }
    .area-list {
      .choose-right {
        width: 25px;
        height: 18px;
        margin-right: 10px;
      }
      margin-left: 15px;
      .active {
        display: block;
      }
      .hide {
        display: none;
      }
      .area-list-item {
        margin-top: 40px;
        &:last-child {
          margin-bottom: 40px;
        }
      }
    }
  }
  // .head-box {
  //   width: 100%;
  //   height: 350px;
  //   margin-bottom: $font-32;
  //   text-align: center;
  //   background: $white;
  //   .right-img {
  //     width: 80px;
  //     height: 80px;
  //     padding-top: 44px;
  //   }
  //   .buy-success {
  //     height: 48px;
  //     padding-top: $font-28;
  //     margin-bottom: $font-32;
  //     color: $dim-gray;
  //     font-size: $font-34;
  //     font-weight: 500;
  //     line-height: 48px;
  //   }
  //   .tip {
  //     height: 33px;
  //     margin-top: 8px;
  //     color: $light-grey;
  //     font-size: 24px;
  //     font-weight: 400;
  //     line-height: 33px;
  //   }
  //   .color-red {
  //     color: #ff6a00;
  //   }
  // }
  .head-tips {
    height: 80px;
    background: #76de73;
    view {
      width: fit-content;
      font-size: 34px;
      font-weight: bold;
      color: #ffffff;
      line-height: 80px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      &::before {
        display: block;
        content: '';
        width: 38px;
        height: 38px;
        background: url('../../../assets/groupbuy/addAddress/icon-success.png')
          no-repeat;
        background-size: 100%;
        background-position: center;
        margin-right: 16px;
      }
    }
  }

  .tips-bottom {
    padding: 0 110px 60px;
    box-sizing: border-box;
    .title {
      height: 46px;
      font-size: 32px;
      color: #727272;
      line-height: 46px;
      text-align: center;
      margin-bottom: 18px;
    }
    .content {
      font-size: 24px;
      color: #b4b4b4;
      line-height: 32px;
    }
  }

  .form-card {
    height: 842px;
    background: #ffffff;
    border-radius: 40px;
    margin: 40px 32px;
    padding: 53px 0 48px 20px;
    box-sizing: border-box;
    overflow: hidden;
    .form-title {
      width: fit-content;
      height: 56px;
      font-size: 40px;
      font-weight: bold;
      color: #333333;
      line-height: 56px;
      display: flex;
      align-items: center;
      margin: 0 auto;
      &::before {
        display: block;
        content: '';
        width: 45px;
        height: 20px;
        background: url('../../../assets/groupbuy/addAddress/form-title-left.png')
          center/100% no-repeat;
        margin-right: 22px;
      }
      &::after {
        display: block;
        content: '';
        width: 45px;
        height: 20px;
        background: url('../../../assets/groupbuy/addAddress/form-title-right.png')
          center/100% no-repeat;
        margin-left: 22px;
      }
    }
    .form-desc {
      width: fit-content;
      height: 70px;
      line-height: 70px;
      text-align: center;
      margin: 24px auto 48px;
      background: #fff9f1;
      border-radius: 35px;
      font-size: 28px;
      color: #ff7000;
      padding: 0 28px 0 44px;
      box-sizing: border-box;
      color: #ff7000;
    }
    .birthday-css {
      border-top: 1px solid #e6eef5;
      &::after {
        border: none;
      }
      .at-input--disabled {
        opacity: 1;
      }
    }
    margin-bottom: 40px;
    .at-textarea {
      border: 0;
      margin-left: 14px;
      padding-top: 14px;
      position: relative;
      z-index: 11;
    }
    .arrow {
      width: 12px;
      height: 22px;
      padding: 0px;
      margin-right: 10px;
    }
    .mobile-text {
      color: $light-grey;
      font-size: $font-30;
      padding-right: 16px;
      padding-left: 0px;
    }
    .region {
      .at-input--disabled {
        opacity: 1;
      }
    }
    .addr-textarea {
      display: flex;
      padding-left: 16px;
      box-sizing: border-box;
      color: #333;
      .addr-left {
        margin: 26px 8px 0 16px;
        width: 172px;
        font-size: 32px;
        vertical-align: middle;
        text-align: left;
        color: #555;
        &::before {
          display: inline-block;
          margin-right: 8px;
          color: #ff4949;
          font-size: 28px;
          font-family: SimSun, sans-serif;
          line-height: 1;
          content: '*';
        }
      }
      .at-textarea {
        flex: 1;
        margin: 0;
        padding: 13px 20px 0 0;
        margin-left: -1px;
      }
    }
    .at-input__children {
      &::after {
        border: 0;
      }
    }
    .at-form::after {
      border: 0;
    }
    .at-input {
      height: 100px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
    }
    .at-input__title {
      color: #555;
      font-weight: normal;
    }
    .at-input__input {
      color: #333;
    }
    .at-textarea__textarea {
      color: #333;
      font-weight: normal !important;
    }
  }
  // .Tips {
  //   padding-right: 105px;
  //   padding-left: 105px;
  //   text-align: center;
  //   margin-bottom: 60px;
  //   .Tips-title {
  //     height: 45px;
  //     margin-bottom: 17px;
  //     color: $silver;
  //     font-size: 32px;
  //     font-weight: 400;
  //     line-height: 45px;
  //   }
  //   .Tips-text {
  //     height: 99px;
  //     color: $light-grey;
  //     font-size: 24px;
  //     font-weight: 400;
  //     line-height: 40px;
  //     text-align: justify;
  //   }
  // }
  // .foot-box {
  //   width: 100%;
  //   height: 112px;
  //   background: $white;
  //   box-shadow: 0px -3px 11px 0px rgba(0, 0, 0, 0.06);
  //   display: flex;
  //   justify-content: center;
  //   align-items: center;
  //   position: fixed;
  //   left: 0;
  //   bottom: 0;
  //   .foot-btn {
  //     width: 638px;
  //     height: 72px;
  //     background: $red-to-yellow;
  //     border-radius: 40px;
  //     text-align: center;
  //     color: $white;
  //     line-height: 72px;
  //   }
  // }
  .foot-box {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 144px;
    padding-bottom: calc(env(safe-area-inset-bottom) * 0.5);
    background-color: #fff;
    .foot-btn {
      border: 0;
      width: 100%;
      margin: 0 30px;
      height: 96px;
      line-height: 96px;
      text-align: center;
      background: linear-gradient(90deg, #ff9c31 0%, #ff5d31 100%);
      border-radius: 52px;
      color: #fff;
      font-size: 36px;
      font-weight: bold;
    }
  }
}
