import { useState, useEffect } from 'react';
import { useRouter } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { supAgeInterval, supSwitch } from '@/api/groupbuy';
import { useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import CheckLevel from '@/utils/checkLevel';
import './index.scss';

export default function Index(props) {
  // 选择卡片后通知父组件显示订单浮窗
  const { watchShowOrder } = props;
  // 选择激活的卡片
  const [tabActive, setTabActive] = useState<number>(-1);
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  const router = useRouter();
  let params = router.params;
  const { orderId } = params;
  const orderid = useSelector((state: UserStateType) => state.orderId);
  //channelId
  const channelId = useSelector((state: UserStateType) => state.channelId);
  const [hasBuyLevel, setHasBuyLevel] = useState<any[]>([]);

  const [level, setLevel] = useState<any>([]);
  const [title, setTitle] = useState<string>('');
  const [supSwitchType, setSupSwitchType] = useState<any>(false);
  useEffect(() => {
    supSwitch()
      .then(res => {
        setSupSwitchType(res.payload);
        if (res.payload) {
          newLevel();
        } else {
          oldLevel();
        }
      })
      .catch(() => {
        newLevel();
      });
  }, []);
  const oldLevel = () => {
    const levelArray = [
      {
        bgcolor: '#ff9c00',
        label: 'S1',
        fit: '3-4.5周岁宝贝',
        range: '2015年8月～2017年7月底出生',
        desc: 'S1级别是孩子成长的美术涂鸦阶段，学习重点是感受与触动',
        courseday: '0',
        period: 0,
      },
      {
        bgcolor: '#91dc4b',
        label: 'S2',
        fit: '4.5-7周岁宝贝',
        range: '2013年8月～2015年7月底出生',
        desc: 'S2级别是孩子美术学习的的启蒙阶段，学习重点是认知与联想',
        courseday: '0',
        period: 0,
      },
      {
        bgcolor: '#30baea',
        label: 'S3',
        fit: '7-8周岁宝贝',
        range: '2012年8月～2013年7月底出生',
        desc: 'S3级别是孩子美术学习的的中级阶段，学习重点是创意与表达',
        courseday: '0',
        period: 0,
      },
    ];
    supAgeInterval().then(res => {
      levelArray.map((item, index) => {
        item.range = res.payload['S' + (index + 1)];
      });
      setLevel(levelArray);
    });
    setTitle('为保证孩子的学习体验，请正确选择孩子的年龄');
  };
  const newLevel = () => {
    let newArray = [
      {
        bgcolor: '#ff9c00',
        label: 'S1',
        fit: `刚刚会拿画笔、初步认识颜色`,
        range: '学习重点：点线涂鸦 | 兴趣探究 | 趣味手工',
        desc: 'S1级别是孩子成长的美术涂鸦阶段，学习重点是感受与触动',
        courseday: '0',
        period: 0,
      },
      {
        bgcolor: '#91dc4b',
        label: 'S2',
        fit: `适合零基础或少量绘画经验`,
        range: '学习重点：创意绘画 | 百科知识 | 思维发散',
        desc: 'S2级别是孩子美术学习的的启蒙阶段，学习重点是认知与联想',
        courseday: '0',
        period: 0,
      },
      {
        bgcolor: '#30baea',
        label: 'S3',
        fit: '适合专项技能提升（推荐7岁以上）',
        range: '学习重点：色彩搭配 | 造型练习 | 线条装饰',
        desc: 'S3级别是孩子美术学习的的中级阶段，学习重点是创意与表达',
        courseday: '0',
        period: 0,
      },
    ];
    setLevel(newArray);
    setTitle('为保证孩子的学习体验，请正确选择对应的级别');
  };

  const filterBuyLevel = sup => {
    return (
      hasBuyLevel &&
      hasBuyLevel.length &&
      hasBuyLevel.findIndex(v => v == sup) > -1
    );
  };

  useEffect(() => {
    new CheckLevel({
      userId,
      channelId,
      orderId: orderid || orderId,
      regtype: '',
      subjects: 'ART_APP',
    })
      .initCheck()
      .then((res: any[]) => {
        setHasBuyLevel(res);
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId, channelId]);

  return (
    <View className='level'>
      <View className='level-tip'>*{title}</View>
      {level.map((item, index) => (
        <View
          className={`card ${filterBuyLevel(item.label) ? `selected` : ``} ${
            tabActive === index ? 'active' : ''
          }`}
          key={`card-${index}`}
          onClick={() => {
            if (filterBuyLevel(item.label)) {
              // Taro.showToast({ title: '已购买过请选择其他级别', icon: 'none' });
              return;
            }
            setTabActive(index);
            watchShowOrder(true, level[index]);
          }}
        >
          <View className='selected-mark'></View>
          <View
            className='label'
            style={`${
              filterBuyLevel(item.label) ? `` : `background: ${item.bgcolor}`
            }`}
          >
            {item.label}
          </View>
          <View className='info'>
            <View className='title'>
              <Text className='fit'>{item.fit}</Text>
            </View>
            <View
              className='desc'
              style={`${
                filterBuyLevel(item.label) ? `` : `color: ${item.bgcolor}`
              }`}
            >
              {item.range}
            </View>
          </View>
        </View>
      ))}
    </View>
  );
}
