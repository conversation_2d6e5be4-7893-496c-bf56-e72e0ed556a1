import { useEffect } from 'react';
import { Image, View } from '@tarojs/components';
import { AtModal, AtModalContent } from 'taro-ui';
import upgradeImg from '@/assets/thirtySix/chooseLevel/upgrade-img.png';
import closeIcon from '@/assets/thirtySix/chooseLevel/icon-close.png';
import gifBtn from '@/assets/thirtySix/chooseLevel/gif-btn.gif';
import { getSupManagements } from '@/api/groupbuy';
import { useThirtySixPay } from '@/hooks/payhook/useThirtySixPay';
import sensors from '@/utils/sensors_data';
import './index.scss';

interface IUpgrademodal {
  ishow: boolean;
  sup: string;
  closeModal: (type: string) => void;
}

const UpgradeModal = (props: IUpgrademodal) => {
  const { ishow, sup, closeModal } = props;
  const { setPeriod, payConfirm } = useThirtySixPay({
    topicId: 4,
    packagesId: 618,
    isIntroduce: false,
    subject: 'ART_APP',
    pType: 'art',
    payPageData: null,
    sup: sup,
  });

  const upgradeHandle = () => {
    payConfirm();
    sensors.track('xxms_testcourse_Packageupgradepopup_buttonclick', {
      is_update: '立即升级',
    });
  };

  useEffect(() => {
    getSupManagements({
      type: 'TESTCOURSE',
      sup,
      subject: 'ART_APP',
    }).then(res => {
      const result = res.payload && res.payload;
      if (result) {
        setPeriod(result.period);
      }
    });
  }, [sup]);

  return (
    <AtModal
      className='upgrade-modal'
      isOpened={ishow}
      closeOnClickOverlay={false}
    >
      <AtModalContent>
        <View className='close-btn'></View>
        <Image
          className='close-btn'
          src={closeIcon}
          onClick={() => closeModal('icon')}
          mode='widthFix'
        ></Image>
        <Image className='top-img' src={upgradeImg} mode='widthFix'></Image>
        <Image
          className='gif-btn'
          src={gifBtn}
          mode='widthFix'
          onClick={upgradeHandle}
        ></Image>
        {/* <View className='count-down-wrap'>
          <van-count-down :time='countDownMs' format='mm:ss:SS' millisecond />
          <View className='count-down-right'>后过期</View>
        </View> */}
      </AtModalContent>
    </AtModal>
  );
};

export default UpgradeModal;
