import { Image, View } from '@tarojs/components';
import { AtModal, AtModalContent } from 'taro-ui';
import redeemImg from '@/assets/groupbuy/thirtySix/upgrade-redeem.png';

import './index.scss';

interface IRedeemmodal {
  ishow: boolean;
  closeModal: (type: string) => void;
}

const RedeemModal = (props: IRedeemmodal) => {
  const { ishow, closeModal } = props;
  return (
    <AtModal
      className='redeem-modal'
      isOpened={ishow}
      closeOnClickOverlay={false}
    >
      <AtModalContent>
        <Image className='redeem-img' src={redeemImg} mode='widthFix'></Image>
        <View className='footer'>
          <View className='btn' onClick={() => closeModal('up')}>
            继续升级
          </View>
          <View className='btn-bottom' onClick={() => closeModal('giveUp')}>
            确认放弃，添加老师微信
          </View>
        </View>
      </AtModalContent>
    </AtModal>
  );
};

export default RedeemModal;
