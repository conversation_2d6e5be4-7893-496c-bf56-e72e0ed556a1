import { Image, Swiper, SwiperItem, Text, View } from '@tarojs/components';
import { useState } from 'react';
import Banner from '@/assets/thirtyOne/banner.png';
import PriceInfoImg from '@/assets/thirtyOne/price-info.png';
import sign from '@/assets/groupbuy/index/sign.png';

import './index.scss';

const contexts = require.context(
  '@/assets/thirtyOne/',
  true,
  /^\.\/.*section\d\.png$/,
);

const imgList = contexts.keys().map(contexts);

const Section = () => {
  return (
    <>
      <View className='header'>
        <Image mode='widthFix' src={Banner} className='banner'></Image>
      </View>
      <View>
        <View>
          <Image mode='widthFix' src={PriceInfoImg} className='payText'></Image>
        </View>
        <View className='bannerContent'>
          <View className='bannerList'>
            {imgList.map((url: string, i) => {
              return (
                <View className='bannerDiv' key={i}>
                  <Image
                    mode='widthFix'
                    src={url}
                    className='bannerImg'
                  ></Image>
                </View>
              );
            })}
          </View>
        </View>
        <View>
          <Image mode='widthFix' src={sign} className='payText'></Image>
        </View>
      </View>
    </>
  );
};

export default Section;
