page {
  width: 100%;
  height: 100%;
  background-color: #ffc500;
  padding-bottom: 64px;
}
.groupBg {
  width: 726px;
  left: 0;
  right: 0;
  position: absolute;
  top: 15px;
  margin: auto;
}
.Index {
  width: 100%;
  height: 100%;
  position: relative;
}
.content {
  position: absolute;
  top: 43px;
  left: 0;
  right: 0;
  margin: auto;
  width: 686px;
  height: fit-content;
  padding-bottom: 42px;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 0 0 40px 40px;
}
.header {
  text-align: center;
  padding-bottom: 56px;
  padding-top: 46px;
  box-sizing: border-box;
}
.headerImage {
  width: 45px;
  height: 20px;
}
.headerText {
  padding: 0 25px;
  box-sizing: border-box;
  font-size: 40px;
}
.header-desc {
  height: 46px;
  font-size: 32px;
  color: #666666;
  line-height: 46px;
  text-align: center;
  margin: -36px 0 40px;
}
.body {
  .head {
    color: #ff7000;
    font-size: 36px;
    width: 500px;
    height: 88px;
    line-height: 88px;
    margin: 0 auto;
    text-align: center;
    background: #fff9f1;
    border-radius: 44px;
    margin-bottom: 36px;
  }
  .qr-con {
    text-align: center;
    margin: 0 auto;
    .qrcode {
      width: 352px;
      height: 352px;
      background: #eaeaea;
      margin: 0 auto;
      border-radius: 20px;
      margin-bottom: 24px;
      overflow: hidden;
      .qr-img {
        width: 100%;
        height: 100%;
      }
    }
    .qr-text {
      font-size: 32px;
      color: #ff9c00;
      .qr-text-img {
        width: 32px;
        height: 32px;
      }
    }
  }
}
.foot {
  text-align: center;
  margin-top: 60px;
  padding-bottom: 40px;
  .foot-item {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    .foot-item-img {
      width: 68px;
      height: 68px;
      margin-right: 23px;
    }
  }
}
.wxcode {
  height: 45px;
  font-size: 32px;
  font-weight: 400;
  color: #555555;
  line-height: 45px;
  text-align: center;
  margin-top: 15px;
}
.copybtn {
  width: 570px;
  height: 90px;
  background: linear-gradient(270deg, #ffa300 0%, #ff6a00 100%);
  border-radius: 45px;
  text-align: center;
  line-height: 90px;
  font-size: 34px;
  font-weight: 500;
  color: #ffffff;
  margin: 30px auto 0;
}
