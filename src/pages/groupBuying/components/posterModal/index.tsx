import { useEffect, useState } from 'react';
import Taro, { hideLoading, showLoading, useRouter } from '@tarojs/taro';
import { useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import { getPosterListApi } from '@/api/1v1k8s';
import { Image, Button, View } from '@tarojs/components';
import clce from '@/assets/newInvitetask/share-popup-ele-clce.png';
import close from '@/assets/groupbuy/thirtySix/close.png';
import { getCodeUrl, posterIds, businessMap, poster9Ids } from './posterdata';

import './index.scss';

const Index = (props: any) => {
  const { params } = useRouter();
  const { showClick, groupId = '', groupParentFrom = false } = props;
  const [artList, setArtList] = useState<any>(['']);

  const userId = useSelector((state: UserStateType) => state.userid);
  useEffect(() => {
    userId && getAllPoster();
  }, [userId]);

  const getAllPoster = async () => {
    const ENV = process.env.NODE_ENV;
    const isLive = ENV === 'online';
    showLoading({
      title: '加载中',
    });
    const art =
      groupParentFrom != 'index9'
        ? isLive
          ? '132'
          : '182'
        : isLive
        ? '111'
        : '167';
    const resArt = await getPosterList(art);
    const artResList = handleResult(resArt, art);
    setArtList(artResList);
    hideLoading();
    return;
  };

  const getPosterList = async (typeId) => {
    const res = await getPosterListApi({
      id: typeId,
      qrCode: getCodeUrl({
        typeId,
        uid: userId,
        channelId: params.channel_id,
        groupId: groupId,
        groupParentFrom,
      }),
      uid: userId,
    });
    if (res.code === 0) {
      return res;
    } else {
      Taro.showToast({
        title: res.errors || '服务器开小差了',
      });
    }
  };

  const handleResult = (res, typeId) => {
    const { art } = groupParentFrom != 'index9' ? posterIds : poster9Ids;
    const { payload } = res;
    // 标准海报随机
    let randomNum = Math.floor(Math.random() * payload.length);
    if (typeId === art) {
      payload.map((v) => (v.pType = businessMap[0]));
    }
    return [payload[randomNum]];
  };

  return (
    <View className='poster-index'>
      <View className='poser'>
        <Image
          mode='widthFix'
          className='art-poster'
          showMenuByLongpress
          src={artList[0].individualizationUrl || ''}
        />
        <View className='text-art'>长按海报保存到相册</View>
        <View className='text-ss'>去朋友圈分享</View>
        <Image
          className='close-img'
          src={close}
          onClick={() => {
            showClick(false);
          }}
        />
      </View>
    </View>
  );
};

export default Index;
