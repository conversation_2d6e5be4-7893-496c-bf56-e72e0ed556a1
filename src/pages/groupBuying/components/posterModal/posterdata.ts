const ENV = process.env.NODE_ENV;

export const isLive = ENV === 'online';

export const businessMap = ['art', 'write', 'music'];

export const typeMap = {
  art: '美术',
  write: '书法',
  music: '音乐',
};

export const posterIds = {
  art: isLive ? 96 : 155,
  write: isLive ? 77 : 128,
  music: isLive ? 78 : 129,
};
export const poster9Ids = {
  art: isLive ? 111 : 167,
};

const getReferralLink = (groupParentFrom: any = 'index') => {
  let links = {
    index: 'b9',
    index9: 'b12',
    newIndex: 'b11',
  };
  return links[groupParentFrom];
};

const artChannels = (groupParentFrom: any = 'index') => {
  let channel = {
    index: '15643',
    index9: '17234',
    newIndex: '17007',
  };
  return channel[groupParentFrom];
};

export const channelIds = {
  art: isLive ? 15643 : 15643,
  write: isLive ? 13056 : 7917,
  music: isLive ? 13055 : 7916,
};

export const getCodeUrl = ({
  typeId,
  uid,
  channelId = '',
  groupId = '',
  groupParentFrom = false,
}) => {
  const { music, write } = posterIds;
  let url = '';
  let tmp = new Date().valueOf().toString();
  tmp = tmp.substr(tmp.length - 2, tmp.length);
  if (typeId == music) {
    const yyChannelId = channelId || channelIds['music'];
    // 小熊音乐链接
    let origin = 'https://ai-xxyy-default.xiaoxiongyinyue.com/';
    if (ENV === 'dev') {
      origin = 'https://ai-xxyy-default.xiaoxiongyinyue.com/';
    } else if (ENV === 'test') {
      origin = 'https://ai-xxyy-test.xiaoxiongyinyue.com/';
    } else if (ENV === 'prod') {
      origin = 'https://ai-xxyy-prod.xiaoxiongyinyue.com/';
    } else if (ENV === 'online') {
      origin = 'https://www.xiaoxiongyinyue.com/';
    }
    url = `${origin}a/0?p=754&s=${uid}&c=${yyChannelId}&t=${tmp}`;
  } else {
    let xzBaseOri;
    let baseUrl = '/';
    let xzBaseUrl = '/';
    if (ENV === 'gitlab') {
      baseUrl = '/ai-app-h5-activity/';
    } else if (ENV === 'dev') {
      baseUrl = '/ai-app-h5-activity-dev/';
      xzBaseUrl = '/ai-h5-writing-activity/';
      xzBaseOri = 'https://dev.meixiu.mobi';
    } else if (ENV === 'test') {
      baseUrl = '/ai-app-h5-activity-test/';
      xzBaseUrl = '/ai-h5-writing-activity/';
      xzBaseOri = 'https://test.meixiu.mobi';
    } else if (ENV === 'prod') {
      baseUrl = '/activity/';
      xzBaseUrl = '/writing/';
      xzBaseOri = 'https://test.meixiu.mobi';
    } else if (ENV === 'online') {
      baseUrl = '/activity/';
      xzBaseUrl = '/ai-h5-writing-activity/';
      xzBaseOri = 'https://www.xiaoxiongmeishu.com';
    }
    if (typeId == write) {
      const xzChannelId = channelId || channelIds['write'];
      url = `${xzBaseOri}${xzBaseUrl}trial-class-v1?channel=${xzChannelId}&sendId=${uid}&t=${tmp}`;
    } else {
      const msChannelId = channelId || artChannels(groupParentFrom);
      url = `${xzBaseOri + baseUrl}${getReferralLink(
        groupParentFrom,
      )}?sendId=${uid}&${'msChannelId=' +
        msChannelId}&groupId=${groupId}&t=${tmp}`;
    }
  }
  return url;
};
