// @import '../../../theme/groupbuy/common.scss';
@import '../../../theme/normalGroup/common.scss';

.container {
  font-size: 0;
  padding-bottom: calc(112rpx + env(safe-area-inset-bottom));
  background: #f8f8f8;
  min-height: 100vh;
  .teacher {
    width: 690px;
    padding: 56px 0;
    text-align: center;
    background-color: #fff;
    margin: -20px auto;
    image {
      width: 382px;
      height: 382px;
    }
    .text {
      color: #ff7300;
      font-size: 44px;
      margin-top: 20px;
      image {
        width: 24px;
        height: 36px;
      }
    }
  }
  .poster {
    position: fixed;
    background-color: #fff;
    width: 100%;
    height: calc(136rpx + env(safe-area-inset-bottom));
    bottom: 0;
    padding: 0 32px;
    box-sizing: border-box;
    .share-btns {
      width: 100%;
      margin-top: 40px;
      display: flex;
      justify-content: space-between;
      .share-btn {
        background: transparent;
        text-align: center;
        padding: 0;
        margin: 0;
        &::after {
          border: none;
        }
      }
      image {
        width: 292px;
        height: 88px;
      }
    }
  }
}
