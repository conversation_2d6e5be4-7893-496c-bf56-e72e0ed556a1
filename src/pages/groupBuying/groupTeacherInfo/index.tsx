import { useEffect, useRef, useState } from 'react';
import Taro, { useDidShow, useRouter, useShareAppMessage } from '@tarojs/taro';
import { Image, View, Text, Button } from '@tarojs/components';
import { getGrouponDetailByUserId, getTeacherInfo } from '@/api/groupbuy';
import GroupCon from '@/components/groupBookInfo';
import sensors from '@/utils/sensors_data';
import { AtFloatLayout, AtModal } from 'taro-ui';
import { useDispatch, useSelector } from 'react-redux';
import { onceDay, subscribeMsgHandle } from '@/utils';
import { UserStateType } from '@/store/groupbuy/state';
import selIcon from '@/assets/groupbuy/index/gift-new.jpg';
import selIconV1 from '@/assets/groupbuy/index/gift-new-v2.png';
import shareFc from '@/assets/newInvitetask/new-share-fc.png';
import shareFr from '@/assets/newInvitetask/new-share-fr.png';
import Arrow from '@/assets/groupBuying/icon-arrows.png';
import shareImg from '@/assets/normalGroup/art/share-img-new.png';
import PosterModal from '../components/posterModal';
import LayoutOrderV1 from '../../../components/orderV1';
import WxLogin from '../../../components/wxlogin';

import './index.scss';
/** 图片部分完 **/
export default () => {
  const router = useRouter();
  const dispatch = useDispatch();
  //userid
  const orderRef: any = useRef(null);
  const userId = useSelector((state: UserStateType) => state.userid);
  const channelId = useSelector((state: UserStateType) => state.channelId);
  // 拼团信息
  const [groupInfo, setGroupInfo] = useState<any>(null);
  const [ownOrderInfo, setOwnOrderInfo] = useState<any>(null);
  //   团id
  const [groupId, setGroupId] = useState('');
  // 老师微信二维码
  const [teacherNo, setTeacherNo] = useState('');
  // 是否发起拼团
  const [isJoinGroup, setIsJoinGroup] = useState(false);
  //   海报显示
  const [posterShow, setPosterShow] = useState(false);
  const order_path = {
    index: '62',
    index9: '1854',
    newIndex: '1823',
  };
  // 随材赠品展示
  const [giveaway] = useState({
    img: selIconV1,
    sImg: selIcon,
    detail: [
      '小熊马克笔',
      '小熊勾线笔',
      'AR涂色卡',
      '各类作品纸',
      '绘画成长手册',
      '学习图谱等',
    ],
    notes: '该图仅为展示，具体以实物为主，每期根据课程不同收到的随材会有所差异',
  });
  //   获取拼团信息
  useEffect(() => {
    if (userId) userGetGrouoonHandler(userId);
  }, [userId]);
  useShareAppMessage(() => {
    let channels = {
      index: '15643',
      index9: '17234',
      newIndex: '17007',
    };
    let shareChannelId = channels[router.params.from || 'index'];
    return {
      title: `有人@你一起抢福利，${
        router.params.from == 'index9' ? '9' : '10'
      }.9元抢原价36元创意美术+画材礼包`,
      path: `/pages/groupBuying/${
        router.params.from || 'index'
      }/index?channelId=${shareChannelId}&groupId=${groupId}&sendId=${userId}`,
      imageUrl: shareImg,
    };
  });

  // 获取老师二维码
  const getTeacherInfoHandler = (_orderId) => {
    getTeacherInfo({
      orderNo: _orderId,
      addSource: router.params.addSource || '1',
    }).then((res) => {
      if (res.code === 0) {
        setTeacherNo(res.payload.teacherWeChatQrCode);
      }
    });
  };
  // 获取拼团信息
  const userGetGrouoonHandler = (vid) => {
    getGrouponDetailByUserId(vid).then((res) => {
      if (res.payload) {
        const { payload } = res;
        // 拼团信息
        if (
          payload.groupon.status == 'DEFAULT' &&
          new Date().getTime() > payload.groupon.endtime
        )
          payload.groupon.status = 'FAIL';
        payload.groupon.joinHeads = payload.groupon.joinHeads.split(',');
        if (payload.groupon.joinHeads.length == 1)
          payload.groupon.joinHeads = payload.groupon.joinHeads.concat([
            '',
            '',
          ]);
        if (payload.groupon.joinHeads.length == 2)
          payload.groupon.joinHeads = payload.groupon.joinHeads.concat(['']);
        setGroupInfo(payload.groupon);
        setGroupId(payload.groupon.id);
        payload.orderSimple && setOwnOrderInfo(payload.orderSimple);
        getTeacherInfoHandler(payload.grouponOrder.oid);
        let _text =
          userId == payload.groupon.uid ? '小程序拼团首页' : '小程序参团首页';
        if (router.params.entrance_page) _text = router.params.entrance_page;
        initTrack(payload.groupon, _text);
      }
    });
  };
  const initTrack = (_groupInfo, text = '') => {
    sensors.track('xxys_piecegroupPage_Piecingtogetherpage_view', {
      entrance_page: text,
      channel_id:
        _groupInfo.status == 'DEFAULT'
          ? '拼团中'
          : _groupInfo.status == 'SUCCESS'
          ? '拼团成功'
          : '拼团失败',
    });
  };
  //   重新发起
  const rebuildHadnler = () => {
    let channels = {
      index: '15643',
      index9: '17234',
      newIndex: '17007',
    };
    dispatch({
      type: 'CHANGE_CHANNELID',
      channelId: channels[router.params.from || 'index'],
    });
    setIsJoinGroup(true);
    sensors.track('xxys_piecegroupPage_Piecingtogetherpage_click', {
      operation_type: '重新发起',
    });
    setTimeout(() => {
      orderRef.current.payConfirm();
    }, 500);
  };
  //   直接下单
  const directHadnler = () => {
    let channels = {
      index: '15645',
      index9: '17235',
      newIndex: '17009',
    };
    dispatch({
      type: 'CHANGE_CHANNELID',
      channelId: channels[router.params.from || 'index'],
    });
    setIsJoinGroup(false);
    sensors.track('xxys_piecegroupPage_Piecingtogetherpage_click', {
      operation_type: '直接购买',
    });
    setTimeout(() => {
      orderRef.current.payConfirm();
    }, 500);
  };
  //   订阅消息授权
  const scrbeMsgHandler = (type = false) => {
    if (onceDay())
      subscribeMsgHandle([
        'WqIike19XxQDgq0RJ4CR2mxM2KTgC8cPUvaajzR0cCk',
        'FmCLb9ig-FU0h9RobYqVNOSvN8LSh_k3f_qLbzvsh20',
        '6lGxqUREkrDEK_8rOxfFAmzzsd7gN0NnkYEX9gbgbiE',
      ]).then((res) => {
        console.log(res, '订阅');
        setPosterShow(type);
      });
    else setPosterShow(type);
    sensors.track('xxys_piecegroupPage_Piecingtogetherpage_click', {
      operation_type: type ? '分享朋友圈' : '分享好友',
    });
  };
  return (
    <View className='container w100 relative'>
      {!userId && <WxLogin subject='ART_APP' />}
      <GroupCon
        groupInfo={groupInfo}
        teacherPage
        rebuildHadnler={rebuildHadnler}
        directHadnler={directHadnler}
        groupParentFrom={router.params.from}
      />
      {groupInfo && (
        <>
          {groupInfo.status == 'SUCCESS' && (
            <View className='teacher'>
              <Image
                src={teacherNo}
                showMenuByLongpress
                onLongPress={() => {
                  sensors.track(
                    'xxys_piecegroupPage_Piecingtogetherpage_click',
                    {
                      operation_type: '长按老师二维码',
                    },
                  );
                }}
              />
              <View className='text'>
                <Image src={Arrow} /> 长按识别二维码添加 <Image src={Arrow} />
              </View>
            </View>
          )}
        </>
      )}
      {groupInfo && (
        <>
          {groupInfo.status == 'DEFAULT' && (
            <View className='poster'>
              <View className='share-btns'>
                <View
                  className='share-btn'
                  onClick={() => scrbeMsgHandler(true)}
                >
                  <Image src={shareFc} />
                </View>
                <Button
                  className='share-btn'
                  openType='share'
                  onClick={() => scrbeMsgHandler(false)}
                >
                  <Image src={shareFr} />
                </Button>
              </View>
            </View>
          )}
        </>
      )}
      <>
        {/* 海报 */}
        <AtModal
          isOpened={posterShow}
          closeOnClickOverlay={false}
          className='drainage-poster-status-wrap'
        >
          {groupId && (
            <PosterModal
              showClick={() => setPosterShow(false)}
              groupId={groupId}
              groupParentFrom={router.params.from}
            />
          )}
        </AtModal>
        {/* 订单 */}
        <AtFloatLayout isOpened={false}>
          <LayoutOrderV1
            isShowOrder={false}
            watchCloseOrder={null}
            payPageData={{}}
            orderType={null}
            subject='ART_APP'
            packagesId={order_path[router.params.from || 'index']}
            classNum={10}
            topicId={3}
            pType='art'
            pName='美术'
            teacherPage
            isReceive={false}
            isJoinGroup={isJoinGroup}
            groupId={groupId}
            ref={orderRef}
            ownOrderInfo={ownOrderInfo}
            giveaway={giveaway}
            levelType={1}
          />
        </AtFloatLayout>
      </>
    </View>
  );
};
