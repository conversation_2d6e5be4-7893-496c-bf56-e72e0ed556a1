import { useEffect, useRef, useState } from 'react';
import Taro, { useDidShow, useRouter, useShareAppMessage } from '@tarojs/taro';
import { AtFloatLayout, AtModal, AtModalContent } from 'taro-ui';
import { Image, View } from '@tarojs/components';
import sensors from '@/utils/sensors_data';
import store from '@/store/groupbuy/index';
import { UserStateType } from '@/store/groupbuy/state';
import { levelV1, levelV2 } from '@/common/data.config';
import '@/theme/custom-taro-ui.scss';
/** 图片部分   **/
import modalCloseImg from '@/assets/groupbuy/thirtySix/close.png';
import redeemImg from '@/assets/normalGroup/newArt/retain-fbb.gif';
import artBanner from '@/assets/normalGroup/newArt/banner-new.png';
import artPriceFull from '@/assets/groupBuying/price.png';
import p36 from '@/assets/groupBuying/p-36.png';
import p9 from '@/assets/groupBuying/p-9.png';
import pall from '@/assets/groupBuying/p-all.png';
import selIcon from '@/assets/groupbuy/index/gift-new.jpg';
import selIconV1 from '@/assets/groupbuy/index/gift-new-v2.png';
import shareImg from '@/assets/normalGroup/art/share-img-new.png';
import leftImg from '@/assets/normalGroup/newArt/left-img.png';
import Orderdetailmp from '@/components/orderDetailMp';
import { useDispatch, useSelector } from 'react-redux';
import VideoShow from '@/pages/normalGroup/art/components/videoShow';
import {
  getGrouponDetailByUserId,
  getGrouponDetailByPackagesId,
  getGrouponDetailById,
  getOrderDetailApi,
} from '@/api/groupbuy';
import GroupCon from '@/components/groupBookInfo';
// @ts-ignore
import CommonTop from '../../../components/commonTop';
import './index.scss';
import LayoutLevel from '../../../components/level';
import LayoutOrderV1 from '../../../components/orderV1';
import WxLogin from '../../../components/wxlogin';
// 中间部分图片
// // 有spreadId
const artRequireContext = require.context(
  '@/assets/groupBuying',
  true,
  /^\.\/.*zjs-img0[123457]\.png$/,
);

/** 图片部分完 **/
export default () => {
  const router = useRouter();
  /** 常量 */
  const footerText = [
    '客服电话：400-002-8080  浙ICP备20001038号-3',
    '地址：北京市朝阳区朝来高科技产业园区36号院10号楼',
    '杭州小伴熊科技有限公司 版权所有',
  ];
  const artBodyPartImages = artRequireContext.keys().map(artRequireContext);

  const orderRef: any = useRef(null);
  /** 常量结束 */
  //redux
  const orderId = useSelector((state: UserStateType) => state.orderId);
  //openid
  const openId = useSelector((state: UserStateType) => state.openid);
  /** 订单浮窗 */
  // 显示套餐隐藏浮窗
  const [isOpenWind, setIsOpenWind] = useState<boolean>(false);
  // 显示隐藏订单浮窗
  const [isShowOrder, setIsShowOrder] = useState<boolean>(false);
  // 订单支付页面的数据
  const [payPageData, setPayPageData] = useState<object | null>();
  // 拼团倒计时需要获取订单详情
  const [payOrderData, setPayOrderData] = useState({
    orderType: '36',
    packagesId: '62',
  });
  // 显示video
  const [showVideo, setShowVideo] = useState(false);
  // 用户放弃付款再次确认弹窗
  const [showRedeemModal, setShowRedeemModal] = useState<boolean>(false);
  // 是否显示挽留窗口（只第一次点击关闭）
  const [needShowRedeem, setNeedShowRedeem] = useState<boolean>(true);
  const [levelType] = useState<number>(1);
  // 拼团信息
  const [groupInfo, setGroupInfo] = useState<any>(null);
  // 是否参与拼团
  const [isJoinGroup, setIsJoinGroup] = useState(false);
  //   团id
  const [groupId, setGroupId] = useState('');

  // 关闭挽留弹窗
  const hideRedeemHandle = () => {
    setShowRedeemModal(false);
  };
  /** 传给子页面的参数 **/

  //openid
  const channelId = useSelector((state: UserStateType) => state.channelId);
  const [orderInfo, setOrderInfo] = useState({
    buyTime: '0',
    packageId: 62,
  });
  const dispatch = useDispatch();
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  // 随材赠品展示
  const [giveaway] = useState({
    img: selIconV1,
    sImg: selIcon,
    detail: [
      '小熊马克笔',
      '小熊勾线笔',
      'AR涂色卡',
      '各类作品纸',
      '绘画成长手册',
      '学习图谱等',
    ],
    notes: '该图仅为展示，具体以实物为主，每期根据课程不同收到的随材会有所差异',
  });
  const [banner, setBanner] = useState(artBanner);

  useDidShow(() => {
    const params: any = router.params;
    let _params = {
      course_subject: 'ART_APP-小熊美术',
      poster_id: params['poster_id'] || '',
      channel_id: params.msChannelId,
      xz_channel_id: params.xzChannelId,
      yy_channel_id: params.yyChannelId,
      user_role: store.getState().userRole,
      Use_equipment: params['equipment'] == 1 ? 'ipad' : '非ipad',
      buy_model: 'model_4',
      sendId: params['sendId'] || '',
      entrance_page: params['entrance_page'] || '',
    };
    if (params['isToWf']) {
      let _json = {
        '1': '好友',
        '2': '朋友圈',
        '3': '保存本地',
        '4': '微信分享',
        '5': '中台生成长',
        '6': '中台生成短',
      };
      _params['share_from'] = _json[params['isToWf']];
    }
    if (params['sharetype']) _params['sharetype'] = params['sharetype'];
    if (params['sText']) _params['invitation_content'] = params['sText'];
    _params['scene'] = Taro.getLaunchOptionsSync().scene;
    sensors.track('xxys_experienceCoursePage_view', _params);
  });

  useShareAppMessage(() => {
    const _channelId = router.params.msChannelId || router.params.channelId;
    let shareChannelId = '15643';
    if (_channelId) {
      shareChannelId = _channelId;
    }
    return {
      title: '有人@你一起抢福利，10.9元抢原价36元创意美术+画材礼包',
      path: `/pages/groupBuying/index/index?channelId=${shareChannelId}&groupId=${groupId}&sendId=${userId}`,
      imageUrl: shareImg,
    };
  });
  //   获取拼团信息
  useEffect(() => {
    if (!openId) return;
    if (userId && !router.params.groupId) userGetGrouoonHandler(userId);
    else if (!userId && router.params.groupId) {
      setGroupId(router.params.groupId || '');
      userGetGrouoonHandler(router.params.groupId, 2);
    } else if (userId && router.params.groupId) {
      setGroupId(router.params.groupId || '');
      userGetGrouoonHandler(router.params.groupId, 2);
    } else initTrack('拼团首页');
  }, [userId, openId]);

  // 获取订单详情
  const getOrderdetail = (_orderId) => {
    getOrderDetailApi(_orderId).then((res) => {
      if (res.code === 0) {
        const _order = res.payload.order;
        setOrderInfo(_order);
        dispatch({
          type: 'CHANGE_ORDERID',
          orderId: _order.id,
        });
        const order_path = {
          '62': 'index',
          '1823': 'newIndex',
          '1854': 'index9',
        };
        if (_order.type == 'GROUPON' && _order.addressId != '0')
          Taro.navigateTo({
            url: `/pages/groupBuying/groupTeacherInfo/index?from=${
              order_path[_order.packagesId] || 'index'
            }`,
          });
        else if (_order.type == 'GROUPON' && _order.addressId == '0')
          Taro.navigateTo({
            url: `/pages/groupbuy/addAddress/index?vType=2&type=new&soul=artindex&sup=${
              _order?.sup
            }&orderType=14.9&orderGroup=true&from=${
              order_path[_order.packagesId] || 'index'
            }`,
          });
      }
    });
  };

  // 监听订单浮窗的打开和关闭
  const watchShowOrder = (state, pageData) => {
    if (!state && needShowRedeem) {
      setShowRedeemModal(true);
      setNeedShowRedeem(false);
      return;
    }
    setNeedShowRedeem(true);
    // setIsShowOrder(state);
    setIsOpenWind(false);
    pageData && setPayPageData(pageData);
    /** 神策埋点
     * 用户点击选择套餐时触发 **/
    pageData &&
      Object.keys(pageData).length > 0 &&
      !isShowOrder &&
      sensors.track('xxys_experienceCoursePage_courseSup_click', {
        course_sup: pageData.sup,
        channel_id: channelId,
        user_role: store.getState().userRole,
        buy_model: 'model_4',
        abtest: levelType == 0 ? '单年龄' : '年龄&介绍',
        sendId: store.getState().sendId || '',
      });
    /** 神策埋点 **/
  };
  // 打开级别、购买弹窗
  const openHandle = (price: string, cgCh = false) => {
    const _channelId = router.params.msChannelId || router.params.channelId;
    if (price === '14.9') {
      !cgCh &&
        store.dispatch({
          type: 'CHANGE_CHANNELID',
          channelId: _channelId ? _channelId : 15642,
        });
      setIsJoinGroup(true);
      if (groupInfo && groupInfo.status != 'DEFAULT') {
        setGroupId('');
      }
      setPayOrderData({
        orderType: '14.9',
        packagesId: '62',
      });
    } else {
      setIsJoinGroup(false);
      !cgCh && store.dispatch({ type: 'CHANGE_CHANNELID', channelId: 15645 });
      setPayOrderData({
        orderType: '36',
        packagesId: '62',
      });
    }

    let _text;
    if (!cgCh) _text = price == '14.9' ? '14.9元拼团购' : '36元单独买';
    else _text = groupInfo.status == 'FAIL' ? '一键开团' : '一键参团';
    sensors.track('xxys_piecegroupPage_buy_click', {
      channel_id: channelId,
      button_position: _text,
      buy_model: 'model_4',
      sendId: store.getState().sendId || '',
    });

    if (levelType == 2) {
      watchShowOrder(true, {});
      setIsShowOrder(true);
      return;
    }
    setIsOpenWind(true);
  };
  const initTrack = (text = '') => {
    sensors.track('xxys_piecegroupPage_view', {
      page_type: text,
      channel_id: channelId,
    });
  };
  // 获取拼团信息
  const userGetGrouoonHandler = (vid, type = 1) => {
    let _url = type == 1 ? getGrouponDetailByPackagesId : getGrouponDetailById;
    _url(vid, 62).then((res) => {
      if (res.payload) {
        const { payload } = res;
        if (
          type == 1 &&
          new Date().getTime() - payload.groupon.endtime > 1209600000
        )
          return;
        // 拼团信息
        if (
          payload.groupon.status == 'DEFAULT' &&
          new Date().getTime() > payload.groupon.endtime
        )
          payload.groupon.status = 'FAIL';
        payload.groupon.joinHeads = payload.groupon.joinHeads.split(',');
        if (payload.groupon.joinHeads.length == 1)
          payload.groupon.joinHeads = payload.groupon.joinHeads.concat([
            '',
            '',
          ]);
        if (payload.groupon.joinHeads.length == 2)
          payload.groupon.joinHeads = payload.groupon.joinHeads.concat(['']);
        setGroupInfo(payload.groupon);
        setGroupId(payload.groupon.id);
        // 订单 getGrouponDetailByUserId 才会返回本人的参团订单id(有的话才执行后续操作)
        // payload.grouponOrder && getOrderdetail(payload.grouponOrder.oid);
        // if (payload.grouponOrders) {
        //   let _i = payload.grouponOrders.findIndex(o => o.uid == userId);
        //   _i > -1 && getOrderdetail(payload.grouponOrders[_i].oid);
        // }else if ()
        payload.orderSimple && getOrderdetail(payload.orderSimple.orderId);
        if (type !== 1) {
          let _i = payload.grouponOrders.find((o) => o.uid == userId);
          if (_i) getOrderdetail(_i.oid);
        }
        if (payload.groupon.status == 'DEFAULT') initTrack('参团首页可拼');
        if (
          payload.groupon.status == 'FAIL' ||
          payload.groupon.status == 'SUCCESS'
        )
          initTrack('参团首页重开');
      }
      //   else {
      //     both && userGetGrouoonHandler(router.params.groupId, 2);
      //     type == 1 && initTrack('拼团首页');
      //   }
    });
  };
  // 点击参团
  const joinGroupHandler = () => {
    const _channelId = router.params.msChannelId || router.params.channelId;
    //   params中无groupId才可以正常发起
    !groupId
      ? store.dispatch({
          type: 'CHANGE_CHANNELID',
          channelId: _channelId ? _channelId : 15642,
        })
      : //   拼团失败开团，拼团中参团
        dispatch({
          type: 'CHANGE_CHANNELID',
          channelId:
            groupInfo.status == 'FAIL'
              ? _channelId
                ? _channelId
                : 15642
              : 15643,
        });
    let _p = '14.9';
    openHandle(_p, true);
  };
  //   视频点击
  const videoclick = () => {
    sensors.track('xxys_experienceCoursePage_buy_click', {
      button_position: '视频购买按钮',
      buy_model: 'model_4',
      sendId: store.getState().sendId || '',
    });
    openHandle('14.9');
    Taro.setStorageSync('vshow', '2');
    setShowVideo(false);
    setIsOpenWind(true);
  };
  const payConfirmHandler = () => {
    setTimeout(() => {
      orderRef.current.payConfirm();
    }, 500);
  };
  const getPhoneNumberHandler = (res) => {
    orderRef.current.getPhoneNumber(res);
  };

  /* 订单弹窗结束 */
  /** 传给子页面的参数结束 **/
  return (
    <View className='container w100 relative'>
      {/* 微信登录组件 */}
      <WxLogin subject='ART_APP' noAskNoaddress />
      {/* 头部导航栏 */}
      <CommonTop currentName='小熊美术' isIntroduce={false} />
      {(groupId && userId != groupId) ||
      (!userId && groupId) ||
      (isJoinGroup && router.params.groupId) ? (
        <GroupCon
          groupInfo={groupInfo}
          joinGroupHandler={joinGroupHandler}
          groupParentFrom='Index'
        />
      ) : (
        <>
          {/* banner图 */}
          <View className='w100 banner-con'>
            <Image className='banner w100' mode='widthFix' src={banner}></Image>
          </View>
          {/* banner图下小介绍框 */}
          <View className='intro-part'>
            <View className='intro-img'>
              <Image
                className='w100'
                src={artPriceFull}
                mode='widthFix'
              ></Image>
            </View>
          </View>
        </>
      )}
      {/* 图片列表 */}
      <View className='body-part newArt'>
        {artBodyPartImages.map((item, index) => {
          return (
            <View className='body-img-part' key={`body-img-${index}`}>
              <Image
                className='body-img w100'
                mode='widthFix'
                src={`${item}`}
              ></Image>
            </View>
          );
        })}
      </View>
      <Orderdetailmp />
      {/* 底部文字 */}
      <View className='footer-part'>
        {footerText.map((item, index) => {
          return (
            <View className='text_al' key={`text-${index}`}>
              {item}
            </View>
          );
        })}
      </View>
      {/* 购买部分*/}
      {!groupInfo && !groupId && (
        <View className='suction-bottom fixed'>
          <View className='bg_white flex w100 trans-bg'>
            <>
              <View className='img-btn-box news'>
                <Image
                  className='left-img'
                  src={leftImg}
                  mode='widthFix'
                ></Image>
                <View className='img-btn-box old'>
                  <View
                    className='thirtySix-btn'
                    data-type='thirtySix'
                    onClick={() => openHandle('36')}
                  >
                    {/* <Image className='w100' src={p36}></Image> */}
                  </View>
                  <View
                    className='twentyNine-btn'
                    data-type='twentyNine'
                    onClick={() => openHandle('14.9')}
                  >
                    {/* <Image className='w100' src={p9}></Image> */}
                  </View>
                  <Image className='w100' src={pall}></Image>
                </View>
              </View>
            </>
          </View>
        </View>
      )}
      {/* 弹窗部分 */}
      {/* 套餐 */}
      <AtFloatLayout
        className='custom-float-layout'
        isOpened={isOpenWind}
        onClose={() => {
          setIsOpenWind(false);
        }}
        title='选择级别'
      >
        <LayoutLevel
          oldLevelArray={levelType == 1 ? levelV2 : levelV1}
          levelType={levelType}
          pType='art'
          newLevelType
          watchShowOrder={watchShowOrder}
          payConfirmHandler={payConfirmHandler}
          getPhoneNumberHandler={getPhoneNumberHandler}
        />
      </AtFloatLayout>
      {/* 订单 */}
      <AtFloatLayout
        className='order-layout custom-float-layout'
        isOpened={isShowOrder}
        onClose={() => {
          watchShowOrder(false, null);
        }}
      >
        {payPageData && (
          <LayoutOrderV1
            isShowOrder={isShowOrder}
            watchCloseOrder={watchShowOrder}
            payPageData={payPageData}
            orderType={payOrderData.orderType}
            subject='ART_APP'
            packagesId={payOrderData.packagesId}
            classNum={10}
            topicId={3}
            pType='art'
            pName='美术'
            isReceive={false}
            payConfirmHandler={payConfirmHandler}
            isJoinGroup={isJoinGroup}
            groupId={groupId}
            ref={orderRef}
            giveaway={giveaway}
            levelType={levelType}
            groupParentFrom='index'
          />
        )}
      </AtFloatLayout>
      {/* 退出确认框 */}
      <AtModal
        className='redeem-modal'
        isOpened={showRedeemModal}
        closeOnClickOverlay={false}
      >
        <Image
          className='close-img'
          src={modalCloseImg}
          onClick={hideRedeemHandle}
        ></Image>
        <AtModalContent>
          <Image className='redeem-img' src={redeemImg} mode='widthFix'></Image>
          <View className='redeem-btn' onClick={hideRedeemHandle}></View>
        </AtModalContent>
      </AtModal>
      {/* 视频弹窗 */}

      {(!orderInfo.buyTime || orderInfo.buyTime == '0') && (
        <View
          className='viewoview'
          onClick={() => {
            sensors.track('xxys_experienceCoursePage_video_click', {});
            setShowVideo(true);
            Taro.setStorageSync('vshow', '1');
          }}
        ></View>
      )}
      <VideoShow
        isShow={showVideo}
        close={() => {
          Taro.setStorageSync('vshow', '2');
          setShowVideo(false);
        }}
        click={() => videoclick()}
      />
    </View>
  );
};
