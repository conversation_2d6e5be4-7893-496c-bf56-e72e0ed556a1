// @import '../../../theme/groupbuy/common.scss';
@import '../../../theme/normalGroup/common.scss';

.container {
  font-size: 0;
  padding-bottom: calc(112rpx + env(safe-area-inset-bottom));
  background: #ffe161;

  .layout-header {
    padding: 40px 0 16px;
  }

  .fixedTop {
    position: sticky;
  }

  .intro-part {
    .intro-con {
      padding: 10px $font-32;
      background-color: $light-yellow;

      .intro-text {
        font-size: $font-24;
        color: $c_red;
      }

      .icon {
        width: $font-32;
        margin-right: 16px;
      }
    }
  }

  .body-part {
    padding: 69px $font-32;
    background-color: $de_orange;
    padding-top: -30px;
    &.new9 {
      background-color: #fffad9 !important;
    }
    .body-img-part {
      position: relative;
      &.new9 {
        padding: 0 36px;
      }
      .overfolw {
        width: 600px;
        margin: 0 auto;
        top: 300px;
        left: 0;
        right: 0;
        height: 746px;
        position: absolute;
        overflow-x: hidden;
        .slide-insert {
          height: 746px;
          overflow-y: visible !important;
          .slide-item {
            overflow-y: visible !important;
            .slide-3 {
              width: 580px !important;
              height: 602px !important;
            }
          }
          .insert-swiper {
            height: 100%;
            text-align: center;
            overflow-y: visible !important;
          }
        }
        &.slide-index-1 {
          height: 1400px;
          top: 350px;
          .slide-insert {
            height: 1350px;
            .slide-1 {
              width: 600px !important;
              height: 1200px !important;
            }
          }
        }
        &.new {
          position: relative;
          width: 678px;
          &.overfolw.slide-index-1 {
            height: 1350px;
            top: 0;
            .slide-insert {
              height: 1300px;
              .slide-1 {
                width: 678px !important;
              }
            }
          }
        }
      }
      &.pd {
        padding: 50px 30px;
        swiper .wx-swiper-dots {
          display: none;
        }
      }
      &:last-child {
        margin: 0;
      }
    }
  }

  .footer-part {
    font-size: $font-24;
    color: #999;
    line-height: $font-38;
    padding: 39px 74px;
    letter-spacing: 1px;
    background-color: $linght-grey;
  }

  .suction-bottom {
    bottom: 0;
    z-index: 101;

    .bg_white {
      padding-bottom: env(safe-area-inset-bottom);
      margin-top: $font-20;
      position: relative;
      box-sizing: content-box;

      .sel-img {
        margin-top: 10px;
        position: relative;
      }

      .img-btn-box {
        width: 100%;
        height: 100%;
        display: flex;
        padding: 13px 19px 0 19px;
        position: relative;
        box-sizing: border-box;
        align-items: center;
        justify-content: space-between;
        &.new {
          position: relative;
          justify-content: center;
        }
        &.old {
          padding: 0;
          width: 520px;
          height: 110px;
          //   border-radius: 90px;
          overflow: hidden;
          position: relative;
          .w100 {
            width: 520px;
            height: 110px;
            position: absolute;
            left: 0;
            top: -4px;
          }
        }
        &.news {
          padding-left: 20px;
          align-items: center;
          justify-content: space-between;
        }
        .left-img {
          width: 133px;
          height: 109px;
        }
        .thirtySix-btn,
        .twentyNine-btn {
          width: 260px;
          height: 90px;
          position: relative;
          z-index: 2;
          image {
            width: 260px;
            height: 90px;
          }
        }
      }

      .coupon-36 {
        position: absolute;
        width: 270px;
        right: 34px;
        top: -60px;
      }
    }
  }

  .at-float-layout .layout-header {
    text-align: center;
    background-color: #fff;
  }

  .viewoview {
    position: fixed;
    right: 24px;
    bottom: 230px;
    width: 160px;
    height: 160px;
    background: url('../../../assets/normalGroup/newArt/show-video-btn.gif')
      no-repeat 0 0/100%;
  }
}

.at-modal__overlay {
  background-color: rgba(0, 0, 0, 0.7);
}
