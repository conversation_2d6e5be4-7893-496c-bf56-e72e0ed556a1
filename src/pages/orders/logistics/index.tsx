import { View } from '@tarojs/components';
import { useEffect, useMemo, useState } from 'react';
import { mapArray } from '@/utils/index';
import { useRouter } from '@tarojs/taro';
import { getExpressDetailapi } from '@/api/groupbuy';
import './index.scss';

export default () => {
  const { params } = useRouter();
  const dataInfo = JSON.parse(params.data as string);
  const [expressData, setExpressData] = useState([]);

  const addressInfo = useMemo(() => {
    const { province, city, area, street } = dataInfo;
    let municipalDistricts = province === city;
    let addressJoin = municipalDistricts
      ? province + area
      : province + city + area;
    const addressData = `${addressJoin}${street}`;
    return addressData;
  }, [dataInfo]);

  const getExpressDetail = () => {
    // 'JDVB04610169886'
    getExpressDetailapi(dataInfo.expressNu).then(res => {
      const _data = Array.isArray(res.payload.expressDetailCenterList)
        ? res.payload.expressDetailCenterList[0].data
        : [];
      setExpressData(_data);
    });
  };

  useEffect(() => {
    getExpressDetail();
  }, [dataInfo.expressNu]);

  return (
    <View className='logistics-wrap'>
      <View className='info'>
        <View className='info-item'>
          <View className='info-title'>物流公司</View>
          <View className='info-word'>{dataInfo.expressCompany}</View>
        </View>
        <View className='info-item'>
          <View className='info-title'>运单号码</View>
          <View className='info-word'>{dataInfo.expressNu}</View>
        </View>
        <View className='info-item'>
          <View className='info-title'>收货地址</View>
          <View className='info-word'>{addressInfo}</View>
        </View>
      </View>
      <View className='hr'></View>
      <View className='step'>
        <View className='step-title'>物流追踪</View>
        <View className='step-box'>
          {mapArray(expressData, {
            init: (item, index) => (
              <View className='step-box-item' key={index}>
                <View className='step-item-l'>
                  <View className='tips'></View>
                  <View className='line'></View>
                </View>
                <View className='step-item-content'>
                  <View className='step-status'>{item.centerStatus}</View>
                  <View className='step-content'>
                    {mapArray(item.data, {
                      init: (val, i) => (
                        <View className='step-content-item' key={i}>
                          <View className='step-content-context'>
                            {val.context}
                          </View>
                          <View className='step-content-time'>{val.time}</View>
                        </View>
                      ),
                    })}
                  </View>
                </View>
              </View>
            ),
          })}
        </View>
      </View>
    </View>
  );
};
