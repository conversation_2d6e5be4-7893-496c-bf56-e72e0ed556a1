.logistics-wrap {
  .info {
    padding: 40px 30px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .info-item {
    width: 100%;
    height: 40px;
    line-height: 40px;
    font-size: 28px;
    margin-bottom: 20px;
  }
  .info-title {
    color: #999999;
    float: left;
  }
  .info-word {
    color: #222222;
    float: left;
    margin-left: 10px;
  }
  .hr {
    width: 100%;
    height: 20px;
    background-color: #f5f5f5;
  }
  .step {
    padding: 30px;
    .step-title {
      padding: 40px 0;
      font-size: 28px;
      font-weight: 400;
      color: #888888;
    }
    .step-box {
      .step-box-item {
        display: flex;
      }
      .step-item-l {
        width: 40px;
        display: flex;
        align-items: center;
        flex-direction: column;
        .tips {
          width: 30px;
          height: 30px;
          background-color: #ffbc00;
          border-radius: 50%;
          overflow: hidden;
        }
        .line {
          width: 2px;
          height: calc(100% - 30px);
          background-color: #f5f5f5;
        }
      }
      .step-item-content {
        flex: 1;
        margin-left: 20px;
        .step-status {
          width: 100%;
          height: 40px;
          font-size: 32px;
          font-weight: 500;
          color: #222222;
        }
        .step-content {
          padding: 20px 0;
          font-weight: 400;
          color: #cccccc;
        }
        .step-content-item {
          margin-bottom: 30px;
        }
        .step-content-context {
          margin-bottom: 10px;
        }
      }
    }
  }
}
