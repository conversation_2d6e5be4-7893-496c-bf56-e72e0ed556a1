import { View, Text } from '@tarojs/components';
import { useState } from 'react';
import { mapArray } from '@/utils/index';
import { getOrderListAndExpressApi } from '@/api/groupbuy';
import { UserStateType } from '@/store/groupbuy/state';
import { useSelector } from 'react-redux';
import Taro, { useDidShow } from '@tarojs/taro';
import './index.scss';

export default () => {
  const [listData, setListData] = useState([]);
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  const H5STATUS = {
    DELIVERY_READY: {
      name: '待发货',
      class: 'active',
    },
    DELIVERY_START: {
      name: '已发货',
      class: 'active',
    },
    COMPLETED: {
      name: '已完成',
      class: '',
    },
    CANCEL: {
      name: '已关闭',
      class: '',
    },
  };
  const handleJump = item => {
    Taro.navigateTo({
      url: `/pages/orders/logistics/index?data=${JSON.stringify(item.express)}`,
    });
  };
  const getOrderListAndExpress = () => {
    // '547127749425500160'
    getOrderListAndExpressApi({ userId })
      .then(res => {
        setListData(res.payload || []);
      })
      .catch(() => {
        setListData([]);
      });
  };

  useDidShow(() => {
    getOrderListAndExpress();
  });

  return (
    <View className='order-list'>
      {mapArray(listData, {
        init: (item, index) => (
          <View className='list-item' key={index}>
            <View className='num-status'>
              <View className='order-number'>订单编号: {item.outTradeNo}</View>
              <View
                className={`order-status ${item.h5State &&
                  H5STATUS[item.h5State]['class']}`}
              >
                {item.h5State && H5STATUS[item.h5State]['name']}
              </View>
            </View>
            <View className='goods-info'>
              <View className='goods-img'>
                <View className='goods-img-word'>
                  {item.subject == 'MUSIC_APP' ? '小熊音乐' : '小熊美术'}
                </View>
              </View>
              <View className='goods-r'>
                <View className='goods-name'>{item.title}</View>
                <View className='goods-price'>
                  实付<Text className='goods-price-num'>￥{item.amount}</Text>
                </View>
              </View>
            </View>
            {item.express && (
              <View className='check-btn' onClick={() => handleJump(item)}>
                <View className='check-btn-gh'>查看物流</View>
              </View>
            )}
          </View>
        ),
      })}
    </View>
  );
};
