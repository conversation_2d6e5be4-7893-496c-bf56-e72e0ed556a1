page {
  background: #f7f7f7;
}
.order-list {
  width: 100%;
  padding: 60px 16px 108px;
  box-sizing: border-box;
  .list-item {
    background-color: #ffffff;
    border-radius: 16px;
    margin-bottom: 24px;
    padding: 32px 24px 24px 24px;
  }
  .num-status {
    height: 40px;
    line-height: 40px;
    font-weight: 400;
    color: #333333;
    margin-bottom: 48px;
    display: flex;
    .order-number {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .order-status {
      width: 100px;
      text-align: right;
      &.active {
        color: #ff5050;
      }
    }
  }
  .goods-info {
    display: flex;
    .goods-img {
      width: 176px;
      height: 176px;
      text-align: center;
      background: url('../../../assets/orders/list/order-goods-bg.png')
        no-repeat left top;
      background-size: contain;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .goods-img-word {
      width: 72px;
      font-size: 36px;
      font-weight: 500;
      color: #ffffff;
      text-shadow: 0px 8px 6px #fabb31;
    }
    .goods-r {
      margin-left: 24px;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .goods-name {
        font-size: 32px;
        font-weight: 500;
        color: #333333;
      }
      .goods-price {
        font-size: 28px;
        font-weight: 400;
        color: #333333;
      }
      .goods-price-num {
        font-size: 36px;
        font-weight: 600;
        color: #ff5050;
      }
    }
  }
  .check-btn {
    margin-top: 44px;
    display: flex;
    justify-content: flex-end;
    .check-btn-gh {
      width: 176px;
      height: 72px;
      border-radius: 36px;
      border: 2px solid #dddddd;
      line-height: 72px;
      text-align: center;
      font-size: 28px;
      font-weight: 400;
    }
  }
}
