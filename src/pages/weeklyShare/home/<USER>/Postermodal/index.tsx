import { Image, Swiper, SwiperItem, View } from '@tarojs/components';
import { AtModal, AtModalContent } from 'taro-ui';
import { FC, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { hideLoading, showLoading, useRouter } from '@tarojs/taro';
import CloseImg from '@/assets/weekSharePoster/close-icon1.png';
import { UserStateType } from '@/store/groupbuy/state';
import Poster from '../../../poster';
import './index.scss';

interface IProps {
  show: boolean;
  userBuyMessage: any;
  onClose: Function;
}

interface IPosterItem {
  id: string;
  posterUrl: string;
  individualization: string;
  individualizationUrl: string;
  copywriting: string;
  posterVersion: string;
  sort: string;
  qr: string;
  head: string;
  originalPosterUrl: string;
  originalIndividualizationUrl: string;
}

const Postermodal: FC<IProps> = props => {
  const { params } = useRouter();
  const [swiperIndex, setSwiperIndex] = useState<number>(0);
  const [allPosterList, setAllPosterList] = useState<IPosterItem[]>([]);

  const userInfo = useSelector((state: UserStateType) => state.userInfo);

  useEffect(() => {
    if (userInfo.id && props.show && allPosterList.length == 0) {
      getPosterList();
    }
  }, [userInfo.id, props.show, props.userBuyMessage, allPosterList.length]);

  const swiperChangeHandle = (e: any) => {
    if (e.detail.source === 'touch') {
      setSwiperIndex(e.detail.current);
      // const currentPoster = allPosterList[e.detail.current];
      // setOpenType(currentPoster['pType'] === 'art' ? 'share' : '');
    }
  };

  // 获取海报
  const getPosterList = () => {
    showLoading({ title: '加载中' });
    new Poster({
      user: userInfo,
      msChannelId: params.msChannelId || '2079',
      yyChannelId: params.yyChannelId || '10093',
      xzChannelId: params.xzChannelId || '4693',
      query: params,
    })
      .getPosterByUser(props.userBuyMessage || {})
      .then((list: IPosterItem[]) => {
        if (list.length) {
          setAllPosterList(list);
        }
      })
      .finally(() => hideLoading());
  };

  return (
    <AtModal
      isOpened={props.show}
      onClose={() => props.onClose()}
      className='custom-modal-poster'
    >
      <AtModalContent>
        <View className='close-main'>
          <Image
            src={CloseImg}
            mode='widthFix'
            onClick={() => props.onClose()}
          ></Image>
        </View>
        <Swiper
          circular={allPosterList.length < 3 ? false : true}
          nextMargin='100rpx'
          previousMargin='100rpx'
          indicatorColor='#E5E5E5'
          indicatorActiveColor='#FD742F'
          current={swiperIndex}
          onChange={swiperChangeHandle}
          className='swiper'
        >
          {allPosterList.map((item: any, index: number) => {
            return (
              <SwiperItem key={index} className='swiper-item'>
                <View
                  className={`swiper-item-view ${
                    swiperIndex === index ? 'swiper-view-active' : ''
                  }`}
                >
                  <Image
                    className='swiper-img'
                    src={item.posterUrl}
                    mode='heightFix'
                    lazyLoad
                    showMenuByLongpress
                  />
                </View>
              </SwiperItem>
            );
          })}
        </Swiper>
        <View className='laert'>
          <View>长按保存海报，分享到朋友圈</View>
          <View className='suc'>截图上传得1000小熊币</View>
        </View>
      </AtModalContent>
    </AtModal>
  );
};

export default Postermodal;
