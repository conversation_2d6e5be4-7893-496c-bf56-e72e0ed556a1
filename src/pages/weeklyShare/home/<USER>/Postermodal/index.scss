.custom-modal-poster {
  .at-modal__overlay {
    background: rgba(0, 0, 0, 0.8) !important;
  }

  .at-modal__container {
    width: 90%;
    background-color: transparent;
    overflow: visible;

    .at-modal__content {
      padding: 0;
      margin: 0;
      max-height: 95vh;
      padding-bottom: env(safe-area-inset-bottom);
    }
  }
}

.swiper {
  width: 100%;
  height: 54vh;
  overflow: hidden;
  box-sizing: border-box;
}

.swiper-item {
  width: fit-content !important;
}

.swiper-item-view {
  width: 480px;
  height: 54vh;
  transform: scale(0.9);
  transform-origin: center;
  transition: all 0.3s ease;
  border-radius: 20px;
}

.swiper-img {
  width: 90%;
  height: 100%;
  display: block;
  border-radius: 20px;
  margin: 0 auto;
}

.swiper-view-active {
  transform: scale(1);
}

.close-main {
  text-align: right;
  padding: 0 20px 40px;

  image {
    width: 60px;
    height: 60px;
  }
}

.laert {
  position: relative;
  margin-top: 80px;
  text-align: center;
  font-weight: bold;

  view {
    color: white;
    font-size: 34px;
    margin-bottom: 0;
  }

  .suc {
    font-size: 58px;
    color: #ffe801;
  }
}
