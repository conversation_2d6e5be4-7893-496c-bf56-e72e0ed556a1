import { Image, Text, View } from '@tarojs/components';
import { FC, useEffect, useState } from 'react';
import { AtFloatLayout } from 'taro-ui';
import { useSelector } from 'react-redux';
import { useDidShow } from '@tarojs/taro';
import { UserStateType } from '@/store/groupbuy/state';
import { getAccountV2, getMonthlyCount } from '@/api/groupbuy';
import AuthPhoneBtn from '@/components/AuthPhoneBtn';
import ExchangeIcon from '@/assets/weekSharePoster/exchange/exchange-icon-flash.png';
import ExchangeRuleIcon from '@/assets/weekSharePoster/exchange/exchange-icon-qmark.png';
import AbleCoin from '@/assets/weekSharePoster/exchange/able-coin.png';
import UnAbleCoin from '@/assets/weekSharePoster/exchange/unable-coin.png';
import Exclusive from '@/assets/weekSharePoster/exchange/icon-exclusive.png';
import LoseTwo from '@/assets/weekSharePoster/exchange/lose-img-two.png';
import LoseOne from '@/assets/weekSharePoster/exchange/lose-img-one.png';
import ExchangeShare from '@/assets/weekSharePoster/exchange/exchange-share.png';
import ExchangeShareNextWeek from '@/assets/weekSharePoster/exchange/exchange-next-week.png';
import './index.scss';

interface IProps {
  uploadStatus: string;
  productList: any[];
  highproductList: any[];
  handleClick: Function;
  exchangeHandle: Function;
}

const ProductsExchange: FC<IProps> = props => {
  const [thisMonth] = useState(() => new Date().getMonth() + 1);
  const [monthLyCount, setMonthLyCount] = useState(0);
  const [earningsCount, setEarningsCount] = useState(0);
  const [isOpened, setIsOpened] = useState(false);

  const userId = useSelector((state: UserStateType) => state.userid);

  useDidShow(() => {
    userId && getAccountV2Handle();
    userId && getWeeklyShareMonthlyCount();
  });

  useEffect(() => {
    userId && getAccountV2Handle();
    userId && getWeeklyShareMonthlyCount();
  }, [userId]);

  // 获取获取用户信息 、小熊币V2
  const getAccountV2Handle = () => {
    getAccountV2({ userId, accountType: 'POINTS', subject: 'ART_APP' }).then(
      res => {
        if (res.code === 0) {
          const { balance } = res.payload;
          setEarningsCount(balance);
        }
      },
    );
  };

  const getWeeklyShareMonthlyCount = () => {
    getMonthlyCount({ uid: userId, subject: 'ART_APP' }).then(res => {
      if (res.code === 0) {
        setMonthLyCount(res.payload);
      }
    });
  };

  const shareClick = (pos: string, uid = '') => {
    props.exchangeHandle({ pos, uid });
  };

  const exchangeHandler = item => {
    if (monthLyCount < 2) {
      props.handleClick(item);
      return;
    }
    props.exchangeHandle({ item });
  };

  return (
    <View className='exchange-wrap'>
      <View className='products-list'>
        <View className='weeklyshare-title'>
          <Image src={ExchangeIcon} className='flash' mode='widthFix' />
          <View className='the-title'>{thisMonth}月限定特权</View>
          <View className='exchange-rule' onClick={() => setIsOpened(true)}>
            <Image
              src={ExchangeRuleIcon}
              className='rule-icon'
              mode='widthFix'
            />
            <Text>规则</Text>
          </View>
        </View>
        <View className='weeklyshare-count'>
          {monthLyCount < 2 ? (
            <View className='count-month'>
              再参加<Text>{2 - monthLyCount}</Text>次 解锁特价兑换权益
            </View>
          ) : (
            <View className='count-month'>恭喜你！解锁特价兑换权益</View>
          )}
        </View>
        <View className='goodsitem-wrap'>
          {props.highproductList.map((item, idx) => {
            return (
              <View
                className='goodsitem-high'
                key={idx}
                onClick={() => exchangeHandler(item)}
              >
                <View className='bg-white'>
                  <View className='goods-pic'>
                    <Image
                      src={`${item.img}?x-oss-process=image/resize,h_400,m_lfit`}
                      mode='aspectFit'
                    />
                  </View>
                  <View className='goods-intro'>
                    <View className='goods-intro_name'>
                      {item.epcReferName}
                    </View>
                    <View className='goods-desc'>
                      <Text>系统课用户专享</Text>
                      <Text>仅限兑换1次</Text>
                    </View>
                    <View
                      className={`convert ${monthLyCount >= 2 ? 'able' : ''}`}
                    >
                      <Image
                        src={monthLyCount >= 2 ? AbleCoin : UnAbleCoin}
                        mode='widthFix'
                        className='coin-icon'
                      />
                      <Text>{item.exchangePrice}</Text>
                    </View>
                    <View className='goods-intro_coin'>
                      <View className='original-cost'>
                        原价:<Text>{item.exchangePrice * 2}</Text>
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            );
          })}
          <View className='goodsitem-flow'>
            {props.productList.map((item, idx) => {
              return (
                <View
                  className='goodsitem'
                  key={idx}
                  onClick={() => exchangeHandler(item)}
                >
                  <View className='goods-pic'>
                    <Image
                      src={Exclusive}
                      className='icon-exclusive'
                      mode='widthFix'
                    />
                    <Image
                      src={`${item.img}?x-oss-process=image/resize,h_400,m_lfit`}
                      mode='aspectFill'
                    />
                  </View>
                  <View className='goods-intro'>
                    <View className='goods-intro_name'>
                      {item.epc_refer_name}
                    </View>
                    <View
                      className={`convert ${monthLyCount >= 2 ? 'able' : ''}`}
                    >
                      <Image
                        src={monthLyCount >= 2 ? AbleCoin : UnAbleCoin}
                        mode='widthFix'
                        className='coin-icon'
                      />
                      <Text>{item.exchange_price}</Text>
                    </View>
                    <View className='goods-intro_coin'>
                      <View className='original-cost'>
                        原价:<Text>{item.exchange_price * 2}</Text>
                      </View>
                    </View>
                  </View>
                </View>
              );
            })}
            {/* {props.productList.length % 3 == 1 && (
              <View className='goodsitem lose-item'>
                <Image src={LoseTwo} className='lose-one' mode='aspectFill' />
              </View>
            )}
            {props.productList.length % 3 <= 2 &&
              props.productList.length % 3 != 0 && (
                <View className='goodsitem lose-item'>
                  <Image src={LoseOne} className='lose-one' mode='aspectFill' />
                </View>
              )} */}
          </View>
          {['INREVIEW', 'ACTIVE', 'EXPIRE', 'REJECTED'].includes(
            props.uploadStatus,
          ) ? (
            <View className='share'>
              <AuthPhoneBtn authSuccess={uid => shareClick('分享', uid)}>
                <Image
                  src={ExchangeShare}
                  className='share-img'
                  mode='widthFix'
                />
              </AuthPhoneBtn>
            </View>
          ) : (
            <Image
              src={ExchangeShareNextWeek}
              className='share-img'
              mode='widthFix'
              onClick={() => shareClick('下周参与')}
            />
          )}
        </View>
      </View>
      <AtFloatLayout
        className='custom-float-layout'
        isOpened={isOpened}
        onClose={() => {
          setIsOpened(false);
        }}
        title='— 活动规则 —'
      >
        <View className='rule-content'>
          <View className='rule-item'>
            <View className='rule-item-summary'>
              1.2022年8月起，每月（自然月）累计参与周周分享2次，可解锁特价兑换权限，仅支持在周周分享活动兑换
            </View>
            <View className='rule-item-summary'>
              2.本活动最终解释权归小熊艺术所有
            </View>
          </View>
        </View>
      </AtFloatLayout>
    </View>
  );
};

export default ProductsExchange;
