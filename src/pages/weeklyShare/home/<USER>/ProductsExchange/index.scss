.exchange-wrap {
  width: 701px;
  height: auto;
  margin: 23px auto;
  font-family: PingFangSC-Regular, PingFang SC;

  .wx-lunch {
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 10;
    opacity: 0;
    left: 0;
    top: 0;
    overflow: hidden;
  }

  .products-list {
    width: 100%;
    background: linear-gradient(to bottom, #ff4610, #fe1b1b, #fe5620);
    border-radius: 30px;
    position: relative;
  }

  .weeklyshare-title {
    margin: 0px 29px 0px 32px;
    padding: 19px 0px 12px 0px;
    display: flex;
    align-items: center;
    position: relative;
    border-bottom: 1px dashed #ffaa94;

    .the-title {
      display: flex;
      align-items: flex-end;
      justify-content: center;
      font-size: 36px;
      color: white;
      font-family: PingFangSC-Regular, PingFang SC;

      text {
        margin-left: 9px;
        font-size: 24px;
        color: #ffe28e;
      }
    }

    .flash {
      width: 37px;
      margin-right: 9px;
    }

    .exchange-rule {
      position: absolute;
      right: 0px;
      top: 26px;
      font-size: 24px;
      color: #ffe28e;
      display: flex;
      align-items: center;

      .rule-icon {
        width: 28px;
        margin-right: 5px;
      }

      text {
        margin-left: 6px;
      }
    }
  }

  .weeklyshare-count {
    width: 100%;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin-top: 20px;

    .weekly-word {
      height: 53px;
      font-size: 38px;
      font-weight: 600;
      color: #ffffff;
      line-height: 53px;
      text-align: center;
    }

    .count-month {
      font-size: 36px;
      font-weight: 400;
      color: #ffffff;
      display: flex;
      text-align: center;
      align-items: center;
      font-family: PingFangSC-Regular, PingFang SC;

      text {
        font-size: 48px;
        color: #fffe95;
        height: 56px;
        line-height: 56px;
        display: inline-block;
        padding: 0 8px;
        background-color: #d91313;
        border-radius: 6px;
        margin: 0 10px;
        font-weight: 600;
      }
    }
  }

  .goodsitem-wrap {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 34px 29px 34px 29px;
    overflow: hidden;

    .share-img {
      display: block;
      margin: 0 auto;
      width: 503px;
    }

    .goodsitem-high {
      width: 100%;
      height: 100%;
      padding: 6px;
      border-radius: 20px;
      margin-bottom: 24px;
      box-sizing: border-box;
      background: linear-gradient(to bottom, #fff9a2, #ffd459);

      .bg-white {
        width: 100%;
        height: 100%;
        padding: 14px;
        display: flex;
        border-radius: 20px;
        box-sizing: border-box;
        background-color: #fff;
        justify-content: space-between;
        background: linear-gradient(to bottom, #fffcd8, #fff, #fff);

        .goods-pic {
          width: 200px;
          height: 200px;
          margin-right: 30px;

          image {
            width: 100%;
            height: 100%;
            border-radius: 20px;
            object-fit: cover;
          }
        }

        .goods-intro {
          flex: 1;
          margin-top: 12px;

          .goods-intro_name {
            font-size: 28px;
            margin-bottom: 20px;
          }

          .goods-desc {
            margin-bottom: 32px;

            text {
              padding: 0 8px;
              font-size: 22px;
              color: #ff3f12;
              border-radius: 4px;
              margin-right: 10px;
              background-color: #ffe5e5;
              border: 1px solid #ff9076;
            }
          }

          .convert {
            width: 170px;
            height: 50px;
            position: relative;
            margin: 0 auto;
            background: url('../../../../../assets/weekSharePoster/exchange/unable-convert-h-img.png')
              no-repeat;
            background-size: contain;
            overflow: hidden;
            display: flex;
            align-items: center;
            font-size: 22px;
            color: white;
            float: left;

            &.able {
              background: url('../../../../../assets/weekSharePoster/exchange/able-convert-h-img.png')
                no-repeat;
              background-size: contain;
            }

            .coin-icon {
              width: 24px;
              margin: 0 4px 0 10px;
            }
          }

          .goods-intro_coin {
            float: left;
            color: #9b9b9b;
            font-size: 18px;
            margin-top: 18px;
            margin-left: 10px;

            text {
              text-decoration: line-through;
            }
          }
        }
      }
    }

    .goodsitem-flow {
      width: 100%;
      height: 100%;
      overflow-y: auto;
    }

    .goodsitem {
      width: 206px;
      height: 314px;
      background: #ffffff;
      border-radius: 16px;
      margin-bottom: 25px;
      margin-right: 12px;
      float: left;
      box-sizing: border-box;

      &:nth-child(3n) {
        margin-right: 0;
      }

      .goods-pic {
        width: 190px;
        height: 190px;
        display: block;
        margin: 0 auto;
        margin-top: 8px;
        background: #cccccc;
        border-radius: 8px;
        // overflow: hidden;
        position: relative;

        image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .icon-exclusive {
          position: absolute;
          width: 103px;
          height: 31px;
          left: -4px;
          top: -4px;
        }
      }

      .goods-intro {
        flex: 1;
        margin-left: 10px;
        position: relative;

        &_name {
          height: 40px;
          font-size: 22px;
          font-weight: 400;
          color: #000000;
          line-height: 40px;
          text-align: center;
          overflow: hidden;
          white-space: nowrap; // 强制一行
          text-overflow: ellipsis; // 文字溢出显示省略号
        }

        &_progress {
          width: 400px;
          height: 14px;
          background: #e8e8e8;
          border-radius: 9px;
          margin-bottom: 13px;
          overflow: hidden;

          .goods-intro_progress_active {
            width: 80%;
            height: 100%;
            background: #ffb430;
            border-radius: 9px;
            transition: width 0.5s;
          }
        }

        &_coin {
          height: 30px;
          overflow: hidden;
          margin-bottom: 56px;

          .coin-i {
            width: 28px;
            height: 28px;
            background: url('../../../../../assets/weekSharePoster/exchange/exchange-coin.png')
              no-repeat left top;
            background-size: contain;
            float: left;
          }

          .coin-num {
            font-size: 22px;
            font-weight: 500;
            color: #fa6400;
            line-height: 30px;
            float: left;
          }

          .coin-total-num {
            font-size: 22px;
            font-weight: 500;
            line-height: 30px;
            float: left;
          }

          .original-cost {
            font-size: 18px;
            color: #9b9b9b;
            text-align: center;
            margin-top: 5px;

            text {
              text-decoration: line-through;
            }
          }
        }

        .convert {
          width: 144px;
          height: 39px;
          line-height: 39px;
          position: relative;
          margin: 0 auto;
          background: url('../../../../../assets/weekSharePoster/exchange/unable-convert-img.png')
            no-repeat;
          background-size: contain;
          border-radius: 39px;
          overflow: hidden;
          display: flex;
          align-items: center;
          font-size: 22px;
          color: white;

          &.able {
            background: url('../../../../../assets/weekSharePoster/exchange/able-convert-img.png')
              no-repeat;
            background-size: contain;
          }

          .coin-icon {
            width: 24px;
            margin: 0 4px 0 12px;
          }
        }

        .goods-intro_btn_wrap {
          position: absolute;
          bottom: 0;
          right: 0;

          .goods-intro_btn {
            width: 190px;
            height: 60px;
            background: url('../../../../../assets/weekSharePoster/exchange/exchange-dh.png')
              no-repeat left top;
            background-size: contain;
          }

          .goods-intro_btn_share {
            width: 195px;
            height: 82px;
            background: url('../../../../../assets/weekSharePoster/exchange/exchange-fx.png')
              no-repeat left top;
            background-size: contain;
          }

          .goods-intro_btn_nextweek {
            width: 190px;
            height: 60px;
            background: url('../../../../../assets/weekSharePoster/exchange/exchange-xzjx.png')
              no-repeat left top;
            background-size: contain;
          }
        }
      }

      .lose-one {
        width: 100%;
        height: 100%;
      }
    }

    .share {
      text-align: center;
      margin-top: 10px;
      position: relative;

      .share-img {
        width: 503px;
      }

      button {
        line-height: 0;
      }
    }
  }

  .layout-header {
    padding: 40px 0 0;
    background-color: #fff;
    font-size: 40px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #222222;
    text-align: center;

    .layout-header__title {
      padding-right: 0;
    }

    .layout-header__btn-close {
      top: 52% !important;
    }
  }

  .rule-content {
    height: 350px;
    overflow-y: auto;

    .rule-item {
      margin-top: 42px;

      &-title {
        font-size: 36px;
        font-weight: 500;
        color: #222222;
        line-height: 50px;
        margin-bottom: 21px;
      }

      &-image {
        width: 533px;
        height: auto;
        margin-left: 44px;
      }

      &-summary {
        padding-left: 44px;
        margin-bottom: 24px;
        font-size: 30px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
        line-height: 44px;
        white-space: pre-line;
      }

      &-spot {
        position: relative;

        &:after {
          position: absolute;
          top: 16px;
          left: 18px;
          content: '';
          display: block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: #cccccc;
        }
      }
    }
  }
}
