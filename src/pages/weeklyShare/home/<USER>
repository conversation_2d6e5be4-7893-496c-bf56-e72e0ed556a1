.indexWrap {
  max-width: 750px;
  margin: 0 auto;
  background: #ffedc2;
  overflow: hidden;

  .wx-lunch {
    width: 100%;
    height: 136px;
    background: red;
    position: absolute;
    z-index: 10;
    opacity: 0;
    left: 0;
    top: 0;
    overflow: hidden;
  }

  .custom-dl {
    .dl-wrap {
      padding: 40px;
      text-align: center;
      font-size: 30px;

      .title {
        font-size: 34px;
        font-weight: 500;
      }
    }
  }

  .paddWrap {
    padding-bottom: 190px;
    margin-top: -20px;
    position: relative;
  }

  .indexButton {
    width: 609px;
    height: 98px;
    background: #ff5642;
    border-radius: 50px;
    text-align: center;
    line-height: 98px;
    font-size: 40px;
    font-family: STYuanti-SC-Bold, STYuanti-SC;
    font-weight: 500;
    border: 5px solid #ffc619;
    color: #ffffff;
  }

  .flex {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .boxShadow {
    box-shadow: 0px 2px 20px 0px rgba(250, 100, 0, 0.2);
  }

  .share-soon {
    position: relative;
    width: 100%;
    top: -46px;
    margin-top: 50px;

    image {
      width: 100%;
      height: 140px;
    }
  }

  .top-share-soon {
    animation: scaleDrew 1.8s ease-in-out infinite;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 190px;
    z-index: 100;
    .img-1000 {
      width: 700px;
      height: 208px;
    }
  }
  .top-share-soon2 {
    top: 140px;
  }

  .coin-info {
    width: 702px;
    box-sizing: border-box;
    padding: 90px 32px 0;
    height: 362px;
    background: #ffffff;
    border-radius: 32px;
    margin: 50px auto;
    position: relative;
    .tit {
      width: 406px;
      height: 78px;
      position: absolute;
      top: -20px;
      left: 50%;
      transform: translateX(-50%);
    }
    .content {
      .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 28px;
        font-weight: 600;
        color: #333333;
        text-align: center;
        margin-bottom: 32px;
        span {
          display: inline-block;
          vertical-align: middle;
        }
        .coin-num,
        .coin {
          width: 32px;
          height: 32px;
          display: inline-block;
          vertical-align: middle;
        }
        .coin {
          margin-left: 10px;
        }
        .coin-num {
          margin-right: 10px;
        }
        .num {
          font-size: 32px;
          font-weight: bold;
          color: #fa6c3a;
          // line-height: 32px;
        }
      }
      .item:last-of-type {
        margin-bottom: 24px;
      }
    }
    .footer {
      text-align: center;
      font-size: 24px;
      font-weight: 400;
      color: #fa6c3a;
      line-height: 24px;
      image {
        width: 16px;
        height: 16px;
      }
    }
  }

  .bottom-share-soon {
    margin-top: 70px;
    width: 100%;
    height: 100px;
    // background-color: #fff;
    position: fixed;
    bottom: 0;

    image {
      width: 660px;
      display: block;
      margin: 0 auto;
    }
  }

  .share {
    width: 100%;
    text-align: center;
    position: fixed;
    bottom: 20px;
    left: 0;
    z-index: 500;
    .share-img {
      width: 734px;
      height: 132px;
    }
  }

  .container .modal-content {
    background: transparent;
  }

  .error-modal {
    .at-modal__content {
      padding: 0;
    }
    .at-modal__container {
      width: 580px;
      height: 560px;
    }
  }
  .error-box {
    width: 580px;
    height: 560px;
    background: #ffffff;
    border-radius: 24px;
    text-align: center;
    .tit {
      font-size: 40px;
      font-weight: 500;
      color: #333333;
      line-height: 40px;
      margin: 48px auto;
    }
    .info {
      font-size: 32px;
      font-weight: 400;
      color: #333333;
      line-height: 48px;
    }
    .phone {
      width: 516px;
      height: 96px;
      line-height: 96px;
      background: #f8f8f8;
      border-radius: 16px;
      margin: 50px auto;
      font-size: 24px;
      text-align: center;
      font-weight: 400;
      color: #999999;
      .text {
        font-size: 32px;
        font-weight: 500;
        color: #333333;
      }
    }
    .btn {
      width: 516px;
      height: 80px;
      margin: 0 auto;
      background: #ffa700;
      border-radius: 44px;
      font-size: 32px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      text-align: center;
      color: #ffffff;
      line-height: 80px;
    }
  }

  .iPad-modal {
    width: 681px;
    height: 852px;
    background: url('../../../assets/weekSharePoster/weekSharePoster-iPad.png')
      no-repeat;
    background-size: 100% 100%;
    position: relative;

    .tit {
      text-align: center;
      position: absolute;
      top: 325px;
      font-size: 34px;
      color: #fd6c09;
      border-bottom: 9px solid #fff6da;
      width: 442px;
      left: 124px;
    }

    .qrcode {
      width: 221px;
      height: 221px;
      top: 467px;
      left: 235px;
      position: absolute;
    }

    .continue {
      font-size: 22px;
      bottom: 78px;
      color: #6c6c6c;
      position: absolute;
      width: 160px;
      border-bottom: 1px solid #6c6c6c;
      left: 0;
      right: 0;
      margin: 0 auto;
    }

    .close {
      width: 52px;
      height: 52px;
      bottom: -60px;
      left: 0;
      right: 0;
      margin: 0 auto;
      position: absolute;
    }
  }

  .tips-modal {
    .at-modal__container {
      width: 80%;
      background-color: transparent;
    }

    .at-modal__content {
      padding: 0 !important;
    }

    .at-modal__footer {
      .at-modal__action {
        justify-content: center;

        .footer-btn {
          color: #ff9c00;
          line-height: 100px;
        }
      }
    }
  }

  .dl-wrap {
    text-align: center;
    position: relative;

    image {
      width: 100%;
      height: 500px;
      border-radius: 36px;
    }

    .footer-btn {
      width: 60%;
      height: 80px;
      position: absolute;
      left: 23%;
      bottom: 9%;
    }
  }
  .buling {
    animation: breathe 1s linear infinite;
  }
  @keyframes breathe {
    0% {
      transform: scale(0.92);
    }
    50% {
      transform: scale(1);
    }
    100% {
      transform: scale(0.92);
    }
  }
}
