import { Image, View, Text } from '@tarojs/components';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  hideLoading,
  showLoading,
  showToast,
  useRouter,
  useShareAppMessage,
} from '@tarojs/taro';
import msbTrack from '@/utils/sensors_data.weapp';
import '@/theme/custom-taro-ui.scss';
import AuthPhoneBtn from '@/components/AuthPhoneBtn';
import { AtModal, AtModalContent } from 'taro-ui';
import {
  checkUserBuy,
  getSharePosterLogList,
  getSharePosterTaskApi,
  getUser,
  getMonthlyCount,
  getWeeklyShareGoods,
} from '@/api/groupbuy';
import { updateUserInfo } from '@/store/groupbuy/action';
import { UserStateType } from '@/store/groupbuy/state';
import CompletedImg from '@/assets/weeklyShare/auditSuccess-btn.png';
import NoCompletedImg from '@/assets/weeklyShare/title-1000.png';
import TitleCoin from '@/assets/weeklyShare/title-coin.png';
import Coin from '@/assets/weeklyShare/coin.png';
import CoinOne from '@/assets/weeklyShare/coin-one.png';
import CoinTwo from '@/assets/weeklyShare/coin-two.png';
import CoinThree from '@/assets/weeklyShare/coin-three.png';
import StepStart from '@/assets/weeklyShare/step-start.png';
import Share from '@/assets/weeklyShare/share.png';
import ExchangeBg from '@/assets/weekSharePoster/exchange-bg.png';
import ExchangeShareNextWeek from '@/assets/weeklyShare/exchange-next-week.png';
import ShareCover from '@/assets/weeklyShare/share-cover.png';
import WxLogin from '@/components/wxlogin';
import IndexBg from './components/IndexBg';
import NewUpload from './components/NewUpload';
// import ProductsExchange from './components/ProductsExchange';
import ProductList from './components/productList';
import Postermodal from './components/Postermodal';
import PrizeDetails from './components/PrizeDetails';
import './index.scss';

const matchType = {
  COIN: 'coinPrice',
  CLASS: 'classPrice',
  CASH: 'cashPrice',
  COINANDCASH: ['coinPrice', 'cashPrice'],
  CLASSCASH: ['classPrice', 'cashPrice'],
};

// const isLive = process.env.NODE_ENV == 'online';

const Home = () => {
  const uploadRef: any = useRef(null);
  const { params } = useRouter();
  const [uploadStatus, setUploadStatus] = useState('ACTIVE');
  const [taskData, setTaskData] = useState({ status: 'ACTIVE' });
  // const [highproductList, setHighproductList] = useState([]);
  const [productList, setProductList] = useState([]);
  const [hisLogList, setHisLogList] = useState([
    { status: 'ACTIVE' },
    { status: 'ACTIVE' },
  ]);
  const [checkUserBuyMessage, setCheckUserBuyMessage] = useState({});
  // const [isPay, setIsPay] = useState(false);
  // const [identity, setIdentity] = useState(null);
  // const [isFailShow, setIsFailShow] = useState(false);
  const [postDailog, setPostDailog] = useState(false);
  const [payload1, setPayload1] = useState(false);
  const [curGoods, setCurGoods] = useState('');
  const [goodsDetailSta, setGoodsDetailSta] = useState(false);
  const [tips, setTips] = useState(false);
  const [showError, setShowError] = useState(false);
  const [curStep, setCurStep] = useState(1);
  const [monthLyCount, setMonthLyCount] = useState(0); // 月分享次数

  const userId = useSelector((state: UserStateType) => state.userid);
  const mobile = useSelector((state: UserStateType) => state.mobile);
  const dispatch = useDispatch();

  const recentlyPriceStatusArr = useMemo(() => {
    return [
      {
        status: hisLogList[1].status != 'PICKED' ? 'failed' : 'PICKED',
        pos_b: 0,
        pos_l: 47,
      },
      {
        status: hisLogList[0].status != 'PICKED' ? 'failed' : 'PICKED',
        pos_b: 103,
        pos_l: 150,
      },
      {
        status: uploadStatus != 'PICKED' ? 'ACTIVE' : 'PICKED',
        pos_b: 139,
        pos_l: 304,
      },
      { status: 'next', pos_b: 103, pos_l: 458 },
      { status: 'more', pos_b: 20, pos_l: 580 },
    ];
  }, [uploadStatus, hisLogList]);

  useShareAppMessage(() => {
    const title = '系统课用户专享，周周分享得小熊币，免费兑好礼！';
    return {
      title,
      path: `/pages/weeklyShare/home/<USER>
        params.msChannelId || ''
      }&entrance_page=${params.entrance_page || ''}`,
      imageUrl: ShareCover,
    };
  });

  useEffect(() => {
    if (userId) {
      toShare(false);
      getUserIdentity().then(() => {
        // const { identityStr = 'REGISTER' } = res || {};
        // setIsPay(identityStr === 'SYSTEM');
        // setIdentity(identityStr);
        // getSharePosterTask();
      });
      getSharePosterLogListHandle();
      getSharePosterTask();
      getWeeklyShareMonthlyCount();
      getProductList();
    }
  }, [userId]);

  useEffect(() => {
    msbTrack.track('xxys_weeklySharePage_view', {
      entrance_page: params.entrance_page || '',
      channel_id: params.msChannelId || '',
      page_environment: '微信内',
    });
  }, []);

  const getSharePosterLogListHandle = () => {
    getSharePosterLogList({ uid: userId, page: 0, size: 2 }).then((res) => {
      let {
        code,
        payload: { content = [] },
        status,
      } = res;
      if (code !== 0) {
        showToast({ title: status, icon: 'none' });
      }
      setHisLogList(content);
    });
  };

  const getSharePosterTask = () => {
    getSharePosterTaskApi({ uid: userId }).then((res) => {
      const { code, payload = {} } = res;
      if (code === 0) {
        setTaskData(payload);
        // setIsFailShow(payload.status === 'REJECTED');
        setUploadStatus(payload.status);
      }
    });
  };

  const getProductList = () => {
    // 获取兑换商品列表  对接有赞  293278215写死的 测试线上都一样
    getWeeklyShareGoods({ tagId: 293278215 }).then((res) => {
      if (res.code === 0) {
        setProductList(res.payload);
      } else {
        showToast({
          title: res.errors || '服务器开小差了',
          mask: true,
          icon: 'none',
        });
      }
    });
  };

  // 本周已获得
  const toastSuccess = () => {
    showToast({ title: '本周已领1000小熊币，下周继续拿', icon: 'none' });
  };

  // 月分享次数
  const getWeeklyShareMonthlyCount = () => {
    getMonthlyCount({ uid: userId, subject: 'ART_APP' }).then((res) => {
      if (res.code === 0) {
        setMonthLyCount(res.payload);
      }
    });
  };

  // 未获得 分享
  const toShare = (isOpenPoster = true, uid = '') => {
    if (!userId && !uid) {
      return;
    }

    // 检查用户是否可购买
    showLoading({ title: '加载中' });
    checkUserBuy({ uid: uid || userId })
      .then((res) => {
        hideLoading();
        let {
          code,
          payload: {
            artStudyEnd,
            artBuyStatus,
            artInStudying,
            writeBuyStatus,
            writeInStudying,
          },
        } = res;
        if (code === 0) {
          const isAuth = !(
            (artBuyStatus === 'SYSTEM' && artInStudying) ||
            (artBuyStatus === 'SYSTEM' && !artInStudying && artStudyEnd) ||
            (writeBuyStatus === 'SYSTEM' && writeInStudying)
          );

          setCheckUserBuyMessage(res.payload);
          setPayload1(isAuth);

          if (isAuth && isOpenPoster) {
            // showToast({
            //   title: '仅限小熊系统课在读学员参与',
            //   icon: 'none',
            //   mask: true,
            // });
            setShowError(true);
            return;
          }
          if (isOpenPoster) {
            setPostDailog(true);
            uploadRef.current.getShareReportHandler(true);
          }
        }
      })
      .catch(() => hideLoading());
  };

  const getUserIdentity = async () => {
    if (!userId) {
      return null;
    }
    const identitys = {
      // REGISTER:注册用户 EXPERIENCE:体验课用户 SYSTEM:系统课用户
      REGISTER: ['ACTIVE'],
      EXPERIENCE: ['STATUS10', 'STATUS20'],
      SYSTEM: [
        'STATUS30',
        'STATUS40',
        'STATUS50',
        'STATUS60',
        'STATUS70',
        'STATUS80',
        'STATUS90',
        'STATUS100',
        'STATUS110',
        'STATUS120',
      ],
      OTHER: ['CLOSE'],
    };
    try {
      const users = await getUser({ userId });
      let {
        payload: { status = null },
      } = users;
      users.code == 0 && dispatch(updateUserInfo(users.payload));
      const userIdentity =
        status &&
        Object.entries(identitys).find((item) => {
          return item[1].includes(status);
        });
      return (
        status && {
          identityStr: userIdentity ? userIdentity[0] : null,
          status,
        }
      );
    } catch (error) {
      console.error('获取用户信息失败');
      return null;
    }
  };

  // 点击商品
  const goodsHandle = (item) => {
    setCurGoods(item);
    setGoodsDetailSta(true);
  };

  // 是否可兑换
  const exchangeHandle = ({ pos, uid = '' }) => {
    switch (pos) {
      case '分享':
        toShare(true, uid);
        break;
      case '下周参与':
        showToast({ title: '本周已领1000小熊币，下周继续拿', icon: 'none' });
        break;
      default:
        setTips(true);
        break;
    }
  };

  return (
    <View className='indexWrap'>
      <WxLogin subject='ART_APP' />
      <IndexBg recentlyPriceStatusArr={recentlyPriceStatusArr} />
      <View className='paddWrap'>
        {userId && (
          <>
            {curStep === 1 ? (
              ['INREVIEW', 'ACTIVE', 'EXPIRE', 'REJECTED'].includes(
                uploadStatus,
              ) ? (
                <View className='top-share-soon'>
                  <AuthPhoneBtn authSuccess={(uid) => toShare(true, uid)}>
                    <Image
                      src={NoCompletedImg}
                      mode='widthFix'
                      className='img-1000 buling'
                    />
                  </AuthPhoneBtn>
                </View>
              ) : (
                <View
                  className='top-share-soon top-share-soon2'
                  onClick={() => toastSuccess()}
                >
                  <Image
                    className='img-1000'
                    src={CompletedImg}
                    mode='widthFix'
                  />
                </View>
              )
            ) : null}
            <NewUpload
              taskData={taskData}
              payload1={payload1}
              ref={uploadRef}
              toShareHandler={(isOpenPoster = true, uid = '') =>
                toShare(isOpenPoster, uid)
              }
              curStep={curStep}
              upCurStep={setCurStep}
              uploadedHandle={() => getSharePosterTask()}
            />
          </>
        )}

        <View className='coin-info'>
          <Image className='tit' src={TitleCoin} />
          <View className='content'>
            <View className='item'>
              <View>
                <Image className='coin-num' src={CoinOne} />
                <Text>分享朋友圈-审核通过</Text>
              </View>
              <View>
                <Text className='num'>+1000</Text>
                <Image className='coin' src={Coin} />
              </View>
            </View>
            <View className='item'>
              <View>
                <Image className='coin-num' src={CoinTwo} />
                <Text>好友通过分享海报报名体验版</Text>
              </View>
              <View>
                <Text className='num'>+8000</Text>
                <Image className='coin' src={Coin} />
              </View>
            </View>
            <View className='item'>
              <View>
                <Image className='coin-num' src={CoinThree} />
                <Text>好友体验后报名系统版</Text>
              </View>
              <View>
                <Text className='num'>+50 元</Text>
              </View>
            </View>
          </View>
          <View className='footer'>
            <Image src={StepStart} />
            <Text> 坚持参与分享，效果更好哦 </Text>
            <Image src={StepStart} />
          </View>
        </View>
        {/* {(highproductList.length > 0 || productList.length > 0) && (
          <ProductsExchange
            uploadStatus={uploadStatus}
            productList={productList}
            highproductList={highproductList}
            handleClick={goodsHandle}
            exchangeHandle={exchangeHandle}
          />
        )} */}
        {productList.length > 0 ? (
          <ProductList
            productList={productList}
            monthLyCount={monthLyCount}
            handleClick={goodsHandle}
            exchangeHandle={exchangeHandle}
          />
        ) : null}
        {['INREVIEW', 'ACTIVE', 'EXPIRE', 'REJECTED'].includes(uploadStatus) ? (
          <View className='share'>
            <AuthPhoneBtn
              authSuccess={(uid) => exchangeHandle({ pos: '分享', uid })}
            >
              <Image src={Share} className='share-img buling' mode='widthFix' />
            </AuthPhoneBtn>
          </View>
        ) : (
          <View className='share'>
            <Image
              src={ExchangeShareNextWeek}
              className='share-img'
              mode='widthFix'
              onClick={() => exchangeHandle({ pos: '下周参与' })}
            />
          </View>
        )}
      </View>
      <Postermodal
        show={postDailog}
        userBuyMessage={checkUserBuyMessage}
        onClose={() => setPostDailog(false)}
      />
      <PrizeDetails
        show={goodsDetailSta}
        onClose={() => setGoodsDetailSta(false)}
        // goodsId={curGoodsId}
        curGoods={curGoods}
        matchType={matchType}
      />
      <AtModal
        isOpened={showError}
        className='error-modal'
        closeOnClickOverlay={false}
      >
        <AtModalContent>
          <View className='error-box'>
            <View className='tit'>很遗憾！</View>
            <View className='info'>本次活动仅限于小熊美术</View>
            <View className='info'>系统课在读学员参加</View>
            <View className='phone'>
              当前账号：
              <Text className='text'>
                {mobile ? mobile.slice(0, 3) + '****' + mobile.slice(7) : ''}
              </Text>
            </View>
            <View className='btn' onClick={() => setShowError(false)}>
              我知道啦
            </View>
          </View>
        </AtModalContent>
      </AtModal>
      <AtModal
        isOpened={tips}
        onClose={() => setTips(false)}
        className='tips-modal'
        closeOnClickOverlay={false}
      >
        <AtModalContent>
          <View className='dl-wrap'>
            <Image src={ExchangeBg} mode='widthFix'></Image>
            <View className='footer-btn' onClick={() => setTips(false)}></View>
          </View>
        </AtModalContent>
      </AtModal>
    </View>
  );
};

export default Home;
