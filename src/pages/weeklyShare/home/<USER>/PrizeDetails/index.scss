.product-banner {
  width: 100%;

  .banner-swiper {
    height: 650px;
  }

  .img-box {
    width: 100%;
    height: 650px;
    display: flex;
    justify-content: center;
    align-items: center;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .swiper-pagination {
    text-align: right;
    width: 88px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 40px;
    color: #fff;
    font-size: 24px;
    right: 20px;
    left: auto;
    bottom: 30px;
  }
}

.product-decorate {
  width: 100%;
  min-height: 160px;
  background-color: #fff;
  padding: 24px 32px 32px 36px;
  margin-bottom: 30px;
  box-sizing: border-box;

  .product-name {
    font-size: 32px;
    margin-bottom: 11px;
    font-weight: 600;
  }

  .product-price {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left {
      font-size: 40px;
      color: #ff5050;
      display: flex;
      align-items: center;

      .icon {
        width: 48px;
        margin-right: 8px;
      }
    }

    .right {
      font-size: 24px;
      color: #888;
    }
  }
}

.product-detail-list {
  background-color: #fff;

  .part {
    box-sizing: border-box;
    padding: 27px 32px;

    .title {
      font-size: 32px;
      font-weight: 600;
      margin-bottom: 52px;
    }

    .row {
      font-size: 28px;
      // display: flex;
      image {
        width: 100%;
        max-width: 100% !important;
        height: auto;
        margin: 10px 0;
      }
    }
  }
}

.load {
  width: 300px;
}

.custom-goods-layout {
  .at-float-layout__container {
    background-color: #f7f7f7;
    border-radius: 32px 32px 0px 0px !important;
    height: auto !important;
    max-height: 70vh;
    overflow: hidden;

    .layout-body {
      height: auto !important;
      max-height: 70vh !important;
      padding: 0 !important;

      .layout-body__content {
        max-height: 70vh !important;
      }
    }
  }
}
