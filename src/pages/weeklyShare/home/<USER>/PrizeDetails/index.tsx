import { Image, RichText, Swiper, SwiperItem, View } from '@tarojs/components';
import { FC, useEffect, useState } from 'react';
// import { hideLoading, showLoading } from '@tarojs/taro';
// import { getGoodsInfoApi } from '@/api/invitetask';
// import { nodesRep } from '@/utils';
import CoinImg from '@/assets/weekSharePoster/icon-coin.png';
import { AtFloatLayout } from 'taro-ui';
import './index.scss';

interface IProps {
  show: boolean;
  goodsId?: string | number;
  curGoods: any;
  matchType: any;
  onClose: Function;
}

const PrizeDetails: FC<IProps> = props => {
  const [prizeDetailData, setPrizeDetailData] = useState<any>({});
  const [banner, setBanner] = useState([]);
  const [text, setText] = useState<any[]>([]);
  const [picture, setPicture] = useState<any[]>([]);
  const [scrollY, setScrollY] = useState(0);

  const { curGoods, show } = props;

  useEffect(() => {
    if (!curGoods) return;
    const {
      imageUrl,
      title,
      yzExchangePrice,
      totalSoldNum,
      picture: pictures,
    } = curGoods;
    setScrollY(0.1);
    setBanner([imageUrl]);
    setPrizeDetailData({
      epcReferName: title,
      exchangePrice: yzExchangePrice.points,
      falseSellNumber: totalSoldNum,
    });
    if (pictures) {
      setPicture(JSON.parse(pictures));
    }
  }, [curGoods]);

  // 获取商品详情
  // const getGoodsDetail = id => {
  //   showLoading({ title: '加载中' });
  //   getGoodsInfoApi({
  //     businessType: 'BEAR',
  //     currencyType: 'STONE',
  //     goodsId: id,
  //   })
  //     .then(res => {
  //       if (res.code == 0) {
  //         let data = res.payload;
  //         if (data.imGoodsPaymentList.length) {
  //           let _it = data.imGoodsPaymentList.find(
  //             o => o.exhibitionStatus == 'SHOW',
  //           );
  //           if (!['COINANDCASH', 'CLASSCASH'].includes(data.paymentType))
  //             data.exchangePrice = _it[props.matchType[data.paymentType]];
  //           else
  //             data.exchangePrice =
  //               _it[props.matchType[data.paymentType][0]] +
  //               '+' +
  //               _it[props.matchType[data.paymentType][1]];
  //         }
  //         setPrizeDetailData(data);
  //         const banners = data.img
  //           ? data.img.split(',')
  //           : data.slideshow.split(',')
  //           ? data.slideshow.split(',')
  //           : [require('@/assets/weekSharePoster/product-default.png')];
  //         setBanner(banners);
  //         setShow(true);
  //         setScrollY(0.1);

  //         // 实物商品不显示使用规则
  //         if (data && data.type != 1) {
  //           setText([
  //             {
  //               title: '使用规则',
  //               part: data.rule || '暂无使用规则',
  //             },
  //             {
  //               title: '商品详情',
  //               part: data.imageText || '暂无商品详情',
  //             },
  //           ]);
  //         } else {
  //           setText([
  //             {
  //               title: '商品详情',
  //               part: data.imageText || '暂无商品详情',
  //             },
  //           ]);
  //         }
  //       }
  //     })
  //     .finally(() => hideLoading());
  // };

  return (
    <AtFloatLayout
      className='custom-goods-layout'
      isOpened={show}
      scrollTop={scrollY}
      scrollWithAnimation
      onClose={() => {
        props.onClose();
      }}
    >
      <View className='product-banner'>
        <Swiper className='banner-swiper'>
          {banner.map((item: any, index: number) => {
            return (
              <SwiperItem key={index} className='swiper-slide'>
                <View>
                  <View className='img-box'>
                    <Image src={item} className='pic' mode='aspectFit' />
                  </View>
                </View>
              </SwiperItem>
            );
          })}
        </Swiper>
      </View>
      <View className='product-decorate'>
        <View className='product-name'>{prizeDetailData.epcReferName}</View>
        <View className='product-price'>
          <View className='left'>
            <Image src={CoinImg} className='icon' mode='widthFix'></Image>
            {prizeDetailData.exchangePrice}
          </View>
          <View className='right'>已兑换{prizeDetailData.falseSellNumber}</View>
        </View>
      </View>
      <View className='product-detail-list'>
        {/* {text.map((item, idx) => {
          return (
            <View className='part' key={idx}>
              <View className='title'>{item.title}</View>
              <View className='row'>
                <RichText nodes={nodesRep(item.part)}></RichText>
              </View>
            </View>
          );
        })} */}
        <View className='part'>
          <View className='title'>商品详情</View>
          <View className='row'>
            {picture.map((item, index) => (
              <Image
                key={index}
                src={item.url}
                className='icon'
                mode='widthFix'
              ></Image>
            ))}
          </View>
        </View>
      </View>
    </AtFloatLayout>
  );
};

export default PrizeDetails;
