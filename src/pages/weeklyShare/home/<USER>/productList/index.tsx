import { Image, View } from '@tarojs/components';
import TitleGift from '@/assets/weeklyShare/title-gift.png';
import InviteGiftCoin from '@/assets/weeklyShare/coin.png';
import StepStart from '@/assets/weeklyShare/step-start.png';
import './index.scss';

export default props => {
  const { productList, handleClick, monthLyCount, exchangeHandle } = props;

  const exchangeHandler = item => {
    if (monthLyCount < 2) {
      handleClick(item);
      return;
    }
    exchangeHandle({ item });
  };

  return (
    <View className='product-list-wrap'>
      <View className='product-list-title'>
        <Image src={TitleGift} />
      </View>
      <View className='product-list'>
        <View className='list'>
          {productList.map(item => (
            <View
              className='item'
              key={item.id}
              onClick={() => exchangeHandler(item)}
            >
              <View className='head-img'>
                <Image style='object-fit: cover' src={item.imageUrl} />
              </View>
              <View className='name'>{item.title}</View>
              <View className='peice-num'>
                <View className='left'>
                  <Image src={InviteGiftCoin} />
                  {item?.yzExchangePrice?.points}
                </View>
                <View>已兑换 {item.totalSoldNum}</View>
              </View>
            </View>
          ))}
        </View>
      </View>
      <View className='upload-text-bottom'>
        <Image src={StepStart} />
        此页面仅做展示，如需兑换请前往小熊商城哦
        <Image src={StepStart} />
      </View>
    </View>
  );
};
