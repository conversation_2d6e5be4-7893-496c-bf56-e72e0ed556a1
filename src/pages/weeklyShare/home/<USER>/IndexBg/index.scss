.indexBg {
  position: relative;
  width: 100%;
  height: 800px;
  background-image: url('https://fe-cdn.xiaoxiongmeishu.com/ai-app-user/online/images/1708595223251_banner.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;

  .index-top {
    padding: 12px 21px;
    padding-right: 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    box-sizing: border-box;
    position: relative;

    .robot-main {
      width: 100%;
      height: 46px;

      .robot-swiper {
        height: 100%;
        width: 80%;

        .robot-swiper-item {
          .robot-swiper-item-main {
            background-color: #ffab56;
            border-radius: 40px;
            height: 100%;
            display: inline-flex;
            align-items: center;
            padding: 0 16px;

            image {
              width: 30px;
              height: 30px;
              border-radius: 20px;
              margin-right: 15px;
            }

            text {
              font-size: 24px;
              color: #fff;
            }
          }
        }
      }
    }
  }

  .huodong {
    width: 128px;
    line-height: 40px;
    background: rgba(0, 0, 0, 0.3);
    box-shadow: 0px 4px 8px 0px rgba(185, 65, 2, 0.12);
    border-radius: 23px 0px 0px 23px;
    font-size: 24px;
    font-weight: 400;
    color: #ffffff;
    text-align: center;
    text-shadow: 0px 4px 8px rgba(185, 65, 2, 0.12);
    position: absolute;
    top: 20px;
    right: 0;
    z-index: 10;
  }

  .poster-swiper-wrap {
    position: absolute;
    top: 608px;
    width: 100%;
  }

  .rule-popup {
    padding: 0 30px;
    box-sizing: border-box;

    p {
      margin: 0;
    }

    .close-icon {
      width: 24px;
      height: 24px;
      background: url('../../../../../assets/weekSharePoster/close-icon.png')
        no-repeat;
      background-size: cover;
      position: absolute;
      top: 36px;
      right: 44px;

      &:after {
        content: '';
        position: absolute;
        top: -20px;
        right: -20px;
        bottom: -20px;
        left: -20px;
      }
    }

    .title {
      font-size: 40px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #222222;
      line-height: 56px;
      text-align: center;
      margin: 40px 0 7px;
    }

    .rule-content {
      height: 950px;
      overflow-y: auto;

      .rule-item {
        margin-top: 42px;

        &-title {
          font-size: 36px;
          font-weight: 500;
          color: #222222;
          line-height: 50px;
          margin-bottom: 21px;
        }

        &-image {
          width: 533px;
          height: auto;
          margin-left: 44px;
        }

        &-summary {
          padding-left: 44px;
          margin-bottom: 24px;
          font-size: 28px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 40px;
          white-space: pre-line;
        }

        &-spot {
          position: relative;

          &:after {
            position: absolute;
            top: 16px;
            left: 18px;
            content: '';
            display: block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #cccccc;
          }
        }
      }
    }
  }

  .prices-list {
    position: relative;
    width: 100%;
    height: 410px;
    margin-top: 40px;

    .prices-item {
      position: absolute;

      .prices-img {
        width: 143px;
      }

      &:nth-child(1) {
        bottom: 0;
        left: 47px;
      }

      &:nth-child(2) {
        bottom: 103px;
        left: 150px;
      }

      &:nth-child(3) {
        bottom: 139px;
        left: 304px;
      }

      &:nth-child(4) {
        bottom: 103px;
        right: 150px;
      }

      &:nth-child(5) {
        bottom: 20px;
        right: 67px;

        .prices-img {
          width: 103px;
        }
      }
    }
  }

  .present-task {
    text-align: center;
    margin-top: 68px;

    button {
      line-height: 0 !important;
    }

    .present-task-name {
      font-size: 24px;
      color: #a84911;
      margin-bottom: 14px;
    }

    .present-task-img image {
      width: 231px;
      height: 70px;
      margin-bottom: 25px;
      padding-left: 14px;
    }

    .present-task-to-list image {
      width: 142px;
      height: 20px;
    }
  }

  .layout-header {
    padding: 40px 0 0;
    background-color: #fff;
    font-size: 40px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #222222;
    text-align: center;

    .layout-header__title {
      padding-right: 0;
    }

    .layout-header__btn-close {
      top: 52% !important;
    }
  }
}

.rule-content {
  height: 950px;
  overflow-y: auto;
  padding: 0 10px;

  .rule-item {
    margin-top: 42px;

    &-title {
      font-size: 36px;
      font-weight: 500;
      color: #222222;
      line-height: 50px;
      margin-bottom: 21px;
    }

    &-image {
      width: 533px;
      height: auto;
      margin-left: 44px;
    }

    &-summary {
      padding-left: 44px;
      margin-bottom: 24px;
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 40px;
      white-space: pre-line;
    }

    &-spot {
      position: relative;

      &:after {
        position: absolute;
        top: 16px;
        left: 18px;
        content: '';
        display: block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #cccccc;
      }
    }
  }
}

.index-bg-81 {
  background-image: url('../../../../../assets/weekSharePoster/index-bg-81.png');
  background-repeat: no-repeat;
  background-size: cover;
}
