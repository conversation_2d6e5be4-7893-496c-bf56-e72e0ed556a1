import { Image, Swiper, SwiperItem, Text, View } from '@tarojs/components';
import { FC, useEffect, useState } from 'react';
import { navigateTo } from '@tarojs/taro';
import { getRandRobot } from '@/api/groupbuy';
import { AtFloatLayout } from 'taro-ui';
import AuthPhoneBtn from '@/components/AuthPhoneBtn';
import Status1 from '@/assets/weekSharePoster/prices-img-failed.png';
import Status2 from '@/assets/weekSharePoster/prices-img-ACTIVE.png';
import Status3 from '@/assets/weekSharePoster/prices-img-next.png';
import Status4 from '@/assets/weekSharePoster/prices-img-more.png';
import Status5 from '@/assets/weekSharePoster/prices-img-PICKED.png';
import FinishSta1 from '@/assets/weekSharePoster/task-1000-finished.png';
import FinishSta2 from '@/assets/weekSharePoster/task-1000-unfinished.png';
import Total from '@/assets/weekSharePoster/task-to-list.png';
import { ruleSource } from '../../data.config';
import './index.scss';

interface Iprops {
  recentlyPriceStatusArr: any;
}

const statusKeyof = {
  failed: Status1,
  ACTIVE: Status2,
  next: Status3,
  more: Status4,
  PICKED: Status5,
};

const IndexBg: FC<Iprops> = props => {
  const [isOpened, setIsOpened] = useState(false);
  const [list, setList] = useState<any[]>([]);

  useEffect(() => {
    getRandRobotHandle();
  }, []);

  // 获取机器人
  const getRandRobotHandle = () => {
    getRandRobot({ num: 30 }).then(res => {
      if (res.code == 0) {
        setList(res.payload);
      }
    });
  };

  // 跳转累计已赚
  const linkMore = () => {
    navigateTo({
      url: '/pages/weeklyShare/historyTask/index',
    });
  };

  return (
    <View className='indexBg'>
      <View className='index-top'>
        {/* <View className='robot-main'>
          <Swiper className='robot-swiper' vertical circular autoplay>
            {list.map((item, idx) => {
              return (
                <SwiperItem className='robot-swiper-item' key={idx}>
                  <View className='robot-swiper-item-main'>
                    <Image
                      src={`https://s1.meixiu.mobi/${item.headimg}?x-oss-process=image/resize,h_50,m_lfit`}
                      mode='aspectFill'
                    />
                    <Text>{item.username}</Text>
                    <Text>已经领取1000小熊币</Text>
                  </View>
                </SwiperItem>
              );
            })}
          </Swiper>
        </View> */}
        <View className='huodong' onClick={() => setIsOpened(true)}>
          活动规则
        </View>
      </View>
      {/* <View className='prices-list'>
        {props.recentlyPriceStatusArr.map((item, idx) => {
          return (
            <View className='prices-item' key={idx}>
              <Image
                className='prices-img'
                src={statusKeyof[item.status]}
                mode='widthFix'
              />
            </View>
          );
        })}
      </View>
      <View className='present-task'>
        <View className='present-task-name'>
          {props.recentlyPriceStatusArr[2].status == 'PICKED'
            ? '本周已获得'
            : '我的本周待领'}
        </View>
        <View className='present-task-img'>
          <Image
            src={
              props.recentlyPriceStatusArr[2].status == 'PICKED'
                ? FinishSta1
                : FinishSta2
            }
            mode='widthFix'
          ></Image>
        </View>
        <AuthPhoneBtn authSuccess={() => linkMore()}>
          <View className='present-task-to-list'>
            <Image src={Total} mode='widthFix' />
          </View>
        </AuthPhoneBtn>
      </View> */}
      <AtFloatLayout
        className='custom-float-layout'
        isOpened={isOpened}
        onClose={() => {
          setIsOpened(false);
        }}
        title='— 活动规则 —'
      >
        <View className='rule-content'>
          {ruleSource.map((item, idx) => {
            return (
              <View className='rule-item' key={idx}>
                <View className='rule-item-title'>{item.title}</View>
                {item.summary.map((childItem, idx1) => {
                  return (
                    <View
                      key={idx1}
                      className={`rule-item-summary ${
                        item.spot ? 'rule-item-spot' : ''
                      }`}
                      style={{ color: childItem.color || '' }}
                    >
                      {childItem.des || childItem}
                    </View>
                  );
                })}
                {item.image && (
                  <Image
                    className='rule-item-image'
                    src={item.image}
                    mode='widthFix'
                  ></Image>
                )}
              </View>
            );
          })}
        </View>
      </AtFloatLayout>
    </View>
  );
};

export default IndexBg;
