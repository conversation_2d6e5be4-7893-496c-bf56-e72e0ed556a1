.new-upload-wrap {
  width: 100%;
  font-size: 40px;
  font-family: PingFangSC-Medium, PingFang SC;
  box-sizing: border-box;
  border-radius: 39px;
  position: relative;

  .upload-text-bottom {
    position: absolute;
    bottom: 33px;
    width: 100%;
    display: block;
    text-align: center;
    font-size: 30px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ff6a54;
    letter-spacing: 1px;
  }

  // .upload-title {
  //   position: relative;
  //   top: 70px;
  //   font-size: 0;
  //   display: flex;
  //   justify-content: center;
  //   width: 100%;
  //   height: 90px;
  //   img {
  //     width: 660px;
  //   }
  // }

  .content {
    width: 700px;
    margin: 0 auto;
    border-radius: 30px;
    overflow: hidden;
    box-sizing: border-box;

    .content-box-middle {
      width: 100%;
      height: 100%;
      position: relative;
    }

    .upload-title {
      margin: 0px auto;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 40px;
      font-weight: bold;
      color: #333333;
      text-align: center;
      margin-bottom: 10px;

      .active-cutdown {
        font-family: PingFangSC-Medium, PingFang SC;
        text-align: center;
        color: #333333;
        line-height: 58px;
        font-size: 24px;
        .cutdown-time {
          display: inline-block;
        }
        .custom-time-main-upload {
          margin-left: 5px;
          .at-countdown__item {
            font-weight: normal;
            .at-countdown__time-box {
              background: #ffd777;
              box-shadow: 0px 1px 0px 0px rgba(255, 255, 255, 0.5),
                inset 0px 1px 1px 0px #ff371d;
              border-radius: 2px;
              margin: 0 4px;
              width: 40px;
              height: 40px;
              line-height: 40px;
              font-size: 26px;
              border-radius: 6px;
              display: inline-block;

              .at-countdown__time {
                font-size: 28px;
                color: #ff371d;
                font-family: Arial-BoldMT, Arial;
                line-height: 32px;
              }
            }

            .at-countdown__separator {
              color: #333333;
              font-size: 24px;
            }
          }
        }
      }
    }

    .content-box {
      width: 700px;
      height: 600px;
      border-radius: 32px;
      box-sizing: border-box;
      margin: 0 auto;
      position: relative;
      .content-box-title-bar {
        position: relative;
        .content-box-title {
          display: flex;
          justify-content: space-around;
          align-items: center;
          padding: 15px 0;
          position: relative;
          z-index: 10;
          > image {
            width: 258px;
            height: 56px;
          }
        }
        .content-box-title-bg {
          box-sizing: border-box;
          position: absolute;
          left: 0;
          top: 10px;
          width: 100%;
          height: 140px;
          z-index: 1;
          background: linear-gradient(
            to bottom,
            rgba(255, 245, 196, 1),
            rgba(252, 233, 133, 1)
          );
          border-radius: 20px 20px 0 0;
          border: 4px solid #fff;
        }
      }
    }

    .content-box-bg {
      width: 100%;
      height: 100%;
      position: absolute;
      z-index: 10;
      left: 0;
      top: 0;
    }

    .content-box-main {
      width: 100%;
      height: 500px;
      position: absolute;
      left: 0;
      top: 100px;
      z-index: 50;
      &-step1 {
        .img-1000 {
          // 不想再改了。。
          opacity: 0;
          width: 100%;
          height: 208px;
          margin-top: 20px;
        }
        .step1-tip {
          height: 200px;
          padding-right: 20px;
          display: flex;
          justify-content: space-evenly;
          align-items: center;
          position: relative;
          .item {
            margin-top: 12px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            image {
              width: 112px;
              height: 112px;
            }
            .tit {
              margin-top: 12px;
              font-size: 24px;
              font-weight: 500;
              color: #333333;
              line-height: 34px;
            }
            .desc {
              font-size: 18px;
              font-weight: 400;
              color: #666666;
              line-height: 26px;
            }
          }
          .item:nth-of-type(2) {
            padding-right: 20px;
          }
          .arrow {
            position: absolute;
            top: 46px;
            width: 48px;
            height: 48px;
            z-index: 100;
            left: 220px;
          }
          .arrow:nth-of-type(2) {
            left: 446px;
          }
        }
      }
      &-step2 {
        .title {
          text-align: center;
          color: #fa6c3a;
          font-size: 24px;
          font-weight: 400;
          color: #fa6c3a;
          line-height: 24px;
          padding: 20px;
          image {
            width: 16px;
            height: 16px;
          }
        }
        .content {
          height: 400px;
          display: flex;
          justify-content: center;
          &-left {
            width: 310px;
            height: 400px;
            background: #fa6c3a;
            border-radius: 16px;
            position: relative;
            text-align: center;
            margin-right: 20px;
            .tit {
              font-size: 24px;
              font-weight: 400;
              color: #ffffff;
              line-height: 50px;
            }
            .demo {
              width: 278px;
              height: 334px;
            }
            .glass {
              width: 48px;
              height: 48px;
              position: absolute;
              bottom: 24px;
              right: 20px;
              z-index: 100;
            }
          }
          &-right {
            width: 310px;
            height: 310px;
            padding-top: 90px;
            background: #ffedc2;
            text-align: center;
            border-radius: 16px;
            .upload-type-img {
              &.default {
                width: 128px;
                height: 128px;
              }
              &.pending,
              &.success,
              &.fail {
                width: 64px;
                height: 64px;
              }
            }
            .before-upload {
              text-align: center;
              color: #fa6c3a;
              font-size: 32px;
              font-weight: 500;
              line-height: 44px;
              &-title {
                margin-top: 10px;
                margin-bottom: 20px;
              }
              &-time {
                font-weight: 400;
                color: #333333;
              }
            }
            .upload {
              position: relative;
              text-align: center;
              height: 100%;
              .upload-text {
                font-size: 32px;
                color: #fa6c3a;
                margin-top: 10px;
                margin-bottom: 20px;
              }
              .upload-sub-text {
                font-size: 24px;
                font-weight: 400;
                color: #666666;
                line-height: 34px;
              }
              .upload-sub-btn {
                width: 144px;
                height: 48px;
                background: #fa6c3a;
                border-radius: 24px;
                text-align: center;
                font-size: 24px;
                font-weight: 400;
                color: #ffffff;
                line-height: 48px;
                margin: 10px auto;
              }
              .select-img-input {
                position: absolute;
                top: 50%;
                left: 50%;
                z-index: 2;
                transform: translate(-50%, -50%);
                width: 100%;
                height: 100%;
                opacity: 0;
              }
            }
          }
        }
      }
    }
  }

  .dl-upload-example {
    .dl-wrap {
      padding: 40px 30px;

      .title {
        font-size: 32px;
        font-weight: 500;
      }

      .desc {
        p {
          font-size: 26px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #222222;
          line-height: 37px;
        }
      }

      .img-desc {
        border: 1px solid #f5f5f5;

        image {
          width: 100%;
        }
      }

      .btn {
        width: 466px;
        height: 96px;
        margin-top: 54px;
        margin-left: 50%;
        transform: translateX(-50%);
        background: linear-gradient(90deg, #ff9f42 0%, #ff512e 100%);
        border-radius: 48px;
        font-size: 36px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 96px;
        text-align: center;
      }
    }
  }
}
