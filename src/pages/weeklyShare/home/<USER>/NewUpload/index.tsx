import { Image, Text, View } from '@tarojs/components';
import {
  FC,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { useSelector } from 'react-redux';
import { AtCountdown } from 'taro-ui';
import { dateFormat } from '@/utils/index';
import AuthPhoneBtn from '@/components/AuthPhoneBtn';
import {
  chooseImage,
  hideLoading,
  navigateTo,
  showLoading,
  showToast,
} from '@tarojs/taro';
import uploadFile from '@/utils/uploadFile';
import { submitScreenShot } from '@/api';
import {
  getClaimTask,
  shareReportHandler,
  checkActiveStatusInfo,
} from '@/api/groupbuy';
import { UserStateType } from '@/store/groupbuy/state';
import Step1Bg from '@/assets/weeklyShare/step1-bg.png';
import Step2Bg from '@/assets/weeklyShare/step2-bg.png';
import NumOne from '@/assets/weeklyShare/num-one.png';
import NumTwo from '@/assets/weeklyShare/num-two.png';
import Title1000 from '@/assets/weeklyShare/title-1000.png';
import StepWx from '@/assets/weeklyShare/step-wx.png';
import StepArrow from '@/assets/weeklyShare/step-arrow.png';
import StepUpload from '@/assets/weeklyShare/step-upload.png';
import StepVerify from '@/assets/weeklyShare/step-verify.png';
import MagnifyingGlass from '@/assets/weeklyShare/magnifying-glass.png';
import ScreenshotDemo from '@/assets/weeklyShare/screenshot-demo.png';
import StepStart from '@/assets/weeklyShare/step-start.png';
import AddBtn from '@/assets/weekSharePoster/add-btn.png';
import UploadIcon from '@/assets/weeklyShare/upload-plus.png';
import uploadingIcon from '@/assets/weeklyShare/upload-process.png';
import uploadSuccessIcon from '@/assets/weeklyShare/upload-success.png';
import uploadFailIcon from '@/assets/weeklyShare/upload-error.png';
import ScreenShotExam from '../../../components/ScreenShotExam';
import './index.scss';

interface IProps {
  taskData: any;
  payload1: boolean;
  toShareHandler: Function;
  uploadedHandle: Function;
}

const NewUpload = forwardRef((props: any, ref) => {
  useImperativeHandle(ref, () => ({
    getShareReportHandler,
  }));
  const [isUpload, setIsUpload] = useState(false);
  const [taskShow, setTaskShow] = useState<any>({});
  const [taskLogId, setTaskLogId] = useState('');
  const [taskId, setTaskId] = useState('');
  const [uploadUrl, setUploadUrl] = useState('');
  const [isOverShareTime, setIsOverShareTime] = useState(false);
  const [overTime, setOverTime] = useState('');
  const [userInfoDesc, setUserInfoDesc] = useState('');
  const [screenShortModal, setScreenShortModal] = useState(false); // 截图示例弹窗
  const uploadJson = {
    ACTIVE: {
      img: UploadIcon,
      text: '点击选择截图',
      mdtext: '',
      subText: '',
      class: 'default',
    },
    INREVIEW: {
      img: uploadingIcon,
      text: '图片已上传',
      mdtext: '人工审核时效',
      subText: '48小时-72小时(工作日)',
      class: 'pending',
    },
    PICKED: {
      img: uploadSuccessIcon,
      text: '审核成功',
      mdtext: '小熊币已到账',
      subText: '下周可继续分享哦~',
      class: 'success',
    },
    REJECTED: {
      img: uploadFailIcon,
      text: '审核失败',
      mdtext: '请重新上传或重新分享',
      class: 'fail',
    },
  };

  const userId = useSelector((state: UserStateType) => state.userid);

  // 计算时间
  const timeInfo = useMemo(() => {
    const date = new Date();
    const day = 7 - date.getDay();
    const hours = 23 - date.getHours();
    const minutes = 60 - date.getMinutes();
    const seconds = 60 - date.getSeconds();

    return { day, hours, minutes, seconds };
  }, []);

  // 是否点击过分享按钮
  useEffect(() => {
    if (userId) {
      checkActiveStatusInfo({
        uid: userId,
        eventType: 'SHARE_POSTER_COPY_BUTTON',
      }).then((res) => {
        if (res.code === 0) {
          if (res.payload) {
            props.upCurStep(2);
          }
        }
      });
      getShareReportHandler();
    }
  }, [userId]);

  useEffect(() => {
    /**
     *  ACTIVE 去上传分享截图
     *  INREVIEW 审核中
     *  PICKED 任务完成
     *  REJECTED 审核未通过
     *  EXPIRE  未完成
     */
    if (!props.taskData) return;
    setTaskLogId(props.taskData.taskLogId);
    setTaskId(props.taskData.taskId);
    let num = props.taskData.amount || 800;
    num += props.taskData.extraAmount || 0;
    switch (props.taskData.status) {
      case 'EXPIRE':
      case 'ACTIVE':
        setIsUpload(false);
        setTaskShow({
          statusImg: null,
          borderColor: '#FFC875',
          text: '去上传分享截图',
        });
        break;
      case 'INREVIEW':
        setIsUpload(true);
        setTaskShow({
          statusImg: require('@/assets/weekSharePoster/status/task-INREVIEW.png'),
          borderColor: '#FFC875',
          text: '等待审核结果,领取奖励',
        });
        break;
      case 'PICKED':
        setIsUpload(true);
        setTaskShow({
          statusImg: require('@/assets/weekSharePoster/status/task-PICKED.png'),
          borderColor: '#58D64C',
          text: `${num}小熊币已到账`,
        });
        break;
      case 'REJECTED':
        setIsUpload(true);
        setTaskShow({
          statusImg: require('@/assets/weekSharePoster/status/task-REJECTED.png'),
          borderColor: '#B4572C',
          text: '点击图片查看失败原因',
        });
        break;
    }
  }, [props.taskData]);

  // 上传
  const allowToUpload = () => {
    if (['PICKED', 'INREVIEW'].includes(props.taskData.status)) {
      return;
    }
    if (props.payload1) {
      showToast({
        title: '仅限小熊系统课在读学员参与',
        icon: 'none',
      });
      return;
    }
    chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['album'],
      success: (res) => {
        if (res.tempFilePaths?.length > 0) {
          setUploadUrl(res.tempFilePaths[0]);
          uploadHandle(res.tempFilePaths[0]);
        }
      },
    });
  };
  // 获取当前点击分享的时间 type为ture则上报点击，且只保留第一次的点击时间
  const getShareReportHandler = (type = false) => {
    if (['PICKED', 'INREVIEW'].includes(props.taskData.status)) {
      return;
    }
    shareReportHandler({
      uid: userId,
      report: type,
    }).then((res) => {
      const { payload } = res;
      if (type) getShareReportHandler();
      if (payload) {
        // 是否过了可以上传截图的时间
        setIsOverShareTime(
          new Date().getTime() > payload * 1 + 60 * 60 * 2 * 1000,
        );
        // 到什么时间可以上传截图
        setOverTime(
          dateFormat(payload * 1 + 60 * 60 * 2 * 1000, 'MM-dd hh:mm'),
        );
        // 点击分享的时间
        setUserInfoDesc(payload);
      }
    });
  };

  // 上传截图
  const uploadHandle = async (temp: string) => {
    showLoading();
    try {
      const res = await uploadFile({
        path: temp,
        entryName: 'weeklyShare',
      });
      if (res && res.url) {
        let logId = '';
        if (taskLogId) {
          logId = taskLogId;
        } else {
          // 根据任务id 获取 任务日志id
          const taskRes = await getClaimTask({ taskId, userId });
          if (taskRes.code == 0) {
            logId = taskRes.payload.taskLogId;
          }
        }
        setTaskLogId(logId);
        const res1 = await submitScreenShot({
          type: 'SHARE_TASK',
          businessLogId: logId,
          userId,
          uploadUrl: res.url,
        });
        if (res1.code === 0) {
          props.uploadedHandle();
        }
      }
      hideLoading();
    } catch (err) {
      hideLoading();
      console.error('err', err);
    }
  };

  // 上传截图页
  const toResult = () => {
    if (
      props.taskData.status === 'INREVIEW' ||
      props.taskData.status === 'PICKED' ||
      props.taskData.status === 'REJECTED'
    ) {
      navigateTo({
        url: `/pages/weeklyShare/results/index?amount=${
          props.taskData.amount || 0
        }&taskId=${props.taskData.taskId}`,
      });
    }
  };

  return (
    <View className='new-upload-wrap'>
      <View className='content'>
        <View className='upload-title'>
          <View className='active-cutdown'>
            {['INREVIEW', 'ACTIVE', 'EXPIRE', 'REJECTED'].includes(
              props.taskData.status,
            ) && (
              <View className='cutdown-time'>
                <Text>距离本期活动结束仅剩</Text>
                <AtCountdown
                  className='custom-time-main-upload'
                  isShowDay
                  day={timeInfo.day}
                  hours={timeInfo.hours}
                  minutes={timeInfo.minutes}
                  seconds={timeInfo.seconds}
                  format={{
                    day: '天',
                    hours: '时',
                    minutes: '分',
                    seconds: '',
                  }}
                />
              </View>
            )}
          </View>
        </View>
        <View className='content-box'>
          <Image
            src={props.curStep == 1 ? Step1Bg : Step2Bg}
            className='content-box-bg'
          />
          <View className='content-box-title-bar'>
            <View className='content-box-title'>
              <Image src={NumOne} onClick={() => props.upCurStep(1)} />
              <Image src={NumTwo} onClick={() => props.upCurStep(2)} />
            </View>
            <View className='content-box-title-bg'></View>
          </View>
          <View className='content-box-main'>
            {props.curStep === 1 ? (
              <View className='content-box-main-step1'>
                <Image className='img-1000' src={Title1000} />
                <View className='step1-tip'>
                  <View className='item'>
                    <Image src={StepWx} />
                    <View className='tit'>分享到朋友圈</View>
                    <View className='desc'>不可分组/私密/删除</View>
                  </View>
                  <Image className='arrow' src={StepArrow} />
                  <View className='item'>
                    <Image src={StepUpload} />
                    <View className='tit'>保留2小时后截图上传</View>
                    <View className='desc'>截图朋友圈上传</View>
                  </View>
                  <Image className='arrow' src={StepArrow} />
                  <View className='item'>
                    <Image src={StepVerify} />
                    <View className='tit'>等待审核</View>
                    <View className='desc'>最多48小时</View>
                  </View>
                </View>
              </View>
            ) : (
              <View className='content-box-main-step2'>
                <View className='title'>
                  <Image src={StepStart} />
                  <Text> 朋友圈保留2小时以上，不可分组/删除/私密 </Text>
                  <Image src={StepStart} />
                </View>
                <View className='content'>
                  <View className='content-left'>
                    <View className='tit'>请上传朋友圈分享的截图</View>
                    <Image className='demo' src={ScreenshotDemo} />
                    <Image
                      className='glass'
                      onClick={() => setScreenShortModal(true)}
                      src={MagnifyingGlass}
                    />
                  </View>
                  <View
                    className={`content-right ${isUpload ? 'hasBg' : ''} ${
                      isOverShareTime ? 'fail' : ''
                    }`}
                  >
                    {!isOverShareTime ? (
                      <View className='before-upload'>
                        {!userInfoDesc ? (
                          <AuthPhoneBtn
                            authSuccess={(uid) => {
                              props.toShareHandler(true, uid);
                            }}
                          >
                            <Image
                              src={UploadIcon}
                              className='upload-type-img default'
                            />
                            <View className='upload-text'>点击选择截图</View>
                          </AuthPhoneBtn>
                        ) : (
                          <>
                            <View className='before-upload-title'>
                              截图上传时间
                            </View>
                            <View className='before-upload-time'>
                              {overTime}后
                            </View>
                            <View className='before-upload-time'>可上传</View>
                          </>
                        )}
                      </View>
                    ) : (
                      <View className='upload' onClick={allowToUpload}>
                        <Image
                          className={`upload-type-img ${
                            uploadJson[props.taskData.status].class
                          }`}
                          src={uploadJson[props.taskData.status].img}
                        ></Image>
                        <View className='upload-text'>
                          {uploadJson[props.taskData.status].text}
                        </View>
                        <View className='upload-sub-text'>
                          {uploadJson[props.taskData.status].subText}
                        </View>
                        {props.taskData.status == 'REJECTED' && (
                          <View className='upload-sub-btn'>重新上传</View>
                        )}
                      </View>
                    )}
                  </View>
                </View>
              </View>
            )}
          </View>
        </View>
      </View>
      <ScreenShotExam
        show={screenShortModal}
        onClose={() => setScreenShortModal(false)}
      />
    </View>
  );
});

export default NewUpload;
