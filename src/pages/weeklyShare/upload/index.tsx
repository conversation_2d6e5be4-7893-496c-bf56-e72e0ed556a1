import { Image, View } from '@tarojs/components';
import { useEffect, useState } from 'react';
import {
  getClaimTask,
  getSharePosterLog,
  getSharePosterTaskApi,
} from '@/api/groupbuy';
import {
  chooseImage,
  hideLoading,
  redirectTo,
  showLoading,
  showToast,
  useRouter,
} from '@tarojs/taro';
import { useSelector } from 'react-redux';
import uploadFile from '@/utils/uploadFile';
import { submitScreenShot } from '@/api';
import { UserStateType } from '@/store/groupbuy/state';
import CloseIcon from '@/assets/weekSharePoster/screenshotClose.png';
import AddIcon from '@/assets/weekSharePoster/add_icon.png';
import ScreenShotExam from '../components/ScreenShotExam';
import './index.scss';

const textList = [
  {
    title: '如何完成任务？',
    icon: require('../../../assets/weekSharePoster/activityRules_icon.png'),
    subTitle: [
      '在朋友圈完成海报分享后，进入“微信-发现-朋友圈”，找到当时分享海报的朋友圈内容，进行截图保存至相册',
      '打开小熊美术App-我的-周周分享，点击「上传分享截图」按钮，按照截图示例进行上传即可',
    ],
  },
  {
    title: '如何提高通过率？',
    icon: require('../../../assets/weekSharePoster/activityRules_icon.png'),
    subTitle: [
      '需上传活动时间内生成的海报截图，海报在朋友圈需保留至少2个小时。',
    ],
  },
];

const Upload = () => {
  const { params } = useRouter();
  const [closeShow, setCloseShow] = useState(false);
  const [uploadUrl, setUploadUrl] = useState('');
  const [taskLogId, setTaskLogId] = useState('');
  const [taskId, setTaskId] = useState('');
  const [screenShortModal, setScreenShortModal] = useState(false);
  const [taskData, setTaskData] = useState<any>({});

  const userId = useSelector((state: UserStateType) => state.userid);

  useEffect(() => {
    userId && handleGetSharePosterTask();
  }, [userId]);

  const handleGetSharePosterTask = async () => {
    if (params.taskId) {
      const res = await getSharePosterLog({
        uid: userId,
        taskId: params.taskId,
      });
      let { code, payload, status } = res;
      if (code !== 0) {
        showToast({ title: status, icon: 'none' });
        return;
      }
      if (!Object.values(payload).length) {
        showToast({ title: '任务已过期', icon: 'none' });
        return;
      }
      setTaskData(res.payload);
      setTaskId(payload && payload.taskId);
      if (payload.taskLogId) {
        setTaskLogId(payload.taskLogId);
      } else {
        getClaimTaskHandle(payload.taskId);
      }
    } else {
      const res = await getSharePosterTaskApi({ uid: userId });
      let { code, payload, status } = res;
      if (code !== 0) {
        showToast({ title: status, icon: 'none' });
        return;
      }
      if (!Object.values(payload).length) {
        showToast({ title: '任务已过期', icon: 'none' });
        return;
      }
      setTaskData(payload);
      setTaskId(payload && payload.taskId);
      if (payload.taskLogId) {
        setTaskLogId(payload.taskLogId);
      } else {
        getClaimTaskHandle(payload.taskId);
      }
    }
  };

  // 根据任务id 获取logid
  const getClaimTaskHandle = async (id: string) => {
    const res = await getClaimTask({
      taskId: id,
      userId,
    });
    if (res.code == 0) {
      setTaskLogId(res.payload.taskLogId);
    } else {
      showToast({ title: res.errors, icon: 'none' });
    }
  };

  // 上传
  const allowToUpload = () => {
    if (['PICKED', 'INREVIEW'].includes(taskData.status)) {
      return;
    }
    if (taskData.status === 'EXPIRE') {
      showToast({
        title: '任务已过期',
        icon: 'none',
      });
      return;
    }
    chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['album'],
      success: res => {
        if (res.tempFilePaths?.length > 0) {
          setUploadUrl(res.tempFilePaths[0]);
          setCloseShow(true);
        }
      },
    });
  };

  // 确认上传
  const submit = () => {
    if (closeShow === false) {
      showToast({
        title: '还未上传截图',
        icon: 'none',
      });
      return;
    }
    uploadHandle();
  };

  // 上传截图
  const uploadHandle = async () => {
    showLoading({ title: '提交中，请稍后' });
    try {
      const res = await uploadFile({
        path: uploadUrl,
        entryName: 'weeklyShare',
      });
      if (res && res.url) {
        let logId = '';
        if (taskLogId) {
          logId = taskLogId;
        } else {
          // 根据任务id 获取 任务日志id
          const taskRes = await getClaimTask({ taskId, userId });
          if (taskRes.code == 0) {
            logId = taskRes.payload.taskLogId;
          }
        }
        setTaskLogId(logId);
        const res1 = await submitScreenShot({
          type: 'SHARE_TASK',
          businessLogId: logId,
          userId,
          uploadUrl: res.url,
        });
        if (res1.code === 0) {
          setTimeout(() => {
            showToast({
              title: '提交成功',
              icon: 'none',
              mask: true,
            });
            redirectTo({
              url: `/pages/weeklyShare/results/index?taskId=${taskId ||
                params.taskId}`,
            });
          }, 100);
        }
      }
      hideLoading();
    } catch (err) {
      hideLoading();
      console.error('err', err);
    }
  };

  return (
    <View className='upload'>
      <View className='screenshotTop'>
        <View className='title'>将分享海报的朋友圈截图并上传</View>
        <View className='addBox'>
          {closeShow === true && (
            <View className='closeImg' onClick={() => setCloseShow(false)}>
              <Image src={CloseIcon} mode='widthFix' />
            </View>
          )}

          {closeShow === false && (
            <View className='addIcon' onClick={() => allowToUpload()}>
              <Image src={AddIcon} mode='widthFix' />
            </View>
          )}
          {closeShow === true && (
            <View className='screenshot'>
              <Image src={uploadUrl} mode='aspectFill' />
            </View>
          )}
        </View>
        <View className='text' onClick={() => setScreenShortModal(true)}>
          查看截图示例
        </View>
        <View
          className={!closeShow ? 'activity' : 'submitBtn'}
          onClick={() => submit()}
        >
          确认上传
        </View>
      </View>
      <View className='screenshotBottom'>
        {textList.map((item, idx) => {
          return (
            <View className={'"box"'} key={idx}>
              <View className='title'>{item.title}</View>

              {item.subTitle.map((childItem, idx1) => {
                return (
                  <View className='subTitleContent' key={idx1}>
                    <View className='icon'>
                      <Image src={item.icon} mode='widthFix' />
                    </View>
                    <View className='subTitle'>{childItem}</View>
                  </View>
                );
              })}
            </View>
          );
        })}
      </View>
      <ScreenShotExam
        show={screenShortModal}
        onClose={() => setScreenShortModal(false)}
      />
    </View>
  );
};

export default Upload;
