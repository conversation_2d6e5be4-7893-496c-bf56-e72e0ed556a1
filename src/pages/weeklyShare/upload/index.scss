.screenshotTop {
  padding-top: 20px;

  .dialogImg {
    width: 100%;
    margin: 30px 0 24px;
  }

  .title {
    font-size: 28px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #3f3f3f;
    text-align: center;
    margin-bottom: 58px;
  }

  .screenshotBox {
    width: 600px;
    height: 406px;
    border-radius: 40px;
    border: 3px dashed #ff9c00;
    margin: 0px auto;
    margin-bottom: 36px;

    image {
      width: 556px;
      height: auto;
      border-radius: 40px;
    }
  }

  .footerText {
    font-size: 28px;
    color: #3f3f3f;
    text-align: center;
    margin-bottom: 83px;
  }

  .buttonBox {
    font-size: 32px;
    display: flex;
    justify-content: center;

    .leftBtn {
      width: 249px;
      height: 80px;
      border-radius: 52px;
      border: 2px solid #ff9c00;
      color: #ff9c00;
      text-align: center;
      line-height: 80px;
    }

    .rightBtn {
      width: 249px;
      height: 80px;
      border-radius: 52px;
      background: #ff9c00;
      font-weight: 500;
      color: #fff;
      text-align: center;
      line-height: 80px;
      margin-left: 24px;
    }
  }

  .addBox {
    width: 406px;
    height: 406px;
    border-radius: 40px;
    border: 3px dashed #ff9c00;
    margin: 0px auto;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 28px;
    position: relative;

    .closeImg {
      width: 56px;
      height: 56px;
      position: absolute;
      top: -28px;
      right: -28px;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .addIcon {
      width: 153px;
      height: 153px;

      image {
        width: 100%;
        height: 100%;
      }

      .select-img-input {
        position: absolute;
        left: 50%;
        top: 50%;
        width: 154px;
        height: 154px;
        margin-left: -79px;
        margin-top: -79px;
        z-index: 2;
        opacity: 0;
      }
    }

    .screenshot {
      width: 406px;
      height: 406px;
      display: flex;
      justify-content: center;

      image {
        max-width: 100%;
        max-height: 100%;
        border-radius: 40px;
      }
    }
  }

  .text {
    font-size: 26px;
    color: #ff9c00;
    text-align: center;
    margin-bottom: 66px;
  }

  .submitBtn {
    width: 463px;
    height: 80px;
    margin: 0px auto;
    background: #ff9c00;
    border-radius: 52px;
    font-size: 32px;
    color: #fff;
    font-weight: 500;
    text-align: center;
    line-height: 80px;
  }

  .activity {
    width: 463px;
    height: 80px;
    margin: 0px auto;
    background: #e6e6e6;
    border-radius: 52px;
    font-size: 32px;
    color: #fff;
    font-weight: 500;
    text-align: center;
    line-height: 80px;
  }
}

.screenshotBottom {
  width: 691px;
  box-sizing: border-box;
  padding: 25px 24px 0px 24px;
  border-radius: 28px;
  background: #f2f3f5;
  margin: 100px auto 0;

  .box {
    .title {
      font-size: 40px;
      color: #222;
      font-weight: 500;
      margin-bottom: 18px;
    }

    .subTitleContent {
      display: flex;

      .icon {
        line-height: 1;

        image {
          width: 18px;
          height: 6px;
        }
      }

      .subTitle {
        font-size: 28px;
        color: #666;
        margin-bottom: 24px;
        margin-left: 24px;
      }
    }
  }
}
