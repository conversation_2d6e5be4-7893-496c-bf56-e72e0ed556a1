import { Button, Image, Text, View } from '@tarojs/components';
import { useEffect, useState } from 'react';
import { redirectTo, showToast, useRouter } from '@tarojs/taro';
import { checkUserBuy, getSharePosterLog } from '@/api/groupbuy';
import { useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import RejectImg from '@/assets/weekSharePoster/results/REJECTED.png';
import Postermodal from '../home/<USER>/Postermodal';
import ScreenShotExam from '../components/ScreenShotExam';
import './index.scss';

const Results = () => {
  const { params } = useRouter();
  const [exampleShow, setExampleShow] = useState(false);
  const [status, setStatus] = useState('EXPIRE');
  const [taskName, setTaskName] = useState('');
  const [imageUrl, setImageUrl] = useState('');
  const [uploadTime, setUploadTime] = useState('');
  const [postDailog, setPostDailog] = useState(false);
  const [checkUserBuyMessage, setCheckUserBuyMessage] = useState({});
  const [textConfig, setTextConfig] = useState({
    INREVIEW: {
      title: '审核中',
      des: '提交的截图将在2-3个工作日审核完成',
      tips: '审核中',
    },
    LAST_WEEK_EXPIRE: {
      title: '任务失败',
      des: '可能原因：不符合截图示例标准',
      tips: '',
    },
    REJECTED: {
      title: '任务失败',
      des: '可能原因：不符合截图示例标准',
      tips: '',
    },
    EXPIRE: {
      title: '任务已过期',
      des: '未按照规定时间完成任务',
      tips: '',
    },
    PICKED: {
      title: '任务完成',
      des: '已发放1600个小熊币',
      tips: '已完成',
    },
    EXPIRE_REJECTED: {
      title: '任务失败',
      des: '可能原因：不符合截图示例标准',
      tips: '',
    },
  });

  const userId = useSelector((state: UserStateType) => state.userid);

  useEffect(() => {
    userId && getSharePosterLogDetail();
  }, [userId]);

  useEffect(() => {
    if (Object.keys(checkUserBuyMessage).length > 0) {
      setPostDailog(true);
    }
  }, [checkUserBuyMessage]);

  // 获取详情
  const getSharePosterLogDetail = () => {
    getSharePosterLog({ uid: userId, taskId: params.taskId as string })
      .then(res => {
        if (res.code !== 0) {
          showToast({ title: status, icon: 'none' });
        } else {
          const { failReason, amount } = res.payload;
          setStatus(res.payload.status || 'EXPIRE');
          setTaskName(res.payload.taskName);
          setImageUrl(res.payload.imageUrl);
          setUploadTime(res.payload.uploadTime);
          if (failReason) {
            setTextConfig({
              ...textConfig,
              REJECTED: { ...textConfig.REJECTED, des: failReason },
              EXPIRE_REJECTED: {
                ...textConfig.EXPIRE_REJECTED,
                des: failReason,
              },
            });
          }
          let amountMoney = +amount || params.amount;
          if (amountMoney) {
            setTextConfig({
              ...textConfig,
              PICKED: {
                ...textConfig.PICKED,
                des: `已发放${amountMoney}个小熊币`,
              },
              EXPIRE_REJECTED: {
                ...textConfig.EXPIRE_REJECTED,
                des: `已发放${amountMoney}个小熊币`,
              },
            });
          }
        }
      })
      .catch(err => {
        console.log('网络不好', err);
      });
  };

  const reshare = async () => {
    if (status == 'EXPIRE_REJECTED') {
      showToast({ title: '任务过期，不能重新分享', icon: 'none' });
      return;
    }
    const res = await checkUserBuy({ uid: userId });
    let {
      code,
      payload: { artBuyStatus, artInStudying, writeBuyStatus, writeInStudying },
    } = res;
    if (code === 0) {
      if (
        !(
          (artBuyStatus === 'SYSTEM' && artInStudying) ||
          (writeBuyStatus === 'SYSTEM' && writeInStudying)
        )
      ) {
        showToast({ title: '仅限小熊系统课在读学员参与', icon: 'none' });
        return;
      }
      setCheckUserBuyMessage(res.payload);
    }
  };

  // 重新上传
  const goToUpload = () => {
    redirectTo({
      url: `/pages/weeklyShare/upload/index?taskId=${params.taskId}`,
    });
  };

  return (
    status && (
      <View className='results'>
        <View className='status'>
          {status == 'LAST_WEEK_EXPIRE' ? (
            <Image src={RejectImg} mode='widthFix'></Image>
          ) : (
            <Image
              src={require(`@/assets/weekSharePoster/results/${status}.png`)}
              mode='widthFix'
            ></Image>
          )}
          <View className='h3'>{textConfig[status].title}</View>

          <View className='p'>{textConfig[status].des}</View>
          {status === 'REJECTED' && (
            <Button className='lookImg' onClick={() => setExampleShow(true)}>
              查看分享截图示例
            </Button>
          )}
          {status === 'REJECTED' && (
            <View className='button-group'>
              <View className='upload' onClick={() => goToUpload()}>
                重新上传
              </View>
              <View onClick={() => reshare()} className='reshare'>
                重新分享
              </View>
            </View>
          )}
        </View>
        <View className='info'>
          <View className='h3'>
            <Text className='span'>{taskName}</Text>
            {status !== 'REJECTED' && status !== 'EXPIRE_REJECTED' && (
              <Text className='span'>{textConfig[status].tips}</Text>
            )}
          </View>
          {status != 'EXPIRE' && (
            <View className='uploadTimeP'>上传时间：{uploadTime}</View>
          )}
          {status !== 'EXPIRE' && (
            <Image src={imageUrl} mode='widthFix'></Image>
          )}
        </View>
        <Postermodal
          show={postDailog}
          userBuyMessage={checkUserBuyMessage}
          onClose={() => setPostDailog(false)}
        />
        <ScreenShotExam
          show={exampleShow}
          onClose={() => setExampleShow(false)}
        />
      </View>
    )
  );
};

export default Results;
