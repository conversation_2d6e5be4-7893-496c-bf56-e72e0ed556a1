.results {
  padding: 60px 30px;

  .lookImg {
    font-size: 24px;
  }

  .uploadTimeP {
    font-size: 24px;
  }

  .button-group {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 41px;

    .upload {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 250px;
      height: 80px;
      border-radius: 52px;
      border: 2px solid #ff9c00;
      box-sizing: border-box;
      font-size: 32px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #ff9c00;
      margin-right: 24px;
    }

    .reshare {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 250px;
      height: 80px;
      background: #ff9c00;
      border-radius: 52px;
      font-size: 32px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #ffffff;
    }
  }

  .status {
    padding-bottom: 48px;
    text-align: center;
    border-bottom: 1px solid #e6e6e6;

    image {
      width: 170px;
      height: 170px;
    }

    .h3 {
      font-size: 40px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #000000;
      line-height: 56px;
      margin-top: 14px;
    }

    .p {
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 40px;
      margin-top: 12px;
    }

    button {
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ff9c31;
      border: none;
      outline: none;
      background: none;

      &::before {
        content: '';
        display: inline-block;
        position: relative;
        top: 5px;
        width: 24px;
        height: 24px;
        background-image: url(../../../assets/weekSharePoster/results/warning.png);
        background-position: center;
        background-size: cover;
        margin-right: 5px;
      }
    }
  }

  .info {
    .h3 {
      height: 70px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .span {
        &:nth-child(1) {
          font-size: 32px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #000000;
        }

        &:nth-child(2) {
          font-size: 24px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #999999;
        }
      }

      .a {
        position: relative;
        top: 10px;
        width: 134px;
        background: #ff9c00;
        border-radius: 26px;
        text-align: center;
        padding: 10px 0;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
      }
    }

    .p {
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 33px;
    }

    image {
      display: block;
      width: 450px;
      margin: auto;
      margin-top: 57px;
    }
  }

  .result-modal {
    .at-modal__container {
      width: 80%;
    }
  }
}
