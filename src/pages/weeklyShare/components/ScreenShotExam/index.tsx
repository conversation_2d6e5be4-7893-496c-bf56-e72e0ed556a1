import { Button, Image, View } from '@tarojs/components';
import { FC } from 'react';
import { AtModal, AtModalContent } from 'taro-ui';
import ExampleImg from '@/assets/weeklyShare/friend-circle-demo.png';
import Close from '@/assets/weeklyShare/close.png';
import './index.scss';

interface IProps {
  show: boolean;
  onClose: Function;
}

const ScreenShotExam: FC<IProps> = props => {
  return (
    <AtModal
      isOpened={props.show}
      onClose={() => props.onClose(false)}
      className='result-modal'
      // closeOnClickOverlay={false}
    >
      <AtModalContent>
        <View className='demo-content'>
          <Image className='demo' src={ExampleImg} mode='widthFix' />
          {/* <Image
            className='close'
            src={Close}
            mode='widthFix'
            onClick={() => props.onClose(false)}
          /> */}
        </View>
      </AtModalContent>
    </AtModal>
  );
};

export default ScreenShotExam;
