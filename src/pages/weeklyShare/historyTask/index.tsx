import { View } from '@tarojs/components';
import { CSSProperties, memo, useEffect, useState } from 'react';
import { getSharePosterLogList } from '@/api/groupbuy';
import { useSelector } from 'react-redux';
import {
  getSystemInfoSync,
  hideLoading,
  navigateTo,
  showLoading,
  showToast,
} from '@tarojs/taro';
import { UserStateType } from '@/store/groupbuy/state';
import VirtualList from '@tarojs/components/virtual-list';
import './index.scss';

const getWindowHeight = () => {
  const info = getSystemInfoSync();
  return info.windowHeight;
};

const HistoryTask = () => {
  const [taskList, setTaskList] = useState<any[]>([]);
  const [wh] = useState<number>(() => getWindowHeight() || 500);

  const userId = useSelector((state: UserStateType) => state.userid);

  useEffect(() => {
    userId && getTaskList();
  }, [userId]);

  // 获取任务列表
  const getTaskList = () => {
    showLoading({ title: '加载中' });
    getSharePosterLogList({ uid: userId, page: 0, size: 1000 })
      .then(res => {
        hideLoading();
        let {
          code,
          payload: { content = [] },
          status,
        } = res;

        if (code !== 0) {
          showToast({ title: status, icon: 'none' });
        }
        setTaskList(content);
      })
      .catch(err => {
        hideLoading();
        console.log('网络不好', err);
      });
  };

  // 动态计算状态
  const setStatus = (status: string) => {
    /**
     * ACTIVE 去上传分享截图
     INREVIEW 审核中
     PICKED 任务完成
     REJECTED 审核未通过
     EXPIRE  未完成
     */
    let obj = { text: '', styless: '' };
    switch (status) {
      case 'INREVIEW':
        obj.text = '审核中';
        obj.styless = 'status2';
        break;
      case 'PICKED':
        obj.text = '任务完成';
        obj.styless = 'status1';
        break;
      case 'REJECTED':
        obj.text = '审核未通过';
        obj.styless = 'status3';
        break;
      // 第一周审核未通过
      case 'LAST_WEEK_EXPIRE':
        obj.text = '审核未通过';
        obj.styless = 'status3';
        break;
      case 'EXPIRE':
        obj.text = '未完成';
        obj.styless = 'status3';
        break;
    }
    return obj;
  };

  // 跳转详情
  const toResults = (taskId: string, amount: number) => {
    navigateTo({
      url: `/pages/weeklyShare/results/index?amount=${amount ||
        0}&taskId=${taskId}`,
    });
  };

  const Row = memo(
    ({
      index,
      style,
      data,
    }: {
      index: number;
      style?: CSSProperties;
      data: any;
    }) => {
      return (
        <View
          className='border taskCard'
          style={style}
          onClick={() => toResults(data[index].taskId, data[index].amount)}
        >
          <View className='tit flex'>
            <View> {data[index].taskName}</View>
            <View className={`status ${setStatus(data[index].status).styless}`}>
              {setStatus(data[index].status).text}
            </View>
          </View>
          {data[index].status !== 'EXPIRE' && (
            <View className='uptime'>
              上传时间：{data[index].uploadTime || '暂无'}
            </View>
          )}
          {data[index].status == 'EXPIRE' && (
            <View className='uptime'>
              上传时间：{data[index].uploadTime || '未上传'}
            </View>
          )}

          <View className='new_uptime'>
            {data[index].amount
              ? ` 获得奖励：${data[index].amount}小熊币`
              : ' 获得奖励：暂无'}
          </View>
        </View>
      );
    },
  );

  return (
    <View className='hostoryTask'>
      {wh > 0 && (
        <VirtualList
          height={wh}
          width='100%'
          itemData={taskList}
          itemCount={taskList.length}
          itemSize={106}
          overscanCount={7}
        >
          {Row}
        </VirtualList>
      )}
    </View>
  );
};

export default HistoryTask;
