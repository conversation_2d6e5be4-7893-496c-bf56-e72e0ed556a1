.taskCard {
  padding: 30px;
}

.status {
  font-size: 26px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
}

//   完成
.status1 {
  color: #6dd400;
}

//   审核中
.status2 {
  color: #f7b500;
}

//   未完成+未通过
.status3 {
  color: #fa6400;
}

.tit {
  display: flex;
  justify-content: flex-start;
  font-size: 30px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #5c5c5c;
  text-align: left;
}

.uptime {
  font-size: 26px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #5c5c5c;
  line-height: 2.8;
}

.new_uptime {
  font-size: 26px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #5c5c5c;
}

.btn {
  width: 254px;
  height: 60px;
  background: #ff5642;
  border-radius: 30px;
  color: #ffffff;
  font-size: 26px;
  font-weight: 500;
  text-align: center;
  line-height: 60px;
  margin: 0px auto;
  margin-bottom: 18px;
}

.border {
  border-bottom: 1px solid #f3f3f3;
}
