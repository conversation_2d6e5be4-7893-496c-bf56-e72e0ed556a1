/**
 * 周周分享需求 app内首页点击立即分享海报到朋友圈 app外弹窗展示海报可保存 => 将posterModal中获取海报逻辑抽离，由原来的在组件获取迁移到首页获取并传入组件
 * 海报展示逻辑 => 双科分享朋友圈优先美术，弹窗中全部展示, 单科展示用户所属科目海报
 * 处理逻辑 => 返回队列按照美术放第一位 双科分享朋友圈取数组0位即可 单科队列只存一组数据也取0位 其余情况取全部数据即可
 */

import { getPosterListAll } from '@/api/groupbuy';

const SCRIPT_ENV = process.env.NODE_ENV || 'dev';
const musicBaseUrl = {
  dev: 'https://ai-xxyy-default.xiaoxiongyinyue.com/',
  test: 'https://ai-xxyy-test.xiaoxiongyinyue.com/',
  gray: 'https://ai-xxyy-prod.xiaoxiongyinyue.com/',
  online: 'https://www.xiaoxiongyinyue.com/',
};
const artBaseUrl = {
  dev: 'https://dev.meixiu.mobi/ai-app-h5-activity-dev/',
  test: 'https://test.meixiu.mobi/ai-app-h5-activity-test/',
  gray: 'https://prod.xiaoxiongmeishu.com/activity/',
  online: 'https://www.xiaoxiongmeishu.com/activity/',
};
const overseasPosterUrl = {
  dev: 'https://dev.meixiu.mobi/ai-app-h5-activity-dev/',
  test: 'https://test.meixiu.mobi/ai-app-h5-activity-test/',
  gray: 'https://prod.xiaoxiongmeishu.com/activity/',
  online: 'https://global.xiaoxiongmeishu.com/activity/',
};

const southAsiaPosterId = {
  dev: 47,
  test: 47,
  gray: 32,
  online: 35,
};

const GATPosterId = {
  dev: 48,
  test: 48,
  gray: 33,
  online: 36,
};

const writeBaseUrl = {
  dev: 'https://dev.meixiu.mobi/ai-app-h5-writing-dev/',
  test: 'https://test.meixiu.mobi/ai-app-h5-writing-test/',
  gray: 'https://test.meixiu.mobi/writing/',
  online: 'https://www.xiaoxiongmeishu.com/writing/',
};

const writePageageId = {
  dev: 7659,
  test: 8915,
  gray: 910,
  online: 910,
};

// 各科目海报配置
const subjectPosterConfig = {
  art: {
    baseUrl: artBaseUrl[SCRIPT_ENV],
    typeId: SCRIPT_ENV === 'online' ? 38 : 52,
  },
  music: {
    baseUrl: musicBaseUrl[SCRIPT_ENV],
    typeId: 21,
  },
  write: {
    baseUrl: writeBaseUrl[SCRIPT_ENV],
    typeId: 18,
  },
};

// 海外=> 目前只有美术
const hwArtPosterConfig = {
  FOREIGN_AREA: {
    typeId: southAsiaPosterId[SCRIPT_ENV],
    baseUrl: overseasPosterUrl[SCRIPT_ENV],
    area: 'Malaysia',
  },
  GAT_AREA: {
    typeId: GATPosterId[SCRIPT_ENV],
    baseUrl: overseasPosterUrl[SCRIPT_ENV],
    area: '',
  },
};

class Poster {
  private readonly user: any;
  private readonly msChannelId: string | undefined;
  private readonly yyChannelId: string;
  private readonly xzChannelId: string;

  constructor(options) {
    this.user = options.user;
    this.msChannelId = options.msChannelId;
    this.yyChannelId = options.yyChannelId;
    this.xzChannelId = options.xzChannelId;
  }

  getReferralLink() {
    /* const bearRouter = [
      // 'bear',
      'b',
      'b1',
      'b2',
      'b3',
      'b4',
      'b5',
      'b6',
      'b7',
      // 'b8',
      'bearArtsAndBearWrite',
    ];
    let randomNumber = Math.floor(Math.random() * bearRouter.length);
    return bearRouter[randomNumber]; */
    return 'FourteenPiontNineNew';
  }

  // 根据不同科目/地区获取配置
  getPosterConfigBySubjec(sunject, tmp, userId) {
    let url = '';
    const { baseUrl, typeId } = subjectPosterConfig[sunject];
    switch (sunject) {
      case 'art': {
        url = `${baseUrl}${this.getReferralLink()}?sendId=${userId}&${
          'msChannelId=' + this.msChannelId
        }&t=${tmp}&channelId=${this.msChannelId}`;
        break;
      }
      case 'music': {
        const user = this.user;
        url = `${baseUrl}a/0?p=754&s=${user.userNum}&c=${this.yyChannelId}&t=${tmp}`;
        break;
      }
      case 'write':
        url = `${baseUrl}experience?packagesId=${
          writePageageId[SCRIPT_ENV]
        }&sendId=${userId}&${'channelId=' + this.xzChannelId}&t=${tmp}`;
        break;
      default:
        break;
    }
    return {
      url,
      id: typeId,
    };
  }

  getPosterConfig(sunject, area) {
    let tmp = new Date().valueOf().toString();
    tmp = tmp.substr(tmp.length - 2, tmp.length);
    const userId = this.user ? this.user.id : '';
    let config: { id?: string; url?: string } = {};
    // 如果是海外用户就只有美术
    if (area === 'CN_AREA') {
      config = this.getPosterConfigBySubjec(sunject, tmp, userId);
    } else {
      const areaInUrl = hwArtPosterConfig[area]['area'];
      config['url'] = `${
        hwArtPosterConfig[area]['baseUrl']
      }o?lang=ZH_HK&sendId=${userId}&channelId=8606${
        areaInUrl ? `&area=${areaInUrl}` : ``
      }&t=${tmp}`;
      config['id'] = hwArtPosterConfig[area]['typeId'];
    }
    return config;
  }

  // 获取海报
  getPoster(subject = 'art') {
    return new Promise((resolve) => {
      const area = this.user ? this.user.area : 'CN_AREA';
      const config = this.getPosterConfig(subject, area);
      const userId = this.user ? this.user.id : '';
      getPosterListAll({
        id: config.id as string,
        qrCode: config.url as string,
        uid: userId,
      })
        .then((res) => {
          if (res.code == 0) {
            const { payload } = res;
            // 标准海报随机
            if (payload && payload.length > 0) {
              let randomNum = Math.floor(Math.random() * payload.length);
              const data = payload[randomNum];
              data['pType'] = subject;
              data['typeId'] = config.id;
              resolve(data);
            } else {
              resolve(null);
            }
          } else {
            resolve(null);
          }
        })
        .catch(() => {
          // reject(err.errors || '服务器开小差了')
          resolve(null);
        });
    });
  }

  // 根据用户身份获取海报
  getPosterByUser(checkUserBuyPayload) {
    const _this = this;
    return new Promise((resolve) => {
      const {
        artInStudying,
        artBuyStatus,
        writeInStudying,
        writeBuyStatus,
        musicInStudying,
        musicBuyStatus,
      } = checkUserBuyPayload;
      // 美术系统课在读
      const tArtInStudying = artInStudying && artBuyStatus === 'SYSTEM';
      // 书法系统课在读
      const tWriteInStudying = writeInStudying && writeBuyStatus === 'SYSTEM';
      // 音乐系统课在读
      const tMusicInStudying = musicInStudying && musicBuyStatus === 'SYSTEM';
      const listType: string[] = [];
      // 按顺序
      if (tArtInStudying) {
        listType.push('art');
      }
      if (tWriteInStudying) {
        listType.push('write');
      }
      if (tMusicInStudying) {
        listType.push('music');
      }
      console.log('listType', listType);

      async function queue(arr) {
        let res: any[] = [];
        for (let v of arr) {
          const data = await _this.getPoster(v);
          data && res.push(data);
        }
        return await res;
      }

      const list = queue(listType);
      resolve(list);
    });
  }
}

export default Poster;
