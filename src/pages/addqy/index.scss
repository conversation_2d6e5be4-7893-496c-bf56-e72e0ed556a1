page {
  background-color: #ffffff;
}
cell {
  height: 100%;
  display: block;
  opacity: 0;
  border: none;
  .cell--messagecard {
    border: none;
  }
  .messagecard {
    border: none;
  }
}
.qy_wrap {
  width: 100%;
  position: relative;
  overflow: hidden;
  .qy_poster {
    width: 100%;
    height: 100%;
    image {
      width: 100%;
    }
  }
  .addqy_box {
    width: 521px;
    position: absolute;
    left: 50%;
    top: 1106px;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .img {
      position: absolute;
      width: 100%;
      pointer-events: none;
      z-index: 99;
    }
    .addqy_copy_img {
      width: 100%;
      height: 100%;
    }
    .addqy_copy {
      width: 437px;
      height: 98px;
    }
    .addqy_btn {
      width: 521px;
      height: 117px;
    }
  }
  .addqy_box_l {
    width: 521px;
    position: absolute;
    left: 50%;
    top: 949px;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .addqy_center {
      text-align: center;
      .addqy_h {
        height: 50px;
        font-size: 36px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #865150;
        line-height: 50px;
        margin-bottom: 10px;
      }
      .addqy_text {
        height: 50px;
        font-size: 50px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #ff4a04;
        line-height: 50px;
      }
      .addqy_qr {
        width: 320px;
        height: 320px;
        margin-bottom: 10px;
        .addqy_qr_img {
          width: 100%;
          height: 100%;
        }
      }
      .addqy_tips {
        height: 41px;
        font-size: 29px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #865150;
        line-height: 41px;
        text-align: center;
      }
    }
  }
}
