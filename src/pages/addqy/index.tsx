import { View, Image, Text } from '@tarojs/components';
import bg01 from '@/assets/addqy/poster_01.png';
import bg02 from '@/assets/addqy/poster_02.png';
import bg03 from '@/assets/addqy/poster_03.png';
import bg04 from '@/assets/addqy/poster_04.png';
import bg05 from '@/assets/addqy/poster_05.png';
import bg06 from '@/assets/addqy/poster_06.png';
import bg07 from '@/assets/addqy/poster_07.png';
import bg08 from '@/assets/addqy/poster_08.png';
import bg09 from '@/assets/addqy/poster_09.png';
import bg10 from '@/assets/addqy/poster_10.png';
import bg11 from '@/assets/addqy/poster_11.png';
import bg12 from '@/assets/addqy/poster_12.png';
import bg13 from '@/assets/addqy/poster_13.png';
import qyBtn from '@/assets/addqy/qy_btn.gif';
import qyw from '@/assets/addqy/qy_w.png';
import qr01 from '@/assets/addqy/qr01.png';
import qr02 from '@/assets/addqy/qr02.png';
import qr03 from '@/assets/addqy/qr03.png';
import qr04 from '@/assets/addqy/qr04.png';
import qr05 from '@/assets/addqy/qr05.png';
import qr06 from '@/assets/addqy/qr06.png';
import { useEffect, useState } from 'react';
import sensors from '@/utils/sensors_data';
import Taro, { useRouter } from '@tarojs/taro';
import { getUserOpenIdSubject } from '@/api/groupbuy';
import { getPosterListByIdApi } from '@/api/1v1k8s';
import './index.scss';

const Indexwebview = () => {
  const router = useRouter();
  const queryImg: string | number = router.params.img || 1;
  const posterId = router.params.posterId || '';
  const [pUrl, setPUrl] = useState('');
  const [indUrl, setIndUrl] = useState('');
  const ibg = () => {
    const bgList = {
      1: bg01,
      2: bg02,
      3: bg03,
      4: bg04,
      5: bg05,
      6: bg06,
      7: bg07,
      8: bg08,
      9: bg09,
      10: bg10,
      11: bg11,
      12: bg12,
      13: bg13,
    };

    return posterId ? pUrl : bgList[queryImg];
  };
  const startmessage = e => {
    console.log(e, 'startmessage');
  };
  const completemessage = e => {
    sensors.track('xxys_privateDomain_addWorkWxPage_addResult', {
      status: e.detail.errcode,
    });
  };

  const handleClick = () => {
    sensors.track('xxys_privateDomain_addWorkWxPage_addClick');
  };

  const onLongPress = () => {
    sensors.track('xxys_privateDomain_addWorkWxPage_wxLongPress');
  };

  // 通过海报id获取
  const getPoster = () => {
    posterId &&
      getPosterListByIdApi(posterId).then(res => {
        if (res.code == 0 && res.payload) {
          const { posterUrl, individualizationUrl } = res.payload;
          setPUrl(posterUrl);
          setIndUrl(individualizationUrl);
        }
      });
  };

  const initEL = () => {
    const _dom01 = (
      <View className='addqy_box'>
        <View className='addqy_copy'>
          <Image src={qyw} mode='widthFix' className='addqy_copy_img' />
        </View>
        <View className='addqy_btn' onClick={handleClick}>
          <Image src={qyBtn} mode='widthFix' className='img' />
          <cell
            onStartmessage={startmessage}
            onCompletemessage={completemessage}
            plugid='46b975125acb04465d8f70c7cb670f44'
          />
        </View>
      </View>
    );

    const _dom02 = (key: any = 8) => {
      const qrList = {
        8: qr01,
        9: qr02,
        10: qr03,
        11: qr04,
        12: qr05,
        13: qr06,
      };
      const topList = {
        8: 949,
        9: 949,
        10: 949,
        11: 1030,
        12: 949,
        13: 980,
      };

      const qr = posterId ? indUrl : qrList[key];
      const qrtop = posterId ? 949 : topList[key];

      return (
        <View className='addqy_box_l' style={{ top: qrtop + 'rpx' }}>
          <View className='addqy_center'>
            <View className='addqy_h'>
              限量前<Text className='addqy_text'>100</Text>名
            </View>
            <View className='addqy_qr'>
              <Image
                src={qr}
                mode='widthFix'
                className='addqy_qr_img'
                showMenuByLongpress
                onLongPress={() => onLongPress()}
              />
            </View>
            <View className='addqy_tips'>长按识别添加，先到先得</View>
          </View>
        </View>
      );
    };

    if (posterId) {
      return _dom02();
    } else {
      return +queryImg <= 7 ? _dom01 : _dom02(queryImg);
    }
  };

  useEffect(() => {
    Taro.login({
      success: function(res) {
        if (res.code) {
          getUserOpenIdSubject({
            code: res.code,
            channel: router.params.channelId || '',
            subject: 'ART_APP',
          }).then(data => {
            if (data.code === 0) {
              if (data.payload.token)
                Taro.setStorageSync('appToken', data.payload.token);
              sensors.init({
                wx_openid: data.payload.openid || '',
                user_id: data.payload.uid || '',
              });
              sensors.track('xxys_privateDomain_addWorkWxPage_view', {
                entrance_page: router.params.entrance_page,
                poster_id: router.params.posterId,
              });
            }
          });
        } else {
          console.log('登录失败！' + res.errMsg);
        }
      },
    });
    getPoster();
  }, []);

  return (
    <View className='qy_wrap'>
      <View className='qy_poster'>
        <Image src={ibg()} mode='widthFix' />
      </View>
      {initEL()}
    </View>
  );
};

export default Indexwebview;
