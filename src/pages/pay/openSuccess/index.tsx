import Taro from '@tarojs/taro';
import { View, Image } from '@tarojs/components';
import successIcon from '@/assets/pay/successIcon.png';
import './index.scss';

export default function Pay() {
  const computedClick = () => {
    Taro.navigateTo({
      url: `/pages/pictureBook/system/index`,
    });
  };
  return (
    <View className='openSuccessIndex'>
      <Image className='openSuccessIndexIcon' src={successIcon} />
      <View className='openSuccessIndexText'>开通成功</View>
      <View className='openSuccessIndexTips'>
        扣款成功后，查收短信添加老师
        <br />
        提前下载App上课
      </View>
      <View onClick={computedClick} className='openSuccessIndexBtn'>
        完成
      </View>
    </View>
  );
}
