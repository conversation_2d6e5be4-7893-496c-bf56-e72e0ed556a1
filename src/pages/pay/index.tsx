import Taro, { useRouter, useDidShow } from '@tarojs/taro';
import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { View, Text, Image } from '@tarojs/components';
import AddressSelect from '@/components/AddressSelect';
import radioSelect from '@/assets/pay/radioSelect.png';
import radioUnSelect from '@/assets/pay/radioUnSelect.png';
import { UserStateType } from '@/store/groupbuy/state';
import { AddressTypes, agreementInfoTypes } from '@/types/types';
import {
  getManagementsByStatus,
  getPackages,
  getUserCouponByPackageIdList,
  queryPackagesIdBySubject,
  queryPackagesIdBySubscribe,
  getPackagesCreateV2,
  eduContractsSign,
  orderSubscribeById,
  getOrderStaus,
  getWeixinProgramPay,
  orderExtendReport,
} from '@/api/groupbuy';

import './index.scss';

export default () => {
  const params = useRouter().params;
  const userId = useSelector((state: UserStateType) => state.userid);
  // const userId = '795021680833830912'
  const sendId = useSelector((state: UserStateType) => state.sendId);
  const channelId = useSelector((state: UserStateType) => state.channelId);
  const openid = useSelector((store: UserStateType) => store.openid);
  const [packagesId, setpackagesId] = useState(params.packagesId || '');
  const [sup] = useState(params.sup || '');
  const [stageInfo, setstageInfo] = useState<any>();
  const [issue, setissue] = useState(0);
  const [address, setAddress] = useState<any>();
  const [packageInfo, setpackageInfo] = useState<any>({
    name: '',
    price: '',
  });
  const [subScribeackageInfo, setsubScribeackageInfo] = useState<any>({}); // 订阅套餐信息
  const [courseInfo, setcourseInfo] = useState<any>({}); // 季课购买信息
  const [purchaseType, setpurchaseType] = useState(0); // 购买类型

  const [couponsInfo, setcouponsInfo] = useState<any>(); // 优惠券
  const [couponId, setcouponId] = useState(0);

  const [flag, setFlag] = useState(true);
  const [Radiochecked, setRadiochecked] = useState(false);
  // 协议列表
  const [agreementUrlList, setAgreementUrlList] = useState<
    Array<agreementInfoTypes>
  >();
  // 签约后订单查询
  const [subscribeId, setsubscribeId] = useState('');
  const [orderId, setorderId] = useState('');
  const [queryOrderNum, setQueryOrderNum] = useState(0);
  const [querySubcNum, setQuerySubcNum] = useState(0);

  useEffect(() => {
    getIssue();
    getSubscribeCourseInfo();
  }, []);

  useDidShow(() => {
    let orderSubscribeInfo: any = null;
    try {
      orderSubscribeInfo = JSON.parse(
        Taro.getStorageSync('orderSubscribeInfo'),
      );
      if (orderSubscribeInfo) {
        setorderId(orderSubscribeInfo.orderId);
        setsubscribeId(orderSubscribeInfo.orderSubscribeId);
      }
    } catch (error) {}
  });

  const getSubscribeCourseInfo = async () => {
    const SubscribeRes = await queryPackagesIdBySubscribe({
      userId: userId,
      openId: openid,
      debitType: 'QUARTERLY',
      subject: 'PICTURE_BOOK',
    });
    // 不可以购买则接口异常
    if (SubscribeRes.code === 0 && SubscribeRes.payload?.planList.length) {
      let planList1 = [...SubscribeRes.payload.planList];
      planList1.shift();
      setstageInfo({
        ...SubscribeRes.payload,
        planList: planList1,
      });
      setpackagesId(SubscribeRes.payload.packagesId);
      const res = await getPackages({
        packagesId: SubscribeRes.payload.packagesId,
      });
      setpackageInfo(res.payload);
      setpurchaseType(1);
      setsubScribeackageInfo(res.payload);
      if (res.payload.agreementUrlList?.length) {
        setAgreementUrlList(res.payload.agreementUrlList);
      }
    }
    getCourseInfo(
      SubscribeRes.code === 0 && SubscribeRes.payload?.planList.length,
    );
  };
  const getCourseInfo = async (hasSubrict = true) => {
    let info = {
      isFirstOrder: true,
      isRenew: false,
      isMakeup: false,
      packagesId: '1602',
    };
    const { payload } = await queryPackagesIdBySubject({
      userId: userId,
      type: 'QUARTER',
      subject: 'PICTURE_BOOK',
    });
    // 如果没有返回套餐 则默认已经是年系统课用户 显示年系统课
    if (payload?.packagesId) {
      info = payload;
      const res = await getPackages({ packagesId: info.packagesId });
      setcourseInfo({
        ...info,
        ...res.payload,
      });
      // 如果不可以订阅则设置普通购买课
      if (!hasSubrict) {
        setAgreementUrlList(res.payload.agreementUrlList);
        setpackageInfo({
          ...info,
          ...res.payload,
        });
        setpurchaseType(2);
      }
      getCouponInfo(payload.packagesId);
    }
    if (!hasSubrict && !payload.packagesId) {
      Taro.showToast({
        title: '您已购买过课程，请去小熊艺术App上课。',
        icon: 'none',
        duration: 2000,
      });
      setTimeout(() => {
        Taro.navigateBack();
      }, 2000);
      return;
    }
  };
  const getCouponInfo = packageIds => {
    getUserCouponByPackageIdList({
      packageIds: packageIds,
      uid: userId,
      isCompose: false,
    }).then(res => {
      const { payload } = res;
      if (Array.isArray(payload) && payload.length > 0) {
        let _maxIndex = -1;
        let max = 0;
        let couponsSelect: any = null;
        for (let index = 0; index < payload.length; index++) {
          const item = payload[index];
          if (item.isMust == 1) {
            couponsSelect = item;
            break;
          } else if (max < item.amount) {
            max = item.amount;
            _maxIndex = index;
          }
        }
        // 选一张非必选的最大值
        if (!couponsSelect && _maxIndex > -1) {
          couponsSelect = payload[_maxIndex];
        }
        setcouponsInfo(couponsSelect);
        setcouponId(couponsSelect?.couponId);
      }
    });
  };
  //获取排期接口
  const getIssue = () => {
    getManagementsByStatus({ subject: 'PICTURE_BOOK' }).then(res => {
      res.payload &&
        res.payload.forEach(item => {
          if (item.type == 'NEW_SYSTEMCOURSE') {
            setissue(item.period);
          }
        });
    });
  };

  // 提交支付
  const onSubmitPay = () => {
    if (!Radiochecked) {
      Taro.showToast({
        title: '请您阅读并勾选协议',
        icon: 'none',
      });
      return false;
    }
    if (!address) {
      Taro.showToast({
        title: '请填写收货地址',
        icon: 'none',
      });
      return false;
    }
    createdAndPay();
  };

  //创建订单并支付
  const createdAndPay = async () => {
    if (!flag) return false;
    setFlag(false);
    Taro.showLoading({
      title: '',
    });
    let createdParams: any = {
      type: 'ALONE',
      userId,
      packagesId,
      stage: issue,
      sup,
      channel: channelId,
      topicId: 0,
      sendId,
      addressId: address.id,
      couponUserIdList: [],
    };
    if (purchaseType === 2) {
      createdParams.packagesId = courseInfo.id;
      if (couponId) createdParams.couponUserIdList = [couponId];
    }
    getPackagesCreateV2(createdParams)
      .then(async res => {
        if (res.code !== 0) {
          Taro.hideLoading();
          return;
        }
        setFlag(true);
        const {
          payload: { order },
        } = res;
        const { staffId, LOB } = params; // 教辅/销售 id
        if (staffId && LOB) {
          await orderExtendReport({
            uid: userId,
            oids: order.id,
            tabType: LOB ? LOB : 'AI',
            tabValue: staffId ? staffId : '',
          });
        }
        if (purchaseType === 1) {
          eduContractsSign({
            userId,
            appId: 'wxca74ba5ac3598b54',
            openId: openid,
            planId: stageInfo.planId,
            packagesId,
            orderId: order.id,
          }).then((singRes: any) => {
            const { code, errors, payload } = singRes;
            if (code !== 0 || !payload) {
              Taro.showToast({
                title: errors || '获取签约信息失败',
                icon: 'none',
              });
              return;
            }
            Taro.hideLoading();
            const { preSignToken, orderSubscribeId } = payload;
            // 续费订阅
            Taro.navigateToMiniProgram({
              appId: 'wx7a985467cbd06913',
              path: 'pages/index/index',
              extraData: {
                presign_token: preSignToken,
              },
              success() {
                Taro.setStorageSync(
                  'orderSubscribeInfo',
                  JSON.stringify({
                    orderSubscribeId,
                    orderId: order.id,
                  }),
                );
              },
            });
          });
        }
        if (purchaseType === 2) {
          getWeixinProgramPay({
            openId: openid,
            orderId: order.id,
            userId,
            payType: 'PICTURE_BOOK_WXPROGRAM',
            notifyUrl: '',
          })
            .then(data => {
              if (data.code === 0) {
                const {
                  timeStamp,
                  nonceStr,
                  // package: packageId,
                  paySign,
                } = data.payload;
                // 不同的支付接口的返回不同
                Taro.requestPayment({
                  timeStamp,
                  nonceStr,
                  package: data.payload.package,
                  //@ts-ignore
                  signType: data.payload.signType,
                  paySign,
                  success: function(payRes) {
                    console.log(payRes, 'payRes');
                    setFlag(true);
                    Taro.navigateTo({
                      url: `/pages/conversion/addteacher/index?from=pictureBook&orderId=${order.id}&isSimple=0`,
                    });
                  },
                  fail: function(failRes) {
                    console.log(failRes, 'failRes');

                    setFlag(true);
                    Taro.showToast({
                      title: '下单失败！',
                      icon: 'none',
                      duration: 2000,
                    });
                  },
                });
              }
            })
            .catch(err => {
              setFlag(true);
              console.log(err);
            });
        }
      })
      .catch(err => {
        Taro.hideLoading();
        setFlag(true);
        Taro.showToast({
          title: '网络异常',
          icon: 'none',
        });
      });
  };

  //获取我的地址
  const handleAddress = (addressInfo: AddressTypes) => {
    if (addressInfo) {
      setAddress(addressInfo);
    }
  };

  const clickAgreement = (url: string) => {
    const href = `https://www.xiaoxiongmeishu.com/handwork/showPDF?pdfUrl=${url}`;
    Taro.navigateTo({
      url: `/pages/webview/index?url=${encodeURIComponent(href)}`,
    });
  };

  useEffect(() => {
    if (!subscribeId) return;
    if (querySubcNum < 5) {
      Taro.showLoading({
        title: '',
      });
      setTimeout(() => {
        queryOrderSubscribeById();
      }, 2000);
    } else {
      Taro.removeStorageSync('orderSubscribeInfo');
      Taro.showToast({
        title: '签约未完成',
        icon: 'none',
        duration: 3000,
      });
    }
  }, [querySubcNum, subscribeId]);

  useEffect(() => {
    if (!orderId) return;
    if (queryOrderNum < 5) {
      Taro.showLoading({
        title: '',
      });
      setTimeout(() => {
        queryOrderStatus();
      }, 2000);
    } else {
      Taro.showToast({
        title: '签约扣款失败',
        icon: 'none',
        duration: 3000,
      });
      Taro.navigateTo({
        url: `/pages/pay/openSuccess/index`,
      });
      Taro.removeStorageSync('orderSubscribeInfo');
    }
  }, [queryOrderNum]);
  // 查询签约状态
  const queryOrderSubscribeById = () => {
    orderSubscribeById({
      id: subscribeId,
    })
      .then(result => {
        Taro.hideLoading();
        if (result.code === 0) {
          const { status, type } = result.payload;
          if (status === 'SIGNING') {
            // 签约完成
            if (type === 'FIRST') {
              // 首单则查询订单状态是否扣款
              queryOrderStatus();
            } else {
              Taro.removeStorageSync('orderSubscribeInfo');
              // 续费则直接跳转签约成功页面
              Taro.navigateTo({
                url: `/pages/pay/openSuccess/index`,
              });
            }
          } else {
            setQuerySubcNum(querySubcNum + 1);
          }
        }
      })
      .catch(err => {
        Taro.hideLoading();
        Taro.showToast({
          title: err.msg || '网络异常',
          icon: 'none',
        });
      });
  };

  //查询订单结果
  const queryOrderStatus = () => {
    if (!orderId) return false;
    getOrderStaus({ orderId })
      .then(res => {
        Taro.hideLoading();
        const { status } = res.payload;
        //普通支付是 COMPLETED  代表支付成功
        if (status === 'COMPLETED') {
          Taro.removeStorageSync('orderSubscribeInfo');
          Taro.navigateTo({
            url: `/pages/conversion/addteacher/index?from=pictureBook&orderId=${orderId}&isSimple=0`,
          });
        } else {
          setQueryOrderNum(queryOrderNum + 1);
        }
      })
      .catch(err => {
        Taro.hideLoading();
        Taro.showToast({
          title: err.message || '订单查询失败,请重试',
          icon: 'none',
        });
      });
  };
  const switchPayment = PaymentType => {
    if (purchaseType === PaymentType) return;
    setpurchaseType(PaymentType);
  };

  return (
    <View className='payindex'>
      <AddressSelect
        title='课程随材将寄送至此地址'
        addressCallback={handleAddress}
      />
      {/* <View className='order'>
        <View className='payindexordertitle'>订单信息</View>
        <View className='class-box'>
          <View className='class-name'>{packageInfo.name}</View>
          <View className='class-price'>¥{packageInfo.price}</View>
        </View>
      </View> */}

      {/* {stageInfo?.planList.length ? (
        <View className='stagingOptionsBox'>
          <View className='stagingOptionsBoxtitle'>
            <Text>扣款计划</Text>
            <Text>共{stageInfo.planList.length}期</Text>
          </View>
          <View className='stagingOptionsContent'>
            {stageInfo.planList.map((planListItem, planListIndex) => {
              return (
                <View key={'planListItem' + planListIndex} className='planItem'>
                  <View className='planItem-name'>¥{planListItem.amount}</View>
                  <View className='planItem-price'>
                    {planListItem.planTime}
                  </View>
                </View>
              );
            })}
          </View>
          <View className='stagingOptionsTips'>
            *到期之后按{packageInfo.price}元/季自动续费，可随时关闭
          </View>
        </View>
      ) : null} */}
      <View className='order'>
        <View className='payindexordertitle'>{packageInfo.name}</View>
        <View className='checkPayway'>
          {Object.keys(subScribeackageInfo).length ? (
            <View
              className={`Payway ${purchaseType == 1 ? 'actived' : ''}`}
              onClick={() => switchPayment(1)}
            >
              <View className='Payway-name'>连续包季</View>
              <View className='Payway-price'>
                <Text className='renminb'>¥</Text>
                {subScribeackageInfo.price}
              </View>
              <View className='Payway-icon'>限时5折</View>
            </View>
          ) : null}
          {Object.keys(courseInfo).length ? (
            <View
              onClick={() => switchPayment(2)}
              className={`Payway ${purchaseType == 2 ? 'actived' : ''}`}
            >
              <View className='Payway-name'>单独季度</View>
              <View className='Payway-price'>
                <Text className='renminb'>¥</Text>
                {courseInfo.price}
              </View>
              <View className='Payway-icon colornone'></View>
            </View>
          ) : null}
        </View>
        <View className='PaywayTips'>
          {purchaseType == 1 ? (
            <View>
              *到期之后按{packageInfo.price}元/季<s>自动续费</s>，可随时关闭
            </View>
          ) : couponsInfo ? (
            <View className='couponsInfo'>
              优惠券 <Text className='renminb'>¥{couponsInfo.amount}</Text>
            </View>
          ) : null}
          <View>含教具随材第一季度盒子¥150</View>
        </View>
      </View>

      <View className='pay-agreement-list'>
        {Radiochecked ? (
          <Image
            className='active_icon'
            onClick={() => setRadiochecked(!Radiochecked)}
            src={radioSelect}
          />
        ) : (
          <Image
            className='active_icon'
            onClick={() => setRadiochecked(!Radiochecked)}
            src={radioUnSelect}
          />
        )}
        <View className='pay-agreement'>
          {agreementUrlList &&
            agreementUrlList.map((item, index) => {
              return (
                <View
                  key={'agreementUrlList' + index}
                  className='pay-agreement-name'
                  onClick={() => clickAgreement(item.agreementUrl)}
                >
                  《{item.agreementName}》
                </View>
              );
            })}
        </View>
      </View>
      <View className='footer-box'>
        <View className='button-box'>
          <View className='text'>
            ¥
            {purchaseType == 1
              ? packageInfo.price
              : couponsInfo
              ? (courseInfo.price * 100 - couponsInfo.amount * 100) / 100
              : courseInfo.price}
          </View>
          <View className='button' onClick={onSubmitPay}>
            {purchaseType == 1 ? (
              <View className='textIcon'>省500元</View>
            ) : null}
            {purchaseType == 1 ? '立即开通' : '立即购买'}
          </View>
        </View>
      </View>
    </View>
  );
};
