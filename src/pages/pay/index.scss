page {
  background: #fff;
}
.payindex {
  min-height: 100vh;
  font-family: PingFangSC-Regular, PingFang SC;
  line-height: 1;
  background: #f8f8f8;
  padding-bottom: 148px;

  .arrow-icon {
    width: 30px;
    height: 30px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .order {
    width: 100%;
    box-sizing: border-box;
    background-color: #fff;

    .payindexordertitle {
      background: #f8f8f8;
      padding: 24px 32px;
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
    }

    .class-box {
      padding: 32px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .class-name {
        font-size: 32px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
      }

      .class-price {
        font-size: 32px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #fa6c3a;
      }
    }

    .desc {
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
    }
  }

  // .stagingOptionsBox {
  //   .stagingOptionsBoxtitle {
  //     display: flex;
  //     justify-content: space-between;
  //     background: #f8f8f8;
  //     padding: 24px 32px;
  //     font-size: 28px;
  //     font-family: PingFangSC-Regular, PingFang SC;
  //     font-weight: 400;
  //     color: #999999;
  //   }

  //   .stagingOptionsContent {
  //     background-color: #ffffff;
  //     padding: 32px 32px 0 32px;
  //     display: flex;
  //     justify-content: space-between;
  //     align-items: center;
  //     .planItem {
  //       padding: 22px 32px;
  //       background: #f8f8f8;
  //       border-radius: 8px;
  //       border: 1px solid #dddddd;
  //       margin-right: 16px;
  //       text-align: center;
  //       .planItem-name {
  //         font-size: 32px;
  //         font-family: PingFangSC-Regular, PingFang SC;
  //         color: #333333;
  //         margin-bottom: 12px;
  //       }

  //       .planItem-price {
  //         font-size: 24px;
  //         font-family: PingFangSC-Regular, PingFang SC;
  //         color: #999999;
  //       }
  //     }
  //   }

  //   .stagingOptionsTips {
  //     font-size: 24px;
  //     font-family: PingFangSC-Regular, PingFang SC;
  //     color: #999999;
  //     background-color: #fff;
  //     padding: 16px 32px 32px;
  //   }
  // }

  .checkPayway {
    background-color: #ffffff;
    padding: 32px;
    display: flex;
    .Payway {
      width: 218px;
      height: 244px;
      margin-right: 16px;
      background: #f8f8f8;
      border-radius: 16px;
      border: 1px solid #f8f8f8;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      &.actived {
        background: rgba(255, 167, 0, 0.08);
        border: 1px solid #ffa700;
        .Payway-icon {
          background: rgba(255, 167, 0, 0.16);
        }
      }
      .Payway-name {
        font-size: 28px;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #333333;
        padding-top: 36px;
      }
      .Payway-price {
        font-size: 48px;
        font-family: DINAlternate-Bold, DINAlternate;
        font-weight: bold;
        color: #fa6c3a;
        s {
          text-decoration: none;
          font-size: 28px;
        }
      }
      .Payway-icon {
        width: 100%;
        height: 48px;
        border-radius: 0px 0px 16px 16px;
        background: rgba(255, 167, 0, 0.08);
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #733600;
        line-height: 48px;
        text-align: center;
        &.colornone {
          background: transparent;
        }
      }
    }
  }
  .PaywayTips {
    box-sizing: border-box;
    height: 100px;
    font-size: 24px;
    font-family: PingFangSC-Regular, PingFang SC;
    color: #999999;
    line-height: 34px;
    padding: 0 0 32px 32px;
  }
  .renminb {
    color: #fa6c3a;
  }

  .active_icon {
    width: 36px;
    height: 36px;
  }

  .pay-agreement-list {
    padding: 30px;
    display: flex;
    align-items: center;

    .pay-agreement {
      flex: 1;

      .pay-agreement-name {
        line-height: 1.5;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #5bbeff;
      }
    }
  }

  .footer-box {
    position: fixed;
    bottom: -1px;
    left: 0;
    padding-bottom: env(safe-area-inset-bottom);
    width: 100%;
    background-color: #fff;
    min-height: 130px;
  }

  .button-box {
    width: 100%;
    box-sizing: border-box;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .text {
      font-family: PingFangSC-Semibold, PingFang SC;
      color: #ff5e32;
      font-size: 56px;
      font-weight: bold;
    }

    .button {
      width: 289px;
      height: 88px;
      background: linear-gradient(
        90deg,
        rgba(255, 156, 49, 1) 0%,
        rgba(255, 93, 49, 1) 100%
      );
      border-radius: 44px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #fff;
      font-size: 36px;
      position: relative;
      .textIcon {
        position: absolute;
        right: -16px;
        top: -32px;
        text-decoration: none;
        width: 128px;
        height: 48px;
        background: #ffd86f;
        border-radius: 24px 24px 24px 0px;
        text-align: center;
        line-height: 48px;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #733600;
      }
    }
  }

  .mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 10;
  }

  .popup {
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    flex-wrap: wrap;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 11;

    img {
      width: 100%;
      height: 100%;
    }

    .gift-box {
      width: 750px;
      height: 507px;
    }

    .close {
      width: 62px;
      height: 62px;
      margin-top: 40px;
    }
  }
}

.button {
  width: 289px;
  height: 88px;
  background: linear-gradient(
    90deg,
    rgba(255, 156, 49, 1) 0%,
    rgba(255, 93, 49, 1) 100%
  );
  border-radius: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 36px;
}

.input_btn {
  width: 100%;
  margin-top: 10px;
}

.pay-modal-wrap {
  :global {
    .am-modal-transparent {
      width: 460px;
    }

    .am-modal-button {
      font-size: 32px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #32c75a;
    }

    .am-modal-title {
      font-size: 36px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #222222;
    }
  }
}

.orderStatus {
  position: fixed;
  z-index: 999;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  // width: 100vw;
  // height: 100vh;
  background-color: #fff;

  display: flex;
  flex-direction: column;
  align-items: center;

  img {
    width: 200px;
    margin-top: 220px;
  }

  .query {
    margin-top: 140px;
    width: 576px;
    height: 96px;
    background: #ffbc00;
    border-radius: 48px;
    font-size: 32px;
    color: #ffffff;
    text-align: center;
    line-height: 96px;
  }

  .again {
    margin-top: 48px;
    width: 576px;
    height: 96px;
    border-radius: 46px;
    border: 1px solid #a1a1a1;
    font-size: 32px;
    color: #333333;
    line-height: 96px;
    text-align: center;
  }
}

.renewModalCss {
  .renewTitle {
    font-size: 42px;
    font-weight: 600;
    vertical-align: middle;
    margin-bottom: 24px;

    img {
      margin-left: 10px;
      width: 40px;
      height: 40px;
      vertical-align: middle;
    }
  }

  .renewContent {
    margin-bottom: 14px;
  }

  .renewbtn {
    width: 100%;
    margin-top: 30px;
  }
}
