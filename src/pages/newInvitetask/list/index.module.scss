@function bgUrl($name) {
  @return 'https://fe-cdn.xiaoxiongmeishu.com/xiaoxiongmpactivity/weapp/wx9b8dcac074fb3151/common/#{$name}';
}
.invitetask-detail-wrap {
  width: 100%;
  background: #fff7e1;
  min-height: 100vh;
  .header {
    width: 100%;
    height: 268px;
    background: url(bgUrl('list-bg.png')) no-repeat 0 0 /100%;
    padding: 70px 0 0 45px;
    box-sizing: border-box;
  }
  .tips {
    font-size: 24px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    text-align: left;
  }
  .section {
    width: 100%;
    background: #fff7e1;
    border-radius: 50px 50px 0px 0px;
    margin-top: -80px;
    box-sizing: border-box;
    padding: 40px 30px 20px 30px;
    .task-item-box {
      width: 690px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 20px;
      box-sizing: border-box;
      padding: 31px 20px 31px 35px;
      box-shadow: 0px 4px 7px 0px rgba(174, 174, 174, 0.29);
      margin-bottom: 28px;
      background: #fff;
      .task-img {
        width: 196px;
        height: 196px;
        margin-right: 44px;
        image {
          width: 196px;
          height: 196px;
          border-radius: 20px;
        }
      }
      .task-info {
        flex: 1;
        .task-name {
          font-size: 32px;
          font-weight: bold;
        }
        .task-details {
          font-weight: 600;
          padding: 16px 0 20px;
          display: flex;
          align-items: center;
          color: #7c2e1a;
          font-size: 24px;
          image {
            width: 30px;
            height: 30px;
            margin-right: 6px;
          }
        }
        .task-time {
          font-size: 20px;
          color: #7c2e1a;
        }
      }
    }
  }

  .none {
    text-align: center;
    width: 690px;
    height: 203px;
    padding-top: 40px;
    border-radius: 30px;
    box-sizing: border-box;
    &-img {
      width: 136px;
    }
    color: #999;
    font-size: 28px;
    text-align: center;
  }
}
