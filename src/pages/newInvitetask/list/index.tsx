import { useRouter } from '@tarojs/taro';
import { View, Image } from '@tarojs/components';
import { rewardList } from '@/api/invitetask';
import { dateFormat } from '@/utils/index';
import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import certificate from '@/assets/newInvitetask/certificate.png';
import listNone from '@/assets/newInvitetask/list-none.png';
import { UserStateType } from '@/store/groupbuy/state';
import styles from './index.module.scss';

export default function Detaillist() {
  const { params } = useRouter();
  const [list, setList] = useState<any>([]);
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  useEffect(() => {
    rewardList(userId, params.managementId).then(res => {
      for (let o of res.payload)
        o.ctime = dateFormat(+o.ctime * 1, 'yyyy-MM-dd-hh:mm:ss');
      setList(res.payload);
    });
  }, []);

  return (
    <View className={styles['invitetask-detail-wrap']}>
      <View className={styles['header']}>
        <View className={styles['tips']}>
          <View>礼物发货时间：领取后30个工作日</View>
          物流查询：小熊美术APP-【我的】-【订单物流】
        </View>
      </View>
      <View className={styles['section']}>
        {list.map(item => {
          return (
            <View key={item.id} className={styles['task-item-box']}>
              <View className={styles['task-img']}>
                <Image src={item.img} />
              </View>
              <View className={styles['task-info']}>
                <View className={styles['task-name']}>
                  小伶diy钻石贴画-hi真棒
                </View>
                <View className={styles['task-details']}>
                  <Image src={certificate} />
                  &nbsp; -{item.boxType == 'SUN_BOX' ? 2 : 1}
                </View>
                <View className={styles['task-time']}>
                  兑换时间：{item.ctime}
                </View>
              </View>
            </View>
          );
        })}
        {!list.length && (
          <View className={styles['none']}>
            <Image
              src={listNone}
              mode='widthFix'
              className={styles['none-img']}
            />
            <View>暂无记录</View>
          </View>
        )}
      </View>
    </View>
  );
}
