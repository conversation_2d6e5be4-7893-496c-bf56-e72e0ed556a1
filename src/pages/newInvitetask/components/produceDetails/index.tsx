import { Image, View, Button } from '@tarojs/components';
import close from '@/assets/groupbuy/thirtySix/close.png';
import msbTrack from '@/utils/sensors_data.weapp';

import './index.scss';

const AddressList = (props: any) => {
  const { item, clickFun, userInfo, getPhoneNumberHandler } = props;

  return (
    <View className='address-model'>
      <View className='mod-title'>确认消耗{item.num}张兑换券</View>
      <Image src={item.img} className='pro-img' />
      <View className='pro-name'>{item.title}</View>
      {!userInfo ? (
        <Button
          open-type='getPhoneNumber'
          onGetPhoneNumber={e =>
            getPhoneNumberHandler(e, true, '兑换券不足分享兑换')
          }
          className='btn make-btn'
        >
          立即分享 获得兑换券
        </Button>
      ) : userInfo.star < item.num ? (
        <Button
          className='btn make-btn'
          openType='share'
          onClick={() => {
            clickFun(true);
            msbTrack.track('xxys_Dailycolla_goodspop_view', {
              button_name: '兑换券不足分享兑换',
            });
          }}
        >
          立即分享 获得兑换券
        </Button>
      ) : (
        <View
          className='btn make-btn'
          onClick={() => {
            clickFun(true);
            msbTrack.track('xxys_Dailycolla_goodspop_view', {
              button_name: '立即兑换',
            });
          }}
        >
          立即兑换
        </View>
      )}
      <Image
        className='close-icon'
        src={close}
        onClick={() => clickFun(false)}
      ></Image>
    </View>
  );
};

export default AddressList;
