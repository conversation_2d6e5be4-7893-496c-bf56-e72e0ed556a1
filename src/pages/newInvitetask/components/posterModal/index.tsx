import { useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import Taro, {
  hideLoading,
  showLoading,
  useRouter,
  useShareAppMessage,
} from '@tarojs/taro';
import { useSelector } from 'react-redux';
import msbTrack from '@/utils/sensors_data.weapp';
import { UserStateType } from '@/store/groupbuy/state';
import { getPosterListApi } from '@/api/1v1k8s';
import { Image, Button, View } from '@tarojs/components';
import clce from '@/assets/newInvitetask/share-popup-ele-clce.png';
import close from '@/assets/groupbuy/thirtySix/close.png';
import {
  getCodeUrl,
  posterIds,
  businessMap,
  channelIds,
} from '@/pages/invitetask/components/postermodal/posterdata';

import './index.scss';

const Index = forwardRef((props: any, ref) => {
  const { params } = useRouter();
  useImperativeHandle(ref, () => ({
    share<PERSON><PERSON><PERSON><PERSON><PERSON>,
    md<PERSON><PERSON><PERSON>,
  }));
  const { showClick, shareFriend } = props;
  const [artList, setArtList] = useState<any>(['']);
  const [bword, setBword] = useState<any>('');
  const [showType, setShowType] = useState<Boolean>(true);

  const userId = useSelector((state: UserStateType) => state.userid);
  useEffect(() => {
    userId && getAllPoster();
    msbTrack.track('xxys_Dailycollar_Invitationpop_view', {});
  }, [userId]);

  const getAllPoster = async () => {
    showLoading({
      title: '加载中',
    });
    const { art } = posterIds;
    const resArt = await getPosterList(art);
    const artResList = handleResult(resArt, art);
    setArtList(artResList);
    hideLoading();
    setTimeout(() => {
      shareWords(artResList);
    }, 0);
    return;
  };

  const shareWords = list => {
    const copywriting = list[0].copywriting || '';
    let word = '';
    if (copywriting.indexOf('<>') > -1) {
      const copywritingList = list[0].copywriting.split('<>');
      const num = Math.round(Math.random() * (copywritingList.length - 1));
      word = copywritingList[num];
    } else {
      word = copywriting;
    }
    setBword(word);
  };

  const getPosterList = async typeId => {
    const res = await getPosterListApi({
      id: typeId,
      qrCode: getCodeUrl({
        typeId,
        uid: userId,
        channelId: params.channel_id,
      }),
      uid: userId,
    });
    if (res.code === 0) {
      return res;
    } else {
      Taro.showToast({
        title: res.errors || '服务器开小差了',
      });
    }
  };

  const handleResult = (res, typeId) => {
    const { art } = posterIds;
    const { payload } = res;
    // 标准海报随机
    let randomNum = Math.floor(Math.random() * payload.length);
    // let randomNum = payload.length - 1;
    if (typeId === art) {
      payload.map(v => (v.pType = businessMap[0]));
    }
    return [payload[randomNum]];
  };

  const copyHandler = () => {
    Taro.setClipboardData({
      data: bword,
      success: function() {
        Taro.showToast({
          title: shareFriend
            ? '邀请语已复制，发圈时记得粘贴哦'
            : '邀请语已复制，发给好友成功概率更高 ',
          icon: 'none',
          duration: 3000,
        });
      },
    });
  };
  // 点击
  const shareClickHandler = (iscopy = false) => {
    iscopy && copyHandler();
  };

  const mdHandler = text => {
    msbTrack.track('xxys_Dailycollar_Invitationpop_click', {
      operation_type: text,
      invitation_content: bword,
      poster_id: artList[0].id,
    });
  };

  useShareAppMessage(() => {
    return {
      title: '小熊美术体验课降价啦！终于等到了，快来',
      path: `/pages/normalGroup/art/index?sendId=${userId}&channelId=${
        channelIds['art']
      }&sText=${bword.substr(0, 5)}`,
      imageUrl:
        'https://fe-cdn.xiaoxiongmeishu.com/ai-mp-user/image/art-share-img.png',
    };
  });

  return (
    <View className='poster-index'>
      {!showType ? (
        <View className='copy-text'>
          <View className='title'>复制邀请语分享，得金币概率翻倍</View>
          <View
            className='change-btn'
            onClick={() => {
              shareWords(artList);
            }}
          >
            <Image className='change-btn-img' src={clce} />
            换一换
          </View>
          <View className='poster-text-content'>{bword}</View>
          {shareFriend ? (
            <View className='share-btns'>
              <View
                className='share-btn'
                onClick={() => {
                  shareClickHandler();
                  mdHandler('直接分享');
                }}
              >
                直接分享
              </View>
              <View
                className='share-btn direct'
                onClick={() => {
                  shareClickHandler(true);
                  mdHandler('一键复制');
                }}
              >
                一键复制
              </View>
            </View>
          ) : (
            <View className='share-btns'>
              <Button
                className='share-btn'
                openType='share'
                onClick={() => {
                  showClick(false);
                  mdHandler('直接分享');
                }}
              >
                直接分享
              </Button>
              <Button
                className='share-btn direct'
                openType='share'
                onClick={() => {
                  copyHandler();
                  showClick(false);
                  mdHandler('一键复制');
                }}
              >
                一键复制
              </Button>
            </View>
          )}
          <Image
            className='close-img'
            src={close}
            onClick={() => {
              showClick(false);
              // setShowType(false);
              mdHandler('关闭按钮');
            }}
          />
        </View>
      ) : (
        <View className='poser'>
          <Image
            mode='widthFix'
            className='art-poster'
            showMenuByLongpress
            src={artList[0].individualizationUrl || ''}
          />
          <View className='text-ss'>长按海报 转发好友或保存图片分享</View>
          <View className='text-art'>推荐成功得兑换券 通通免费领</View>
          <Image
            className='close-img'
            src={close}
            onClick={() => {
              showClick(false);
              // setShowType(false);
            }}
          />
        </View>
      )}
    </View>
  );
});

export default Index;
