.drainage-poster-status-wrap {
  .at-modal__container {
    width: 580px;
    background-color: transparent;
    overflow: visible;
    top: 45%;
    .poster-index {
      position: relative;
      .copy-text {
        width: 580px;
        min-height: 538px;
        border-radius: 30px;
        box-sizing: border-box;
        padding: 48px 32px 40px;
        background-color: #fff;
        .title {
          font-size: 32px;
          text-align: center;
          font-weight: bold;
        }
        .change-btn {
          font-size: 24px;
          color: #ffa700;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          margin: 32px 0;
          &-img {
            width: 28px;
            height: 28px;
            margin-right: 6px;
          }
        }
        .poster-text-content {
          font-size: 28px;
          min-height: 150px;
          padding: 32px -32px;
        }
        .share-btns {
          display: flex;
          margin-top: 64px;
          align-items: center;
          justify-content: space-between;
          .share-btn {
            width: 242px;
            height: 84px;
            border: 2px solid #fa6c3a;
            line-height: 84px;
            box-sizing: border-box;
            font-size: 34px;
            color: #fa6c3a;
            text-align: center;
            border-radius: 84px;
            font-weight: bold;
            background: #fff;
            &.direct {
              background: #fa6c3a;
              color: #fff;
            }
          }
        }
      }
      .close-img {
        position: absolute;
        width: 64px;
        height: 64px;
        left: 0;
        right: 0;
        bottom: -150px;
        margin: 0 auto;
      }
      .poser {
        .art-poster {
          width: 100%;
          border-radius: 20px;
        }
        .text-ss {
          color: #fff;
          font-size: 34px;
          text-align: center;
          margin-top: 30px;
        }
        .text-art {
          color: #ffe801;
          font-size: 42px;
          text-align: center;
          width: 600px;
          margin-top: 30px;
        }
      }
    }
  }
  @media screen and (min-width: 480px) {
    .at-modal__container {
      text-align: center;
      .poster-index {
        .poser .art-poster {
          width: 350px;
        }
        .close-img {
          bottom: -80px;
        }
      }
    }
  }
}
.at-modal__overlay {
  background: rgba($color: #000000, $alpha: 0.7);
}
.drainage-afterShare-status-wrap {
  .at-modal__container {
    width: 600px;
    background-color: transparent;
    overflow: visible;
    top: 45%;
  }
  background: transparent;
  color: #fff;
  font-size: 34px;
  text-align: center;
  overflow: visible;
  .echo-wrap-title {
    margin-bottom: 22px;
  }
  .echo-wrap-content {
    height: 845px;
    padding: 75px 66px;
    box-sizing: border-box;
    background: url('https://fe-cdn.xiaoxiongmeishu.com/xiaoxiongmpactivity/weapp/wx9b8dcac074fb3151/common/echoShow-bg.png')
      no-repeat 0 0 /100%;
    &-item {
      width: 226px;
      height: 286px;
      border-radius: 18.5px;
      margin: 20px 16px 0 0;
      background-color: #fffad3;
      float: left;
      text-align: center;
      padding-top: 4px;
      box-sizing: border-box;
      &:nth-child(2n) {
        margin-right: 0;
      }
      &-img {
        width: 217px;
        height: 217px;
        margin-bottom: 8px;
        border-radius: 14px;
      }
      &-name {
        font-size: 25px;
        color: #a24c00;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .echo-wrap-close {
    position: absolute;
    left: 0;
    right: 0;
    width: 64px;
    height: 64px;
    bottom: -96px;
    margin: 0 auto;
  }
}
