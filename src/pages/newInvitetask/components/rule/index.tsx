import { View, Image } from '@tarojs/components';
import closeIcon from '@/assets/invitetask/it-rule-close.png';
import styles from './index.module.scss';

export default function Rule({ visible, close }) {
  const handleClose = () => {
    close();
  };

  return (
    <View
      className={`styles['at-popup'] ${
        visible ? '' : styles['at-popup-hiden']
      }`}
    >
      <View className={styles['overlay']} onClick={handleClose}></View>
      <View className={styles['layout']}>
        <View className={styles['close']} onClick={handleClose}>
          <Image src={closeIcon} mode='widthFix' />
        </View>
        <View className={styles['title']}>— 活动规则 —</View>
        <View className={styles['content-box']}>
          <View className={styles['content-box']}>
            <View className={styles['content']}>
              <View className={styles['sub-title']}>参与用户</View>
              <View className={styles['desc']}>小熊美术所有用户</View>
            </View>
            <View className={styles['content']}>
              <View className={styles['sub-title']}>活动变更</View>
              <View className={styles['desc']}>
                6月30号天天领新版上线后，老版领取记录可在首页右上角，历史记录里面查看
              </View>
            </View>
            <View className={styles['content']}>
              <View className={styles['sub-title']}>推荐规则</View>
              <View className={styles['desc']}>
                通过该活动，每邀请1位有效好友购买小熊美术双周体验版课程，获得兑换券
              </View>
              <View className={styles['desc']}>
                兑换券可兑换活动内任意实物商品、金币，对应实物、金币所需消耗兑换券数量以页面为准
              </View>
              <View className={styles['desc']}>
                有效好友：推荐好友需为正常有线上美术学习需求的用户且手机号并未购买该学科体验课，好友不满足以上条件则不计入有效推荐，推荐人无法获得兑换资格
              </View>
            </View>
            <View className={styles['content']}>
              <View className={styles['sub-title']}>兑换规则</View>
              <View className={styles['desc']}>
                兑换券兑换的实物于30个工作日内发货，仅支持邮寄中国大陆地址，个别实物不支持邮寄新疆、西藏、青海地区，可在页面进行更换
              </View>
              <View className={styles['desc']}>
                使用兑换券兑换的商品、金币，请慎重选择，商品无法进行更改
              </View>
              <View className={styles['desc']}>
                使用兑换券兑换商品时，请慎重填写地址，地址无法进行更改
              </View>
              <View className={styles['desc']}>
                若好友购买成功后退款，视为邀请失败，将扣除兑换券
              </View>
              <View className={styles['desc']}>
                如获得兑换券后，在活动结束未领取对应实物、金币，将视为放弃领取
              </View>
            </View>
            <View className={styles['content']}>
              <View className={styles['sub-title']}>其他说明</View>
              <View className={styles['desc']}>
                推荐人自己不能购买，如发现作弊，刷单等行为将取消推荐人获得奖励的资格
              </View>
              <View className={styles['desc']}>
                活动最终解释权归小熊美术所有。
              </View>
              <View className={styles['desc']}>
                如有特殊情况可联系班主任或客服处理
              </View>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
}
