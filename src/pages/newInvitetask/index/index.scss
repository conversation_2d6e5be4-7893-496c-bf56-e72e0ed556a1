@import '@/theme/groupbuy/common.scss';
page {
  background: #f8f8f8;
}
.index {
  font-size: 0;
  position: relative;
  padding-bottom: calc(112px + env(safe-area-inset-bottom));
  position: relative;
  padding-top: 312px;
  .banner {
    position: absolute;
    width: 100%;
    height: 454px;
    top: 0;
  }
  .float-btn {
    position: absolute;
    right: 0;
    width: 128px;
    height: 40px;
    line-height: 40px;
    font-size: 24px;
    color: #ffa700;
    background-color: #fff;
    text-align: center;
    z-index: 2;
    border-radius: 23px 0 0 23px;
    &.rule {
      top: 24px;
    }
    &.my-list {
      top: 80px;
    }
    &.history-list {
      top: 136px;
    }
  }
  .top-content {
    width: 686px;
    height: 580px;
    border-radius: 24px;
    background-color: #fff;
    padding: 120px 24px 0;
    box-sizing: border-box;
    margin: 0 auto;
    position: relative;
    z-index: 2;
    .title-banner {
      width: 580px;
      height: 80px;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      margin: 0 auto;
    }
    .swiper-content {
      width: 638px;
      height: 292px;
      border-radius: 24px;
      background: #ffeadf;
      box-sizing: border-box;
      padding: 20px 10px;
      .swiper {
        height: 250px;
        &-view {
          margin-right: 10px;
          height: 248px;
          background: #fff;
          text-align: center;
          padding-top: 4px;
          border-radius: 16px;
          box-sizing: border-box;
        }
        &-item {
          padding: 4px;
          height: 248px !important;
          box-sizing: border-box;
          &-img {
            width: 178px;
            height: 178px;
            border-radius: 12px;
          }
          &-name {
            font-size: 22px;
            color: #a24c00;
            margin-top: 12px;
            padding: 0 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
    .share-btns {
      width: 100%;
      margin-top: 40px;
      display: flex;
      justify-content: space-between;
      .share-btn {
        background: transparent;
        text-align: center;
        padding: 0;
        &::after {
          border: none;
        }
      }
      image {
        width: 292px;
        height: 88px;
      }
    }
  }
  .content {
    width: 686px;
    border-radius: 24px;
    background-color: #fff;
    padding: 30px;
    box-sizing: border-box;
    margin: 24px auto;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      &-img {
        width: 424px;
        height: 50px;
      }
      .coupon {
        font-size: 26px;
        color: #a24c00;
        height: 48px;
        line-height: 48px;
        padding: 0 16px;
        display: flex;
        background-color: #fffad3;
        border-radius: 24px;
        align-items: center;
        font-weight: 600;
        &-img {
          width: 30px;
          height: 30px;
          margin-right: 12px;
        }
      }
    }
    .p-list {
      margin-top: 24px;
      &-item {
        width: 300px;
        margin-right: 26px;
        margin-bottom: 20px;
        float: left;
        box-sizing: border-box;
        border: 1px solid #f0f0f0;
        border-radius: 20px;
        overflow: hidden;
        padding-bottom: 20px;
        &:nth-child(2n) {
          margin-right: 0;
        }
        &-img {
          width: 300px;
          height: 300px;
          margin-bottom: 12px;
        }
        &-name {
          padding: 0 20px;
          font-size: 28px;
          font-weight: bold;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-bottom: 24px;
        }
        &-exc {
          display: flex;
          padding: 0 20px;
          justify-content: space-between;
          &-img {
            width: 30px;
            height: 30px;
            margin-right: 12px;
          }
          &-num {
            font-size: 24px;
            display: flex;
            align-items: center;
            color: #ff5050;
          }
        }
      }
    }
  }
  .fixed-btn {
    position: fixed;
    bottom: 0;
    background: #fff;
    left: 0;
    right: 0;
    bottom: 0;
    height: 140px;
    text-align: center;
    padding-top: 10px;
    image {
      width: 726px;
      height: 140px;
      animation: movepoint 2s infinite;
    }
  }
  @keyframes movepoint {
    0% {
      transform: scale(1);
    }

    25% {
      transform: scale(1.05);
    }

    50% {
      transform: scale(1);
    }

    75% {
      transform: scale(1.05);
    }
  }
  .drainage-toapp-status-wrap {
    .at-modal__container {
      top: 45%;
      background: transparent;
      width: 580px;
      overflow: visible;
      .to-app {
        width: 580px;
        height: 934px;
      }
      .close-icon {
        position: absolute;
        width: 64px;
        height: 64px;
        left: 0;
        right: 0;
        bottom: -100px;
        margin: 0 auto;
      }
    }
  }
}
