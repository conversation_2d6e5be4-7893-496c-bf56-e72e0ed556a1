import Taro, { useRouter } from '@tarojs/taro';
import { useEffect, useRef, useState } from 'react';
import { Button, Swiper, SwiperItem, Image, View } from '@tarojs/components';
import { getProgramUserSubject } from '@/api/groupbuy';
import { AtModal } from 'taro-ui';
import { useDispatch, useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
// @ts-ignore
import msbTrack from '@/utils/sensors_data.weapp';
import { wxLogin } from '@/utils/auth';
import newTitle from '@/assets/newInvitetask/new-title.png';
import certificate from '@/assets/newInvitetask/certificate.png';
import shareFc from '@/assets/newInvitetask/new-share-fc.png';
import close from '@/assets/groupbuy/thirtySix/close.png';
import shareFr from '@/assets/newInvitetask/new-share-fr.png';
import toApp from '@/assets/newInvitetask/toapp.png';
import banner from '@/assets/newInvitetask/new-banner.png';
import titleImg from '@/assets/newInvitetask/title-img.png';
import shareBottomBtn from '@/assets/newInvitetask/share-bottom-btn.png';
import InviteTask from '../../invitetask/reduce/task';
import PosterModal from '../components/posterModal';
import ProduceDetailsModal from '../components/produceDetails';
import Rule from '../components/rule';
import './index.scss';

export default () => {
  const posterRef: any = useRef(null);
  const { params } = useRouter();
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  //openid
  const openId = useSelector((state: UserStateType) => state.openid);

  const [plist, setPlist] = useState<any>([]);
  const [posterShow, setPosterShow] = useState(false);
  const [productShow, setProductShow] = useState(false);
  const [toAppShow, setToAppShow] = useState(false);
  const [afterShareShow, setAfterShareShow] = useState(false);
  const [selItem, setSelItem] = useState<any>({});
  const [ruleVisible, setruleVisible] = useState(false);
  const [userInfo, setUserInfo] = useState<any>(null);
  const [selInt, setSelInt] = useState<number>(0); // 选择框状态
  const [isClickFriend, setIsClickFriend] = useState(true); // 点击的是否是好友

  //redux
  const dispatch = useDispatch();
  useEffect(() => {
    wxLogin();
    msbTrack.track('xxys_Dailycollar_homepage_view', {
      entrance_page: params.entrance_page || '',
      channel_id: params.channel || '',
      page_environment: '微信内',
    });
  }, []);

  useEffect(() => {
    getInit();
    userId && getUserIndex();
  }, [userId]);

  // 获取基础数据 未登录用户uid=-1
  const getInit = async () => {
    const task = new InviteTask({ uid: userId || '-1' });
    let _list = await task.getPrezzieListV2();
    setPlist(_list);
  };
  //   初始化
  const getUserIndex = () => {
    const task = new InviteTask({ uid: userId });
    task.prezzieinit().then(res => {
      setUserInfo(res);
    });
  };
  // 选中商品
  const setItem = item => {
    setSelItem(item);
    setProductShow(true);
    msbTrack.track('xxys_Dailycolla_goods_click', {
      goods_id: item.goodsId,
    });
    msbTrack.track('xxys_Dailycolla_goodspop_view', {
      button_name:
        userInfo && userInfo.star >= item.num
          ? '立即兑换'
          : '兑换券不足分享兑换',
    });
  };
  //   点击兑换
  const clickHandler = sType => {
    setProductShow(false);
    if (!sType || !userInfo) return;
    if (userInfo.star >= selItem.num) {
      setToAppShow(true);
    } else {
      BtnClickHandler(false, '分享好友');
      setTimeout(() => {
        posterRef.current.shareClickHandler(true);
        posterRef.current.mdHandler('一键复制');
      }, 500);
      msbTrack.track('xxys_Dailycollar_shareclick', {
        button_name: '兑换券不足分享兑换 ',
      });
    }
  };
  // 海报点击
  const posterClickHandler = sType => {
    setPosterShow(sType);
  };
  //   分享按钮点击
  const BtnClickHandler = (isFriend = true, text) => {
    setIsClickFriend(isFriend);
    isFriend && setPosterShow(true);
    msbTrack.track('xxys_Dailycollar_shareclick', {
      button_name: text,
    });
  };
  // 获取用户手机号
  const getPhoneNumber = (res, type, text) => {
    if (res.detail.errMsg === 'getPhoneNumber:fail user deny') {
      console.log(res.detail.errMsg);
    } else {
      const { encryptedData, iv } = res.detail;
      getProgramUserSubject({
        openId,
        encryptedData,
        iv,
        subject: 'ART_APP',
      }).then(phone => {
        if (phone.payload.uid) {
          dispatch({
            type: 'CHANGE_USERID',
            userid: phone.payload.uid,
          });
        }
        if (phone.payload.token)
          Taro.setStorageSync('appToken', phone.payload.token);
        phone.payload.mobile &&
          dispatch({
            type: 'CHANGE_MOBILE',
            mobile: phone.payload.mobile,
          });
        setProductShow(false);
        BtnClickHandler(type, text);
      });
    }
  };

  // 历史任务
  const jumpPage = () => {
    msbTrack.track('xxys_Dailycolla_Otherbuttons_click', {
      button_name: '历史任务',
    });
    Taro.navigateTo({
      url: '/pages/invitetask/detail/index',
    });
  };
  //   我的兑换
  const jumpList = () => {
    msbTrack.track('xxys_Dailycolla_Otherbuttons_click', {
      button_name: '我的兑换',
    });
    Taro.navigateTo({
      url: `/pages/newInvitetask/list/index?managementId=${
        userInfo ? userInfo.managementId : ''
      }`,
    });
  };
  //   活动规则
  const handleRule = () => {
    setruleVisible(val => !val);
    !ruleVisible &&
      msbTrack.track('xxys_Dailycolla_Otherbuttons_click', {
        button_name: '活动规则',
      });
  };

  return (
    <View className='index container w100 relative'>
      <View className='float-btn rule' onClick={handleRule}>
        活动规则
      </View>
      <View className='float-btn my-list' onClick={jumpList}>
        我的兑换
      </View>
      <View className='float-btn history-list' onClick={jumpPage}>
        历史任务
      </View>
      <Image className='banner' src={banner} />
      <View className='top-content'>
        <Image className='title-banner' src={titleImg} />
        <View className='swiper-content'>
          <Swiper
            autoplay
            circular
            easingFunction='linear'
            className='swiper'
            duration={5000}
            current={selInt}
            displayMultipleItems={3}
            skipHiddenItemLayout
            onChange={e => {
              setSelInt(e.detail.current);
            }}
          >
            {plist.map((o, idx) => (
              <SwiperItem
                key={idx}
                className='swiper-item'
                onClick={() => setItem(o)}
              >
                <View className='swiper-view'>
                  <Image
                    src={o.img}
                    className='swiper-item-img'
                    mode='aspectFill'
                  ></Image>
                  <View className='swiper-item-name'>{o.title}</View>
                </View>
              </SwiperItem>
            ))}
          </Swiper>
        </View>
        {userId ? (
          <View className='share-btns'>
            {/* <View
              className='share-btn'
              onClick={() => BtnClickHandler(true, '分享朋友圈')}
            >
              <Image src={shareFc} />
            </View>
            <View
              className='share-btn'
              onClick={() => BtnClickHandler(false, '分享好友')}
            >
              <Image src={shareFr} />
            </View> */}
            <View
              className='share-btn'
              //   onGetPhoneNumber={e => getPhoneNumber(e, true, '分享朋友圈')}
              onClick={() => {
                BtnClickHandler(true, '分享朋友圈');
                posterRef.current.shareClickHandler(true);
                posterRef.current.mdHandler('一键复制');
              }}
            >
              <Image src={shareFc} />
            </View>
            <Button
              className='share-btn'
              openType='share'
              //   onGetPhoneNumber={e => getPhoneNumber(e, false, '分享好友')}
              onClick={() => {
                BtnClickHandler(false, '分享好友');
                setTimeout(() => {
                  posterRef.current.shareClickHandler(true);
                  posterRef.current.mdHandler('一键复制');
                }, 500);
              }}
            >
              <Image src={shareFr} />
            </Button>
          </View>
        ) : (
          <View className='share-btns'>
            <Button
              className='share-btn'
              open-type='getPhoneNumber'
              onGetPhoneNumber={e => getPhoneNumber(e, true, '分享朋友圈')}
            >
              <Image src={shareFc} />
            </Button>
            <Button
              className='share-btn'
              open-type='getPhoneNumber'
              onGetPhoneNumber={e => getPhoneNumber(e, false, '分享好友')}
            >
              <Image src={shareFr} />
            </Button>
          </View>
        )}
      </View>
      <View className='content'>
        <View className='title'>
          <Image className='title-img' src={newTitle} />
          <View className='coupon'>
            <Image className='coupon-img' src={certificate} />
            {userInfo ? userInfo.star : 0}张
          </View>
        </View>
        <View className='p-list cl'>
          {plist.map((o, idx) => (
            <View className='p-list-item' key={idx} onClick={() => setItem(o)}>
              <Image className='p-list-item-img' src={o.img} />
              <View className='p-list-item-name'>{o.title}</View>
              <View className='p-list-item-exc'>
                <View className='p-list-item-exc-num'>
                  <Image className='p-list-item-exc-img' src={certificate} />{' '}
                  {o.num}
                </View>
              </View>
            </View>
          ))}
        </View>
      </View>
      {userId ? (
        <Button
          className='fixed-btn'
          openType='share'
          onClick={() => {
            BtnClickHandler(false, '立即分享');
            setTimeout(() => {
              posterRef.current.shareClickHandler(true);
              posterRef.current.mdHandler('一键复制');
            }, 500);
          }}
        >
          <Image src={shareBottomBtn} />
        </Button>
      ) : (
        <Button
          className='fixed-btn'
          open-type='getPhoneNumber'
          onGetPhoneNumber={e => getPhoneNumber(e, true, '立即分享')}
        >
          <Image src={shareBottomBtn} />
        </Button>
      )}
      <>
        {/* 海报 */}
        <AtModal
          isOpened={posterShow}
          closeOnClickOverlay={false}
          className='drainage-poster-status-wrap'
        >
          <PosterModal
            showClick={posterClickHandler}
            shareFriend={isClickFriend}
            ref={posterRef}
            afterShareHandler={() => {
              setAfterShareShow(true);
            }}
          />
        </AtModal>
        {/* 详情弹窗 */}
        <AtModal
          isOpened={productShow}
          className='drainage-address-status-wrap'
        >
          <ProduceDetailsModal
            item={selItem}
            clickFun={clickHandler}
            getPhoneNumberHandler={getPhoneNumber}
            userInfo={userInfo}
          />
        </AtModal>
        {/* 分享成功 */}
        <AtModal
          isOpened={afterShareShow}
          className='drainage-afterShare-status-wrap'
        >
          <View className='echo-wrap-title'>分享成功</View>
          <View
            className='echo-wrap-content'
            onClick={() => {
              setAfterShareShow(false);
            }}
          >
            {plist.slice(0, 4).map((item, index) => {
              return (
                <View className='echo-wrap-content-item' key={index}>
                  <Image
                    src={item.img}
                    style='object-fit: cover'
                    className='echo-wrap-content-item-img'
                  />
                  <View className='echo-wrap-content-item-name'>
                    {item.title}
                  </View>
                </View>
              );
            })}
          </View>
          <Image
            src={close}
            className='echo-wrap-close'
            onClick={() => {
              setAfterShareShow(false);
            }}
          />
        </AtModal>
        {/* 点兑换 */}
        <AtModal isOpened={toAppShow} className='drainage-toapp-status-wrap'>
          <Image
            className='to-app'
            src={toApp}
            onClick={() => setToAppShow(false)}
          ></Image>
          <Image
            className='close-icon'
            src={close}
            onClick={() => setToAppShow(false)}
          ></Image>
        </AtModal>
        {/* 规则 */}
        <Rule visible={ruleVisible} close={handleRule} />
      </>
    </View>
  );
};
