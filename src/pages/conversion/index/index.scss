page {
  background: #f7f7f7;
}
.conversion_wrap {
  width: 100%;
  height: 1500px;

  position: relative;
  .top-wrap {
    width: 100%;
    height: 628px;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .form_table {
    width: 690px;
    height: 850px;
    background: #ffffff;
    border-radius: 30rpx;
    position: absolute;
    left: 50%;
    top: 282px;
    margin-left: -345px;
    padding: 43px 30px 56px 30px;
    box-sizing: border-box;
  }
  .table_item {
    &_title {
      height: 45rpx;
      font-size: 32rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #222222;
      line-height: 45rpx;
      margin-bottom: 32px;
    }
    &_input {
      width: 630rpx;
      height: 88rpx;
      border-radius: 16rpx;
      border: 1rpx solid #e6e6e6;
      box-sizing: border-box;
      margin-bottom: 32px;
      padding-left: 12px;
    }
    &_code {
      position: relative;
      &_input {
        width: 630rpx;
        height: 88rpx;
        border-radius: 16rpx;
        border: 1rpx solid #e6e6e6;
        box-sizing: border-box;
        margin-bottom: 30px;
        margin-top: -12px;
        padding-left: 12px;
      }
      &_btn {
        width: 181rpx;
        height: 76rpx;
        line-height: 76px;
        text-align: center;
        background: #fff5e1;
        border-radius: 10rpx;
        position: absolute;
        top: 6px;
        right: 11px;
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ff8c0f;
        z-index: 10;
      }
    }
  }
  .table_item_place {
    font-size: 32rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #999999;
  }
  .agree_wrap {
    height: 40px;
    overflow: hidden;
    margin-bottom: 70px;
    display: flex;
    align-items: center;
    .peg {
      position: relative;
      width: 26px;
      height: 26px;
      background: #fff;
      border: 1px solid #111;
      border-radius: 50%;
      float: left;
      margin-right: 20px;
    }
    .select {
      &::after {
        content: '';
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        width: 0.29867rem;
        height: 0.29867rem;
        background: #ff8c0f;
        border: 0.04267rem solid #111;
        border-radius: 50%;
      }
    }
    .agree_tips {
      float: left;
      font-size: 26px;
      text-align: center;
      .agree_tips_text {
        color: #ff8c0f;
      }
    }
  }
  .form-btn {
    width: 600rpx;
    height: 88rpx;
    background: linear-gradient(90deg, #ffa300 0%, #ff6a00 100%);
    border-radius: 44rpx;
    font-size: 32rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #ffffff;
    line-height: 88px;
    text-align: center;
    margin: 0 auto;
  }
  .du_tips {
    font-size: 24rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #ff8c0f;
    margin-top: 20px;
    text-align: center;
  }
  .footer {
    position: absolute;
    left: 50%;
    bottom: 50px;
    width: 406px;
    margin-left: -203px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .foot-logo {
      width: 270px;
      height: 81px;
      image {
        width: 100%;
        height: 100%;
      }
    }
    .foot-tips {
      margin-top: 9px;
      .foot-tel {
        height: 36rpx;
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #d4d4d4;
        line-height: 36rpx;
        text-align: center;
      }
      .foot-email {
        height: 36rpx;
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #d4d4d4;
        line-height: 36rpx;
        text-align: center;
      }
    }
  }
}
