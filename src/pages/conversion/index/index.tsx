import { View, Image, Input, Text } from '@tarojs/components';
import { useDispatch, useSelector } from 'react-redux';
import msbTrack from '@/utils/msbTrack';
import Taro, {
  navigateTo,
  showToast,
  showLoading,
  useRouter,
} from '@tarojs/taro';
import {
  getLogin,
  getSupManagements,
  getUserOpenIdSubject,
  getVerificationCode,
} from '@/api/groupbuy';
import { checkExchangeCode } from '@/api/1v1k8s';
import { UserStateType } from '@/store/groupbuy/state';
import topbanner from '@/assets/conversion/top_banner.png';
import logo from '@/assets/conversion/logo.png';
import { useEffect, useState } from 'react';
import './index.scss';

/**
 * 双周排期
 *
 */

export default () => {
  const params = useRouter().params;
  const [codeKey, setCodeKey] = useState('');
  const [newMobile, setNewMobile] = useState<string>('');
  const [newCode, setNewCode] = useState('');
  const [codeSendStatus, setCodeSendStatus] = useState(true);
  const [codeText, setCodeText] = useState<string>('获取验证码');
  const [codeStatus, setCodeStatus] = useState(true);
  const [thirdOid, setThirdOid] = useState<string>('');
  const [isAgree, setIsAgree] = useState(true);

  const reg_tel = /^1(3|4|5|6|7|8|9)\d{9}$/;

  const dispatch = useDispatch();
  const uid = useSelector((state: UserStateType) => state.userid);
  console.log(uid);

  useEffect(() => {
    if (params.code) setCodeKey(params.code);
    if (params.oid) setThirdOid(params.oid);
  }, [params]);
  const onSubmit = () => {
    try {
      msbTrack.track('xxms_testcourse_applethomepage_buybuttonclick', {
        buy_model: 'model_3',
      });
      if (codeKey == '') {
        showToast({ title: '请输入兑换码', icon: 'none' });
        return false;
      }
      if (newMobile == '') {
        showToast({ title: '请输入手机号', icon: 'none' });
        return false;
      }
      if (!reg_tel.test(newMobile)) {
        showToast({ title: '请输入正确的手机号', icon: 'none' });
        return false;
      }
      if (newCode == '') {
        showToast({ title: '请输入验证码', icon: 'none' });
        return false;
      }
      if (!isAgree) {
        showToast({ title: '请同意并勾选用户协议及隐私协议', icon: 'none' });
        return false;
      }
      if (!codeSendStatus) {
        return false;
      }
      setCodeSendStatus(false);
      showLoading({ title: '加载中...' });
      login();
    } catch (error) {
      showToast({
        title: '系统错误，请联系管理员',
        icon: 'none',
        duration: 2000,
      });
    }
  };

  // 填写验证码
  const codeChange = val => {
    const value = String(val);
    setNewCode(value);
  };
  // 获取验证码
  const newSendCode = () => {
    if (newMobile == '') {
      showToast({ title: '请输入手机号', icon: 'none' });
      return false;
    }
    if (!reg_tel.test(newMobile)) {
      showToast({ title: '请输入正确的手机号', icon: 'none' });
      return false;
    }
    if (!codeStatus) {
      return false;
    }
    let timer_num = 60;
    const timeClock: any = setInterval(() => {
      timer_num--;
      setCodeText(`${timer_num}秒`);
      setCodeStatus(false);
      if (timer_num == 0) {
        clearInterval(timeClock);
        setCodeText('获取验证码');
        setCodeStatus(true);
      }
    }, 1000);
    // 发送验证码
    getVerificationCode(newMobile).then((res: any) => {
      if (res.code == 0) {
        showToast({ title: '发送成功', icon: 'none', duration: 2000 });
      } else {
        showToast({ title: '发送失败', icon: 'none', duration: 2000 });
        clearInterval(timeClock);
        setCodeText('获取验证码');
        setCodeStatus(true);
      }
    });
  };

  const login = () => {
    const DATA = {
      mobile: newMobile,
      code: newCode,
      channel: process.env.NODE_ENV === 'online' ? '13448' : '7925',
      sendId: '',
    };
    getLogin(DATA).then((res: any) => {
      setCodeSendStatus(true);
      if (res.code === 0) {
        dispatch({
          type: 'CHANGE_USERID',
          userid: res.payload.id,
        });
        dispatch({
          type: 'CHANGE_MOBILE',
          mobile: newMobile,
        });
        getSup(res.payload.id);
      } else {
        showToast({
          title: res.errors || '请输入正确信息',
          icon: 'none',
          duration: 2000,
        });
      }
    });
  };

  // 获取排期
  const getSup = userId => {
    getSupManagements({ type: 'TESTCOURSE', sup: 'S1' }).then(res => {
      const result = res.payload && res.payload;
      if (result) {
        checkExpress({ userId, stage: result.period });
      } else {
        showToast({
          title: '请输入正确信息' || res.errors,
          icon: 'none',
          duration: 2000,
        });
      }
    });
  };

  // 校验
  const checkExpress = ({ userId, stage }) => {
    checkExchangeCode({ userId, code: codeKey, stage, thirdOid }).then(res => {
      console.log(res, '====>');

      if (res.code === 0) {
        navigateTo({
          url: `/pages/conversion/course/index?mobile=${newMobile}&orderId=${res.payload}&userId=${userId}`,
        });
      } else {
        showToast({
          title: res.errors || '兑换码兑换失败',
          icon: 'none',
          duration: 5000,
        });
      }
    });
  };

  const handleAgree = () => {
    setIsAgree(val => !val);
  };

  const toAgree = name => {
    navigateTo({
      url: '/pages/conversion/agree/index?name=' + name,
    });
  };

  useEffect(() => {
    Taro.login({
      success: function(res) {
        if (res.code) {
          getUserOpenIdSubject({
            code: res.code,
            channel: params.channelId || '',
            subject: 'ART_APP',
          }).then(data => {
            if (data.code === 0) {
              if (data.payload.token)
                Taro.setStorageSync('appToken', data.payload.token);
              msbTrack.init({
                wx_openid: data.payload.openid,
                app_name: '小伴熊美术',
              });
              msbTrack.track('xxms_testcourse_applethomepage_view', {
                buy_model: 'model_3',
                channel_id:
                  process.env.NODE_ENV === 'online' ? '13448' : '7925',
              });
            }
          });
        } else {
          console.log('登录失败！' + res.errMsg);
        }
      },
    });
  }, []);

  // useEffect(() => {}, []);

  return (
    <View className='conversion_wrap'>
      <View className='top-wrap'>
        <Image src={topbanner} mode='widthFix' />
      </View>
      <View className='form_table'>
        <View className='table_item'>
          <View className='table_item_title'>您的兑换码</View>
          <Input
            className='table_item_input'
            cursorSpacing={10}
            type='text'
            placeholder='例：ERYI6279'
            value={codeKey}
            placeholderClass='table_item_place'
            style={!!params.code ? 'color:#999;background:#f0f0f0;' : ''}
            disabled={!!params.code}
            onInput={(e: any) => {
              const value = e.detail.value;
              setCodeKey(value);
            }}
          />
        </View>
        <View className='table_item'>
          <View className='table_item_title'>请绑定您的账号</View>
          <Input
            className='table_item_input'
            placeholderClass='table_item_place'
            type='number'
            placeholder='手机号'
            value={newMobile}
            cursorSpacing={10}
            maxlength={11}
            disabled={false}
            onInput={(e: any) => {
              const value = e.detail.value;
              setNewMobile(value);
            }}
          />
        </View>
        <View className='table_item_code'>
          <Input
            className='table_item_code_input'
            type='number'
            placeholder='验证码'
            value={newCode}
            cursorSpacing={10}
            maxlength={4}
            onInput={(e: any) => {
              const value = e.detail.value;
              codeChange(value);
            }}
            placeholderClass='table_item_place'
          />
          <View className='table_item_code_btn' onClick={newSendCode}>
            {codeText}
          </View>
        </View>
        <View className='agree_wrap'>
          <View
            className={`peg ${isAgree ? 'select' : ''}`}
            onClick={handleAgree}
          ></View>
          <View className='agree_tips'>
            我已阅读并同意
            <Text
              className='agree_tips_text'
              onClick={() => toAgree('registAgreement')}
            >
              《用户协议》
            </Text>
            和
            <Text
              className='agree_tips_text'
              onClick={() => toAgree('privacyprotocol')}
            >
              《隐私协议》
            </Text>
          </View>
        </View>
        <View className='form-btn' onClick={onSubmit}>
          立即兑换
        </View>
        <View className='du_tips'>兑换码3天内有效，请您尽快兑换</View>
      </View>

      <View className='footer'>
        <View className='foot-logo'>
          <Image src={logo} mode='widthFix' />
        </View>
        <View className='foot-tips'>
          {/* <View className='foot-tel'>举报电话：************</View>
          <View className='foot-email'>举报邮箱：<EMAIL></View> */}
        </View>
      </View>
    </View>
  );
};
