import Taro, { useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import { View, Image } from '@tarojs/components';
import { judgeMinienv } from '@/utils/auth';
import msbTrack from '@/utils/msbTrack';
import DefaultAddress from '@/components/defaultAddress';
import arrowRight from '@/assets/groupbuy/index/arrow-right.png';
import imgGift from '@/assets/conversion/img-gift.png';
import imgGiftFour from '@/assets/conversion/img-gift-four.png';
import imgS1 from '@/assets/conversion/icon-S1.png';
import imgS2 from '@/assets/conversion/icon-S2.png';
import imgS3 from '@/assets/conversion/icon-S3.png';
import imgS4 from '@/assets/conversion/icon-S4.png';
import iconS1 from '@/assets/conversion/icon-abc-S1.png';
import iconS2 from '@/assets/conversion/icon-abc-S2.png';
import iconS3 from '@/assets/conversion/icon-abc-S3.png';
import iconS4 from '@/assets/conversion/icon-abc-S4.png';
import chooseRight from '@/assets/groupbuy/addAddress/chooseCity.png';
// import LevelTips from '@/components/levelTips';
// import CheckLevel from '@/utils/checkLevel';

import { AtForm, AtInput, AtButton, AtTextarea, AtFloatLayout } from 'taro-ui';
import {
  getCenterAddressList,
  getAddressTownList,
  createUserAddress,
  submitSupApi,
  createAddressForExpressAndOrder,
  updateUserAddress,
} from '@/api/groupbuy';
import './index.scss';

export default () => {
  let lock = true;
  let timer: any = null;
  const params = useRouter().params;
  const userId = params.userId || '';
  const orderId = params.orderId || '';
  const recall: any = params.recall || '0';

  const [province, setProvince] = useState('');
  const [city, setCity] = useState('');
  const [street, setStreet] = useState('');
  const [area, setArea] = useState('');
  const [tabList] = useState(['请选择']);
  const [addressList, setAddressList] = useState<any>([]);
  const [telAreaCode, setTelAreaCode] = useState('');
  const [cityList, setCityList] = useState<any>([]);
  const [county, setCounty] = useState<any>([]);
  const [town, setTown] = useState<any>([]);
  const [selInd, setSelInd] = useState<any>(null);
  const [currentTab, setcurrentTab] = useState(0);
  const [receiver, setReceiver] = useState<string>('');
  const [mobile, setMobile] = useState<string>('');
  const [address, setAddress] = useState<string>('');
  const [details, setDetails] = useState<string>('');
  const [isOpen, setIsOpen] = useState(false);
  // 是否已经购买过级别
  const [hasBuyLevel, setHasBuyLevel] = useState<any[]>([]);

  const [addressId, setAddressId] = useState('');
  const [defaultAddressModal, setDefaultAddressModal] = useState({
    show: false,
    type: '1',
    isCheck: true,
    isChange: false,
    newAddress: false,
  });
  const [defaultAddressData, setDefaultAddressData] = useState({
    addressId: '',
    telAreaCode: '',
    province: '',
    city: '',
    area: '',
    street: '',
    addressData: '',
    addressDetail: '',
    name: '',
    phone: '',
  });
  const filterBuyLevel = sup => {
    console.log(hasBuyLevel, 'sup');

    // return (
    //   hasBuyLevel &&
    //   hasBuyLevel.length &&
    //   hasBuyLevel.findIndex(v => v == sup) > -1
    // );
    return false;
  };
  const SUP = [
    {
      title: '3–4.5周岁',
      desc: '启蒙 | 儿童启蒙绘画和手工',
      color: '#FF9C00',
      sup: 'S1',
      cho_img: imgS1,
      abs_img: iconS1,
    },
    {
      title: '4.5–7周岁',
      desc: '入门 | 儿童创意画和手工',
      color: '#8DD54F',
      sup: 'S2',
      cho_img: imgS2,
      abs_img: iconS2,
    },
    {
      title: '7–9周岁',
      desc: '进阶 | 创意画和专业基础',
      color: '#26C1FC',
      sup: 'S3',
      cho_img: imgS3,
      abs_img: iconS3,
    },
    {
      title: '9–12周岁',
      desc: '专业 | 彩铅、动漫、国画',
      color: '#AA56DD',
      sup: 'S4',
      cho_img: imgS4,
      abs_img: iconS4,
    },
  ];
  const handleClickProvince = item => {
    tabList[0] = item.provinceName;
    tabList[1] = '请选择';
    tabList[2] = '';
    tabList[3] = '';
    setTelAreaCode(item.telAreaCode);
    setProvince(item.provinceName);
    setcurrentTab(1);
    setCityList(item.citys);
  };
  const handleClickCity = item => {
    tabList[1] = item.cityName;
    tabList[2] = '请选择';
    tabList[3] = '';
    setCity(item.cityName);
    setcurrentTab(2);
    setCounty(item.countys);
  };
  const handleClickRegion = item => {
    tabList[2] = item.countyName;
    tabList[3] = '请选择';
    setArea(item.countyName);
    setcurrentTab(3);
    getAddressTownList({ code: item.countyCode }).then(res => {
      let data = res.payload;
      data.unshift({ townName: '暂不选择' });
      setTown(data);
    });
  };
  const handleClickTown = item => {
    setcurrentTab(4);
    if (item.townName !== '暂不选择') {
      tabList[3] = item.townName;
      setStreet(item.townName);
    } else {
      tabList[3] = '请选择';
      setStreet('');
    }
    setcurrentTab(3);
    setIsOpen(false);
  };
  const openArea = () => {
    setIsOpen(true);
  };
  const closeArea = () => {
    setIsOpen(false);
  };
  function onReceiver(res) {
    clearTimeout(timer);
    timer = setTimeout(() => {
      setReceiver(res);
    }, 400);
  }
  function onMobile(res) {
    clearTimeout(timer);
    timer = setTimeout(() => {
      setMobile(res);
    }, 400);
  }
  function onDetails(res) {
    clearTimeout(timer);
    timer = setTimeout(() => {
      setDetails(res);
    }, 400);
  }
  const handleClick = value => {
    setcurrentTab(value);
  };
  function onAddressInput() {}
  function vuerify() {
    const errors: Array<String> = [];
    if (selInd == null) {
      errors.push('sup required.');
      Taro.showToast({
        title: '请选择年龄',
        icon: 'none',
        duration: 2000,
      });
    } else if (!receiver) {
      errors.push('Name required.');
      Taro.showToast({
        title: '请输入收货人姓名',
        icon: 'none',
        duration: 2000,
      });
    } else if (!mobile) {
      errors.push('mobile required.');
      Taro.showToast({
        title: '请填写手机号',
        icon: 'none',
        duration: 2000,
      });
    } else if (!address) {
      errors.push('province required.');
      Taro.showToast({
        title: '请选择省市区',
        icon: 'none',
        duration: 2000,
      });
    } else if (!details) {
      errors.push('detail required.');
      Taro.showToast({
        title: '请输入详细地址',
        icon: 'none',
        duration: 2000,
      });
    }
    if (!errors.length) {
      return true;
    }
  }
  // 新建地址
  function createNewAddress() {
    return new Promise((resolve, reject) => {
      // 判断是否有默认地址或者修改了默认地址
      if (!addressId || defaultAddressModal.newAddress) {
        createUserAddress({
          subject: 'ART_APP',
          userId: userId,
          receiptName: receiver,
          receiptTel: mobile,
          province: province,
          city: city,
          area: area,
          street: street,
          addressDetail: details,
          telAreaCode: telAreaCode,
          areaCode: '',
          idCode: '',
          isDefault: addressId ? '0' : '1',
        })
          .then(res => {
            if (res.code === 0) {
              resolve({ addressId: res.payload.id });
            } else {
              reject(res);
            }
          })
          .catch(error => {
            reject(error);
          });
      } else if (defaultAddressModal.isChange) {
        updateUserAddress({
          addressId: addressId,
          userId: userId,
          receiptName: receiver,
          receiptTel: mobile,
          province: province,
          city: city,
          area: area,
          street: street,
          addressDetail: details,
          telAreaCode: telAreaCode,
          areaCode: '',
          idCode: '',
          isDefault: '1',
        }).then(res => {
          if (res.code === 0) {
            resolve({ addressId: res.payload.id });
          } else {
            setDefaultAddressModal({
              ...defaultAddressModal,
              isCheck: true,
              isChange: false,
            });
            reject(res);
          }
        });
      } else {
        // 使用默认地址可以直接关联订单生成物流信息
        resolve({ addressId: addressId });
      }
    });
  }
  function onSubmit() {
    msbTrack.track('xxms_testcourse_appletaddresspage_submitbuttonclick', {
      buy_model: 'model_3',
    });
    const flag = vuerify();
    if (flag) {
      if (vuerifyaddress()) {
        setDefaultAddressModal({
          show: true,
          type: '2',
          isCheck: true,
          isChange: false,
          newAddress: false,
        });
      } else {
        if (lock) {
          lock = false;
          Taro.showLoading();
          createNewAddress()
            .then((resAddress: any) => {
              createAddressForExpressAndOrder({
                userId,
                orderId,
                addressId: resAddress.addressId,
              })
                // eslint-disable-next-line @typescript-eslint/no-shadow
                .then(res => {
                  lock = true;
                  Taro.hideLoading();
                  if (res.code === 0) {
                    submitSupApi({
                      orderId: orderId,
                      subject: 'ART_APP',
                      sup: SUP[selInd].sup,
                      userId,
                    }).then(res1 => {
                      if (!res1.code) {
                        let _url = `/pages/conversion/addteacher/index?orderId=${orderId}`;
                        Taro.navigateTo({
                          url: _url,
                        });
                      } else {
                        lock = true;
                        Taro.showToast({
                          title: '选择年龄失败，请联系管理员',
                          icon: 'none',
                          duration: 2000,
                        });
                      }
                    });
                  } else {
                    Taro.showToast({
                      title: res.errors || '服务器开小差了',
                      icon: 'none',
                      duration: 2000,
                    });
                  }
                })
                .catch(() => {
                  lock = true;
                  Taro.hideLoading();
                });
            })
            .catch(error => {
              lock = true;
              Taro.showToast({
                title: error.errors || '服务器开小差了',
                icon: 'none',
                duration: 2000,
              });
              // Taro.hideLoading();
            });
        }
      }
    }
  }
  useEffect(() => {
    getCenterAddressList().then(res => {
      const { data } = res.payload;
      setAddressList(data);
    });
    return () => {
      timer && clearTimeout(timer);
    };
  }, [timer]);
  useEffect(() => {
    if (street) {
      province !== city
        ? setAddress(`${province}/${city}/${area}/${street}`)
        : setAddress(`${province}/${area}/${street}`);
    } else if (province && city && area) {
      province !== city
        ? setAddress(`${province}/${city}/${area}`)
        : setAddress(`${province}/${area}`);
    }
  }, [province, city, area, street]);

  useEffect(() => {
    if (params.mobile) setMobile(params.mobile);
  }, [params]);

  useEffect(() => {
    msbTrack.track('xxms_testcourse_appletaddresspage_view', {
      buy_model: 'model_3',
      abtest: recall == 1 ? '召回流程' : '购买流程',
    });
  }, []);
  // 获取默认地址

  useEffect(() => {
    console.log('submit==');

    defaultAddressModal.isChange && onSubmit();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultAddressModal.isChange]);

  // 默认地址与页面数据关联
  function variableAssignment(onlysetId: boolean, data: any) {
    console.error(data);
    if (!onlysetId) {
      setReceiver(data.name);
      setMobile(data.phone);
      setAddress(data.addressData);
      setDetails(data.addressDetail);
      setProvince(data.province);
      setTelAreaCode(data.telAreaCode);
      setCity(data.city);
      setArea(data.area);
      setStreet(data.street);
      setDefaultAddressData(data);
    }
    setAddressId(data.addressId);
  }

  //校验默认地址是否修改 或者未使用默认地址
  function vuerifyaddress() {
    if (!addressId) {
      return false;
    }
    console.error(defaultAddressModal);
    let isNeedCheck = true;
    if (defaultAddressModal.isCheck) {
      if (
        receiver === defaultAddressData.name &&
        mobile === defaultAddressData.phone &&
        details === defaultAddressData.addressDetail &&
        province === defaultAddressData.province &&
        city === defaultAddressData.city &&
        area === defaultAddressData.area &&
        street === defaultAddressData.street &&
        telAreaCode === defaultAddressData.telAreaCode
      ) {
        isNeedCheck = false;
      }
    } else {
      isNeedCheck = false;
    }

    return isNeedCheck;
  }

  // 获取是否已经购买过级别
  // useEffect(() => {
  //   userId && new CheckLevel({
  //     userId,
  //     channelId: '11071',
  //     orderId: orderId || '',
  //     regtype: 'EXPERIENCE',
  //     subjects: 'ART_APP',
  //     packageId: '62'
  //   })
  //     .initCheck()
  //     .then((res: any[]) => {
  //       console.log(res, '[[[');

  //       setHasBuyLevel(res);
  //     });

  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [userId]);

  return (
    <View className='conversion-wrap'>
      {judgeMinienv('release') && (
        <View className='form-card'>
          <DefaultAddress
            setDefaultAddressModal={setDefaultAddressModal}
            defaultAddressModal={defaultAddressModal}
            variableAssignment={variableAssignment}
          />
          <View className='form-title'>
            {recall == 1 ? '您的礼包尚未领取' : '确认收货地址'}
          </View>
          <View className='form-desc'>
            {recall == 1 ? '请尽快填写地址' : '画材大礼盒正在准备'}
          </View>
          <View className='level-part'>
            <View className='level-part-title'>画材大礼盒</View>
            <View className='level-part-img'>
              <Image
                className='level-part-img-abs'
                src={selInd != null ? SUP[selInd].cho_img : imgS1}
              />
              <Image
                className='level-part-img-bg'
                src={selInd != 3 ? imgGift : imgGiftFour}
              />
            </View>
            <View className='level-part-choose'>
              <View className='level-part-choose-title'>请选择适合年龄</View>
              <View className='level-part-choose-items'>
                {SUP.map((o, i) => {
                  return (
                    i != 3 && (
                      <View
                        className={`level-part-choose-item ${
                          filterBuyLevel(o.sup) ? `selected` : ``
                        } ${selInd == i ? 'sel' : ''}`}
                        key={i}
                        onClick={() => {
                          if (filterBuyLevel(o.sup)) {
                            return;
                          }
                          setSelInd(i);
                        }}
                        style={`${
                          selInd == i ? 'border-color:' + o.color : ''
                        }`}
                      >
                        <View className='level-part-choose-item-title'>
                          {o.title}
                        </View>
                        <View
                          className='level-part-choose-item-desc'
                          style={`color:${o.color}`}
                        >
                          {o.desc}
                        </View>
                        <Image
                          className='level-part-choose-item-abs'
                          src={o.abs_img}
                        />
                      </View>
                    )
                  );
                })}
              </View>
              {/* <LevelTips /> */}
            </View>
          </View>
          <AtForm>
            <View className='form-desc ss'>填写地址</View>
            <AtInput
              required
              name='receiver'
              title='收货人:'
              type='text'
              placeholder='请输入收货人姓名'
              value={receiver}
              onChange={onReceiver}
            />
            <AtInput
              required
              name='mobile'
              type='phone'
              title='手机号码:'
              // disabled
              placeholder='请输入手机号码'
              value={mobile}
              onChange={onMobile}
            ></AtInput>
            <View className='region'>
              <AtInput
                required
                name='address'
                type='text'
                title='所在地址:'
                placeholder='所在地址'
                value={address}
                editable={false}
                onChange={onAddressInput}
                onClick={openArea}
              >
                <View>
                  <Image src={arrowRight} className='arrow' />
                </View>
              </AtInput>
            </View>
            <View className='addr-textarea'>
              <View className='addr-left'>详细地址:</View>
              <AtTextarea
                className={isOpen ? 'textareaHide' : ''}
                count={false}
                value={details}
                onChange={onDetails}
                maxLength={200}
                height={160}
                placeholder='如街道、小区门牌号等'
              />
            </View>
            {!isOpen && (
              <View className='foot-box'>
                <AtButton className='foot-btn' onClick={onSubmit}>
                  领取大礼包
                </AtButton>
              </View>
            )}
          </AtForm>
        </View>
      )}
      <AtFloatLayout
        className='address-area-float'
        isOpened={isOpen}
        onClose={closeArea}
        title='请选择所在地区'
      >
        <View className='area-box'>
          <View className='area-tabs'>
            {tabList.map((item, index) => {
              return (
                <View
                  className={`area-tabs-item ${
                    currentTab === index ? 'active' : ''
                  }`}
                  key={`tab-${index}`}
                  onClick={() => handleClick(index)}
                >
                  {item}
                </View>
              );
            })}
          </View>
          <View className='area-list'>
            <View
              className={`area-province ${
                currentTab === 0 ? 'active' : 'hide'
              }`}
            >
              {addressList.map((item, index) => {
                return (
                  <View
                    className='area-list-item'
                    key={`area-province${index}`}
                    onClick={() => handleClickProvince(item)}
                  >
                    {item.provinceName === province && (
                      <Image src={chooseRight} className='choose-right' />
                    )}
                    {item.provinceName}
                  </View>
                );
              })}
            </View>
            <View
              className={`area-city ${currentTab === 1 ? 'active' : 'hide'}`}
            >
              {cityList.map((item, index) => {
                return (
                  <View
                    className='area-list-item'
                    key={`area-city${index}`}
                    onClick={() => handleClickCity(item)}
                  >
                    {item.cityName === city && (
                      <Image src={chooseRight} className='choose-right' />
                    )}
                    {item.cityName}
                  </View>
                );
              })}
            </View>
            <View
              className={`area-region ${currentTab === 2 ? 'active' : 'hide'}`}
            >
              {county.map((item, index) => {
                return (
                  <View
                    className='area-list-item'
                    key={`area-region${index}`}
                    onClick={() => handleClickRegion(item)}
                  >
                    {item.countyName === area && (
                      <Image src={chooseRight} className='choose-right' />
                    )}
                    {item.countyName}
                  </View>
                );
              })}
            </View>
            <View
              className={`area-street ${currentTab === 3 ? 'active' : 'hide'}`}
            >
              {town.map((item, index) => {
                return (
                  <View
                    className='area-list-item'
                    key={`area-street${index}`}
                    onClick={() => handleClickTown(item)}
                  >
                    {item.townName === street && (
                      <Image src={chooseRight} className='choose-right' />
                    )}
                    {item.townName}
                  </View>
                );
              })}
            </View>
          </View>
        </View>
      </AtFloatLayout>
    </View>
  );
};
