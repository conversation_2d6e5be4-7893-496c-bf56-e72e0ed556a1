@import '../../../theme/groupbuy/common.scss';
.conversion-wrap {
  width: 100%;
  background: #f7f7f7;
  min-height: 100vh;
  .form-card {
    padding: 29px 0 128px 0px;
    box-sizing: border-box;
    overflow: hidden;
    margin-bottom: 40px;
    .form-title {
      width: fit-content;
      height: 56px;
      font-size: 36px;
      font-weight: bold;
      color: #333333;
      line-height: 56px;
      display: flex;
      align-items: center;
      margin: 0 auto;
      &::before {
        display: block;
        content: '';
        width: 45px;
        height: 20px;
        background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAoCAYAAAB+Qu3IAAAB9klEQVRoQ+3asUocURTG8f+xkBRBaxsfQCGFCPYhCKntNJ0aEC3UJ/AJ1hSKELRb7awDItYKmkLYPEAaa3ULsdgTZseRZYkw9453QPfbbuCcM8tvhnPvuYzR9/MNJnGWMGZxxoGP/TG6/q9AG+MvzgnGvm3T6o2y4sK3GOaOBs4KzpAwKwgYHYw9Rti0LR6zSl3oLvItv3A+Vyiv1H4B44xRvmbYOfQmO3RYlVQCgSF2rcGadXsyXKtdJEDOe0YH+GS+zjawnug2KpsL/Mje6BbOhEQSChh/sjf6Xlu4hMh56bagkxsX0God6amfWocWw/TU3cVQ27uU0MX2TgNLSmWgGFg0gieE7h/Bn7F1qPQ66i8dKvVW1zFptHW5Y9Lo8kosJfB8Hl0qWkHRAoKOpgtLFHSYV3S0oKPpwhIFHeYVHS3oaLqwREGHeUVHCzqaLixR0GFe0dGCjqYLSxR0mFd0dO3Q7m78PlzCWcRsEvf39W2fWRv3FsYBUwv7ZubZ06kV2i+Px7CHJu6D8emZ2Rn+4ZtNz93UBp2/yUenA4NcNJkMe2r+S33QV81lnJ/RTe4tJxrf64O+bJ4DM2/Zq8J/v6gP+urw/t0tfGXlzdqCLotVJa5WaLWOKo+qfK5rMSyPVSVS27sqeoG5GlgCwaqED+oI/g+Lvb+FLrcynwAAAABJRU5ErkJggg==')
          center/100% no-repeat;
        margin-right: 22px;
      }
      &::after {
        display: block;
        content: '';
        width: 45px;
        height: 20px;
        background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAoCAYAAAB+Qu3IAAACDklEQVRoQ+3asU8UQRTH8e+PglgI1Db+AZJYAInWxJhY28l1IgnRAvgL+AvAAmOi2B121ibGUGsCFibnH2BDrVxBKO6Z3XWRXDTZeetsFN51m7w3e/e5yZt5kxFjH1tnFmMZcRfjOnB1PCaefyswRHzFeIfY1TaD81GqH2yTSb6zhbGKMRGYLQTECPGcaTa0yWkxUgldIn/jLcZii+EjdVxA7DPDvQK7gt5ghxGPQyqDwATPtMUTlTUZPke5yIBc1YwRcFO2xjawluk1MWwl8LSY0QOMGyGSUUB8KWb0cWzhMiJXQw8DOrtxDR2lIz/1z9IRi2F+6nIxjO1dTuh6excNS05loG5YogXPCD3egp9hx6HS31H/06HS+dHjmNRt3eyY1D18JDYSODuPbhQdQW6BgHbTpSUGdJqXOzqg3XRpiQGd5uWODmg3XVpiQKd5uaMD2k2XlhjQaV7u6IB206UlBnSalzv615UwM/FpbxnjIdIsZhfrzp00xGyAeMXc0q4kc6s5EqubSgdvrqGTPmaX40qYtI9d6Wnh/pHDzJUiK2fy6/eXBrlmKrDnHtzpambLDvuPMF64/qb/PUmsaL73soufITvofwBudfGyf/AdH7XQu93F95Id7h1fuIWvqZw01PzSVNPwNnEB3Rl0lI6uSkcshm1KQtPc2N511LhEw9J0SraMixa8JWDT9B/yccap56KHfwAAAABJRU5ErkJggg==')
          center/100% no-repeat;
        margin-left: 22px;
      }
    }
    .form-desc {
      height: 56px;
      font-size: 36px;
      color: #333333;
      font-weight: bold;
      line-height: 56px;
      margin-bottom: 28px;
      text-align: center;
      &.ss {
        padding-left: 30px;
        text-align: left;
        padding-top: 20px;
      }
    }
    .at-textarea {
      border: 0;
      margin-left: 14px;
      padding-top: 14px;
    }
    .arrow {
      width: 12px;
      height: 22px;
      padding: 0px;
      margin-right: 10px;
    }
    .mobile-text {
      color: $light-grey;
      font-size: $font-30;
      padding-right: 16px;
      padding-left: 0px;
    }
    .region {
      .at-input--disabled {
        opacity: 1;
      }
    }
    .addr-textarea {
      display: flex;
      padding-left: 16px;
      box-sizing: border-box;
      color: #333;

      .addr-left {
        margin: 26px 8px 0 16px;
        width: 172px;
        font-size: 32px;
        vertical-align: middle;
        text-align: left;
        color: #555;
        &::before {
          display: inline-block;
          margin-right: 8px;
          color: #ff4949;
          font-size: 28px;
          font-family: SimSun, sans-serif;
          line-height: 1;
          content: '*';
        }
      }
      .at-textarea {
        flex: 1;
        margin: 0;
        padding: 26px 20px 0 0;
        margin-left: -1px;
      }
    }
    .at-input__children {
      &::after {
        border: 0;
      }
    }
    .at-form::after {
      border: 0;
    }
    .at-input {
      height: 100px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
    }
    .at-input__title {
      color: #555;
      font-weight: normal;
    }
    .at-input__input {
      color: #333;
    }
    .at-textarea__textarea {
      color: #333;
      font-weight: normal !important;
    }
    .level-part {
      width: 686px;
      padding: 24px 34px;
      background-color: #fff;
      &-title {
        font-size: 34px;
        color: #333333;
        font-weight: 600;
        margin-bottom: 19px;
      }
      &-img {
        position: relative;
        width: 100%;
        &-bg {
          width: 100%;
          height: 180px;
        }
        &-abs {
          position: absolute;
          width: 76px;
          height: 76px;
          left: -13px;
          top: -13px;
        }
      }
      &-choose {
        &-title {
          font-size: 32px;
          padding: 22px 0;
        }
        &-item {
          width: 330px;
          height: 92px;
          padding: 6px 0;
          text-align: center;
          border-radius: 15px;
          display: inline-block;
          border: 1px solid #ccc;
          margin-right: 26px;
          box-sizing: border-box;
          position: relative;
          margin-bottom: 30px;
          padding-top: 6px;
          &:nth-child(2n) {
            margin-right: 0;
          }
          &-desc {
            font-size: 24px;
          }
          &-abs {
            position: absolute;
            left: 0;
            top: 0;
            width: 50px;
            height: 27px;
          }
          &:nth-child(1).sel {
            background-color: rgba(255, 156, 0, 0.1);
          }
          &:nth-child(2).sel {
            background-color: rgba(141, 213, 79, 0.1);
          }
          &:nth-child(3).sel {
            background-color: rgba(38, 193, 252, 0.1);
          }
          &:nth-child(4).sel {
            background-color: rgba(170, 86, 221, 0.1);
          }
          &.selected {
            box-shadow: 0 0 13px 0 rgba(177, 177, 177, 0.26);
            background-color: #ffffff !important;

            .level-part-choose-item-title,
            .level-part-choose-item-desc {
              color: #b1b1b1 !important;
            }
            &::after {
              content: ' ';
              width: 80px;
              height: 64px;
              position: absolute;
              top: 0;
              right: 0;
              background-image: url('../../../assets/thirtySix/chooseLevel/hasbuy.png');
              background-repeat: no-repeat;
              background-position: 0 0;
              background-size: cover;
            }
          }
        }
      }
    }
    .at-form {
      margin-top: 40px;
      font-weight: 400 !important;
      .at-input__children {
        display: none;
      }
    }
    .foot-box {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 999;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 144px;
      padding-bottom: calc(env(safe-area-inset-bottom) * 0.5);
      background-color: #fff;
      .foot-btn {
        border: 0;
        width: 100%;
        margin: 0 30px;
        height: 96px;
        line-height: 96px;
        text-align: center;
        background: linear-gradient(90deg, #ff5d31 0%, #ff9c31 100%);
        border-radius: 52px;
        color: #fff;
        font-size: 36px;
        font-weight: bold;
      }
    }
  }

  .address-area-float {
    .layout {
      height: 80%;
    }

    .at-float-layout__container {
      .layout-body {
        .layout-body__content {
          max-height: 840px;
        }
      }
    }
  }
  .area-box {
    padding-top: 60px;
    .area-tabs {
      width: 100%;
      position: fixed;
      top: 90px;
      height: 80px;
      padding-left: 15px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      border-bottom: 1px solid #f5f5f5;
      background-color: #fff;
      transform: translateZ(0);
      -webkit-overflow-scrolling: touch;
      .active {
        color: #ff9c00;
      }
      .area-tabs-item {
        margin: 10px 0 20px 30px;
        &:first-child {
          margin-left: 0;
        }
      }
    }
    .area-list {
      .choose-right {
        width: 25px;
        height: 18px;
        margin-right: 10px;
      }
      margin-left: 15px;
      .active {
        display: block;
      }
      .hide {
        display: none;
      }
      .area-list-item {
        margin-top: 40px;
        &:last-child {
          margin-bottom: 40px;
        }
      }
    }
  }
}
