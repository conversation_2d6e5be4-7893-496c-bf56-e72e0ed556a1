import { WebView } from '@tarojs/components';
import { useRouter } from '@tarojs/taro';
import { useEffect } from 'react';

export default () => {
  const router = useRouter();
  const name = router.params.name || 'registAgreement';
  const baseUrl =
    'https://www.xiaoxiongmeishu.com/h5/account/allAgreement?agreementName=' +
    name;

  const handleOnLoad = () => {
    Taro.hideLoading();
  };

  useEffect(() => {
    Taro.showLoading({ title: '加载中...' });
  }, []);

  return <WebView src={baseUrl} onLoad={handleOnLoad} />;
};
