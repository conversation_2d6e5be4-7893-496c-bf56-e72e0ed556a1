import { View, Image } from '@tarojs/components';
import retainIcon from '@/assets/conversion/tc_retain_icon.png';
import retaintips from '@/assets/conversion/tc_retain_tips.png';
import './index.scss';

export default function RetainModal({ close, show }) {
  return (
    <View className={`conver_retain_wrap ${show ? '' : 'hide'}`}>
      <View className='conver_retain_wrap_layout' onClick={close}></View>
      <View className='conver_retain_wrap_section'>
        <View className='section_icon'>
          <Image src={retainIcon} mode='widthFix' />
        </View>
        <View className='section_word'>放弃后将无法激活课程！</View>
        <View className='section_icon_bottom'>
          <Image src={retaintips} mode='widthFix' />
        </View>
        <View className='section_btn_wrap'>
          <View className='section_btn_left' onClick={close}>
            放弃权益
          </View>
          <View className='section_btn_right' onClick={close}>
            马上添加
          </View>
        </View>
      </View>
    </View>
  );
}
