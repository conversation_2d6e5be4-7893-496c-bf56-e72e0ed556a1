import { View, Image, Text } from '@tarojs/components';
import { useEffect, useRef, useState } from 'react';
import { getTeacherInfo } from '@/api/groupbuy';
import msbTrack from '@/utils/msbTrack';
import { useRouter } from '@tarojs/taro';
import { AtToast } from 'taro-ui';
import tctop from '@/assets/conversion/tc_top.png';
import tcqy from '@/assets/conversion/tc_qy.png';
import tcsm from '@/assets/conversion/tc_sm.png';
import tcqrtip from '@/assets/conversion/tc_qrcode_tips.png';
import tcteactip from '@/assets/conversion/tc_teacher_tips.png';
import tcteactipthree from '@/assets/conversion/tc_teacher_tips_v2.png';
import icon_desc from '@/assets/conversion/icon_desc.png';
import imgTeacher from '@/assets/conversion/img-teacher.png';
import originIcon from '@/assets/conversion/origin-icon.png';
import DefaultHead from '@/assets/conversion/default-head.jpg';
import PageContainerWrap from './pageContainerWrap';
import RetainModal from './retainModal';
import './index.scss';

const abtest = ['无介绍', '四个介绍', '三个介绍', '老师团队'];
export default () => {
  // const [qrcodeNo, setQrcodeNo] = useState('');
  const [teacherName, setteacherName] = useState('');
  const [pageCVisible, setPageCVisible] = useState(false);
  const router = useRouter();
  let params = router.params;
  const [retainVisible, setRetainVisible] = useState(true);
  const [isLongPressHandle, setIsLongPressHandle] = useState(false);
  const [isOpen, setIsOpen] = useState(true);
  const [teacherTX, setteacherTX] = useState('');
  const [randomNumber, setrandomNumber] = useState<any>(null);
  const qrcodeNo = useRef('');

  const getArtTeacher = () => {
    params.orderId &&
      getTeacherInfo({
        orderNo: params.orderId,
        addSource: params.addSource || '1',
      }).then(res => {
        if (res.code === 0) {
          // setQrcodeNo(res.payload.teacherWeChatQrCode);
          qrcodeNo.current = res.payload.teacherWeChatQrCode;
          setteacherName(res.payload.teacherName);
          setteacherTX(res.payload.teacherHead);
          if (res.payload.teacherWeChatQrCode) {
            setIsOpen(false);
          }
        }
      });
  };

  const onLongPresshandle = () => {
    if (params.from !== 'pictureBook') {
      msbTrack.track('xxms_testcourse_appletaddteacherpage_Longpresscode', {
        buy_model: 'model_3',
        diversion_subject: '美术',
        abtest: abtest[randomNumber],
      });

      setTimeout(() => {
        setIsLongPressHandle(true);
      }, 500);
    }
  };

  const onBeforeLeave = () => {
    setPageCVisible(true);
  };

  const initEl = () => (
    <View className={`conversion_teacher_wrap ${randomNumber == 3 && 'wh'}`}>
      <View className='header'>
        <View className='tc_suc'></View>
        <View className='tc_word'>此二维码已经通过安全验证，请放心扫码</View>
      </View>
      {randomNumber != 3 && (
        <View className='top-wrap'>
          <Image src={tctop} mode='widthFix' />
        </View>
      )}
      <View className='section'>
        <View className='header_pic'>
          <View className='header_box'>
            <Image src={teacherTX || DefaultHead} mode='widthFix' />
          </View>
        </View>
        <View className='section_box'>
          <View className='name'>{teacherName}</View>
          <View className='certification'>
            <View className='certification_l'>
              <Image src={tcqy} mode='widthFix' />
            </View>
            <View className='certification_r'>
              <Image src={tcsm} mode='widthFix' />
            </View>
          </View>
          {randomNumber == 2 && (
            <View>
              <View className='section_box_text s'>请务必添加二维码</View>
              <View className='section_box_text'>否则无法激活课程</View>
            </View>
          )}
          {params.isSimple == '0' && (
            <View className='isSimpleInfo'>
              <View className='orange'>
                <Image className='orange-img' src={originIcon}></Image>
                不同科目的辅导老师不同
              </View>
              <View className='text'>
                请您务必添加阅读绘本的辅导老师，以免影响正常学习哦！
              </View>
            </View>
          )}
          <View className='qrCode'>
            <Image
              src={qrcodeNo.current}
              mode='widthFix'
              showMenuByLongpress
              onLongPress={() => onLongPresshandle()}
            />
          </View>
          <View className='qrcode_tips'>
            <Image src={tcqrtip} mode='widthFix' />
          </View>
          {randomNumber == 0 && (
            <View className='teacher_tips'>
              <Image src={tcteactip} mode='widthFix' />
            </View>
          )}
          {randomNumber == 1 && (
            <View className='teacher_tips three'>
              <View className='tt'>
                <Text />
                添加老师 获取特权
                <Text />
              </View>
              <Image
                src={params.from == 'pictureBook' ? icon_desc : tcteactipthree}
                mode='widthFix'
              />
            </View>
          )}
          {/* <View className='footer_tips'>
            *若添加失败，截图保存二维码在微信中识别打开
          </View> */}
        </View>
      </View>
      {randomNumber == 3 && (
        <Image src={imgTeacher} mode='widthFix' className='bt-teacher' />
      )}
      <AtToast
        isOpened={isOpen}
        text='正在为您匹配老师， 请稍等片刻...'
        status='loading'
        icon='loading-2'
        duration={0}
      ></AtToast>
    </View>
  );

  const retainClose = () => {
    setRetainVisible(false);
  };

  useEffect(() => {
    if (params.from == 'pictureBook') setrandomNumber(1);
    else setrandomNumber(Math.random() * 4);
    for (let i = 1; i <= 6; i++) {
      setTimeout(() => {
        !qrcodeNo.current && getArtTeacher();
        if (i === 6 && isOpen) {
          setIsOpen(false);
        }
      }, 1500 * i);
    }
  }, []);

  return (
    <>
      {pageCVisible ? (
        <>
          {initEl()}
          <RetainModal
            show={retainVisible && !isLongPressHandle}
            close={retainClose}
          />
        </>
      ) : (
        <PageContainerWrap onBeforeLeave={onBeforeLeave}>
          {initEl()}
        </PageContainerWrap>
      )}
    </>
  );
};
