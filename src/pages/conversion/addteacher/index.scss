page {
  background-color: #ffc500;
}
.conversion_teacher_wrap {
  width: 100%;
  min-height: 100vh;
  padding-bottom: 128px;
  &.wh {
    background: #f7f7f7;
  }
  image {
    width: 100%;
    height: 100%;
  }
  .header {
    height: 72rpx;
    background: #efffe8;
    display: flex;
    align-items: center;
    justify-content: center;
    .tc_suc {
      width: 40px;
      height: 40px;
      background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAMAAAC7IEhfAAAAYFBMVEUAAABFsklEs0hFsklFsklFsklFsklFsklFs0lGs0lHsUdAtUpAv0BJtklEtUpFskn///9PtlNLtU/P69Da79vT7NPW7tfw+PC54ru+47+Q0ZJbu177/fvK6cyk2aZlv2gcCCkyAAAAD3RSTlMA+Zr06uLIx4lXJBgMBy2iyMvOAAAA6klEQVQ4y9XU2Q6DIBAF0IJUxW3QLm5d/v8vm3QSrwhI4pv3Sc2JA8NyOV8qLVMlhEqlrnZYIRNaksgiwOpckBWR1z5XZuQkK13XXMmTa+P8D86Wpe3qjALJ7HHmFExu9UWEoVh3SdJOJFyVkJtumjruPNZIe1x7N2bkR43Kfmf6be2UP3yHT7t2ZuCXdIGKB9Ub82xX7tYxVAvk5szGsITjiA2kN0s4Byrigo+/nOFQGpOBfMFhMmgPJJyzNJogXUfaXkJIuGUJURty7AfLkTywzSIb98hRiB+u+HE9fAHEr5T4JRW/9k6XH91pL7srwu0/AAAAAElFTkSuQmCC');
      background-position: 0 0;
      background-repeat: no-repeat;
      background-size: cover;
    }
    .tc_word {
      font-size: 30rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #45b249;
      margin-left: 8px;
    }
  }
  .top-wrap {
    width: 637px;
    height: 155px;
    margin: 43px auto 0;
  }
  .section {
    width: 670px;
    background: #ffffff;
    border-radius: 40rpx;
    margin: 98px auto 0;
    position: relative;
    .header_pic {
      width: 150px;
      height: 150px;
      border-radius: 50%;
      position: absolute;
      margin-left: -75px;
      top: -75px;
      left: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #ffffff;
      .header_box {
        width: 130px;
        height: 130px;
        border-radius: 50%;
        overflow: hidden;
        image {
          width: 130px;
          height: 130px;
        }
      }
    }
    .section_box {
      padding-top: 87px;
      padding-bottom: 20px;
      &_text {
        text-align: center;
        font-size: 56px;
        font-weight: 600;
        &.s {
          margin-top: 40px;
        }
      }
      .name {
        text-align: center;
        height: 40rpx;
        font-size: 28rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #666666;
        line-height: 40rpx;
      }
      .certification {
        margin-top: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        .certification_l {
          width: 110px;
          height: 34px;
        }
        .certification_r {
          width: 110px;
          height: 34px;
          margin-left: 10px;
        }
      }
      .qrCode {
        width: 260rpx;
        height: 260rpx;
        border-radius: 10rpx;
        margin: 40px auto 0;
        overflow: hidden;
        border: 1px solid #cccccc;
        image {
          width: 100%;
          height: 100%;
        }
      }
      .qrcode_tips {
        width: 417rpx;
        height: 55;
        margin: 36px auto 0;
      }
      .teacher_tips {
        width: 100%;
        margin-top: 50px;
        &.three {
          width: 520px;
          margin: 0 auto;
          .tt {
            font-size: 34px;
            display: flex;
            align-items: center;
            font-weight: 600;
            justify-content: center;
            margin: 50px 0 20px 0;
            Text {
              display: inline-block;
              width: 83px;
              height: 2px;
              background: linear-gradient(to right, #fff, #989898);
              &:nth-child(2) {
                background: linear-gradient(to left, #fff, #989898);
              }
              margin: 0px 16px;
            }
          }
        }
      }
      .footer_tips {
        height: 33rpx;
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        line-height: 33rpx;
        text-align: center;
      }
    }
  }
  .bt-teacher {
    width: 686px;
    height: 444px;
    display: block;
    margin: 40px auto;
  }
  .toast-body-content__img-item {
    width: 60px;
    height: 60px;
  }
  .at-toast__body--loading {
    background-color: rgba(0, 0, 0, 0.7);
    top: 48%;
  }
  .isSimpleInfo {
    text-align: center;
    font-size: 22px;
    margin-top: 22px;
    .orange {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fa6c3a;
      &-img {
        width: 24px;
        height: 24px;
      }
    }
    .text {
      color: #999999;
    }
  }
}
