import { useDidShow, useShareAppMessage, useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import { View, Image, Button } from '@tarojs/components';
import { useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import { getOrderId } from '@/common/order';
import bottomPay from '@/assets/zeroDotOne/img-footer.png';
import buy from '@/assets/zeroDotOne/img-buy.png';
import { useThirtySixPay } from '@/hooks/payhook/useThirtySixPay';
import sensors from '@/utils/sensors_data';
import { getSupManagements } from '@/api/groupbuy';

// @ts-ignore
import CommonTop from '@/components/commonTop';
import WxLogin from '@/components/wxlogin';
import Section from './components/section';
import './index.scss';

export default () => {
  const { params } = useRouter();
  const [urlType] = useState('zeroDotOne');
  const userId = useSelector((state: UserStateType) => state.userid);
  const channelId = useSelector((state: UserStateType) => state.channelId);
  const { authSuccess, authError, setPeriod, payConfirm } = useThirtySixPay({
    topicId: 4,
    packagesId: 617,
    isIntroduce: false,
    subject: 'ART_APP',
    pType: 'art',
    payPageData: null,
    urlType,
  });
  useEffect(() => {
    sensors.track('xxms_testcourse_homepage_view', {
      page_source: '0.1元体验包支付宝小程序页',
      channel_id: channelId,
    });
    sensors.track('ai_marketing_AlipayminiAPP_buypagebrowse', {
      channel_id: params.channelId,
      urlType: urlType,
    });
    getSupManagements({
      type: 'TESTCOURSE',
      sup: 'DEFAULT',
      subject: 'ART_APP',
    }).then(res => {
      const result = res.payload && res.payload;
      if (result) {
        setPeriod(result.period);
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useDidShow(() => {
    userId && getOrderId(userId, undefined, false, urlType);
  });

  const bottonBtnClick = () => {
    // 小程序购买页立即购买点击
    sensors.track('ai_marketing_AlipayminiAPP_buypageclick', {
      channel_id: params.channelId,
      urlType: urlType,
    });
    payConfirm();
  };
  const getauthSuccess = () => {
    sensors.track('ai_marketing_AlipayminiAPP_buypageclick', {
      channel_id: params.channelId,
      urlType: urlType,
    });
    authSuccess({ sup: 'DEFAULT' });
  };
  useShareAppMessage(() => {
    return {
      title: '小熊美术',
      path: `/pages/zeroDotOne/index${
        channelId ? `?channelId=${channelId}` : ''
      }`,
    };
  });

  return (
    <View className='index container w100 relative'>
      {process.env.TARO_ENV === 'weapp' && (
        /* 微信登录组件 */
        <WxLogin subject='ART_APP' isIntroduce={false} />
      )}
      {/* 头部导航栏 */}
      <CommonTop currentName='小熊美术' isIntroduce={false} />
      {/* 图片区 */}
      <Section />
      {/* 购买按钮 */}
      <View className='suction-bottom'>
        <Image mode='widthFix' src={bottomPay} className='pay-img' />
        {userId ? (
          <Button className='pay-btn' onClick={bottonBtnClick}>
            <Image mode='widthFix' src={buy} className='pay-fixed' />
          </Button>
        ) : (
          <Button
            plain
            className='pay-btn'
            openType='getAuthorize'
            scope='phoneNumber'
            onError={authError}
            onGetAuthorize={getauthSuccess}
          >
            <Image mode='widthFix' src={buy} className='pay-fixed' />
          </Button>
        )}
      </View>
    </View>
  );
};
