/**
 * 绘本季课
 */
import Taro from '@tarojs/taro';
import { useState } from 'react';
// @ts-ignore
import { View, Video, CoverView, CoverImage } from '@tarojs/components';
import pauseIcon from '@/assets/pictureBook/pauseIcon.png';
import playIcon from '@/assets/pictureBook/playIcon.png';
import posterImage from '@/assets/pictureBook/banner_cover.png';
import './index.scss';

export default function CalligraphyExperience() {
  const detailsVideoContext = Taro.createVideoContext('detialsId');
  const bannerVideoContext = Taro.createVideoContext('videoId');
  const [detailsVideoPlay, setdetailsVideoPlay] = useState(false);

  const videoClick = action => {
    detailsVideoContext.pause();
    if (action === 'paly') {
      bannerVideoContext.play();
    }
    if (action === 'pause') {
      bannerVideoContext.pause();
    }
  };
  return (
    <View className='bannerVideoBox'>
      <Video
        id='videoId'
        className='bannerVideo'
        src='http://s2.xiaoxiongmeishu.com/courseware/mp4/new.mp4'
        onPause={() => setdetailsVideoPlay(false)}
        onPlay={() => setdetailsVideoPlay(true)}
        poster={posterImage}
        showCenterPlayBtn={false}
        objectFit='fill'
        controls={false}
        direction={0}
      >
        <CoverView className='videoBtn'>
          {detailsVideoPlay ? (
            <CoverImage
              src={pauseIcon}
              className='play_icon'
              onClick={() => {
                videoClick('pause');
              }}
            />
          ) : (
            <CoverImage
              src={playIcon}
              className='play_icon'
              onClick={() => {
                videoClick('paly');
              }}
            />
          )}
        </CoverView>
      </Video>
    </View>
  );
}
