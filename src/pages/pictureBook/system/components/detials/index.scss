.detailsBox {
  width: 100%;
  font-size: 0;
  padding-bottom: 150px;

  .detailImg {
    width: 100%;
  }
  .videoImg {
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    position: relative;
    width: 100%;
    height: 1116px;

    .detailsVideoBox {
      position: absolute;
      top: 148px;
      width: 630px;
      left: 60px;
      height: 364px;
      &.halfDetailsVideoBox {
        top: 124px;
      }
      .detailsVideoCss {
        width: 100%;
        height: 100%;
        border-radius: 16px;
      }

      .play_icon {
        width: 104px;
        height: 104px;
        position: absolute;
        border-radius: 50%;
        top: 50%;
        left: 50%;
        transform: translate(-52px, -52px);
      }
    }
  }
}
