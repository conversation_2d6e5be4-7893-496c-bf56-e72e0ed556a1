/**
 * 绘本季课
 */
import Taro from '@tarojs/taro';
import { useState } from 'react';
// @ts-ignore
import { View, Image, Video, CoverView, CoverImage } from '@tarojs/components';
import pauseIcon from '@/assets/pictureBook/pauseIcon.png';
import playIcon from '@/assets/pictureBook/playIcon.png';
import videoImgBg from '@/assets/pictureBook/halfYear/halfYear_detial10.png';
import './index.scss';

export default function CalligraphyExperience() {
  const detailsVideoContext = Taro.createVideoContext('detialsId');
  const bannerVideoContext = Taro.createVideoContext('videoId');
  const [bannerVideoPlay, setbannerVideoPlay] = useState(false);

  const [detailsTopImgList] = useState([
    require('@/assets/pictureBook/halfYear/halfYear_detial1.png'),
    require('@/assets/pictureBook/halfYear/halfYear_detial2.png'),
    require('@/assets/pictureBook/halfYear/halfYear_detial3.png'),
    require('@/assets/pictureBook/halfYear/halfYear_detial4.png'),
    require('@/assets/pictureBook/halfYear/halfYear_detial5.png'),
    require('@/assets/pictureBook/halfYear/halfYear_detial6.png'),
    require('@/assets/pictureBook/halfYear/halfYear_detial7.png'),
    require('@/assets/pictureBook/halfYear/halfYear_detial8.png'),
    require('@/assets/pictureBook/halfYear/halfYear_detial9.png'),
  ]);
  const [detailsBottomImgList] = useState([
    require('@/assets/pictureBook/halfYear/halfYear_detial11.png'),
    require('@/assets/pictureBook/halfYear/halfYear_detial12.png'),
    require('@/assets/pictureBook/halfYear/halfYear_detial13.png'),
  ]);

  const videoClick = action => {
    bannerVideoContext.pause();
    if (action === 'paly') {
      detailsVideoContext.play();
    }
    if (action === 'pause') {
      detailsVideoContext.pause();
    }
  };
  return (
    <View className='detailsBox'>
      {detailsTopImgList.map((TopImgItem, TopImgIndex) => {
        return (
          <Image
            key={'TopImgItem' + TopImgIndex}
            className='detailImg'
            mode='widthFix'
            src={TopImgItem}
          ></Image>
        );
      })}
      <View
        className='videoImg'
        style={{ backgroundImage: 'url(' + videoImgBg + ')' }}
      >
        <View className='detailsVideoBox'>
          <Video
            id='detialsId'
            className='detailsVideoCss'
            src='https://s2.xiaoxiongmeishu.com/courseware/mp4/2.mp4'
            onPause={() => setbannerVideoPlay(false)}
            onPlay={() => setbannerVideoPlay(true)}
            showCenterPlayBtn={false}
            objectFit='fill'
            controls={false}
            direction={0}
          >
            <CoverView className='videoBtn'>
              {bannerVideoPlay ? (
                <CoverImage
                  src={pauseIcon}
                  className='play_icon'
                  onClick={() => {
                    videoClick('pause');
                  }}
                />
              ) : (
                <CoverImage
                  src={playIcon}
                  className='play_icon'
                  onClick={() => {
                    videoClick('paly');
                  }}
                />
              )}
            </CoverView>
          </Video>
        </View>
      </View>
      {detailsBottomImgList.map((BottomImgItem, BottomImgIndex) => {
        return (
          <Image
            key={'BottomImgItem' + BottomImgIndex}
            src={BottomImgItem}
            className='detailImg'
            mode='widthFix'
          ></Image>
        );
      })}
    </View>
  );
}
