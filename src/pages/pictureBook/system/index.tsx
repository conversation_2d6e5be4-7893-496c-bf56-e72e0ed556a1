/**
 * 绘本季课
 */
import Taro, {
  useShareAppMessage,
  useRouter,
  setStorageSync,
} from '@tarojs/taro';
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import banner from '@/assets/pictureBook/share-img.png';
import WxLogin from '@/components/wxlogin';
import { View, Button } from '@tarojs/components';
import { UserStateType } from '@/store/groupbuy/state';
import {
  queryPackagesIdBySubject,
  getPackages,
  getProgramUserSubject,
} from '@/api/groupbuy';

import Banner from './components/banner';
import Detials from './components/detials';

import './index.scss';

export default function CalligraphyExperience() {
  const params = useRouter().params;
  const dispatch = useDispatch();
  const [canbuy, setcanbuy] = useState(true);
  const [selectedSup] = useState('S1');
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  const channelId = useSelector((state: UserStateType) => state.channelId);
  const openid = useSelector((store: UserStateType) => store.openid);
  const [packageInfo, setpackageInfo] = useState<any>({
    name: '',
    price: '',
    coursePage: '',
  });
  useEffect(() => {
    if (!openid) return;
    getCourseInfo();
    params.channelId &&
      dispatch({
        type: 'CHANGE_CHANNELID',
        channelId: params.channelId,
      });
    params.sendId &&
      dispatch({
        type: 'CHANGE_SENDID',
        sendId: params.sendId,
      });
  }, [params, userId, openid]);

  const getCourseInfo = async () => {
    let coursePage = params.coursePage || 'QUARTER';
    // 获取用户应该购买的年课
    // type 值 HALFYEAR, YEAR, QUARTER, MONTHLY;
    const packageMap =
      process.env.NODE_ENV === 'online'
        ? {
            YEAR: 1604,
            HALFYEAR: 1776,
            QUARTER: 1602,
            MONTHLY: 1385,
          }
        : {
            YEAR: 7673,
            HALFYEAR: 7708,
            QUARTER: 1602,
            MONTHLY: 7668,
          };

    // 访问链接不对 显示年系统课
    let info = {
      isFirstOrder: true,
      isRenew: false,
      isMakeup: false,
      packagesId: packageMap[coursePage],
      coursePage: '',
      courseType: '',
      type: '',
    };
    if (userId) {
      const SubscribeRes = await queryPackagesIdBySubject({
        userId: userId,
        openId: openid,
        debitType: coursePage,
        type: coursePage,
        subject: 'PICTURE_BOOK',
      });
      // 不可以购买则接口异常
      if (SubscribeRes.code === 0) {
        info = {
          ...info,
          ...SubscribeRes.payload,
        };
      }
    }
    const COURSETYPE = {
      YEAR: '年系统版',
      HALFYEAR: '半年系统版',
      QUARTER: '季系统版',
      MONTHLY: '月系统版',
    };
    const res = await getPackages({ packagesId: info.packagesId });
    info.coursePage = COURSETYPE[info.type] || '季系统版';
    info.courseType = coursePage;
    setpackageInfo({
      ...info,
      ...res.payload,
    });
  };
  useShareAppMessage(() => {
    let _channelId = '15821';
    if (params.channelId) _channelId = params.channelId;
    return {
      title: '',
      path: `/pages/pictureBook/system/index?channelId=${_channelId}&sendId=${userId}`,
      imageUrl: banner,
    };
  });
  const onsubmit = () => {
    if (userId) {
      if (!canbuy) {
        Taro.showToast({
          title: '您已购买过课程，请去小熊艺术App上课。',
          icon: 'none',
        });
        return;
      }
      let queryParams = {
        sup: selectedSup,
        packagesId: packageInfo.packagesId,
        staffId: params.staffId,
        LOB: params.LOB,
      };
      // this.bearSensors('ai_xxyy_producting_HBlandingpage_Register_now', {
      //   channel_id: this.$route.query.channelId || '',
      //   course_term: `第${this.issue}期`,
      //   course_sup: selectedSup
      // })
      let href = `pages/pay/index?channelId=${channelId}`;
      for (const key in queryParams) {
        if (Object.prototype.hasOwnProperty.call(queryParams, key)) {
          const element = queryParams[key];
          if (element) {
            href += `&${key}=${element}`;
          }
        }
      }
      Taro.navigateTo({
        url: `/${href}`,
      });
    } else {
      console.error('登录');
    }
  };
  // 手机号授权
  const getUserPhoneNumber = res => {
    if (res.detail.errMsg === 'getPhoneNumber:fail user deny') {
      console.log(res.detail.errMsg);
      Taro.showToast({
        title: res.detail.errMsg || '获取手机号失败!',
        icon: 'none',
      });
    } else {
      const { encryptedData, iv } = res.detail;
      getProgramUserSubject({
        openId: openid,
        encryptedData,
        iv,
        subject: 'PICTURE_BOOK',
      }).then(phone => {
        if (phone.payload.token)
          Taro.setStorageSync('appToken', phone.payload.token);
        phone.payload.uid &&
          Taro.setStorageSync('__msb_user_id__', phone.payload.uid);
        phone.payload.uid &&
          dispatch({
            type: 'CHANGE_USERID',
            userid: phone.payload.uid,
          });
        phone.payload.mobile &&
          dispatch({
            type: 'CHANGE_MOBILE',
            mobile: phone.payload.mobile,
          });
        onsubmit();
      });
    }
  };
  return (
    <View className='picturesystemPage'>
      <Banner />
      <View className='centerContent'>
        <View className='commodity-msg'>
          {/* 课程标题 */}
          <View className='titlePeriods'>
            <View className='headline'>{packageInfo.name}</View>
            {/* <View className='headlineDesc'>
              到期之后{packageInfo.price}元/季自动续费，可随时关闭
            </View> */}
          </View>
          {/* 价格 */}
          <View className='Renewals'>
            <View className='symbol'>¥</View>
            <View className='money'>{packageInfo.price}</View>
            {packageInfo.coursePage ? (
              <View className='block'>{packageInfo.coursePage}</View>
            ) : null}
          </View>
        </View>
      </View>
      <Detials />
      <View className='flexBottom'>
        <View className='flexBottomdefaultContent'>
          {userId ? (
            <View className='btn' onClick={onsubmit}>
              立即购买
            </View>
          ) : (
            <Button
              className='btn'
              openType='getPhoneNumber'
              onGetPhoneNumber={getUserPhoneNumber}
            >
              立即购买
            </Button>
          )}
        </View>
        <WxLogin noAskNoaddress subject='PICTURE_BOOK' isIntroduce />
      </View>
    </View>
  );
}
