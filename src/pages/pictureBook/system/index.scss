// （头部一下，课程大纲以上内容）
page {
  background: #fff;
}
.picturesystemPage {
  padding-bottom: 86px;
  background-color: #ffc179;
}
.centerContent {
  background: #fff;
  .commodity-msg {
    box-sizing: border-box;
    padding: 24px 0 24px 32px;

    .titlePeriods {
      .lobe {
        width: 8px;
        height: 8px;
        margin-top: 8px;
        background-color: #ff9c00;

        .impression {
          width: 8px;
          height: 8px;
          background-color: #fff;
          border-radius: 0 10px 0 0;
        }
      }
      .headline {
        font-size: 38px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333333;
      }
      .headlineDesc {
        font-size: 28px;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #878787;
      }
    }

    .character {
      font-size: 28px;
      color: #878787;
      font-family: PingFangSC-Regular, PingFang SC;
    }

    .price {
      margin-top: 32px;
      margin-left: 8px;

      &:after {
        content: '';
        clear: left;
        display: block;
      }

      .fl {
        float: left;
      }
    }

    .Renewals {
      margin-top: 12px;
      font-weight: 600;
      display: flex;
      align-items: baseline;
      color: #fa6c3a;
      font-family: PingFangSC-Semibold, PingFang SC;
      .symbol {
        font-size: 24px;
      }
      .money {
        font-size: 44px;
      }
      .block {
        height: 36px;
        line-height: 36px;
        padding: 0 12px;
        text-align: center;
        background: rgba(250, 108, 58, 0.16);
        border-radius: 7px;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #fa6c3a;
        margin-left: 16px;
        align-self: center;
      }
    }
  }
}

.flexBottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  box-sizing: border-box;
  .flexBottomdefaultContent {
    background: #ffffff;
    padding: 32px 30px 0 30px;
    padding-bottom: calc(30px + constant(safe-area-inset-bottom));
    padding-bottom: calc(30px + env(safe-area-inset-bottom));
    display: flex;
    .btn {
      flex: 1;
      height: 86px;
      line-height: 86px;
      text-align: center;
      background: linear-gradient(90deg, #ff9b00 0%, #fe6c01 100%);
      border-radius: 43px;
      font-size: 34px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #ffffff;
      animation: scaleDrew 1.8s ease-in-out infinite;
      margin: 0;
    }
    @keyframes scaleDrew {
      /* 定义关键帧、scaleDrew是需要绑定到选择器的关键帧名称 */
      0% {
        transform: scale(1);
      }

      25% {
        transform: scale(1.05);
      }

      50% {
        transform: scale(1);
      }

      75% {
        transform: scale(1.05);
      }
    }
  }
}
