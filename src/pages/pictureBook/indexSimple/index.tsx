/**
 * 小熊书法，新增0元小程序页
 */
import Taro, { useShareAppMessage, useRouter } from '@tarojs/taro';
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import banner from '@/assets/pictureBook/share-img.png';
// @ts-ignore
import { View, Image, PageContainer } from '@tarojs/components';
import WxLogin from '@/components/wxlogin';
import { UserStateType } from '@/store/groupbuy/state';
import sensors from '@/utils/sensors_data';
import { getBuyOrderByUserId } from '@/api/groupbuy';
import { AtModal } from 'taro-ui';
import gif from '@/assets/pictureBookSimple/schedule-btn.gif';
import closeImg from '@/assets/pictureBookSimple/icon_close.png';
import Modal from '../components/payModal';
import PushModal from '../components/pushModal';
import './index.scss';

const artRequireContext = require.context(
  '@/assets/pictureBookSimple/',
  true,
  /^\.\/.*pictureBookSimple-class-img\d|dd\.png$/,
);

const imgList = artRequireContext.keys().map(artRequireContext);

export default function CalligraphyExperience() {
  const params = useRouter().params;
  const dispatch = useDispatch();
  const [buyType, setbuyType] = useState(false);
  const [mpdalShow, setmpdalShow] = useState(false);
  const [touchStart, settouchStart] = useState(0);
  const [isTouched, setisTouched] = useState(false);
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  useEffect(() => {
    params.channelId &&
      dispatch({
        type: 'CHANGE_CHANNELID',
        channelId: params.channelId,
      });
    params.sendId &&
      dispatch({
        type: 'CHANGE_SENDID',
        sendId: params.sendId,
      });
    userId && getUserType();
  }, [params, userId]);

  useEffect(() => {
    sensors.track('ai_xxyy_producting_Picturebookmarketing_browse', {
      userId: userId,
      entrance_page: params.entrance_page || '',
      channelId: params.channelId || '',
    });
  }, []);

  useShareAppMessage(() => {
    sensors.track(
      'ai_xxyy_producting_HB_TRIAL_COURES_miniprogramshareclick',
      {},
    );
    let _channelId = '15821';
    if (params.channelId) _channelId = params.channelId;
    return {
      title: '4天培养阅读习惯，提升表达认知，今日特价仅0.1元',
      path: `/pages/pictureBook/indexSimple/index?channelId=${_channelId}&sendId=${userId}`,
      imageUrl: banner,
    };
  });

  const getUserType = () => {
    getBuyOrderByUserId({
      userId,
      orderRegType: 'FIRST',
      subjects: 'PICTURE_BOOK',
    }).then(res => {
      let { payload = [] } = res;
      if (payload.length > 0) {
        setbuyType(true);
      } else {
        getBuyOrderByUserId({
          userId,
          orderRegType: 'EXPERIENCE',
          subjects: 'PICTURE_BOOK',
          includeRefund: true,
        }).then(res1 => {
          if (res1.payload.length > 0) setbuyType(true);
        });
      }
    });
  };

  const touchStartHandler = e => {
    let touchDot = e.touches[0].pageX;
    settouchStart(touchDot);
  };
  const touchMoveHandler = e => {
    // let touchDot = e.touches[0].pageX;
    if (isTouched) return;
    // if (touchDot - touchStart > 10) {
    setmpdalShow(true);
    setisTouched(true);
    sensors.track(
      'ai_xxyy_producting_Picturebookmarketing_Retentionbrowse',
      {},
    );
    // }
  };

  return (
    <>
      <View
        className='newCalligraphyExperience'
        onTouchStart={touchStartHandler}
        onTouchMove={touchMoveHandler}
      >
        {imgList.map((e: any, i) => {
          return (
            <View key={i}>
              <Image src={e} className='banner' mode='widthFix'></Image>
            </View>
          );
        })}
        <View className='none'></View>
        <View className='bottomPay new'>
          <Modal
            price='0.1'
            packagesId={process.env.NODE_ENV === 'online' ? '1582' : '7670'}
            buyType={buyType}
            isSimple
          />
        </View>
        <WxLogin subject='ART_APP' isIntroduce={false} />
        <AtModal
          isOpened={mpdalShow}
          closeOnClickOverlay={false}
          className='drainage-address-status-wrap'
        >
          <View className='schedule-gift' onClick={() => setmpdalShow(false)}>
            <Modal
              price='0.1'
              packagesId={process.env.NODE_ENV === 'online' ? '1582' : '7670'}
              className='submit'
              Img={gif}
              buyType={buyType}
              isSimple
              israimed
            />
            <Image
              src={closeImg}
              className='close'
              onClick={() => {
                sensors.track(
                  'ai_xxyy_producting_Picturebookmarketing_closeclick',
                  {},
                );
                setmpdalShow(false);
              }}
            />
          </View>
        </AtModal>
      </View>
      <PageContainer
        className='newCalligraphyExperience abs'
        onTouchStart={touchStartHandler}
        onTouchMove={touchMoveHandler}
        show
        duration={0}
        onLeave={touchMoveHandler}
      >
        {imgList.map((e: any, i) => {
          return (
            <View key={i}>
              <Image src={e} className='banner' mode='widthFix'></Image>
            </View>
          );
        })}
        <View className='none'></View>
        <View className='bottomPay new'>
          <Modal
            price='0.1'
            packagesId={process.env.NODE_ENV === 'online' ? '1582' : '7670'}
            buyType={buyType}
            isSimple
          />
        </View>
        <WxLogin subject='ART_APP' isIntroduce={false} />
      </PageContainer>
      <PushModal isSimple buyType={buyType} />
    </>
  );
}
