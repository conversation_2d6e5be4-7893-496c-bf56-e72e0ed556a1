.calligraphyExperience {
  width: 100%;
  height: 100%;
  background: #76cd58;
  padding-bottom: 50px;
  .banner {
    width: 100%;
  }
  .payButton {
    position: fixed;
    bottom: 0;
    z-index: 10;
    width: 100%;
  }
  .none {
    width: 100%;
    height: 120px;
  }
}

.newCalligraphyExperience {
  width: 100%;
  height: 100%;
  font-size: 0;
  padding-bottom: 60px;
  background-color: #aae6b3;
  .banner {
    width: 100%;
  }
  .bannerTop {
    width: 100%;
    margin-top: -40px;
  }
  .none {
    width: 100%;
    height: 120px;
  }
  .bottomPay {
    width: 100%;
    height: 120px;
    padding-bottom: calc(env(safe-area-inset-bottom));
    background: #ffffff;
    box-shadow: 0px 0px 13px 0px rgba(136, 136, 136, 0.2);
    position: fixed;
    bottom: 0;
    z-index: 2;
    &.new {
      background: transparent;
      position: absolute;
      box-shadow: none;
      bottom: 0;
      .modaPay1 {
        padding-top: 50px;
      }
      overflow: visible;
    }
    .button {
      width: 90%;
      height: 88px;
      background: linear-gradient(180deg, #ffb51a 0%, #ff7e0b 100%) !important;
      border-radius: 44px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #fff;
      font-size: 38px;
      font-family: PingFangSC-Medium, PingFang SC;
      margin: 40px auto;
      animation: scaleJumpbtn 0.9s ease-in-out infinite;
    }
    .button-img {
      width: 100%;
      height: 117px;
      margin: 0px auto;
      margin-top: 20px;
      margin-bottom: 40px;
      animation: scaleJumpbtn 0.9s ease-in-out infinite;
    }
  }
  @keyframes scaleJumpbtn {
    0% {
      transform: scale(0.95);
    }
    50% {
      transform: scale(1.02);
    }
    100% {
      transform: scale(0.95);
    }
  }
  .bottomPayNew {
    width: 100%;
    background: #ffffff;
    position: fixed;
    bottom: 0;
    z-index: 2;

    .payButton {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    image {
      display: block;
      width: 584x;
      height: 82px;
      transform: scale(0.8);
    }
  }
  .giveMask {
    .at-modal__container {
      width: 750px;
      height: 100%;
      background-color: transparent;
    }
    .giveMaskImg {
      width: 750px;
      height: 1334px;
      //   margin-top: -100px;
    }
  }
}

.at-modal__container {
  width: 600px;
  height: 800px;
  background-color: transparent;
  overflow: visible;
  top: 45%;
  .schedule-gift {
    width: 600px;
    height: 800px;
    position: relative;
    background: no-repeat url(~@/assets/pictureBookSimple/detention-bg.png);
    background-size: cover;
    text-align: center;
    overflow: visible;
    padding-top: 664px;
    box-sizing: border-box;
    .submit {
      margin: 0 auto 0;
      width: 400px;
      height: 96px;
      .modaPay1 .modaPayButton image {
        width: 400px;
        height: 96px;
      }
    }
    .close {
      position: absolute;
      width: 64px;
      height: 64px;
      bottom: -86px;
      left: 0;
      right: 0;
      margin: 0 auto;
    }
  }
}
