import Taro from '@tarojs/taro';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import sensors from '@/utils/sensors_data';
import { View, Text, Image } from '@tarojs/components';
import { useThirtySixPay } from '@/hooks/payhook/useThirtySixPay';
import { getManagementByTypeAndCategory } from '@/api/groupbuy';
import payIcon from '@/assets/normalGroup/art/pay-ismg.png';
import experienceSimpleBtn from '@/assets/normalGroup/art/experienceSimpleBtn.gif';
import Paybtn from '../../thirtySix/components/payBtn/index.weapp';

import './payModal.scss';

export default function Modal(props: any) {
  const {
    isSimple = false,
    israimed = false,
    closeHandler = null,
    notShowImg = false,
  } = props;
  const [payPageData, setPayPageData] = useState<object | null>({
    sup: 'S1',
    courseday: '0',
    period: 0,
    addTeacher: true,
  });
  const [timeStr, setTimeStr] = useState('');
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  //channelId
  const channelId =
    useSelector((state: UserStateType) => state.channelId) || '15822';

  const payMessage = useThirtySixPay({
    topicId: '3',
    packagesId: props.packagesId,
    isIntroduce: false,
    ispictureBook: true,
    isSimple,
    subject: 'ART_APP',
    payPageData,
  });
  const authSuccess = res => {
    israimed &&
      sensors.track(
        'ai_xxyy_producting_Picturebookmarketing_Retentionclick',
        {},
      );
    sensors.track(
      isSimple
        ? 'ai_xxyy_producting_Picturebookmarketing_payclick'
        : 'ai_xxyy_producting_HB_TRIAL_COURES_miniprogramclick',
      {
        userId: userId,
      },
    );
    res.sup = 'S1';
    payMessage.authSuccess(res);
    if (closeHandler != null) closeHandler();
  };

  useEffect(() => {
    getManagementByTypeAndCategory(channelId).then(res => {
      const { payload, status } = res;
      if (status == 'OK') {
        let _payPageData = JSON.parse(JSON.stringify(payPageData));
        _payPageData.period = payload.period;
        setPayPageData(_payPageData);
        const date = new Date(payload.courseDay / 1);
        let _str = `${date.getMonth() + 1}月${date.getDate()}开课`;
        setTimeStr(_str);
      }
    });
  }, []);

  const authError = () => {
    israimed &&
      sensors.track(
        'ai_xxyy_producting_Picturebookmarketing_Retentionclick',
        {},
      );
    Taro.showToast({
      title: `支付失败`,
      icon: 'none',
    });
    if (closeHandler != null) closeHandler();
  };

  return (
    <View className={'modals ' + props.className}>
      {!isSimple ? (
        <View className='modaPay1'>
          <View className='modaPayText'>
            <View className='modaPayBigWord'>￥</View>
            <View className='modaPayBigText'>{props.price}</View>
            <View className='modaPayBigTime'>
              /<Text>4</Text>日体验包
            </View>
          </View>
          <View>
            <View className='rel'>
              <Image src={payIcon} className='pay-icon' />
              {props.buyType ? (
                <View
                  className='modaPayButton'
                  onClick={() => {
                    Taro.showToast({
                      title: '您已购买过课程，请去小熊美术App上课。',
                      icon: 'none',
                    });
                  }}
                >
                  <Image src={payIcon} className='pay-icon' />
                  立即报名
                </View>
              ) : (
                <Paybtn
                  className='modaPayButton'
                  authError={authError}
                  authSuccess={authSuccess}
                  btnName='立即报名'
                ></Paybtn>
              )}
            </View>
          </View>
        </View>
      ) : (
        <View className='modaPay1'>
          <Paybtn
            className='modaPayButton new'
            authError={authError}
            authSuccess={authSuccess}
            // eslint-disable-next-line react/no-children-prop
            children={
              notShowImg ? (
                <></>
              ) : (
                <Image
                  src={props.Img ? props.Img : experienceSimpleBtn}
                ></Image>
              )
            }
          ></Paybtn>
        </View>
      )}
    </View>
  );
}
