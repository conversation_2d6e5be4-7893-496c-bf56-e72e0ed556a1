.pushModal-wrap {
  .blackbg {
    .at-modal__container {
      overflow: visible;
      background: transparent;
      width: 600px;
      top: 40%;
    }
    .bannerImg {
      width: 600px;
    }
    .count-down-p {
      position: absolute;
      color: #fff;
      font-size: 28px;
      top: 640px;
      left: 0;
      right: 0;
      margin: 0 auto;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      .count-down {
        color: #fff;
        font-size: 24px;
        line-height: 40px;
        margin: 0 10px;
      }
    }
    .close-icon {
      position: absolute;
      bottom: -80px;
      width: 60px;
      height: 60px;
      left: 0;
      right: 0;
      margin: 0 auto;
    }
  }
}
.timer {
  margin: 0 10px;
  padding: 0;
  .time-num,
  .colon {
    color: #fff !important;
    font-size: 28px !important;
  }
}
.push-parent .modal {
  width: 600px;
  height: 800px;
  position: absolute;
  top: 0;
  .modaPay1 {
    .modaPayButton {
      height: 800px;
      opacity: 0;
    }
  }
}
