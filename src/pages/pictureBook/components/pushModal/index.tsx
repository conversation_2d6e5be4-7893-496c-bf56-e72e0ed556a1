import { View, Image } from '@tarojs/components';
import { useEffect, useState } from 'react';
import { AtModal, AtModalAction, AtModalContent, AtModalHeader } from 'taro-ui';
import pushModal from '@/assets/normalGroup/art/pushModal.gif';
import modalCloseImg from '@/assets/groupbuy/thirtySix/close.png';
import CountDown from '@/components/groupbuy/countdown/index';
import sensors from '@/utils/sensors_data';
import Modal from '../payModal';
import './index.scss';

export default props => {
  const { isSimple = false, buyType } = props;
  const [showModalType, setshowModalType] = useState(true);
  const closeShare = type => {
    sensors.track(
      type
        ? 'ai_xxyy_producting_HB_TRIAL_COURES_ClaimNowcloseclick'
        : 'ai_xxyy_producting_HB_TRIAL_COURES_ClaimNowclosecloseclick',
      {
        open_source: isSimple ? '营销绘本' : '正常绘本',
        game_channel: '小程序',
      },
    );
    setshowModalType(false);
  };

  useEffect(() => {
    setTimeout(() => {
      setshowModalType(false);
    }, 15000);
  }, []);

  return (
    <View className='pushModal-wrap'>
      <AtModal
        isOpened={showModalType}
        closeOnClickOverlay={false}
        className='blackbg'
        onClose={() => closeShare(false)}
      >
        <Image
          className='bannerImg'
          src={pushModal}
          mode='widthFix'
          onClick={() => closeShare(true)}
        />
        <View className='push-parent'>
          <Modal
            price='0.1'
            packagesId={process.env.NODE_ENV === 'online' ? '1582' : '7670'}
            buyType={buyType}
            closeHandler={() => {
              closeShare(true);
            }}
            Img={null}
            notShowImg
            isSimple
          />
        </View>
        <View
          className='count-down-p'
          onClick={() => {
            closeShare(true);
          }}
        >
          倒计时
          <CountDown payCountDown={1000 * 15} isShowMin={false} />
          结束
        </View>
        <Image
          src={modalCloseImg}
          className='close-icon'
          onClick={() => closeShare(false)}
        />
      </AtModal>
    </View>
  );
};
