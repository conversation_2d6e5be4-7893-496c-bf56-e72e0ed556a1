/**
 * 小熊书法，新增0元小程序页
 */
import Taro, { useShareAppMessage, useRouter } from '@tarojs/taro';
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import banner from '@/assets/pictureBook/share-img.png';
import { View, Image } from '@tarojs/components';
import WxLogin from '@/components/wxlogin';
import { UserStateType } from '@/store/groupbuy/state';
import sensors from '@/utils/sensors_data';
import { getBuyOrderByUserId } from '@/api/groupbuy';
import Modal from '../components/payModal';
import PushModal from '../components/pushModal';
import './index.scss';

const artRequireContext = require.context(
  '@/assets/pictureBook/',
  true,
  /^\.\/.*pictureBook-class-img\d|dd\.png$/,
);

const imgList = artRequireContext.keys().map(artRequireContext);

const artRequireContextNew = require.context(
  '@/assets/pictureBook/',
  true,
  /^\.\/.*pictureBookExperience1_detial-new-\d|dd\.png$/,
);

const imgListNew = artRequireContextNew.keys().map(artRequireContextNew);

export default function CalligraphyExperience() {
  const params = useRouter().params;
  const dispatch = useDispatch();
  const [buyType, setbuyType] = useState(false);
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  const isOver = new Date().getTime() > 1696348800000;
  useEffect(() => {
    params.channelId &&
      dispatch({
        type: 'CHANGE_CHANNELID',
        channelId: params.channelId,
      });
    params.sendId &&
      dispatch({
        type: 'CHANGE_SENDID',
        sendId: params.sendId,
      });
    userId && getUserType();
  }, [params, userId]);

  useEffect(() => {
    sensors.track('ai_xxyy_producting_HB_TRIAL_COURES_miniprogrambrowse', {
      userId: userId,
      entrance_page: params.entrance_page || '',
      channelId: params.channelId || '',
    });
  }, []);

  useShareAppMessage(() => {
    sensors.track(
      'ai_xxyy_producting_HB_TRIAL_COURES_miniprogramshareclick',
      {},
    );
    return {
      title: '4天培养阅读习惯，提升表达认知，今日特价仅0.1元',
      path: `/pages/pictureBook/index/index?channelId=15821&sendId=${userId}`,
      imageUrl: banner,
    };
  });

  const getUserType = () => {
    getBuyOrderByUserId({
      userId,
      orderRegType: 'FIRST',
      subjects: 'PICTURE_BOOK',
    }).then(res => {
      let { payload = [] } = res;
      if (payload.length > 0) {
        setbuyType(true);
      } else {
        getBuyOrderByUserId({
          userId,
          orderRegType: 'EXPERIENCE',
          subjects: 'PICTURE_BOOK',
          includeRefund: true,
        }).then(res1 => {
          if (res1.payload.length > 0) setbuyType(true);
        });
      }
    });
  };

  return (
    <View className='newCalligraphyExperience'>
      {(isOver ? imgListNew : imgList).map((e: any, i) => {
        return (
          <View key={i}>
            <Image src={e} className='banner' mode='widthFix'></Image>
          </View>
        );
      })}
      <View className='none'></View>
      <View className='bottomPay'>
        <Modal
          price='0.1'
          packagesId={process.env.NODE_ENV === 'online' ? '1582' : '7670'}
          buyType={buyType}
        />
      </View>
      <WxLogin subject='ART_APP' isIntroduce={false} />
      {/* <PushModal buyType={buyType} /> */}
    </View>
  );
}
