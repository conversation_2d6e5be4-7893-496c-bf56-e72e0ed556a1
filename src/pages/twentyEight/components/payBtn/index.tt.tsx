import { Button } from '@tarojs/components';

interface Paybtnprops {
  className?: string;
  authError?: (res: any) => void;
  authSuccess: (res: any) => void;
  btnName?: string;
  children?: any;
}

const Paybtn = ({
  className = 'button',
  authError,
  authSuccess,
  btnName,
  children,
}: Paybtnprops) => {
  // 获取用户手机号
  const getPhoneNumber = res => {
    if (res.detail.errMsg === 'getPhoneNumber:fail user deny') {
      authError && authError(res);
    } else {
      authSuccess(res);
    }
  };

  return (
    <Button
      className={className}
      open-type='getPhoneNumber'
      onGetPhoneNumber={getPhoneNumber}
    >
      {btnName || children || '确认支付'}
    </Button>
  );
};

export default Paybtn;
