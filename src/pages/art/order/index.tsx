import { Image, Text, View } from '@tarojs/components';
import sensors from '@/utils/sensors_data';
import { queryOrderByUserId } from '@/api/groupbuy';
import materialImg from '@/assets/art/twentynine/material-01.png';
import chooseIcon01 from '@/assets/art/twentynine/choose-icon-01.png';
import chooseIcon02 from '@/assets/art/twentynine/choose-icon-02.png';
import musicAd from '@/assets/art/twentynine/music-ad.png';
import writeAd from '@/assets/art/twentynine/write-ad.png';
import Diversion from '@/pages/art/utils/diversion';
import Orderpage from '@/pages/art/utils/order';
import { useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import { useEffect, useState } from 'react';
import Taro, { useRouter } from '@tarojs/taro';
import { dateFormat } from '@/utils';
import usePay from '../commons/usePay';
import { ARTSUPTOERITE, SUBJECBYDL } from '../utils/common';
import './index.scss';

export default () => {
  // 常量
  const levelList = [
    {
      key: 'S1',
      name: 'S1:适合3-4.5周岁宝贝',
      attr: 'normal',
    },
    {
      key: 'S2',
      name: 'S2:适合4.5-7周岁宝贝',
      attr: 'normal',
    },
    {
      key: 'S3',
      name: 'S3:适合7-8周岁宝贝',
      attr: 'normal',
    },
  ];

  const materialList = [
    {
      tips: '【优惠】',
      word: '已领优惠券，立减',
      text: '7元',
    },
    {
      tips: '【赠品】',
      word: '配套随材礼包',
      text: '（收货信息将在付款后填写）',
    },
    {
      tips: '【提醒】',
      word: '随材礼盒为课程配套物品，不同级别的礼盒略有差异',
      text: '',
    },
  ];

  const materialWords = [
    '小熊马克笔',
    '小熊勾线笔',
    'AR涂色卡',
    '各类作品纸',
    '绘画成长手册',
    '学习图谱等',
  ];

  const adList = {
    'yy_0.1': musicAd,
    'xz_0.1': writeAd,
  };

  const { params } = useRouter();
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  const mobile = useSelector((state: UserStateType) => state.mobile);
  const channelId = useSelector((state: UserStateType) => state.channelId);
  const channel = params.channelId || channelId;
  const { payFlag, pay } = usePay();
  const diversion = new Diversion({
    uid: userId,
  });
  const orderpage = new Orderpage({ userId });
  const [diversionType, setDiversionType] = useState('');
  const [showDiversionType, setShowDiversionType] = useState('');
  const [level, setLevel] = useState(levelList);
  const [supData, setSupData] = useState<any>({
    date: '',
    stage: '',
  });
  // 是否选择加购导流
  const [isChoosed, setIsChoosed] = useState(false);
  // 所剩名额
  const [remianNum, setRemianNum] = useState(0);

  // 判断用户是否购买过美术
  const getUserBuyArt = () => {
    queryOrderByUserId({
      userId: userId,
      channels: channel,
      subjects: 'ART_APP',
      packageId: '610',
    }).then(res => {
      const { subjectOrderMap, experienceCheckMap } = res.payload;
      // experienceCheckMap.ART_APP为true 表示之前已经买过体验课了
      if (experienceCheckMap['ART_APP']) {
        const iexperience =
          subjectOrderMap &&
          subjectOrderMap['ART_APP'] &&
          subjectOrderMap['ART_APP'].EXPERIENCE &&
          subjectOrderMap['ART_APP'].EXPERIENCE.length > 0;
        const iexperienceoneweek =
          subjectOrderMap &&
          subjectOrderMap['ART_APP'] &&
          subjectOrderMap['ART_APP'].EXPERIENCE_ONE_WEEK &&
          subjectOrderMap['ART_APP'].EXPERIENCE_ONE_WEEK.length > 0;
        if (iexperience || iexperienceoneweek) {
          const experience: never[] = iexperience
            ? subjectOrderMap['ART_APP']['EXPERIENCE']
            : subjectOrderMap['ART_APP']['EXPERIENCE_ONE_WEEK'];
          let supList: string[] = [];
          for (let i = 0; i < experience.length; i++) {
            supList.push(experience[i]['sup']);
          }
          setLevel(val => {
            val.forEach(v => {
              if (supList.includes(v.key)) {
                v.attr = 'disabled';
              }
            });
            return [...val];
          });
        }
      }
    });
  };

  const getDiversionType = async () => {
    const res = await diversion.getDiversionType({
      switchsiteType: '1',
      switchpageType: 'orderpage',
    });
    setDiversionType(res || '');
    // 音乐/写字未购买过 => 引流直接展示 写字引流需要选择级别判断下在展示
    if (res === 'yy_0.1' || diversion.supList.length == 0) {
      setShowDiversionType(res || '');
    }
    if (res) {
      sensors.track('xxms_testcourse_appletorderpage_Additionalpurchaseview', {
        diversion_subject: res === 'xz_0.1' ? '书法' : '音乐',
      });
    }
  };

  const levelClick = (item, index) => {
    if (item.attr == 'disabled') {
      return;
    }
    // 选择级别后展示导流
    setShowDiversionType(diversionType);

    // 选级别时判断可复购且写字导流是否已购买过同级别
    const _writeSup = ARTSUPTOERITE[item.key];
    if (
      diversion.userOrderInfo.WRITE_APP.repeatBuy &&
      diversion.supList.includes(_writeSup) &&
      diversionType == 'xz_0.1'
    ) {
      setShowDiversionType('');
    }
    setLevel(val => {
      val.forEach(v => (v.attr === 'active' ? (v.attr = 'normal') : ''));
      val[index].attr = 'active';
      return [...val];
    });
  };

  const getExperienceClassStartTime = async (initSup = '') => {
    let sup = '';
    const activeSup = level.find(v => v.attr === 'active');
    if (activeSup && activeSup.key) {
      sup = activeSup.key;
    }
    if (initSup) {
      sup = initSup;
    }
    if (!sup) {
      return;
    }
    const res = await orderpage.getExperienceClassStartTime({
      sup,
      type: 'TESTCOURSE',
      channelId: channel,
    });
    const openCourseDate = dateFormat(res.date * 1, 'MM' + '月' + 'dd' + '日');
    setSupData({
      date: openCourseDate,
      stage: res.stage,
    });
  };

  const handleIconClick = () => {
    setIsChoosed(v => {
      if (v) {
        sensors.track(
          'xxms_testcourse_appletorderpage_Additionalpurchasecheck',
        );
      }
      return !v;
    });
  };

  const handlePay = () => {
    sensors.track('xxms_testcourse_appletorderpage_payButtonClick', {
      buy_model: 'model_4',
    });
    const sup = level.find(v => v.attr === 'active');
    if (!sup) {
      Taro.showToast({ title: '请选择课程难度', icon: 'none', duration: 2000 });
      return;
    }

    const subjects = ['ART_APP'];
    if (isChoosed) {
      const addSubject = SUBJECBYDL[showDiversionType];
      subjects.push(addSubject);
    }

    payFlag &&
      pay({
        uid: userId,
        sup: sup.key,
        period: supData.stage,
        topicId: 0,
        subjects,
        switchsiteType: '1',
      });
  };

  useEffect(() => {
    getExperienceClassStartTime();
  }, [level]);

  useEffect(() => {
    if (showDiversionType) {
      setRemianNum(10);
      setTimeout(() => {
        setRemianNum(7);
        setTimeout(() => {
          setRemianNum(5);
        }, 6000);
      }, 3000);
    }
  }, [showDiversionType]);

  useEffect(() => {
    getUserBuyArt();
    getDiversionType();
    getExperienceClassStartTime('S1'); // 获取默认排期
  }, []);

  return (
    <View className='order-wrap'>
      <View className='section'>
        <View className='level'>
          <View className='title'>选择课程难度</View>
          <View className='level-box'>
            {level.map((item, index) => {
              return (
                <View
                  className={['level-item', `level-item_${item.attr}`].join(
                    ' ',
                  )}
                  key={item.key}
                  onClick={() => levelClick(item, index)}
                >
                  <View className='level-word'>{item.name}</View>
                </View>
              );
            })}
          </View>
        </View>
        <View className='orderinfo'>
          <View className='title'>
            <View className='title-l'>订单信息</View>
            {supData.date && (
              <View className='start-time'>{supData.date}开课</View>
            )}
          </View>
          <View className='material-wrap'>
            <View className='material-intro'>
              {materialList.map((item, index) => {
                return (
                  <View className='intro-item' key={index}>
                    <View className='intro-item-tips'>{item.tips}</View>
                    <View className='intro-item-word'>
                      {item.word}
                      <Text className='intro-item-text'>{item.text}</Text>
                    </View>
                  </View>
                );
              })}
            </View>
            <View className='material-show'>
              <View className='show-left'>
                <Image src={materialImg} mode='widthFix' className='l-img' />
              </View>
              <View className='show-right'>
                {materialWords.map((item, index) => {
                  return (
                    <View key={index} className='show-right-word'>
                      {item}
                    </View>
                  );
                })}
              </View>
            </View>
          </View>
        </View>
      </View>

      {showDiversionType && (
        <View className='purchased'>
          <View className='title' onClick={() => handleIconClick()}>
            <View className='title-l'>【加购福利】</View>
            <View className='surplus-quota'>
              剩余名额<Text className='surplus-quota-num'>{remianNum}</Text>个
            </View>
            <View className='choose-icon'>
              <Image src={isChoosed ? chooseIcon02 : chooseIcon01} />
            </View>
          </View>
          <View className='pur-img'>
            <Image
              src={adList[showDiversionType]}
              mode='widthFix'
              className='l-img'
            />
          </View>
        </View>
      )}
      <View className='btn-wrap'>
        <View className='btn-price'>
          <Text className='price-unit'>￥</Text>
          {`29${isChoosed ? '.1' : ''}`}
        </View>
        <View className='btn-pay' onClick={handlePay}>
          立即支付
        </View>
      </View>
    </View>
  );
};
