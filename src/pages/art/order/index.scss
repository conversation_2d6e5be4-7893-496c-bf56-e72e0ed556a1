page {
  background: #f5f5f5;
}
.order-wrap {
  .section {
    width: 100%;
    height: 849px;
    background: #ffffff;
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.08) inset;
    padding: 0 30px;
    box-sizing: border-box;
  }
  .level {
    padding-top: 53px;
    .title {
      height: 45px;
      font-size: 32px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333333;
      line-height: 45px;
      text-align: left;
    }
    .level-box {
      margin-top: 24px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
    .level-item {
      width: 332px;
      height: 109px;
      background: #ffffff;
      border-radius: 20px;
      border: 1px solid #e6e6e6;
      font-size: 30px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;
      line-height: 109px;
      text-align: center;
      margin-bottom: 20px;
      box-sizing: border-box;
      &_active {
        background: #fffbec;
        box-shadow: 0px 0px 13px 0px rgba(177, 177, 177, 0.26);
        border: 3px solid #ff9b00;
      }
      &_disabled {
        background: #ffffff;
        color: #bfbfbf;
        position: relative;
        &::after {
          content: '';
          width: 73px;
          height: 34px;
          background: url('../../../assets/art/twentynine/yigou.png') no-repeat
            0 0/100%;
          position: absolute;
          top: 0;
          right: 0;
        }
      }
    }
  }
  .orderinfo {
    margin-top: 20px;
    .title {
      padding-bottom: 18px;
      border-bottom: 1px solid #e6e6e6;
      display: flex;
      justify-content: space-between;
      .title-l {
        width: 140px;
        height: 45px;
        font-size: 32px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333333;
        line-height: 45px;
        text-align: left;
      }
      .start-time {
        height: 33px;
        font-size: 24px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ff9c00;
        text-align: right;
      }
    }
    .material-wrap {
      margin-top: 24px;
    }
    .material-intro {
      margin-bottom: 20px;
      .intro-item {
        display: flex;
        margin-bottom: 12px;
        .intro-item-tips {
          width: 100px;
          height: 33px;
          font-size: 24px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #ff9b00;
          line-height: 33px;
        }
        .intro-item-word {
          flex: 1;
          height: 33px;
          font-size: 24px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #727272;
          line-height: 33px;
          text-align: left;
        }
        .intro-item-text {
          color: #ff9b00;
        }
      }
    }
    .material-show {
      width: 686px;
      height: 179px;
      border-radius: 20px;
      border: 1px dotted #b4b4b4;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 0 17px 27px;
      box-sizing: border-box;
      .show-left {
        width: 202px;
        height: 142px;
        .l-img {
          width: 100%;
          height: 100%;
        }
      }
      .show-right {
        flex: 1;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        margin-left: 20px;
      }
      .show-right-word {
        width: 180px;
        height: 37px;
        font-size: 26px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #b4b4b4;
        line-height: 37px;
        position: relative;
        text-align: left;
        padding-left: 20px;
        margin-top: 10px;
        &::after {
          content: ' ';
          position: absolute;
          width: 6px;
          height: 6px;
          background-color: #b4b4b4;
          border-radius: 50%;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
  }
  .purchased {
    width: 100%;
    height: 446px;
    background: #ffffff;
    padding: 30px 30px 40px 30px;
    box-sizing: border-box;
    margin-top: 19px;

    .title {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      .title-l {
        width: 200px;
        height: 45px;
        font-size: 32px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ff7000;
        line-height: 45px;
      }
      .surplus-quota {
        flex: 1;
        height: 40px;
        font-size: 28px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        line-height: 40px;
        text-align: right;
      }
      .surplus-quota-num {
        color: #ff7000;
      }
      .choose-icon {
        width: 42px;
        height: 42px;
        margin-left: 10px;
        image {
          width: 100%;
          height: 100%;
        }
      }
    }
    .pur-img {
      width: 690px;
      height: 310px;
      overflow: hidden;
      .l-img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .btn-wrap {
    width: 100%;
    height: 183px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32px 0 63px;
    box-sizing: border-box;
    .btn-price {
      width: 140px;
      height: 92px;
      font-size: 66px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #ff7000;
      line-height: 92px;
      .price-unit {
        font-size: 34px;
      }
    }
    .btn-pay {
      width: 226px;
      height: 96px;
      background: linear-gradient(270deg, #ffa300 0%, #ff6a00 100%);
      border-radius: 96px;
      font-size: 34px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #ffffff;
      text-align: center;
      line-height: 96px;
    }
  }
}
