import Taro, { useRouter } from '@tarojs/taro';
import { useDispatch, useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
// import sensors from '@/utils/sensors_data';
import { useState } from 'react';
import {
  getPackagesCreate,
  postReportReady,
  packagesCrossCreate,
  wxCombine,
  getWeixinProgramPay,
} from '@/api/groupbuy';
import {
  ARTSUPTOERITE,
  CHANNELIDS,
  PACKAGEIDS,
  PAYTYPE,
} from '../utils/common';

export default function usePay() {
  const { params } = useRouter();
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  //openid
  const openId = useSelector((state: UserStateType) => state.openid);
  //channelId
  const channelId = useSelector((state: UserStateType) => state.channelId);
  const channel = params.channelId || channelId;
  console.log(channel, 'usepay===');

  //clickId
  const clickId = useSelector((state: UserStateType) => state.clickId);
  //wxAccountNu
  const wxAccountNu = useSelector((state: UserStateType) => state.wxAccountNu);
  //routePath
  const routePath = useSelector((state: UserStateType) => state.routePath);
  //adPlatform
  const adPlatform = useSelector((state: UserStateType) => state.adPlatform);
  //sendId
  const sendId = useSelector((state: UserStateType) => state.sendId);
  //sendId
  const spreadId = useSelector((state: UserStateType) => state.spreadId);
  //posterId
  const posterId = useSelector((state: UserStateType) => state.posterId);
  // const userRole = useSelector((state: UserStateType) => state.userRole);
  const teacherId = params.tid || params.teacherId || '';
  const platform = params.from == '1' ? 2 : 0;
  //支付flag
  const [payFlag, setPayFlag] = useState<boolean>(true);
  const [paySuccess, setPaySuccess] = useState(false); //支付状态
  const [payOrderId, setPayOrderId] = useState('');
  const dispatch = useDispatch();

  // 跳转成功页
  const goAddAddress = (sup, orderId) => {
    Taro.navigateTo({
      url: `/pages/groupbuy/addAddress/index?sup=${sup}&orderId=${orderId}`,
    });
  };

  // 下单
  function pay({
    uid = userId,
    sup = '',
    period = '',
    topicId = 0,
    subjects = ['ART_APP'],
    switchsiteType,
  }) {
    setPayFlag(false);
    // setCanBuyArt(true)
    if (subjects.length == 2) {
      let packagesSplitList = subjects.map(v => {
        const packageId = PACKAGEIDS[v];
        let _channel = CHANNELIDS[v][switchsiteType];
        let _sup = sup;
        if (v === 'ART_APP') {
          _channel = channel || _channel;
        }
        if (v === 'MUSIC_APP') {
          return {
            packagesId: packageId,
            stage: 0,
            sup: 'S3',
            topicId: 4,
            channel: _channel,
          };
        }
        if (v === 'WRITE_APP') {
          _sup = ARTSUPTOERITE[sup];
        }
        return {
          packagesId: packageId,
          stage: period,
          sup: _sup,
          topicId,
          channel: _channel,
        };
      });

      packagesCrossCreate({
        type: 'ALONE',
        userId: uid,
        addressId: 0,
        sendId,
        spreadId,
        posterId,
        packagesSplitList,
        come: params.come,
      }).then(res => {
        if (res.status === 'EXCEPTION') {
          setPayFlag(true);
          if (res.code == 80000053) {
            Taro.showToast({
              title: '您已购买体验课，不支持再次购买',
              icon: 'none',
              duration: 2000,
            });
          } else {
            Taro.showToast({
              title: res.errors || '下单失败！',
              icon: 'none',
              duration: 2000,
            });
          }
          return false;
        }
        if (res.code === 0) {
          const { orderSimpleList } = res.payload;
          const orderIdList = [];
          orderSimpleList.forEach((v: never) => {
            orderIdList.push(v['orderId']);
            if (v['subject'] === 'ART_APP') {
              dispatch({
                type: 'CHANGE_ORDERID',
                orderId: v['orderId'],
              });
            }
          });
          setTimeout(() => {
            Taro.setStorageSync('outTradeNo', orderIdList.join(','));
          }, 200);
          wxCombine({
            userId: uid,
            openId,
            payType: 'AI_WXPROGRAM',
            mergeOutTradeNo: res.payload['unionOutTradeNo'],
            notifyUrl: '',
          })
            .then(result => {
              const { timeStamp, nonceStr, paySign } = result.payload;
              Taro.requestPayment({
                timeStamp,
                nonceStr,
                package: result.payload.package,
                //@ts-ignore
                signType: result.payload.signType,
                paySign,
                success: function() {
                  setPayFlag(true);
                  setPaySuccess(true);
                  goAddAddress(sup, orderIdList);
                },
                fail: function() {
                  setPayFlag(true);
                  Taro.showToast({
                    title: '下单失败！',
                    icon: 'none',
                    duration: 2000,
                  });
                },
              });
            })
            .catch(() => {
              setPayFlag(true);
            });
        }
      });
    } else {
      const v = subjects[0];
      const packageId = PACKAGEIDS[v];
      let _channel = channel;
      let _sup = sup;
      let _period = period;
      let _topicId = topicId;
      if (v === 'MUSIC_APP') {
        _sup = 'S3';
        _period = '0';
        _topicId = 4;
        _channel = CHANNELIDS[v][switchsiteType];
      }
      if (v === 'WRITE_APP') {
        _sup = ARTSUPTOERITE[sup];
        _channel = CHANNELIDS[v][switchsiteType];
      }
      getPackagesCreate({
        type: 'ALONE',
        userId: uid,
        packagesId: packageId,
        stage: _period,
        sup: _sup,
        channel: _channel,
        sendId,
        spreadId,
        topicId: _topicId,
        posterId,
        teacherId,
        platform,
        come: params.come || '',
      }).then(res => {
        if (res.status === 'EXCEPTION') {
          setPayFlag(true);
          if (res.code == 80000053) {
            Taro.showToast({
              title: '您已购买体验课，不支持再次购买',
              icon: 'none',
              duration: 2000,
            });
          } else {
            Taro.showToast({
              title: res.errors || '下单失败！',
              icon: 'none',
              duration: 2000,
            });
          }
          return false;
        }
        if (res.code === 0) {
          clickId &&
            postReportReady({
              orderId: res.payload.order.id,
              platform: adPlatform ? adPlatform.toLocaleUpperCase() : 'WX',
              type: 'RESERVATION',
              clickId: clickId,
              url: routePath,
              params: '',
              wxAccountNu: wxAccountNu,
            });
          if (v === 'ART_APP') {
            dispatch({
              type: 'CHANGE_ORDERID',
              orderId: res.payload.order.id,
            });
          } else {
            setPayOrderId(res.payload.order.id);
          }

          getWeixinProgramPay({
            openId,
            orderId: res.payload.order.id,
            userId: uid,
            payType: PAYTYPE[v],
            notifyUrl: '',
          })
            .then(data => {
              if (data.code === 0) {
                const {
                  timeStamp,
                  nonceStr,
                  // package: packageId,
                  paySign,
                } = data.payload;
                // 不同的支付接口的返回不同
                Taro.requestPayment({
                  timeStamp,
                  nonceStr,
                  package: data.payload.package,
                  //@ts-ignore
                  signType: data.payload.signType,
                  paySign,
                  success: function(payRes) {
                    console.log(payRes, 'payRes');

                    setPayFlag(true);
                    setPaySuccess(true);
                    if (v === 'ART_APP') {
                      goAddAddress(sup, res.payload.order.id);
                    } else {
                      Taro.showToast({
                        title: '购课成功，请添加老师',
                        icon: 'none',
                        duration: 2000,
                      });
                    }
                  },
                  fail: function(failRes) {
                    console.log(failRes, 'failRes');

                    setPayFlag(true);
                    Taro.showToast({
                      title: '下单失败！',
                      icon: 'none',
                      duration: 2000,
                    });
                  },
                });
              }
            })
            .catch(err => {
              setPayFlag(true);
              console.log(err);
            });
        }
      });
    }
  }

  return {
    payFlag,
    pay,
    paySuccess,
    payOrderId,
  };
}
