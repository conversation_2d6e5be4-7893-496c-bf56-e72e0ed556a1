@import '../../../theme/normalGroup/common.scss';
page {
  background-color: #f5f5f5;
}
.container {
  font-size: 0;
  padding-bottom: calc(112rpx + env(safe-area-inset-bottom));
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;

  .payIcon {
    width: 126px;
    height: 126px;
    margin: 136px 0 32px;
  }

  .orderLoading {
    font-size: 32px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #333333;
    margin-bottom: 88px;
  }

  .orderInfo {
    font-size: 28px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #666666;
    padding: 0 60px;
    width: 100%;
    box-sizing: border-box;

    text-align: left;
  }

  .orderSuccess {
    text-align: center;
    font-size: 36px;
  }

  .orderInfoTips {
    font-size: 28px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #666666;
    margin: 88px 60px;

    .originColor {
      color: #ffa700;
    }

    .undeline {
      text-decoration: underline;
    }
  }

  .orderBack {
    width: 516px;
    height: 80px;
    background: #ffa700;
    border-radius: 44px;
    font-size: 32px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 80px;
    text-align: center;
  }
  .repaymentBtn {
    box-sizing: border-box;
    width: 630px;
    height: 80px;
    border-radius: 44px;
    margin-top: 64px;
    line-height: 80px;
    text-align: center;
    font-size: 32px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    border: 1px solid #ffa700;
    color: #ffa700;
  }
}
