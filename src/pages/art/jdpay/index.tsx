import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Taro, { useRouter } from '@tarojs/taro';
import { View, Button, Navigator, Image, Text } from '@tarojs/components';
import { UserStateType } from '@/store/groupbuy/state';
import { jdPayV2, getUserOpenIdSubject, getOrderStaus } from '@/api/groupbuy';
// @ts-ignore
import CommonTop from '@/components/commonTop';
import payIcon from '@/assets/art/payIcon.png';
import payIcon1 from '@/assets/art/payIcon1.png';
import { CHANNELIDS } from '../utils/common';
import './index.scss';

export default props => {
  const openId = useSelector((state: UserStateType) => state.openid);
  const [orderStatus, setorderStatus] = useState('opening');
  const [repayment, setrepayment] = useState(false);
  const routerparams = useRouter().params;
  const dispatch = useDispatch();

  useEffect(() => {
    Taro.removeStorage({ key: 'openminiWechartState' });
    callpay();
  }, [routerparams]);
  const callpay = () => {
    // orderId=2771793586553&userId=638083596775870464&price=0.02&subject=ART_APP
    Taro.showLoading();
    // 安卓支付完成之后会重载小程序
    getOrderStaus({ orderId: routerparams.orderId }).then(res => {
      const { status } = res.payload;
      //普通支付是 COMPLETED  代表支付成功
      if (
        status === 'COMPLETED' ||
        status === 'WAIT_COMPLETED' ||
        status === 'WAIT_COMPLETED_MULTI'
      ) {
        setorderStatus('success');
      } else {
        if (openId) {
          openWeChat(null);
        } else {
          Tarologin();
        }
      }
    });
  };
  const Tarologin = () => {
    Taro.login({
      success: function(res) {
        if (res.code) {
          getUserOpenIdSubject({
            code: res.code,
            channel: routerparams.channelId || CHANNELIDS['ART_APP'][1],
            subject: 'ART_APP',
          }).then(data => {
            if (data.code === 0) {
              if (data.payload.uid) {
                dispatch({
                  type: 'CHANGE_USERID',
                  userid: data.payload.uid,
                });
              } else {
                dispatch({ type: 'CHANGE_USERID', userid: '' });
              }
              data.payload.mobile &&
                dispatch({
                  type: 'CHANGE_MOBILE',
                  mobile: data.payload.mobile,
                });
              dispatch({
                type: 'CHANGE_OPENID',
                openid: data.payload.openid,
              });
              dispatch({
                type: 'CHANGE_UNIONID',
                unionId: data.payload.unionid,
              });
              if (data.payload.token) {
                Taro.setStorageSync('appToken', data.payload.token);
              }
              openWeChat(data.payload.openid);
            }
          });
        } else {
          console.log('登录失败！' + res.errMsg);
        }
      },
      complete: function() {
        Taro.hideLoading();
      },
    });
  };
  const openWeChat = paramsopenid => {
    const { subject, userId = '' } = routerparams;
    let payType = '';
    if (subject === 'ART_APP') {
      payType = 'ART_JDJH_WX_PRM';
    }
    if (subject === 'PICTURE_BOOK') {
      payType = 'PICTURE_BOOK_JDJH_WX_PRM';
    }
    if (subject === 'WRITE_APP') {
      payType = 'WRITE_JDJH_WX_PRM';
    }
    Taro.showLoading();
    jdPayV2({
      orderId: routerparams.orderId || '',
      payType: payType,
      userId: userId,
      openId: paramsopenid || openId,
      notifyUrl: '',
    })
      .then(res => {
        const { code, payload, errors } = res;
        if (code === 0 && payload && payload.resultCode === '0000') {
          let payInfo: any = {};
          try {
            payInfo = JSON.parse(payload.payInfo);
          } catch (error) {
            payInfo = {};
          }
          if (Object.keys(payInfo).length) {
            Taro.requestPayment({
              ...payInfo,
              success: function(payRes) {
                console.log(payRes, 'payRes');
                Taro.showToast({
                  title: '支付成功',
                  icon: 'none',
                  duration: 2000,
                });
                setorderStatus('success');
                // if (routerparams.sourceForm === 'app') {
                //   Taro.navigateTo({
                //     url: routerparams.returnNotifyUrl,
                //   });
                // } else {
                //   Taro.navigateTo({
                //     url: `/pages/launch/follow/index?sup=${routerparams.sup}&uid=${routerparams.userId}&orderId=${orderId}`,
                //   });
                // }
              },
              fail: function(failRes) {
                console.log(failRes, 'failRes');
                setrepayment(true);
                Taro.showToast({
                  title: '支付未完成！',
                  icon: 'none',
                  duration: 2000,
                });
              },
            });
          }
        } else {
          Taro.showToast({
            title: errors || '唤起支付失败,请重试',
            icon: 'none',
            duration: 2000,
          });
        }
      })
      .finally(() => {
        Taro.hideLoading();
      });
  };

  const launchAppError = e => {
    console.log(e.detail.errMsg);
  };

  return (
    <View className='container'>
      <CommonTop currentName='小熊美术' isIntroduce={false} />
      {orderStatus == 'opening' ? (
        <>
          <Image className='payIcon' src={payIcon1}></Image>
          <View className='orderLoading'>支付中</View>
          <View className='orderInfo'>订单号: {routerparams.orderId}</View>
          <View className='orderInfo'>支付金额: ¥{routerparams.price}</View>
          {repayment ? (
            <View className='repaymentBtn' onClick={callpay}>
              重新支付
            </View>
          ) : null}
        </>
      ) : (
        <>
          <Image className='payIcon' src={payIcon}></Image>
          <View className='orderSuccess'>支付成功</View>
          <View className='orderInfoTips'>
            支付成功后请
            <Text className='originColor'>
              添加<Text className='undeline'>专属老师</Text>，请务必添加
              <Text className='undeline'>老师微信</Text>才能上课哦～
            </Text>
          </View>
          {routerparams.sourcePage === 'APP' ? (
            <Button
              className='orderBack'
              open-type='launchApp'
              app-parameter='wechat'
              binderror={launchAppError}
            >
              返回商家
            </Button>
          ) : (
            <Navigator
              className='orderBack'
              open-type='exit'
              target='miniProgram'
            >
              返回商家
            </Navigator>
          )}
        </>
      )}
    </View>
  );
};
