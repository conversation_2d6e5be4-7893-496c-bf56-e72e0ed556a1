@import '../../../theme/normalGroup/common.scss';

.container {
  font-size: 0;
  padding-bottom: calc(112rpx + env(safe-area-inset-bottom));
  background: #ffe66d;

  .layout-header {
    padding: 40px 0 16px;
  }

  .fixedTop {
    position: sticky;
  }

  .intro-part {
    .intro-con {
      padding: 10px $font-32;
      background-color: $light-yellow;

      .intro-text {
        font-size: $font-24;
        color: $c_red;
      }

      .icon {
        width: $font-32;
        margin-right: 16px;
      }
    }
  }

  .body-part {
    // margin-top: 80px;
    padding: 69px $font-32;
    background-color: $de_orange;

    &.newArt {
      background-color: #ffe66d;
    }

    .body-img-part {
      position: relative;
      margin-bottom: 80px;

      .slide-insert {
        position: absolute;
        top: 220px;
        left: 0;
        right: 0;
        height: 660px;

        .insert-swiper {
          height: 100%;
          text-align: center;
        }

        image {
          height: 606px;
        }
      }

      &:last-child {
        margin: 0;
      }
    }
  }

  .suction-bottom {
    bottom: 0;
    z-index: 101;
    // ios安全区域
    padding-bottom: calc(10px + env(safe-area-inset-bottom));
    background-color: #ffffff;
    .bg_white {
      height: 116px;
      margin-top: $font-20;
      position: relative;

      .sel-img {
        width: 670px;
        height: 90px;
        animation-name: scaleDraw; /*关键帧名称*/
        animation-timing-function: ease-in-out; /*动画的速度曲线*/
        animation-iteration-count: infinite; /*动画播放的次数*/
        animation-duration: 1.2s; /*动画所花费的时间*/
      }

      .img-btn-box {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        bottom: 0;
        display: flex;
        padding-left: 200px;
        box-sizing: border-box;
        align-items: center;
        justify-content: center;

        .thirtySix-btn {
          width: 250px;
          height: 84px;
        }

        .twentyNine-btn {
          width: 250px;
          height: 84px;
        }
      }

      .sle-img-part {
        margin-left: 18px;

        .sel-img {
          width: 165px;
          margin-top: -25px;
        }
      }

      .sle-price-part {
        margin-left: 18px;
        font-size: $font-32;

        .dollar,
        .price {
          font-size: $font-36;
          color: $c_red;
        }

        .price {
          font-size: 56px;
        }
      }

      .buy-submit {
        width: 297px;
        height: 88px;
        font-size: $font-32;
        line-height: 88px;
        margin-right: $font-28;
        border-radius: 88px;
        background-color: $c_red;
      }
    }
  }

  .giveMask {
    .at-modal__overlay {
      background-color: rgba(0, 0, 0, 0.7);
    }
    .at-modal__container {
      width: 600px;
      background-color: transparent;
    }

    .giveMaskImg {
      width: 600px;
      height: 800px;
    }
  }

  .redeem-modal {
    .at-modal__overlay {
      background-color: rgba(0, 0, 0, 0.7);
    }

    .at-modal__container {
      padding-top: 90px;
      box-sizing: border-box;
      width: 580px;
      border-radius: 30px;
      overflow: auto;
      background: transparent;

      .close-img {
        width: 60px;
        height: 60px;
        position: absolute;
        top: 0;
        right: 0;
      }

      .at-modal__content {
        padding: 0;
        overflow: auto;
        background: transparent;

        .redeem-img {
          width: 100%;
          height: 519px;
          vertical-align: top;
        }

        .footer {
          height: 168px;
          background: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;

          .btns {
            width: 500px;
            height: 88px;
            background: #ff9c00;
            border-radius: 44px;
            font-size: 36px;
            font-weight: bold;
            color: #ffffff;
            line-height: 88px;
            text-align: center;
          }
        }
      }
    }
  }
}

.bttn {
  background-color: transparent;
  padding: 0;

  &::after {
    border: none;
  }
}
@keyframes scaleDraw {
  /*定义关键帧、scaleDrew是需要绑定到选择器的关键帧名称*/
  0% {
    transform: scale(1); /*开始为原始大小*/
  }
  25% {
    transform: scale(1.06); /*放大1.1倍*/
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(1.06);
  }
}
