import { AtFloatLayout, AtModal, AtModalContent } from 'taro-ui';
import { useEffect, useRef, useState } from 'react';
import Taro, { useRouter, useShareAppMessage } from '@tarojs/taro';
import { Image, Swiper, SwiperItem, Text, View } from '@tarojs/components';
import { UserStateType } from '@/store/groupbuy/state';
import Orderdetailmp from '@/components/orderDetailMp';
import LayoutOrderV1 from '@/components/orderV1';
import store from '@/store/groupbuy/index';
import { decodeUrl } from '@/utils';
import '@/theme/custom-taro-ui.scss';
/** 图片部分   **/
import selIcon from '@/assets/groupbuy/index/gift-new.jpg';
import selIconV1 from '@/assets/groupbuy/index/gift-new-v2.png';
// import SlideS4 from '@/assets/art/twentynine/slide-s4.png';
import SlideS1To3 from '@/assets/art/twentynine/slide-s1-3.png';
import artBanner from '@/assets/art/twentynine/banners.png';
import shareimg from '@/assets/art/twentynine/shareimg.png';
import rebackBtn36 from '@/assets/art/twentynine/paybtn.png';
import giveMaskImg from '@/assets/normalGroup/newArt/mask-art-29.gif';
import modalCloseImg from '@/assets/groupbuy/thirtySix/close.png';
import redeemImg from '@/assets/groupbuy/thirtySix/redeem-img2.png';
import body_part_6_artworld from '@/assets/normalGroup/newArt/body_part_6_artworld.png';
import sensors from '@/utils/sensors_data';
import { levelV1, levelV2 } from '@/common/data.config';
import { getOrdersId, getUserOpenIdSubject } from '@/api/groupbuy';
import { useDispatch, useSelector } from 'react-redux';
// @ts-ignore
import CommonTop from '../../../components/commonTop';
import { CHANNELIDS } from '../utils/common';
import LayoutLevel from '../../../components/level';
import './index.scss';

// 中间部分图片
// // 有spreadId
const artRequireContext = require.context(
  '@/assets/art/twentynine',
  true,
  /^\.\/.*ten-img-\d\.png$/,
);

const artBodyPartImages = artRequireContext.keys().map(artRequireContext);
/** 图片部分完 **/
export default (props) => {
  const router = useRouter();
  const dispatch = useDispatch();
  const depar = decodeUrl(decodeURIComponent(props.tid)) as any;
  /** 常量 */

  /** 常量结束 */
  //openid
  let channelId = useSelector((state: UserStateType) => state.channelId);
  let userRole = useSelector((state: UserStateType) => state.userRole);
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  let _channelId = '12706'; // 默认渠道
  if (Object.keys(depar).length > 1) {
    _channelId = depar['channelId'] || channelId;
  }

  if (router.params.channelId) _channelId = router.params['channelId'];

  useEffect(() => {
    if (router.params.channelId) {
      store.dispatch({
        type: 'CHANGE_CHANNELID',
        channelId: router.params['channelId'],
      });
    }
  }, [router.params]);

  // 判断周周分享渠道进入
  const country = useRef(false);
  const [banner] = useState(artBanner);
  const [showGiveMask, setShowGiveMask] = useState(false);
  const [isOpenWind, setIsOpenWind] = useState<boolean>(false);
  // 显示级别浮窗
  const [isOpened, setIsOpened] = useState<boolean>(false);
  // 级别类型
  //   const [levelType] = useState<number>(Math.floor(Math.random() * 3));
  const [levelType] = useState<number>(1);
  const [needShowRedeem, setNeedShowRedeem] = useState<boolean>(true);
  // 显示隐藏订单浮窗
  const [isShowOrder, setIsShowOrder] = useState<boolean>(false);
  const [showRedeemModal, setShowRedeemModal] = useState<boolean>(false);
  // 支付页面的数据
  const [payPageData, setPayPageData] = useState<object | null>(null);

  // 随材赠品展示
  const [giveaway] = useState({
    img: selIcon,
    sImg: selIconV1,
    detail: [
      '小熊模切',
      '超轻粘土',
      '小熊作品纸',
      '黑色勾线笔',
      '小熊马克笔',
      '重彩油画棒',
      '手指画颜料',
      '其他材料若干',
    ],
    notes: '该图仅为展示，具体以实物为主，每期根据课程不同收到的随材会有所差异',
  });

  const handleWeeklySharing = () => {
    setTimeout(() => {
      if (!country.current && !showGiveMask) {
        sensors.track('xx_testcourse_homepage_Purchasepromotionpopup_view', {
          channel_id: _channelId,
        });
        setShowGiveMask(true);
      }
    }, 20000);
  };

  useEffect(() => {
    if (isOpenWind) {
      country.current = true;
    }
  }, [isOpenWind]);

  // 监听订单浮窗的打开和关闭
  const watchShowOrder = (state, pageData) => {
    if (!state && needShowRedeem) {
      setShowRedeemModal(true);
      setNeedShowRedeem(false);
      return;
    }
    setNeedShowRedeem(true);
    setIsShowOrder(state);
    pageData && setPayPageData(pageData);

    pageData &&
      Object.keys(pageData).length > 0 &&
      !isShowOrder &&
      sensors.track('xxms_testcourse_authorizedlayer_courseSup_click', {
        course_sup: pageData.sup,
        channel_id: _channelId,
        user_role: userRole,
        buy_model: 'model_4',
        abtest: levelType == 0 ? '单年龄' : '年龄&介绍',
      });
  };

  // 点击购买
  const purchaseHandle = () => {
    if (levelType == 2) {
      watchShowOrder(true, {});
      setIsShowOrder(true);
      return;
    }
    setIsOpened(true);
  };

  // 关闭挽留弹窗
  const hideRedeemHandle = () => {
    setShowRedeemModal(false);
  };

  useEffect(() => {
    Taro.login({
      success: function (res) {
        if (res.code) {
          getUserOpenIdSubject({
            code: res.code,
            channel: router.params.channelId || CHANNELIDS['ART_APP'][1],
            subject: 'ART_APP',
          }).then((data) => {
            if (data.code === 0) {
              if (data.payload.uid) {
                dispatch({
                  type: 'CHANGE_USERID',
                  userid: data.payload.uid,
                });
              } else {
                dispatch({ type: 'CHANGE_USERID', userid: '' });
              }
              data.payload.mobile &&
                dispatch({
                  type: 'CHANGE_MOBILE',
                  mobile: data.payload.mobile,
                });
              dispatch({
                type: 'CHANGE_OPENID',
                openid: data.payload.openid,
              });
              dispatch({
                type: 'CHANGE_UNIONID',
                unionId: data.payload.unionid,
              });
              if (data.payload.token)
                Taro.setStorageSync('appToken', data.payload.token);
              data.payload.uid &&
                getOrdersId({
                  userId: data.payload.uid,
                  addressId: 0,
                  subjects: 'ART_APP',
                }).then((item) => {
                  let _payload = item.payload[0];
                  if (item.code === 0 && _payload) {
                    dispatch({
                      type: 'CHANGE_ORDERID',
                      orderId: _payload.id,
                    });
                    // 用户订单是否已退费 REFUNDEND=>已退费
                    if (_payload.isRefund === 'REFUNDEND') {
                      return;
                    }
                    // model=4  模式4
                    Taro.navigateTo({
                      url: `/pages/groupbuy/addAddress/index?sup=${
                        _payload.sup
                      }${_payload.model == 4 ? '&vType=2' : ''}&isV9=1`,
                    });
                  }
                });
            }
          });
        } else {
          console.log('登录失败！' + res.errMsg);
        }
      },
    });
  }, [dispatch, router, router.params]);

  useEffect(() => {
    sensors.track('xxms_testcourse_applethomepage_view', {
      channel_id: _channelId,
      buy_model: 'model_4',
    });
    handleWeeklySharing();
  }, []);

  useShareAppMessage(() => {
    return {
      title: '孩子喜欢的画画课降价学，还包邮送16种画材',
      path: `/pages/art/twentynine/index?sendId=${userId}&channelId=${channelId}`,
      imageUrl: shareimg, //自定义图片路径，可以是本地文件路径、代码包文件路径或者网络图片路径，支持PNG及JPG，不传入 imageUrl 则使用默认截图。显示图片长宽比是 5:4
      success: function (res) {
        // 转发成功之后的回调
        if (res.errMsg == 'shareAppMessage:ok') {
        }
      },
      fail: function () {},
    };
  });

  /* 订单弹窗结束 */
  /** 传给子页面的参数结束 **/
  return (
    <View className='container w100 relative'>
      {/* 头部导航栏 */}
      <CommonTop
        currentName='小熊美术'
        isIntroduce={false}
        defaultChannel={_channelId}
      />
      {/* banner图 */}
      <View className='w100 banner-con'>
        <Image className='banner w100' mode='widthFix' src={banner}></Image>
      </View>

      {/* 图片列表 */}
      <View className='body-part newArt'>
        {artBodyPartImages.map((item, index) => {
          return (
            <View className='body-img-part' key={`body-img-${index}`}>
              <Image
                className='body-img w100'
                mode='widthFix'
                src={`${
                  index == artBodyPartImages.length - 1 &&
                  router.params.from == '1'
                    ? body_part_6_artworld
                    : item
                }`}
              ></Image>
              {index === 4 && (
                <View className='slide-insert'>
                  <Swiper
                    className='insert-swiper'
                    // autoplay
                    // circular
                    // indicatorDots
                    // indicatorColor='rgba(239, 236, 236, 1)'
                    // indicatorActiveColor='rgba(255, 93, 27, 1)'
                  >
                    <SwiperItem>
                      <Image src={SlideS1To3} mode='heightFix'></Image>
                    </SwiperItem>
                    {/* <SwiperItem>
                      <Image src={SlideS4} mode='heightFix'></Image>
                    </SwiperItem> */}
                  </Swiper>
                  <Text></Text>
                </View>
              )}
            </View>
          );
        })}
      </View>
      <Orderdetailmp />
      {/* 购买部分*/}
      <View className='suction-bottom fixed'>
        <View
          className='bg_white flex w100'
          onClick={() => {
            sensors.track('xxms_testcourse_applethomepage_buybuttonclick', {
              buy_model: 'model_4',
            });
            setIsOpenWind(true);
            purchaseHandle();
          }}
        >
          <Image
            className='sel-img'
            src={rebackBtn36}
            mode='widthFix'
            data-type='thirtySix'
          ></Image>
        </View>
      </View>
      <AtModal
        className='giveMask'
        isOpened={showGiveMask}
        // closeOnClickOverlay={false}
        onClose={() => {
          setShowGiveMask(false);
        }}
      >
        <Image
          className='giveMaskImg'
          src={giveMaskImg}
          onClick={() => {
            sensors.track(
              'xx_testcourse_homepage_Purchasepromotionpopup_buyclick',
            );
            setIsOpenWind(true);
            purchaseHandle();
            setShowGiveMask(false);
          }}
        ></Image>
      </AtModal>

      <AtFloatLayout
        className='custom-float-layout'
        isOpened={isOpened}
        onClose={() => {
          setIsOpened(false);
        }}
        title='选择级别'
      >
        <LayoutLevel
          oldLevelArray={levelType == 1 ? levelV2 : levelV1}
          levelType={levelType}
          pType='art'
          regtype='EXPERIENCE'
          watchShowOrder={watchShowOrder}
        />
      </AtFloatLayout>

      <AtFloatLayout
        className='order-layout custom-float-layout'
        isOpened={isShowOrder}
        onClose={() => {
          watchShowOrder(false, null);
        }}
      >
        {payPageData && (
          <LayoutOrderV1
            isShowOrder={isShowOrder}
            watchCloseOrder={watchShowOrder}
            payPageData={payPageData}
            orderType='29'
            subject='ART_APP'
            packagesId={610}
            classNum={10}
            topicId={router.params.topicId || 0}
            pType='art'
            isIntroduce={false}
            pName='美术'
            giveaway={giveaway}
            levelType={levelType}
            regtype='EXPERIENCE'
          />
        )}
      </AtFloatLayout>
      <AtModal
        className='redeem-modal'
        isOpened={showRedeemModal}
        closeOnClickOverlay={false}
      >
        <Image
          className='close-img'
          src={modalCloseImg}
          onClick={hideRedeemHandle}
        ></Image>
        <AtModalContent>
          <Image className='redeem-img' src={redeemImg}></Image>
          <View className='footer'>
            <View className='btns' onClick={hideRedeemHandle}>
              继续支付
            </View>
          </View>
        </AtModalContent>
      </AtModal>
    </View>
  );
};
