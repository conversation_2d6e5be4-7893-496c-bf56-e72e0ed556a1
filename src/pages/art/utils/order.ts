/**
 *  开课时间、用户订单详情
 */

import { getSupManagements } from '@/api/groupbuy';

class Orderpage {
  userId = '';
  constructor(uid) {
    this.userId = uid;
  }

  // 获取用户是否复购
  getUserExperienceClassInfo() {}
  // 获取体验课开课时间
  async getExperienceClassStartTime({
    sup,
    type,
    channelId,
    subject = 'ART_APP',
  }) {
    const res = await getSupManagements({ sup, type, channelId, subject });
    return {
      date: res.payload.courseDay,
      stage: res.payload.period,
    };
  }
  // 获取用户状态
  getUserStatusBuyOrderId() {}
}

export default Orderpage;
