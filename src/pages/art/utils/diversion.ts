import { getSwitchFlag } from '@/api/1v1k8s';
import { queryOrderByUserId } from '@/api/groupbuy';
import { CHANNELIDS, PACKAGEIDS, SWITCHFLAG } from './common';

//610
type pageTypeInter = 'orderpage' | 'teacherpage';
type switchsiteInter = '1' | '2' | '3' | '2,3';
interface getDiversionTypeProps {
  switchsiteType: switchsiteInter;
  switchpageType: pageTypeInter;
}
class Diversion {
  needCheckData: any[] = [];
  userId: string = '';
  supList: string[] = []; // 已购级别
  userOrderInfo = {
    MUSIC_APP: {
      firstBuy: false,
      repeatBuy: false,
    },
    WRITE_APP: {
      firstBuy: false,
      repeatBuy: false,
    },
  };
  constructor({ uid }) {
    this.userId = uid;
  }

  // 检测导流类型
  checkDiversionTypes({ respTypes, diversionType }): Promise<string> {
    return new Promise(resolve => {
      getSwitchFlag(respTypes)
        .then(res => {
          res.payload == '1' ? resolve(diversionType) : resolve('');
        })
        .catch(() => {
          resolve('');
        });
    });
  }

  checkUserCanJoin({ subject, switchsiteType }) {
    return new Promise(resolve => {
      const packageId = PACKAGEIDS[subject];
      let channels = CHANNELIDS[subject][switchsiteType];
      if (switchsiteType.indexOf(',') > -1) {
        const _si = switchsiteType.split(',');
        channels = _si.map(v => CHANNELIDS[subject][v]).join(',');
      }
      queryOrderByUserId({
        userId: this.userId,
        channels,
        subjects: subject,
        packageId,
      }).then(res => {
        this.checkSubject(res, subject);
        resolve('');
      });
    });
  }

  // subject=> MUSIC_APP WRITE_APP
  checkSubject(res, subject) {
    const {
      channelCheck,
      systemCheckMap,
      pluralCheckMap,
      subjectOrderMap,
      experienceCheckMap,
    } = res.payload;
    // experienceCheckMap.ART_APP为true 表示之前已经买过体验课了
    if (experienceCheckMap[subject]) {
      const iexperience =
        subjectOrderMap &&
        subjectOrderMap[subject] &&
        subjectOrderMap[subject].EXPERIENCE &&
        subjectOrderMap[subject].EXPERIENCE.length > 0;
      const iexperienceoneweek =
        subjectOrderMap &&
        subjectOrderMap[subject] &&
        subjectOrderMap[subject].EXPERIENCE_ONE_WEEK &&
        subjectOrderMap[subject].EXPERIENCE_ONE_WEEK.length > 0;
      if (iexperience || iexperienceoneweek) {
        const experience: never[] = iexperience
          ? subjectOrderMap[subject]['EXPERIENCE']
          : subjectOrderMap[subject]['EXPERIENCE_ONE_WEEK'];
        if (subject === 'WRITE_APP') {
          let supList = [];
          for (let i = 0; i < experience.length; i++) {
            supList.push(experience[i]['sup']);
          }
          this.supList = supList;
        }

        // 写字、美术需要进一步根据选择级别去判断是否可以重构 音乐没有级别判断(subjectOrderMap[subject].EXPERIENCE.length == 0)
      }
      if (channelCheck && !systemCheckMap[subject] && pluralCheckMap[subject]) {
        // 可以重复购买
        this.userOrderInfo[subject].repeatBuy = true;
      }
    } else {
      // experienceCheckMap.ART_APP为false 表示之前没有买过体验课了，是首单
      this.userOrderInfo[subject].firstBuy = true;
    }
  }

  async getDiversionType({
    switchsiteType,
    switchpageType,
  }: getDiversionTypeProps) {
    const res = await this.checkDiversionTypes(SWITCHFLAG[switchpageType]);
    if (res) {
      await this.checkUserCanJoin({ subject: 'MUSIC_APP', switchsiteType });
      await this.checkUserCanJoin({ subject: 'WRITE_APP', switchsiteType });
      // 优先导流音乐
      if (this.userOrderInfo.MUSIC_APP.firstBuy) {
        return 'yy_0.1';
      }
      if (this.userOrderInfo.WRITE_APP.firstBuy) {
        return 'xz_0.1';
      }
      if (this.userOrderInfo.MUSIC_APP.repeatBuy) {
        return 'yy_0.1';
      }
      if (this.userOrderInfo.WRITE_APP.repeatBuy) {
        return 'xz_0.1';
      }
      return '';
    }
  }
}

export default Diversion;
