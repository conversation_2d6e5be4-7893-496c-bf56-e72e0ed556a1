export const PACKAGEIDS = {
  ART_APP: '610',
  MUSIC_APP: '754',
  WRITE_APP: '124',
};

export const PAYTYPE = {
  ART_APP: 'AI_WXPROGRAM',
  MUSIC_APP: 'ART_TO_MUSIC_WXPROGRAM',
  WRITE_APP: 'ART_TO_WRITE_WXPROGRAM',
};

export const SUBJECBYDL = {
  'yy_0.1': 'MUSIC_APP',
  'xz_0.1': 'WRITE_APP',
};

/**
 *  订单页	=> 1
    购课后弹窗	=> 2
    购课后悬浮按钮	=> 3
 */

/**
 * art
 * 12706 默认渠道
 * 
 * music
 * 1=>订单页	12699	小熊美术订单页-加购
   2=>购课后弹窗	12700	小熊美术购课后-弹窗
   3=>购课后悬浮按钮	12701	小熊美术购课后-悬浮按钮

  write
  1=>订单页	12699	小熊美术订单页-加购
  2=>购课后弹窗	12700	小熊美术购课后-弹窗
  3=>购课后悬浮按钮	12701	小熊美术购课后-悬浮按钮

 */

export const CHANNELIDS = {
  ART_APP: {
    1: '12706',
  },
  MUSIC_APP: {
    1: '12699',
    2: '12700',
    3: '12701',
  },
  WRITE_APP: {
    1: '12702',
    2: '12703',
    3: '12704',
  },
};

// 订单页和老师页的两块导流，通过开关分别控制展示
export const SWITCHFLAG = {
  orderpage: {
    respTypes: 'msdl_xcx_dd',
    diversionType: 'msdl_xcx_dd',
    name: '订单页引流',
  },
  teacherpage: {
    respTypes: 'msdl_xcx_ls',
    diversionType: 'msdl_xcx_ls',
    name: '老师页引流',
  },
};
// 书法级别、美术级别对应关系
export const ARTSUPTOERITE = {
  S1: 'S1',
  S2: 'S3',
  S3: 'S2',
};
