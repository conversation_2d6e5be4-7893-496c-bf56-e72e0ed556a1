import Taro, { useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import { View, Image } from '@tarojs/components';
import { useDispatch, useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import { decodeUrl } from '@/utils';
import sensors from '@/utils/sensors_data';
import {
  getUserOpenIdSubject,
  getWeixinParamQrcodeImageUrlApi,
} from '@/api/groupbuy';
import yyIcon from '@/assets/art/addteacher/salesmodal/yyicon.gif';
import xzIcon from '@/assets/art/addteacher/salesmodal/xzicon.gif';
import SalesModal from './salesModal';
import usePay from '../commons/usePay';
import Diversion from '../utils/diversion';
import { ARTSUPTOERITE, CHANNELIDS } from '../utils/common';
import './index.scss';

export default function AddTeacher(props) {
  const router = useRouter();
  const dispatch = useDispatch();
  const params = router.params;
  const depar = decodeUrl(decodeURIComponent(props.tid)) as any;
  let { sup = '' } = params;
  const { payFlag, pay, paySuccess, payOrderId } = usePay();
  const showIcons = {
    'yy_0.1': yyIcon,
    'xz_0.1': xzIcon,
  };

  //redux
  const orderId = useSelector((state: UserStateType) => state.orderId);
  let orderid =
    params.orderIds || params.outTradeNo || params.orderId || orderId;

  let userId =
    params.uid || useSelector((state: UserStateType) => state.userid);

  //openid
  const openId = useSelector((state: UserStateType) => state.openid);
  const diversion = new Diversion({ uid: userId });

  if (Object.keys(depar).length > 1) {
    orderid = depar['orderIds'] || orderid;
    userId = depar['uid'] || userId;
    sup = depar['sup'] || sup;
  }

  // 老师微信号
  const [qrcodeNo, setQrcodeNo] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [switchsiteType, setSwitchsiteType] = useState('2');
  const [showDiversionType, setShowDiversionType] = useState('');

  // 获取美术公众号 5 已添加老师未关注公众号 1 未添加老师未关注公众号
  const getWeixinParamQrcodeImageUrl = outTradeNo => {
    getWeixinParamQrcodeImageUrlApi({
      uid: userId,
      type: 112,
      appsubject: 'ART_APP',
      channel: params.channel || '',
      orderIds: outTradeNo || orderid || '',
    }).then(res => {
      if (res.code === 0) {
        setQrcodeNo(res.payload);
      }
    });
  };

  const handleModal = async () => {
    sensors.track(
      'xxms_testcourse_appletaddteacherpage_diversionbuybuttonclick',
      {
        diversion_subject: showDiversionType === 'xz_0.1' ? '书法' : '音乐',
      },
    );
    const otherSubject =
      showDiversionType == 'xz_0.1' ? 'WRITE_APP' : 'MUSIC_APP';
    // 写字美术都是及时开课没有排期
    if (payFlag) {
      await pay({
        uid: userId,
        sup: sup,
        period: '0',
        topicId: 0,
        subjects: [otherSubject],
        switchsiteType,
      });
    }
  };

  const getDiversionType = async () => {
    if (orderid.includes(',')) {
      setShowDiversionType('');
      getWeixinParamQrcodeImageUrl(orderid);
      return;
    }
    const res = await diversion.getDiversionType({
      switchsiteType: '2,3',
      switchpageType: 'teacherpage',
    });
    console.log(res, '===============');

    if (res) {
      // 判断可复购且写字导流是否已购买过同级别
      const _writeSup = ARTSUPTOERITE[sup];
      if (
        sup &&
        diversion.userOrderInfo.WRITE_APP.repeatBuy &&
        diversion.supList.includes(_writeSup) &&
        res == 'xz_0.1'
      ) {
        setShowDiversionType('');
        getWeixinParamQrcodeImageUrl(orderid);
      } else {
        // 先获取二维码 只有美术的(用户关闭弹窗不必再次获取)
        await getWeixinParamQrcodeImageUrl(orderid);
        setShowDiversionType(res);
        setShowModal(true);
        sensors.track('xxms_testcourse_appletaddteacherpage_diversionview', {
          diversion_subject: res === 'xz_0.1' ? '书法' : '音乐',
        });
      }
    } else {
      setShowDiversionType('');
      getWeixinParamQrcodeImageUrl(orderid);
    }
  };

  const getMergeQr = async (oid: string) => {
    if (oid) {
      const _orderids = [orderid, oid].join(',');
      await getWeixinParamQrcodeImageUrl(_orderids);
    }
    setShowDiversionType('');
    setShowModal(false);
  };

  useEffect(() => {
    // getQrByUserWxStatus();
    getDiversionType();
  }, [openId]);

  useEffect(() => {
    if (paySuccess) {
      getMergeQr(payOrderId);
    }
  }, [paySuccess]);

  // 获取h5跳过来新用户openId
  useEffect(() => {
    if (!openId) {
      Taro.login({
        success: function(res) {
          if (res.code) {
            getUserOpenIdSubject({
              code: res.code,
              channel: router.params.channelId || CHANNELIDS['ART_APP'][1],
              subject: 'ART_APP',
            }).then(data => {
              if (data.code === 0) {
                if (data.payload.uid) {
                  dispatch({
                    type: 'CHANGE_USERID',
                    userid: data.payload.uid,
                  });
                } else {
                  dispatch({ type: 'CHANGE_USERID', userid: '' });
                }
                data.payload.mobile &&
                  dispatch({
                    type: 'CHANGE_MOBILE',
                    mobile: data.payload.mobile,
                  });
                dispatch({
                  type: 'CHANGE_OPENID',
                  openid: data.payload.openid,
                });
                dispatch({
                  type: 'CHANGE_UNIONID',
                  unionId: data.payload.unionid,
                });
                if (data.payload.token)
                  Taro.setStorageSync('appToken', data.payload.token);
              }
            });
          } else {
            console.log('登录失败！' + res.errMsg);
          }
        },
      });
    }
  }, []);

  return (
    <View className='add-teacher'>
      <View className='card'>
        <View className='without-wechat-title'>添加老师 学习无忧</View>
        <View className='desc broswer-desc'>请务必添加老师激活课程</View>
        <View className='qr-wrap'>
          <Image
            src={qrcodeNo}
            mode='widthFix'
            showMenuByLongpress
            onLongPress={() => {
              sensors.track(
                'xxms_testcourse_appletaddteacherpage_Longpresscode',
                {
                  buy_model: 'model_4',
                },
              );
            }}
          />
        </View>
        <View className='desc broswer-desc'>长按识别关注后即可添加老师</View>

        {showDiversionType && (
          <View
            className='show-icon'
            onClick={() => {
              setSwitchsiteType('3');
              setShowModal(true);
              sensors.track(
                'xxms_testcourse_appletaddteacherpage_activitybuttonclick',
              );
            }}
          >
            <Image src={showIcons[showDiversionType]} />
          </View>
        )}
        <SalesModal
          show={showModal}
          subject={showDiversionType == 'yy_0.1' ? 'MUSIC_APP' : 'WRITE_APP'}
          close={() => setShowModal(false)}
          handleOk={handleModal}
        />
      </View>
    </View>
  );
}
