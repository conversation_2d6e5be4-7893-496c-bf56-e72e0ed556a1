import { View, Image } from '@tarojs/components';
import yy1 from '@/assets/art/addteacher/salesmodal/yy1.png';
import xz1 from '@/assets/art/addteacher/salesmodal/xz1.png';
import xzbtn from '@/assets/art/addteacher/salesmodal/xzbtn.gif';
import yybtn from '@/assets/art/addteacher/salesmodal/yybtn.gif';
import './index.scss';

export default ({ subject, show, close, handleOk }) => {
  const showImg = {
    MUSIC_APP: {
      viewimg: yy1,
      btn: yybtn,
    },
    WRITE_APP: {
      viewimg: xz1,
      btn: xzbtn,
    },
  };

  return (
    <>
      {show && (
        <View className='sales-modal'>
          {/* <View className='sales-modal-popup'></View> */}
          <View className='sales-modal-section'>
            <View className='sales-modal-section_box'>
              <View
                className='sales-modal-section-close'
                onClick={close}
              ></View>
              <View className='sales-modal-section-contain'>
                <View className='sales-modal-section-contain_view'>
                  <Image src={showImg[subject].viewimg} mode='widthFix' />
                </View>
                <View
                  className='sales-modal-section-contain_btn'
                  onClick={handleOk}
                >
                  <Image src={showImg[subject].btn} mode='widthFix' />
                </View>
              </View>
            </View>
          </View>
        </View>
      )}
    </>
  );
};
