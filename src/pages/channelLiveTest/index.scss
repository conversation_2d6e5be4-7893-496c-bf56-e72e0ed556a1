.channel-live-test {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  &__header {
    margin-bottom: 20px;
    text-align: center;
  }

  &__title {
    display: block;
    font-size: 22px;
    font-weight: bold;
    color: #1a1a1a;
    margin-bottom: 12px;
    line-height: 1.3;
  }

  &__channel-id,
  &__time-range {
    display: block;
    background: #e9f5ff;
    padding: 8px 12px;
    border-radius: 6px;
    margin-bottom: 10px;
    font-size: 13px;
    color: #1890ff;
    line-height: 1.4;
  }

  &__warning {
    background: #fffbe6;
    border: 1px solid #ffe58f;
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    text-align: center;

    &-text {
      display: block;
      color: #faad14;
      font-size: 14px;
      line-height: 1.5;
      margin-bottom: 8px;
    }

    &-tip {
      display: block;
      color: #d48806;
      font-size: 12px;
      line-height: 1.4;
    }
  }

  &__controls {
    margin: 15px 0;
    text-align: center;
  }

  &__btn {
    background: #1890ff !important;
    color: white !important;
    border-radius: 20px;
    font-size: 15px;
    box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
    transition: all 0.2s;
    border: none;
    padding: 12px 24px;

    &:active {
      transform: scale(0.98);
      background: #40a9ff !important;
    }
    
    &[disabled] {
      background: #bae7ff !important;
      box-shadow: none;
    }
  }

  &__error {
    background: #fff2f0;
    border: 1px solid #ffccc7;
    padding: 12px;
    border-radius: 6px;
    margin: 10px 0;
    color: #f5222d;
    font-size: 13px;
    text-align: center;
    line-height: 1.5;
  }

  &__content {
    margin: 20px 0;
  }

  &__info {
    background: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    &-title {
      display: block;
      font-weight: bold;
      color: #1890ff;
      font-size: 14px;
      margin-bottom: 10px;
    }

    &-item {
      display: block;
      margin-bottom: 8px;
      font-size: 14px;
      color: #595959;
      line-height: 1.5;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  &__live-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    margin-bottom: 15px;
  }

  &__live-label {
    display: block;
    padding: 10px 15px;
    background: #f0f7ff;
    font-size: 14px;
    color: #1890ff;
    font-weight: 500;
  }

  &__live-wrapper {
    position: relative;
  }

  &__live-component {
    width: 100%;
    height: 200px;
    display: block;
  }

  &__live-placeholder {
    width: 100%;
    height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #f5f5f5;
    border: 2px dashed #d9d9d9;
    padding: 20px;
    box-sizing: border-box;
  }

  &__placeholder-text {
    font-size: 16px;
    font-weight: bold;
    color: #666;
    margin-bottom: 8px;
  }

  &__placeholder-note {
    font-size: 12px;
    color: #999;
    text-align: center;
    line-height: 1.4;
    margin-bottom: 12px;
  }

  &__placeholder-info {
    font-size: 11px;
    color: #1890ff;
    margin-bottom: 4px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__jump-info {
    background: #f6ffed;
    border: 1px solid #b7eb8f;
    padding: 12px 15px;
    border-radius: 6px;
    margin-bottom: 20px;

    &-title {
      display: block;
      font-weight: bold;
      color: #52c41a;
      margin-bottom: 5px;
      font-size: 13px;
    }

    &-content {
      display: block;
      font-size: 15px;
      color: #237804;
      line-height: 1.4;
    }
  }

  &__component-demo {
    margin: 20px 0;
  }

  &__demo-title {
    display: block;
    font-weight: bold;
    color: #1890ff;
    font-size: 16px;
    margin-bottom: 10px;
  }

  &__player {
    margin-top: 10px;
  }

  &__instructions,
  &__requirements {
    background: white;
    padding: 15px;
    border-radius: 8px;
    margin-top: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  &__section-title {
    display: block;
    font-weight: bold;
    margin-bottom: 10px;
    color: #1890ff;
    font-size: 14px;
  }

  &__instruction-item,
  &__requirement-item {
    display: block;
    margin-bottom: 8px;
    padding-left: 15px;
    position: relative;
    font-size: 13px;
    line-height: 1.6;
    color: #595959;

    &::before {
      content: '•';
      position: absolute;
      left: 0;
      color: #1890ff;
      font-weight: bold;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  // 响应式设计
  @media (max-width: 375px) {
    padding: 15px;

    &__title {
      font-size: 20px;
    }

    &__channel-id,
    &__time-range {
      font-size: 12px;
      padding: 6px 10px;
    }

    &__live-component {
      height: 180px;
    }

    &__info,
    &__instructions,
    &__requirements {
      padding: 12px;
    }
  }

  // 深色模式适配
  @media (prefers-color-scheme: dark) {
    background: #1a1a1a;
    color: #fff;

    &__title {
      color: #fff;
    }

    &__info,
    &__instructions,
    &__requirements {
      background: #2a2a2a;
      color: #fff;
    }

    &__live-container {
      background: #2a2a2a;
    }

    &__live-label {
      background: #3a3a3a;
    }
  }
}
