import React, { useEffect, useState } from 'react';
import { View, Text, Button } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { ChannelLiveInfo } from '@/types/types';
import {
  checkChannelLiveSupport,
  getChannelsLiveInfo,
  generateTimeRange,
  getLiveStatusText,
  getReplayStatusText,
  getJumpPageText,
  formatTimestamp,
} from '@/utils/channelLive';
import ChannelLivePlayer from '@/components/ChannelLivePlayer';
import './index.scss';

const ChannelLiveTest: React.FC = () => {
  const [liveInfo, setLiveInfo] = useState<ChannelLiveInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [compatible, setCompatible] = useState(true);

  // 使用文档提供的测试视频号ID
  const VIDEO_CHANNEL_ID = 'sphiIWE56KZ1yZD';
  const { startTime, endTime } = generateTimeRange();

  // 检测基础库版本
  useEffect(() => {
    const isSupported = checkChannelLiveSupport();
    setCompatible(isSupported);

    if (!isSupported) {
      setError('当前微信版本过低，请升级至最新版本（基础库需≥2.29.0）');
    }
  }, []);

  // 获取直播信息
  const fetchLiveInfo = async () => {
    if (!compatible) return;

    setLoading(true);
    setError('');

    try {
      const info = await getChannelsLiveInfo(
        VIDEO_CHANNEL_ID,
        startTime,
        endTime,
      );
      setLiveInfo(info);
    } catch (err) {
      const errorMsg = err.message || '获取直播信息失败';
      setError(errorMsg);
      Taro.showToast({
        title: errorMsg,
        icon: 'none',
        duration: 2000,
      });
    } finally {
      setLoading(false);
    }
  };

  // 处理组件错误回调
  const handleComponentError = (errorMsg: string) => {
    console.error('ChannelLivePlayer组件错误:', errorMsg);
  };

  // 处理状态变化回调
  const handleStatusChange = (status: number) => {
    console.log('直播状态变化:', status, getLiveStatusText(status));
  };

  return (
    <View className='channel-live-test'>
      <View className='channel-live-test__header'>
        <Text className='channel-live-test__title'>视频号直播嵌入测试</Text>
        <Text className='channel-live-test__channel-id'>
          视频号ID: {VIDEO_CHANNEL_ID}
        </Text>
        <Text className='channel-live-test__time-range'>
          查询时间段: {formatTimestamp(startTime)} - {formatTimestamp(endTime)}
        </Text>
      </View>

      {!compatible ? (
        <View className='channel-live-test__warning'>
          <Text className='channel-live-test__warning-text'>{error}</Text>
          <Text className='channel-live-test__warning-tip'>
            请升级微信客户端至最新版本
          </Text>
        </View>
      ) : (
        <>
          <View className='channel-live-test__controls'>
            <Button
              className='channel-live-test__btn'
              onClick={fetchLiveInfo}
              loading={loading}
              disabled={loading}
            >
              {loading ? '获取中...' : '获取直播信息'}
            </Button>
          </View>

          {error && (
            <View className='channel-live-test__error'>
              <Text>{error}</Text>
            </View>
          )}

          {liveInfo && (
            <View className='channel-live-test__content'>
              <View className='channel-live-test__info'>
                <Text className='channel-live-test__info-title'>
                  直播信息详情:
                </Text>
                <Text className='channel-live-test__info-item'>
                  直播状态: {getLiveStatusText(liveInfo.status)}
                </Text>
                {liveInfo.status === 3 && (
                  <Text className='channel-live-test__info-item'>
                    回放状态: {getReplayStatusText(liveInfo.replayStatus)}
                  </Text>
                )}
                <Text className='channel-live-test__info-item'>
                  Feed ID: {liveInfo.feedId || '无'}
                </Text>
              </View>

              <View className='channel-live-test__live-container'>
                <Text className='channel-live-test__live-label'>
                  原生ChannelLive组件:
                </Text>
                <View className='channel-live-test__live-wrapper'>
                  {/* 注意：ChannelLive组件在Taro 3.3.9中可能不可用，需要升级到更新版本 */}
                  <View className='channel-live-test__live-placeholder'>
                    <Text className='channel-live-test__placeholder-text'>
                      视频号直播组件占位符
                    </Text>
                    <Text className='channel-live-test__placeholder-note'>
                      实际使用时需要升级Taro版本或使用原生小程序组件
                    </Text>
                    <Text className='channel-live-test__placeholder-info'>
                      Feed ID: {liveInfo.feedId}
                    </Text>
                    <Text className='channel-live-test__placeholder-info'>
                      视频号: {VIDEO_CHANNEL_ID}
                    </Text>
                  </View>
                </View>
              </View>

              <View className='channel-live-test__jump-info'>
                <Text className='channel-live-test__jump-title'>
                  点击封面将跳转至:
                </Text>
                <Text className='channel-live-test__jump-content'>
                  {getJumpPageText(liveInfo.status, liveInfo.replayStatus)}
                </Text>
              </View>
            </View>
          )}

          <View className='channel-live-test__component-demo'>
            <Text className='channel-live-test__demo-title'>封装组件演示:</Text>
            <ChannelLivePlayer
              finderUserName={VIDEO_CHANNEL_ID}
              feedId={liveInfo?.feedId}
              className='channel-live-test__player'
              onError={handleComponentError}
              onStatusChange={handleStatusChange}
            />
          </View>

          <View className='channel-live-test__instructions'>
            <Text className='channel-live-test__section-title'>使用说明:</Text>
            <Text className='channel-live-test__instruction-item'>
              1. 点击"获取直播信息"按钮查询直播状态
            </Text>
            <Text className='channel-live-test__instruction-item'>
              2. 根据直播状态显示对应封面组件
            </Text>
            <Text className='channel-live-test__instruction-item'>
              3. 点击封面将跳转至视频号相应页面
            </Text>
            <Text className='channel-live-test__instruction-item'>
              4. 不同直播状态跳转页面不同（见上方说明）
            </Text>
          </View>

          <View className='channel-live-test__requirements'>
            <Text className='channel-live-test__section-title'>技术要求:</Text>
            <Text className='channel-live-test__requirement-item'>
              • 基础库版本 ≥ 2.29.0
            </Text>
            <Text className='channel-live-test__requirement-item'>
              • 需在app.config.ts中声明权限
            </Text>
            <Text className='channel-live-test__requirement-item'>
              • 需要视频号ID和有效的直播feedId
            </Text>
          </View>
        </>
      )}
    </View>
  );
};

export default ChannelLiveTest;
