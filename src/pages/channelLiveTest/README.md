# 微信小程序视频号直播功能实现指南

## 概述

本项目为小马AI学新增了微信小程序内嵌视频号直播的完整功能实现。由于当前项目使用的Taro版本（3.3.9）可能还不支持`ChannelLive`组件，本实现提供了完整的架构和占位符，便于后续升级。

## 功能特性

- ✅ 基础库版本兼容性检测（≥2.29.0）
- ✅ 视频号直播信息获取
- ✅ 直播状态管理和显示
- ✅ 错误处理和用户提示
- ✅ 可复用的组件设计
- ✅ 完整的测试页面
- ⚠️ ChannelLive组件占位符（需要Taro版本升级）

## 文件结构

```
src/
├── components/ChannelLivePlayer/     # 可复用直播组件
│   ├── index.tsx                     # 组件主文件
│   └── index.scss                    # 组件样式
├── pages/channelLiveTest/            # 测试页面
│   ├── index.tsx                     # 页面主文件
│   ├── index.config.ts               # 页面配置
│   ├── index.scss                    # 页面样式
│   └── README.md                     # 说明文档
├── utils/channelLive.ts              # 工具函数
├── types/types.ts                    # 类型定义（已扩展）
└── app.config.ts                     # 应用配置（已更新）
```

## 使用方法

### 1. 基本使用

```tsx
import ChannelLivePlayer from '@/components/ChannelLivePlayer';

const MyPage = () => {
  return (
    <ChannelLivePlayer
      finderUserName="sphiIWE56KZ1yZD"  // 视频号ID
      feedId="可选的直播feedId"
      onError={(error) => console.error(error)}
      onStatusChange={(status) => console.log('状态变化:', status)}
    />
  );
};
```

### 2. 手动获取直播信息

```tsx
import { getChannelsLiveInfo, generateTimeRange } from '@/utils/channelLive';

const fetchLiveInfo = async () => {
  const { startTime, endTime } = generateTimeRange();
  try {
    const info = await getChannelsLiveInfo('视频号ID', startTime, endTime);
    console.log('直播信息:', info);
  } catch (error) {
    console.error('获取失败:', error);
  }
};
```

## 升级到真正的ChannelLive组件

当Taro版本升级到支持`ChannelLive`组件时，需要进行以下修改：

### 1. 更新组件导入

```tsx
// 将这行：
import { View, Text } from '@tarojs/components';

// 改为：
import { View, Text, ChannelLive } from '@tarojs/components';
```

### 2. 替换占位符

将占位符代码：

```tsx
<View className="channel-live-player__placeholder">
  {/* 占位符内容 */}
</View>
```

替换为真正的组件：

```tsx
<ChannelLive
  feedId={liveInfo.feedId}
  finderUserName={finderUserName}
  className="channel-live-player__component"
/>
```

### 3. 检查Taro版本

确保Taro版本支持ChannelLive组件：

```bash
# 检查当前版本
npm list @tarojs/components

# 升级到最新版本（如果需要）
npm update @tarojs/components @tarojs/taro @tarojs/cli
```

## 配置要求

### 1. app.config.ts 配置

已自动添加以下配置：

```typescript
{
  requiredPrivateInfos: ['chooseAddress', 'getChannelsLiveInfo'],
  permission: {
    'scope.userFuzzyLocation': {
      desc: '你的位置信息将用于提供更好的直播服务'
    }
  }
}
```

### 2. 微信开发者工具设置

确保在微信开发者工具中：
- 基础库版本设置为 2.29.0 或更高
- 启用"不校验合法域名"（开发阶段）

## API 说明

### getChannelsLiveInfo

获取视频号直播信息的核心API。

```typescript
interface ChannelLiveInfo {
  errMsg: string;
  status: number;        // 0-未知，1-直播中，2-未开始，3-已结束，4-禁播，5-暂停，6-异常
  replayStatus: number;  // 0-未知，1-回放生成中，2-回放生成完成，3-回放生成失败
  feedId: string;        // 直播feedId
}
```

### 直播状态说明

| 状态码 | 状态描述 | 跳转页面 |
|--------|----------|----------|
| 1 | 直播中 | 直播页面 |
| 2 | 未开始 | 上一场直播的结束页 |
| 3 | 已结束 | 直播结束页/回放页（取决于回放状态） |
| 4 | 禁播 | 相应提示页面 |
| 5 | 暂停 | 暂停页面 |
| 6 | 异常 | 错误页面 |

## 测试说明

访问测试页面：`pages/channelLiveTest/index`

测试功能包括：
- 基础库版本检测
- 直播信息获取
- 组件状态展示
- 错误处理验证

## 注意事项

1. **版本兼容性**：确保微信基础库版本 ≥ 2.29.0
2. **权限配置**：已在app.config.ts中配置必要权限
3. **组件升级**：当前使用占位符，需要Taro版本升级后替换
4. **测试环境**：建议在真机上测试，模拟器可能不支持某些功能

## 故障排除

### 1. 基础库版本过低
- 提示用户升级微信客户端
- 检查开发者工具中的基础库设置

### 2. 获取直播信息失败
- 检查视频号ID是否正确
- 确认时间范围参数
- 验证网络连接

### 3. 组件不显示
- 确认Taro版本是否支持ChannelLive
- 检查组件导入是否正确
- 查看控制台错误信息

## 后续优化建议

1. **性能优化**：添加直播信息缓存机制
2. **用户体验**：增加更多交互反馈
3. **功能扩展**：支持多个视频号同时展示
4. **监控统计**：添加使用情况统计

## 联系支持

如有问题，请联系开发团队或查看微信小程序官方文档。
