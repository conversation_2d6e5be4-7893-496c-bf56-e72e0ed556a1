page {
  background: #f0f0f0;
}
.content {
  position: relative;
  .view-bg {
    width: 100%;
    position: absolute;
    height: 1448px;
  }
  .b-dy.finish {
    padding-top: 36px;
    position: relative;
    .head-img {
      margin-left: 24px;
      display: flex;
      align-items: center;
      font-size: 36px;
      color: #fff;
      margin-bottom: 855px;
      .img {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        margin-top: 6px;
        margin-right: 24px;
      }
    }
  }
  .head-part {
    padding-top: 364px;
    text-align: center;
    font-size: 36px;
    color: #000;
    position: relative;
    .head-img {
      position: relative;
      width: 350px;
      height: 160px;
      margin: 0 auto;
      overflow: visible;
      .head-part-bg {
        position: absolute;
        width: 100%;
        height: 160px;
        left: 0;
      }
      .img {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        margin-top: 6px;
      }
    }
  }
  .submit {
    width: 631px;
    height: 88px;
    line-height: 88px;
    border-radius: 88px;
    text-align: center;
    color: #fff;
    font-size: 36px;
    margin: 500px auto 0;
    background: linear-gradient(to bottom, #ff8369, #ff1919);
    animation: scaleJumpbtn 1s linear infinite;
    position: relative;
    &.mt {
      margin-top: 550px;
    }
  }
  .time {
    font-size: 28px;
    font-weight: 400;
    text-align: center;
    margin-top: 40px;
    position: relative;
  }
  @keyframes scaleJumpbtn {
    0% {
      transform: scale(0.93);
    }
    50% {
      transform: scale(1);
    }
    100% {
      transform: scale(0.93);
    }
  }
  .custom-time-main .at-countdown__item:last-child {
    display: inline-flex;
  }
  .giveMask .at-modal__container {
    width: 580px;
    overflow: visible;
  }
  .coupon-content {
    width: 100%;
    background: #fff;
    border-radius: 20px;
    margin: 0px auto;
    margin-top: 12px;
    height: 520px;
    box-sizing: border-box;
    padding: 20px 7px 78px;
    text-align: center;
    position: relative;
    .title {
      font-size: 32px;
      margin-bottom: 40px;
    }
    .titleBox {
      display: flex;
      justify-content: center;
      //   align-items: center;
      box-sizing: border-box;
      .leftImg {
        width: 45px;
        height: 20px;
        margin-right: 24px;
        image {
          width: 100%;
          height: 100%;
        }
      }
      .teacher {
        font-size: 32px;
        font-weight: 500;
        color: #333;
        Text {
          color: #fa6c3a;
        }
      }
      .rightImg {
        width: 45px;
        height: 20px;
        margin-left: 24px;
        image {
          width: 100%;
          height: 100%;
        }
      }
    }
    .coupon-img {
      width: 500px;
      height: 171px;
      margin: 40px auto 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      .bg-img {
        position: absolute;
        width: 100%;
        height: 100%;
      }
      .coupon-con {
        width: 200px;
        text-align: left;
        position: relative;
        &-title {
          font-size: 22px;
          font-weight: bold;
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          Text {
            height: 21px;
            font-size: 14px;
            color: #fa6c3a;
            background: #feede7;
            padding: 0 4px;
            border-radius: 6px;
          }
        }
        &-time {
          font-size: 16px;
          margin: 4px 0 14px;
        }
        &-gray {
          font-size: 16px;
          color: #999999;
          padding-right: 20px;
        }
      }
    }
    .coupon-info {
      width: 375px;
      height: 44px;
      line-height: 44px;
      text-align: center;
      margin: 18px auto;
      border-radius: 44px;
      background: #fff2ef;
      font-size: 24px;
      color: #f45029;
    }
    .close {
      position: absolute;
      bottom: -96px;
      width: 64px;
      height: 64px;
      left: 50%;
      margin-left: -32px;
    }
    .jump-mini--btn {
      width: 400px;
      height: 88px;
      line-height: 88px;
      text-align: center;
      color: #fff;
      font-size: 32px;
      border-radius: 88px;
      margin: 0 auto;
      background: linear-gradient(to right, #ffa300, #ff6a00);
      animation: scaleDrew 1.8s ease-in-out infinite;
    }
    @keyframes scaleDrew {
      /* 定义关键帧、scaleDrew是需要绑定到选择器的关键帧名称 */
      0% {
        transform: scale(1);
      }

      25% {
        transform: scale(1.05);
      }

      50% {
        transform: scale(1);
      }

      75% {
        transform: scale(1.05);
      }
    }
  }
}
