import { AtCountdown, AtModal } from 'taro-ui';

import sensors from '@/utils/sensors_data';
import { useDispatch, useSelector } from 'react-redux';
import { useEffect, useMemo, useState } from 'react';
import { UserStateType } from '@/store/groupbuy/state';
import { View, Text, Button, Image } from '@tarojs/components';
import Taro, { useRouter, useShareAppMessage } from '@tarojs/taro';
import haedBg from '@/assets/midCoupon/index-head-bg.png';
import viewBg from '@/assets/midCoupon/index-bg.png';
import couponContent from '@/assets/midCoupon/coupon-content.png';
import rightImg from '@/assets/midCoupon/rightImg.png';
import leftImg from '@/assets/midCoupon/leftImg.png';
import finishBg from '@/assets/midCoupon/index-finish-bg.png';
import {
  getUserCouponByPackageId,
  getProgramUserSubject,
  getUser,
  getFriendCoupon,
} from '@/api/groupbuy';
import WxLogin from '@/components/wxlogin';
import closeImg from '@/assets/pictureBookSimple/icon_close.png';
import './index.scss';

const Indexwebview = () => {
  const shareImg =
    'https://fe-cdn.xiaoxiongmeishu.com/apph5/video/discount-share-bg.jpg';
  const defaultHead = 'https://s1.xiaoxiongmeishu.com/image/bear_head.png';
  const dispatch = useDispatch();
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  //openid
  const openId = useSelector((state: UserStateType) => state.openid);
  const router = useRouter();
  const [sendId, setsendId] = useState('');
  const [endTime, setendTime] = useState('');
  const [userInfo, setuserInfo] = useState<any>({});
  const [showGiveMask, setShowGiveMask] = useState(false);
  const [isFull, setIsfull] = useState(false);
  const _NODE_ENV = process.env.NODE_ENV;
  const disPackagesId = {
    dev: 7698,
    test: 8932,
    online: 1733,
  };
  useEffect(() => {
    var _sendId;
    if (router.params.scene) {
      _sendId = decodeURIComponent(router.params.scene).split('=');
      setsendId(_sendId[1]);
    }
    if (router.params.sendId) setsendId(router.params.sendId);
    sensors.track('xxys_testcoursecouponpage_view', {
      entrance_page: router.params.entrance_page || '',
      channel_id: router.params.channelId || '16074',
      refer_id: sendId,
    });
  }, []);

  const getConponHandler = () => {
    if (!sendId && userId == null) return;
    getUserCouponByPackageId(
      userId ? userId : sendId,
      disPackagesId[_NODE_ENV],
    ).then(res => {
      //   res.payload = [{"couponId":"328","couponUserId":"871755103575609344","amount":26.0,"name":"体验课优惠券","status":"NOACTIVE","startDate":"1688009078750","endDate":"1688572800000","description":"测试","trainingCampCouponId":"0","stack":"STACKABLE","isMust":0}];
      if (res.payload.length) {
        let i = res.payload.findIndex(o => o.newCouponId == '328');
        if (i > -1 && res.payload[i].endDate > new Date().getTime()) {
          setendTime(res.payload[i].endDate);
          setIsfull(true);
        }
      } else {
        setIsfull(false);
        setendTime('');
      }
    });
  };

  useEffect(() => {
    if (sendId || (!sendId && userId)) {
      getConponHandler();
      getUserInfo();
    }
  }, [sendId, userId]);

  const getUserInfo = async () => {
    if (!sendId && userId == null) return;
    const { payload } = await getUser({ userId: sendId || userId });
    setuserInfo(payload);
  };

  useShareAppMessage(res => {
    if (res.from === 'button') {
      // 来自页面内转发按钮
      console.log(res.target);
    } else
      sensors.track('xxys_testcoursecouponpage_shareclick', {
        operation_type: '右上角转发给朋友',
      });

    var channel = '16074';
    if (router.params.channelId) channel = router.params.channelId;
    const title = '送你1张亲友券，9.9元学10节美术创意课，还免费送画材礼包';
    return {
      title,
      path: `/pages/normalGroup/art/discount9_9/index?sendId=${
        userId ? userId : sendId
      }&channelId=${channel}&msChannelId=${channel}`,
      imageUrl: shareImg,
    };
  });

  // 获取用户手机号
  const getPhoneNumber = res => {
    if (res.detail.errMsg === 'getPhoneNumber:fail user deny') {
      console.log(res.detail.errMsg);
    } else {
      const { encryptedData, iv } = res.detail;
      // getProgramUserSubject兼容getProgramUser
      getProgramUserSubject({
        openId,
        encryptedData,
        iv,
        subject: 'ART_APP',
      }).then(phone => {
        if (phone.payload.uid) {
          dispatch({
            type: 'CHANGE_USERID',
            userid: phone.payload.uid,
          });
        }
        if (phone.payload.token)
          Taro.setStorageSync('appToken', phone.payload.token);
        achieveHandler(phone.payload.uid);
        phone.payload.mobile &&
          dispatch({
            type: 'CHANGE_MOBILE',
            mobile: phone.payload.mobile,
          });
      });
    }
  };
  const timeInfo = useMemo(() => {
    const date = new Date().getTime();
    var second = Math.floor((+endTime - date) / 1000);
    // 天数
    var day = Math.floor(second / 3600 / 24);
    // 小时
    var hours = Math.floor((second / 3600) % 24);
    // 分钟
    var minutes = Math.floor((second / 60) % 60);
    // 秒
    var seconds = Math.floor(second % 60);
    return { day, hours, minutes, seconds };
  }, [endTime]);

  const achieveHandler = (uid = userId) => {
    sensors.track('xxys_testcoursecouponpage_receiveclick', {});
    getFriendCoupon({
      userId: uid,
    }).then(res => {
      if (!res.code) {
        setIsfull(true);
        setShowGiveMask(true);
        getConponHandler();
      } else
        Taro.showToast({
          title: res.errors || '领取失败',
          icon: 'none',
          duration: 2000,
        });
    });
  };

  return (
    <View className='content'>
      <WxLogin subject='ART_APP' noAskNoaddress />
      <Image className='view-bg' src={!isFull ? viewBg : finishBg} />
      {!isFull ? (
        <View className='b-dy'>
          <View className='head-part'>
            <View className='head-img'>
              <Image className='head-part-bg' src={haedBg} />
              <Image src={userInfo.head || defaultHead} className='img' />
            </View>
            {userInfo.username || '小熊用户'}
          </View>
          {userId ? (
            <View
              className={!endTime ? 'submit mt' : 'submit'}
              onClick={() => achieveHandler(userId)}
            >
              立即领取
            </View>
          ) : (
            <Button
              className={!endTime ? 'submit mt' : 'submit'}
              open-type='getPhoneNumber'
              onGetPhoneNumber={getPhoneNumber}
            >
              立即赠送
            </Button>
          )}
        </View>
      ) : (
        <View className='b-dy finish'>
          <View className='finished'>
            <View className='head-img'>
              <Image src={userInfo.head || defaultHead} className='img' />
              {userInfo.username || '小熊用户'}
            </View>
            <Button
              className={!endTime ? 'submit mt' : 'submit'}
              open-type='share'
              onClick={() => {
                sensors.track('xxys_testcoursecouponpage_shareclick', {
                  operation_type: '页面上立即赠送',
                });
              }}
            >
              立即赠送
            </Button>
          </View>
        </View>
      )}
      {endTime && (
        <View className='time'>
          有效期{' '}
          <AtCountdown
            className='custom-time-main'
            isShowDay
            key={timeInfo.seconds}
            day={timeInfo.day}
            hours={timeInfo.hours}
            minutes={timeInfo.minutes}
            seconds={timeInfo.seconds}
            format={{ day: ':', hours: ':', minutes: ':', seconds: '' }}
          />{' '}
          结束
        </View>
      )}
      <AtModal
        className='giveMask'
        isOpened={showGiveMask}
        closeOnClickOverlay={false}
        onClose={() => {
          setShowGiveMask(false);
        }}
      >
        <View className='coupon-content'>
          <View className='title'>金币已到账</View>
          <View className='titleBox'>
            <View className='leftImg'>
              <Image src={leftImg} />
            </View>
            <Text className='teacher'>
              恭喜获得<Text>体验课亲友券</Text>
            </Text>
            <View className='rightImg'>
              <Image src={rightImg} />
            </View>
          </View>
          <View className='coupon-img'>
            <Image className='bg-img' src={couponContent}></Image>
            <View className='coupon-con'>
              <View className='coupon-con-title'>
                亲友学仅9.9元 <Text>限1张</Text>
              </View>
              <View className='coupon-con-gray'>
                小熊美术双周体验课，额外送画材礼包
              </View>
            </View>
          </View>
          <Button
            openType='share'
            className='jump-mini--btn'
            onClick={() => {
              setShowGiveMask(false);
              sensors.track('xxys_testcoursecouponpage_shareclick', {
                operation_type: '弹窗上立即赠送',
              });
            }}
          >
            立即赠送
          </Button>
          <Image
            className='close'
            src={closeImg}
            onClick={() => setShowGiveMask(false)}
          ></Image>
        </View>
      </AtModal>
    </View>
  );
};
export default Indexwebview;
