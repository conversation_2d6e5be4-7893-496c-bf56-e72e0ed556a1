/* eslint-disable @typescript-eslint/no-shadow */
import Taro, {
  requirePlugin,
  useRouter,
  useShareAppMessage,
} from '@tarojs/taro';
import { useEffect, useState } from 'react';
import { useLogin } from '@/hooks/loginhook/useLogin';
import { Button, Image, View } from '@tarojs/components';
import { useDispatch, useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import { useThirtySixPay } from '@/hooks/alipay/useThirtySixPay';

// @ts-ignore
import arrowRight from '@/assets/pay/arrow-right.png';
import S1 from '@/assets/payOrder/S1.png';
import S2 from '@/assets/payOrder/S2.png';
import S3 from '@/assets/payOrder/S3.png';
import S5 from '@/assets/payOrder/s5.png';
import select from '@/assets/payOrder/select.png';
import alipay from '@/assets/payOrder/alipay.png';
import pay from '@/assets/payOrder/pay.png';
import { decryptAliUserApi, createUserAddress, getSupManagements } from '@/api/groupbuy';
import sensors from '@/utils/sensors_data';

import './index.scss';

const supMap = [
  {
    title: 'S1（适合3-4.5岁）',
    sup: 'S1'
  },
  {
    title: 'S2（适合4.5-7岁）',
    sup: 'S2'
  },
  {
    title: 'S3（适合7+岁）',
    sup: 'S3'
  }
]

export default () => {
  const { params } = useRouter();

  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  const aliUserId = useSelector((state: UserStateType) => state.aliUserId);
  const supImageMap = {
    S1,
    S2,
    S3
  }
  //redux
  const dispatch = useDispatch();
  const { iLoading } = useLogin({ subject: 'ART_APP' });
  const plugin = requirePlugin('tradePay');
  const [addressInfo, setAddressInfo] = useState<any>({});
  const [packageInfo, setPackageInfo] = useState<any>({});
  const [sup, setSup] = useState<string>('')
  const { setPeriod, payConfirm } = useThirtySixPay({
    topicId: 3,
    packagesId: params.packageId || 610,
    sup: '',
    canReport: params.bizScenario == 'msyq',
    urlType: '',
    originalCost: '',
    activityConsultId: packageInfo?.activityConsultId,
    goods_id: packageInfo?.outItemId,
    out_item_id: packageInfo?.outItemId,
    out_sku_id: packageInfo?.outSkuId,
    address_id: addressInfo?.id,
  });

  useEffect(() => {
    // sensors.track('ai_marketing_AlipayminiAPP_buypagebrowse', {
    //   channel_id: params.channelId,
    //   urlType: urlType,
    // });
    // outItemId，outSkuId，price，packageId和channelId
    // 17141 - 17141 - 26 - 610 - 15609
    // 17142 - 17141 - 26 - 610 - 15609
    // 17143 - 17141 - 26 - 610 - 15609
    getOrderPageDiscountInfo();
  }, []);

  useEffect(() => {
    getSupManagements({
      type: 'TESTCOURSE',
      sup,
      subject: 'ART_APP',
    }).then((res) => {
      const result = res.code == 0 && res.payload;
      if (result) {
        setPeriod(result.period as number);
      }
    });
  }, [sup])


  const getOrderPageDiscountInfo = async () => {
    /* const ali_params = {
      // 仅为代码说明，具体参数参考入参表格！
      itemDetailInfo: {
        outItemId: '2023103022000404872134',
        outSkuId: '17141',
        price: '36',
      },
    };
    singleConsume
    const discountResult = await plugin.getOrderPageDiscountInfo(ali_params) || {}; */
    console.log('params ====> ', params)
    const ali_params = {
      orderDetailInfo: {
        amount: params?.price || '26',
        itemDetailInfoList: [
          {
            outItemId: params?.outItemId || '17143',
            outSkuId: params?.outSkuId || '17143',
            price: params?.price || '26',
            quantity: 1, 
          },
        ],
      },
    };
    const discountResult = await plugin.getOrderPageDiscountInfo(ali_params);
    const { success } = discountResult;
    if (!success) {
      console.error('商详前置优惠接口失败', discountResult);
    } else {
      console.log('商详前置优惠接口成功', discountResult);
      setPackageInfo({
        activityConsultId: discountResult?.orderDiscountDetailInfo?.activityConsultId,
        ...discountResult?.orderDiscountDetailInfo?.itemConsultDetailInfoList?.[0] || {}
      })
      console.log('setPackageInfo', {
        activityConsultId: discountResult?.orderDiscountDetailInfo?.activityConsultId,
        ...discountResult?.orderDiscountDetailInfo?.itemConsultDetailInfoList?.[0] || {}
      })
    }
  };

  useShareAppMessage(() => {
    return {
      title: '小熊美术',
      path: `/pages/twentyNine/index${
        params.channelId ? `?channelId=${params.channelId}` : ''
      }`,
      success() {
        console.log('分享成功');
      },
      fail() {
        console.log('分享失败');
      },
    };
  });


  const getMobile = () => {
    my.getPhoneNumber({
      success: (res) => {
        const { sign, response } = JSON.parse(res.response);
        decryptAliUserApi({
          sign,
          response,
          subject: 'ART_APP',
          aliUserId: aliUserId,
        }).then((result) => {
          if (result.code == 0) {
            const payload = result.payload;
            payload.uid &&
              dispatch({
                type: 'CHANGE_USERID',
                userid: payload.uid,
              });
            payload.mobile &&
              dispatch({
                type: 'CHANGE_MOBILE',
                mobile: payload.mobile,
              });
            if (result.payload.token)
              Taro.setStorageSync('appToken', result.payload.token);
            sensors.track('xxms_testcourse_loginsignupresult', {
              is_success: '是',
            });
            getAddress(payload.uid);
          } else {
            sensors.track('xxms_testcourse_loginsignupresult', {
              is_success: '否',
            });
          }
        });
      },
      fail: (res) => {
        Taro.showModal({
          content: res.errorMessage,
          showCancel: false,
        });
      },
    });
  };
const getAddress = (uid?: any) => {
  my.getAddress({
      success: function(res) {
        if (res.resultStatus === '9000') {
          createUserAddress({
            subject: 'ART_APP',
            userId: uid || userId,
            receiptName: res?.result?.fullname,
            receiptTel: res?.result?.mobilePhone,
            province: res?.result?.prov,
            city: res?.result?.city,
            area: res?.result?.area,
            street: res?.result?.street,
            addressDetail: res?.result?.address,
            telAreaCode: res?.result?.telAreaCode || '+86',
            areaCode: '',
            idCode: '',
            isDefault: '1',
          })
            .then(response => {
              if (response.code === 0) {
                setAddressInfo(response?.payload || {})
                console.log('userIduserId', userId, addressInfo)
              } else {
                setAddressInfo({})
              }
            })
            .catch(error => {
              setAddressInfo({})
            });
        }
      },
      fail: function(err) {
        setAddressInfo({})
      }
    });
  }
  const chooseAddress = () => {
    if (iLoading) return
    getAddress()
  }

  const bottonBtnClick = () => {
    if (!sup) {
      Taro.showToast({ title: '请先选择级别', icon: 'none' });
      return 
    }
    if (!addressInfo?.id) {
      Taro.showToast({ title: '请先添加收货地址', icon: 'none' });
      return 
    }
    payConfirm(sup)
    // 小程序购买页立即购买点击
    // sensors.track('ai_marketing_AlipayminiAPP_buypageclick', {
    //   channel_id: params.channelId,
    //   urlType: urlType,
    // });
    // 小程序选择级别浏览
    // sensors.track('ai_marketing_AlipayminiAPP_selectionlevelbrowse', {
    //   channel_id: params.channelId,
    //   urlType: urlType,
    // });
  };
  return (
    <View className='index container w100 relative'>
      {userId && userId !== '0'? <View className='address' onClick={chooseAddress}>
      {addressInfo?.id ? 
          <View className='address-text-content'>
            <View className='address-text-top'> 
            <View className='address-text-name'>{addressInfo?.receiptName}</View>
            <View className='address-text-phone'>{addressInfo?.receiptTel}</View>
            </View>
            <View className='address-text-address'>{`${
                    addressInfo?.countryCode === 'cn' ? '' : addressInfo?.countryName
                  }${addressInfo?.province}${addressInfo?.city}${addressInfo?.addressDetail}`}</View>
          </View>
        : <View className='address-text-add'>请先添加收货地址</View> }
        <Image mode='widthFix' src={arrowRight} className='address-arrow' />
      </View> : 
       <Button
        openType='getAuthorize'
        scope='phoneNumber'
        onGetAuthorize={getMobile}
        className='address address-button'
      >
        <View className='address-text-add'>请先添加收货地址</View>
        <Image mode='widthFix' src={arrowRight} className='address-arrow' />
     </Button>
      }
      <View className='goods-info'>
        <View className='goods-info-detail'>
          <Image mode='widthFix' src={params?.packageId === '1819' ? S5 :supImageMap[sup || 'S2']} className='goods-info-img' />
          <View className='goods-info-content'>
            <View className='goods-info-title'>10日趣味美术体验包</View>
            <View className='goods-info-price'>
              <View className='goods-info-priceN'>会员价{packageInfo?.salePrice || 29}元</View>
              <View className='goods-info-priceO'>原价{packageInfo?.originalPrice || 36}元</View>
            </View>
          </View>
        </View>
        {packageInfo?.singleConsume ? <View className='goods-info-promo'>
          <View className='goods-info-promo-title'>消耗积分</View>
          <View className='goods-info-promo-count'>{packageInfo?.singleConsume || '0.00'}</View>
        </View> : null}
      </View>
      <View className='sup-choose'>
        <View className='sup-choose-title'>选择级别</View>
        <View className='sup-choose-level'>
          {supMap.map((item) => (
             <View 
               key={item.sup}
               onClick={() =>  setSup(item.sup)}
               className={`sup-choose-level-default sup-choose-level-${sup === item.sup ? 'select': 'unselect'}`}
             >{item.title}</View>
          ))}
        </View>
      </View>
      <View className='pay-info'>
        <View className='pay-info-text'>
        <Image mode='widthFix' src={alipay} className='pay-info-alipay' />
          <View>支付宝</View>
        </View>
        <Image mode='widthFix' src={select} className='pay-info-select' />
      </View>
      <View className='suction-bottom'>
        <View className='suction-bottom-info'>
          <View className='suction-bottom-small'>¥</View>
          <View className='suction-bottom-normal'>{packageInfo?.salePrice}</View>
          <View className='suction-bottom-small'>元</View>
          {packageInfo?.singleConsume ? <><View className='suction-bottom-normal'>+</View>
          <View className='suction-bottom-normal'>{packageInfo?.singleConsume || '0.00'}</View>
          <View className='suction-bottom-small'>积分</View></> : null}
        </View> 
        <Button className='pay-btn' onClick={bottonBtnClick}>
          <Image mode='widthFix' src={pay} className='pay-fixed' />
        </Button>
      </View>
    </View>
  );
};
