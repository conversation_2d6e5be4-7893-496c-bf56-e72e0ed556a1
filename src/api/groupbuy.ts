import qs from 'qs';
import Taro from '@tarojs/taro';
import request from './request';
import msbTrack from '../utils/msbTrack';

let serverConfig;
serverConfig = {
  development: 'https://dev.meixiu.mobi',
  dev: 'https://dev.meixiu.mobi',
  test: 'https://test.meixiu.mobi',
  gray: 'https://prod.xiaoxiongmeishu.com',
  online: 'https://www.xiaoxiongmeishu.com',
};

let weappAppid = '';

// 兼容支付宝小程序
if (process.env.TARO_ENV !== 'alipay') {
  const accountInfo = Taro.getAccountInfoSync();
  weappAppid = accountInfo.miniProgram.appId;
}

const api1 = {
  gitlab: 'vip-app-live',
  default: 'vip-app-default',
  dev: 'vip-app-dev',
  test: 'vip-app-dev',
  prod: 'vip-app-dev',
  online: 'vip-app-live',
};

export const API1 = api1[process.env.NODE_ENV] || 'vip-app-dev';

const server = serverConfig[process.env.NODE_ENV || 'dev'];
const config = {
  header: { 'content-type': 'application/x-www-form-urlencoded' },
};

function POST<T>(path, data, header?): Promise<T> {
  if (header) {
    return request<T>(`${server}${path}`, 'POST', data, { header });
  }
  return request<T>(`${server}${path}`, 'POST', data, config);
}

function POST_JOSN<T>(path, data): Promise<T> {
  return request<T>(`${server}${path}`, 'POST', data, {
    header: { 'content-type': 'application/json' },
  });
}

function GET<T>(path, data): Promise<T> {
  return request<T>(`${server}${path}`, 'GET', data);
}

function GETONLINE<T>(path, data): Promise<T> {
  return request<T>(`https://www.xiaoxiongmeishu.com${path}`, 'GET', data);
}

interface Res<T> {
  errors: string;
  status: string;
  code: number;
  payload: T;
}

export interface TeacherInfo {
  courseTitle: string;
  location: string;
  startTime: string;
  studentId: string;
  teacherId: string;
  teacherName: string;
  teacherWeChat: string;
  teacherWeChatId: string;
  teacherWeChatQrCode: string;
  teacherHead: string;
}

interface TeacherInfoParams {
  orderNo: string;
  addSource: string;
}

interface GetWriteTeacherInfoParams {
  orderNo: string;
  subject: string;
}

export function getTeacherInfo(data: TeacherInfoParams) {
  return GET<Res<TeacherInfo>>(
    '/api/ts/v1/teaching/student/order/teacher/info',
    data,
  );
}

export function getWriteTeacherInfo(data: GetWriteTeacherInfoParams) {
  return GET<Res<TeacherInfo>>(
    '/api/ts/v2/teaching/student/order/teacher/info',
    data,
  );
}

export interface createExpressCallBack {}

interface createExpressParams {
  userId: string;
  receiptName: string;
  receiptTel: string;
  province: number;
  city: number;
  area: number;
  addressDetail: string;
  areaCode: number;
  orderId: string;
  birthdayActivity: string;
}

export function createExpress(data: createExpressParams) {
  return GET<Res<createExpressCallBack>>(
    '/api/o/v1/express/createExpress',
    data,
  );
}

interface getRandRobotParams {
  num: number;
}

export interface getRandRobotCallBack {
  id: string;
  headimg: string;
  username: string;
}

export function getRandRobot(data: getRandRobotParams) {
  return GET<Res<getRandRobotCallBack[]>>('/api/u/v1/robot/getRandRobot', data);
}

export interface supAgeIntervalCallBack {
  S1: string;
  S2: string;
  S3: string;
}

export function supAgeInterval() {
  return GET<Res<supAgeIntervalCallBack>>(
    '/api/f/v1/public/supAgeInterval',
    '',
  );
}

export function supSwitch(data = {}) {
  return POST<Res<any>>('/api/c/v3/channel/supSwitch', data);
}

export interface getManagementsByStatus {
  type: string;
  courseDay: any;
  period: number | string;
}

export function getManagements() {
  return GET<Res<getManagementsByStatus[]>>(
    '/api/s/v1/management/getManagementsByStatus',
    '',
  );
}

export interface getSupManagementsParams {
  sup: string;
  subject?: string;
  type: string;
  channelId?: string;
  category?: string;
}

export function getSupManagements(data: getSupManagementsParams) {
  return GET<Res<getManagementsByStatus>>(
    '/api/s/v1/management/sup/management',
    data,
  );
}

export function getSupManagementsV1(data: getSupManagementsParams) {
  return GET<Res<getManagementsByStatus>>(
    '/api/s/v1/management/sup/managementByType',
    data,
  );
}

interface queryOrderByUserIdParams {
  userId: string;
  channels: string;
  subjects: string;
  packageId?: string | number; //音乐需要传
}

export interface queryOrderByUserIdRes {
  channelCheck: any;
  systemCheckMap: any;
  pluralCheckMap: any;
  subjectOrderMap: any;
  experienceCheckMap: any;
}

export function queryOrderByUserId(data: queryOrderByUserIdParams) {
  return GET<Res<queryOrderByUserIdRes>>(
    '/api/o/v1/order/queryOrderByUserId',
    data,
  );
}

interface packagesCrossCreateParams {
  type?: string;
  userId: string;
  addressId: number;
  sendId: string;
  spreadId: string;
  posterId?: string;
  spreadCode?: string;
  packagesSplitList: any;
  come?: string;
  model?: string;
}

export interface packagesCrossCreateRes {
  order: any;
  orderSimpleList: any;
  id: string;
}

export function packagesCrossCreate(data: packagesCrossCreateParams) {
  return POST_JOSN<Res<packagesCrossCreateRes>>(
    '/api/o/v1/buy/packagesCrossCreate',
    data,
  );
}

interface unionSwitchParams {
  propertiesKey: string;
}

export interface unionSwitchRes {
  payload: any;
}

export function unionSwitch(data: unionSwitchParams) {
  return GET<Res<unionSwitchRes>>(
    '/api/a/v1/referral/config/getProperties',
    data,
  );
}

interface wxCombineParams {
  userId: string;
  openId: string;
  payType?: string;
  mergeOutTradeNo: string;
  notifyUrl: string;
}

export interface wxCombineRes {
  payload: any;
  timeStamp: string;
  nonceStr: string;
  package: string;
  paySign: string;
  signType: 'MD5' | 'HMAC-SHA256' | 'RSA';
}

export function wxCombine(data: wxCombineParams) {
  return GET<Res<wxCombineRes>>('/api/o/v2/pay/wxCombine', {
    ...data,
    appId: weappAppid,
  });
}

export interface getUserOpenIdParams {
  code: string;
  channel?: string;
  subject?: string;
}

export interface getInitProgramUser {
  openid: string;
  uid: string;
  mobile: string;
  unionid: string;
  sensorsId: string;
  hash?: string;
  token?: string;
}

// export function getUserOpenId(data: getUserOpenIdParams) {
//   return GET<Res<getInitProgramUser>>(
//     '/api/wp/v1/weixinProgram/initProgramUser',
//     data,
//   );
// }

export function getUserOpenIdSubject(data: getUserOpenIdParams) {
  return GET<Res<getInitProgramUser>>(
    '/api/wp/v2/weixinProgram/initProgramUser',
    { ...data, ...{ appletType: process.env.APPLETTYPE } },
  );
}

interface getProgramUserParams {
  openId: string;
  encryptedData: string;
  iv: string;
  subject?: string;
}

export interface getProgramUserRes {
  openid: string;
  uid: string;
  mobile: string;
  hash?: string;
  token?: string;
}

// export function getProgramUser(data: getProgramUserParams) {
//   return GET<Res<getProgramUserRes>>(
//     '/api/wp/v1/weixinProgram/getProgramUser',
//     data,
//   );
// }
export function getProgramUserSubject(data: getProgramUserParams) {
  return GET<Res<getProgramUserRes>>(
    '/api/wp/v2/weixinProgram/getProgramUser',
    { ...data, ...{ appletType: process.env.APPLETTYPE } },
  );
}

interface getWeixinByUnidParams {
  unionid: string;
}

interface getWeixinByUnidRes {
  user_role: string;
}

export function getWeixinByUnid(data: getWeixinByUnidParams) {
  return GET<Res<getWeixinByUnidRes>>('/api/w/v1/weixin/getWeixinByUnid', data);
}

export interface getPackagesCreateParams {
  type: string;
  userId: string;
  packagesId: number | string;
  stage: number | string;
  sup: string;
  channel: string;
  sendId: string;
  spreadId?: string;
  topicId?: number | string;
  posterId?: string;
  come?: string;
  teacherId?: string | number;
  platform?: string | number;
  model?: string | number;
  spreadCode?: string | number;
  category?: string | number;
  addressId?: string;
  couponUserIdList?: string | number[];
  thirdId?: any;
  path?: any;
  sourceId?: any;
  outSkuId?: any;
  outItemId?: any;
  goodsId?: any;
}

export interface getPackagesCreateRes {
  order: any;
  id: string;
}

export function getPackagesCreate(data: getPackagesCreateParams) {
  return GET<Res<getPackagesCreateRes>>('/api/o/v1/buy/packagesCreate', data);
}
export interface zeroPackagesCreateParams {
  userId: string;
  packageId: string;
  packagesId?: string;
  sup: string;
  channel?: string;
  channelId: string;
  stage: string;
  topicId?: string;
  model?: string;
  sendId?: string;
  couponUserId?: string;
  addressId?: number;
}
// 获取
export function getPackageId(data: any) {
  return GET<Res<any>>('/api/s/v1/cache/get/package/id', data);
}
// 0元
export function zeroPackagesCreate(data: zeroPackagesCreateParams) {
  return GET<Res<any>>('/api/o/v1/buy/createOrderByPackageId2', data);
}

// 绘本课创建订单
export function getPackagesCreateV2(data: getPackagesCreateParams) {
  return POST_JOSN<Res<getPackagesCreateRes>>(
    '/api/o/v2/buy/packagesCreate',
    data,
  );
}

interface getPackagesCreateParamsV3 {
  type: string;
  packagesId: number | string;
  stage: number | string;
  sup: string;
  channel: string;
  topicId: string;
  come?: string;
}

export interface getPackagesCreateResV3 {
  order: any;
  id: string;
}

interface PackagesCreateV3Header {
  'app-current-user-id': string;
  subject: string;
}

export function getPackagesCreateV3(
  data: getPackagesCreateParamsV3,
  header: PackagesCreateV3Header,
) {
  return POST<Res<getPackagesCreateResV3>>(
    '/api/o/v3/buy/packagesCreate',
    data,
    header,
  );
}

// 小熊美术支付
interface getWeixinProgramPayParams {
  openId: string;
  orderId: string;
  payType: string;
  userId: string;
  notifyUrl: string;
}

export interface getWeixinProgramPayRes {
  order: any;
  id: string;
  timeStamp: string;
  nonceStr: string;
  package: string;
  signType: string;
  paySign: string;
}

export function getWeixinProgramPay(data: getWeixinProgramPayParams) {
  return GET<Res<getWeixinProgramPayRes>>('/api/o/v2/pay/wx', {
    ...data,
    appId: weappAppid,
  });
}

// 京东聚合支付
export interface jdPayV2Params {
  payType: string;
  userId: string;
  orderId: string;
  notifyUrl: string;
  openId?: string;
}
export function jdPayV2(data: getWeixinProgramPayParams) {
  return GET<Res<any>>('/api/o/v2/pay/jdjh', {
    ...data,
    appId: weappAppid,
  });
}

interface getOrderIdParams {
  userId: string;
  addressId: number;
  status?: string;
}

interface getOrdersIdParams {
  userId: string;
  addressId: number;
  subjects?: string;
}

export interface getOrderIdRes {
  id: string;
}

export function getOrderId(data: getOrderIdParams) {
  return GET<Res<getOrderIdRes>>(
    '/api/o/v1/order/getOrderByuserIdAndAddressId',
    data,
  );
}

export function getOrdersId(data: getOrdersIdParams) {
  return GET<Res<getOrderIdRes>>(
    '/api/o/v1/order/getOrdersByUserIdAndAddressIdBySubjects',
    data,
  );
}

interface postReportReadyParams {
  orderId: string;
  platform: string;
  type: string;
  clickId: string;
  url: string;
  params: string;
  wxAccountNu: string;
}

export interface postReportReadyRes {}

export function postReportReady(data: postReportReadyParams) {
  return POST<Res<postReportReadyRes>>(
    '/api/f/v1/reportPlatform/reportReady',
    data,
  );
}
export function dyReport(data: any) {
  return POST<Res<postReportReadyRes>>(
    `/api/r/v1/reportPlatform/reportReady?${qs.stringify(data)}`,
    '',
  );
}

export interface getCenterAddressListRes {
  data: any;
}

export function getCenterAddressList() {
  return GET<Res<getCenterAddressListRes>>(
    '/api/ex/v1/express/getCenterAddressList',
    '',
  );
}
// 获取海外国家地址列表
export function getCenterCountryList() {
  return GET<Res<getCenterAddressListRes>>(
    '/api/ex/v1/express/oversea/getCenterCountryList',
    '',
  );
}
// 获取海外国家三级地址列表
export function getCenterOSAddressList(data) {
  return POST<Res<getCenterAddressListRes>>(
    '/api/ex/v1/express/oversea/getCenterOSAddressList',
    data,
  );
}

interface getAddressTownListParams {
  code: string;
}

export interface getAddressTownListRes {
  unshift: Function;
}

export function getAddressTownList(data: getAddressTownListParams) {
  return GET<Res<getAddressTownListRes>>(
    '/api/ex/v1/express/getCenterAddressTownList',
    data,
  );
}

interface createAddressNewParams {
  orderId: string;
  userId: string;
  receiptName: string;
  receiptTel: string;
  province: string;
  city: string;
  area: string;
  street: string;
  addressDetail: string;
  birthdayActivity: string;
  telAreaCode: string;
}

export interface createAddressNewRes {
  id: string;
}

export function createAddressNew(data: createAddressNewParams) {
  return GET<Res<getOrderIdRes>>(
    '/api/ex/v1/express/createAddressAndExpressForOrder',
    data,
  );
}

// 获取微信群号
export interface getTeamInfoResult {
  qrCodeUrl: string;
  type: string;
  messageInfo: string;
}

export function getTeamInfoApi(orderNo: string) {
  return GET<Res<getTeamInfoResult>>(
    '/api/ts/v1/teaching/student/order/team/info',
    { orderNo },
  );
}

// alipay

// 通过code获取小程序用户数据
interface initAliprogramRes {
  aliUserId: string;
  uid: string;
  token?: string;
}

interface initAliprogramOneToOneRes {
  aliUserId: string;
  uid: string;
  token?: string;
  mobile?: string;
}

export function initAliprogram(code: string, subject: string = 'ART_APP') {
  msbTrack.init({ wx_openid: code });
  return GET<Res<initAliprogramRes>>('/api/wp/v2/aliProgram/initAliprogram', {
    code,
    subject,
  });
}

export function initAliprogramOneToOne(code: string, mobile: string) {
  return GET<Res<initAliprogramOneToOneRes>>(
    '/api/wp/v2/aliProgram/oneToOne/getAliUserByAuthCode',
    {
      code,
      mobile,
    },
  );
}

// 获取支付宝数据
interface decryptAliUserApiParams {
  sign: string;
  response: string;
  subject: string;
  aliUserId: string;
}

export function decryptAliUserApi(data: decryptAliUserApiParams) {
  return POST_JOSN<Res<any>>('/api/wp/v2/aliProgram/decryptAliUser', data);
}

// 先购课在提交级别接口
interface submitSupApiParams {
  orderId: string;
  subject: string;
  sup: string;
  userId: string;
}

export function submitSupApi(data: submitSupApiParams) {
  return POST_JOSN<Res<any>>(
    '/api/o/v1/order/updateOrderSupStatusStageInfo',
    data,
  );
}

// 微信or支付宝or字节 支付中台 具体参数https://alidocs.dingtalk.com/document/preview?dentryKey=R9a1ebo8mf7Zo39g&corpId=ding942f3e00f7a0414835c2f4657eb6378f&nodeId=dy0mV9pdBbq7wX89&workspaceId=dy0mVyV96eoYX89D
interface getProgramPayParams {
  type: 'wx' | 'ali' | 'bd';
  payType:
    | 'AI_WXPROGRAM'
    | 'AI_PRM_ALI'
    | 'ART_BYTE_DANCE_PRM' // 字节
    | 'MUSIC_WXPROGRAM2'
    | 'MUSIC_PRM_ALI'
    | 'WRITE_WXPROGRAM'
    | 'ART_TO_MUSIC_WXPROGRAM'
    | 'HANDWORK_WXPROGRAM';
  orderId: string;
  userId: string;
  openId?: string; // 微信、字节openId
  code?: string;
  notifyUrl?: string; // 回调页url（公众号，小程序没有传空）
  returnType?: 'GET' | 'FORM'; // 支付宝H5连接提交 | 支付宝H5表单提交
  thirdId?: string; // 支付宝userId
  appId?: string; // 微信小程序必传
  hbFqNum?: number; // 花呗分期数
}

export interface getProgramPayRes {
  order: any;
  id: string;
  timeStamp: string;
  nonceStr: string;
  package: string;
  signType: string;
  paySign: string;
}

export function getProgramPay(data: getProgramPayParams) {
  const { type, ...rest } = data;
  if (data.type === 'wx') {
    rest.appId = weappAppid;
  }
  return GET<Res<getProgramPayRes>>(`/api/o/v2/pay/${data.type}`, rest);
}

// 获取订单状态
interface getOrdersIdApiParams {
  userId: string;
  addressId: number;
  subjects: string;
}

export function getOrdersIdApi(data: getOrdersIdApiParams) {
  return GET<Res<any>>(
    '/api/o/v1/order/getOrdersByUserIdAndAddressIdBySubjects',
    data,
  );
}

// 订单查询

export function getOrderDetailApi(orderId: string) {
  return GET<Res<any>>('/api/o/v1/order/detail', { orderId });
}

// 获取验证码
export function getVerificationCode(mobile) {
  return POST<Res<any>>('/api/m/v1/sms/sendCodeFour', { mobile });
}

// 手机号登录
interface getLoginParams {
  mobile: string;
  code: string;
  sendId: string;
  channel: string;
  weixinOpenId?: string;
}

export function getLogin(data: getLoginParams) {
  return POST<Res<any>>('/api/u/v1/user/login', {
    ...data,
  });
}

export function uploadStatusInfo(data: {
  uid: string;
  eventType: string;
  eventStatus: string;
  eventLogId: string | number;
}) {
  return POST<Res<any>>('/api/a/v1/account/uploadStatusInfo', {
    ...data,
  });
}

// tt手机号授权获取uid
interface getInitzjProgramApiProps {
  sessionKey: string;
  openid: string;
  encryptedData: string;
  iv: string;
  appid: string;
  appsubject: string;
}

export function ttInitzjProgramApi(data: getInitzjProgramApiProps) {
  return GET<Res<any>>('/api/wp/v2/zjProgram/initzjProgram', data);
}

export function getUser(data: { userId: string }) {
  return GET<Res<any>>('/api/u/v1/user/getUser', data);
}

// tt通过openid获取uid
interface ttGetUidByOpenidProps {
  openid: string;
  appsubject: string;
}

export function ttGetUidByOpenid(data: ttGetUidByOpenidProps) {
  return GET<Res<any>>('/api/wp/v2/zjProgram/getUserByOpenid', data);
}

// 获取公众号二维码
interface getWeixinParamQrcodeImageUrlProps {
  uid: string;
  type: number;
  appsubject: string;
  orderIds?: string;
  outTradeNo?: string;
  channel?: string;
  source?: string;
}

export function getWeixinParamQrcodeImageUrlApi(
  data: getWeixinParamQrcodeImageUrlProps,
) {
  return POST_JOSN<Res<any>>(
    '/api/w/v1/weixin/getWeixinParamQrcodeImageUrl',
    data,
  );
}

// 查询用户默认地址
export function getDefaultAddress(userId: string) {
  return GET<Res<any>>('/api/ex/v1/express/getDefaultAddress', { userId });
}

// 创建用户收货地址（新）
interface createAddressData {
  subject: string;
  userId: string;
  receiptName: string;
  telAreaCode: string;
  receiptTel: string;
  idCode: string;
  province: string;
  city: string;
  area: string;
  street: string;
  addressDetail: string;
  areaCode: string;
  isDefault: '1' | '0';
}

export function createUserAddress(data: createAddressData) {
  return GET<Res<any>>('/api/ex/v1/express/createAddressNew', data);
}

// 修改用户收货地址（新）
interface updateUserAddressData {
  addressId: string;
  userId: string;
  receiptName: string;
  telAreaCode: string;
  receiptTel: string;
  idCode: string;
  province: string;
  city: string;
  area: string;
  street: string;
  addressDetail: string;
  areaCode: string;
  isDefault: '1' | '0';
}

export function updateUserAddress(data: updateUserAddressData) {
  return GET<Res<any>>('/api/ex/v1/express/updateAddressNew', data);
}
// 生成物流
interface createAddressForExpressAndOrderData {
  orderId: string;
  userId: string;
  addressId: string;
}

export function createAddressForExpressAndOrder(
  data: createAddressForExpressAndOrderData,
) {
  return GET<Res<any>>(
    '/api/ex/v1/express/createAddressForExpressAndOrder',
    data,
  );
}

// 复购资格查询
interface checkOrdersByUserIdProps {
  userId: string;
  channels: string;
  subjects: string;
  packageId?: string;
}

export function checkOrdersByUserId(data: checkOrdersByUserIdProps) {
  return GET<Res<any>>('/api/o/v1/order/queryOrderByUserId', data);
}

export function packagesZeroCreate(data: any) {
  return POST_JOSN<Res<any>>('/api/o/v1/buy/flowToWrite', data);
}

// 上报扫码次数
interface reportScanNumProps {
  openid: string;
  spreadUserId: string;
}

export function reportScanNum(data: reportScanNumProps) {
  return GET<Res<any>>('/api/fx/app/user/api/reportScanNum', data);
}

// 根据orderid和subject获取教师信息
interface getTeacherInfoByOrderIdsAndSubjectProps {
  orderNo: string;
  subject: string;
}

export function getTeacherInfoByOrderIdsAndSubject(
  data: getTeacherInfoByOrderIdsAndSubjectProps,
) {
  return GET<Res<any>>('/api/ts/v2/teaching/student/order/teacher/info', data);
}

// 获取美术音乐写字订单
interface getOrderIdAllNewProps {
  userId: string;
  regtypes: string;
  statuses: string;
  subjects: string;
}

export function getOrderIdAllNew(data: getOrderIdAllNewProps) {
  return GET<Res<any>>(
    '/api/o/v1/order/getOrderByRegTypesAndStatuesAndSubjects',
    data,
  );
}

// 助力活动
interface getWxAssistProps {
  uid: string;
  targetOpenId: string;
  type: string;
  avatar: string;
}

export function getWxAssistProps(data: getWxAssistProps) {
  return GET<Res<any>>('/api/a/v1/referral/task/wxAssist/bind', data);
}

interface getActivityCheckProps {
  uid: string;
  subject: string;
  type: string;
}

export function getActivityCheck(data: getActivityCheckProps) {
  return GET<Res<any>>('/api/a/v1/referral/active/participate/check', data);
}

export interface getInitProgramUserByOenid {
  openId: string;
  subject?: string;
}

export function getProgramUserByOenid(data: getInitProgramUserByOenid) {
  return GET<Res<any>>('/api/wp/v2/weixinProgram/getProgramUserByOpenId', data);
}

// 判断用户购课状态
interface UserBuyProps {
  uid: string;
}

export function checkUserBuy(data: UserBuyProps) {
  return GET<Res<any>>('/api/u/v1/user/checkUserBuy', data);
}

interface AssistProps {
  uid: string;
  type: string;
}

export function getAssistCount(data: AssistProps) {
  return GET<Res<any>>('/api/a/v1/referral/task/wxAssist/count', data);
}

// 获取分享海报历史任务记录
export function getSharePosterLogList(data: {
  uid: string;
  page: number;
  size: number;
}) {
  return GET<Res<any>>('/api/a/v1/task/getSharePosterLogList', data);
}

// 获取分享海报历史任务记录 单条
export function getSharePosterLog(data: { uid: string; taskId: string }) {
  return GET<Res<any>>('/api/a/v1/task/getSharePosterLog', data);
}

// 加群绑定unionid接口
interface UnionIdProps {
  uid: string;
  unionid: string;
  roomType: string;
}

export function bindUnionId(data: UnionIdProps) {
  return POST_JOSN<Res<any>>('/api/zq/v1/qw/saveUnionid', data);
}

// 获取老师微信二维码
interface getWechatTeacherProp {
  planCourseId: string;
  type: string;
}

export function getWechatTeacher(data: getWechatTeacherProp) {
  return GET<Res<any>>(
    '/api/ts/v2/teaching/isAddWechat/getWechatTeacher',
    data,
  );
}

// 获取用户添加老师和公众号情况
interface getUserFollowStatusProps {
  appsubject: string;
  orderNo: string;
  userId: string;
}

export function getUserFollowStatusApi(data: getUserFollowStatusProps) {
  return POST_JOSN<Res<any>>(
    '/api/w/v1/weixin/getWeixinAndTeacherAddInfo',
    data,
  );
}

// 获取订单及物流信息
interface getOrderListAndExpressProps {
  userId: string;
  subjects?: string;
  tradeTypes?: string;
}

export function getOrderListAndExpressApi(data: getOrderListAndExpressProps) {
  return GET<Res<any>>(
    '/api/o/v2/order/getOrderListByUserIdAndSubjectsAndTradeType',
    {
      subjects: 'ART_APP,MUSIC_APP',
      tradeTypes: 'ALI_PRM',
      ...data,
    },
  );
}

// 根据快递号查询物流信息
export function getExpressDetailapi(expressNo: string) {
  return GET<Res<any>>('/api/ex/v1/express/getExpressDetailForSubject', {
    expressNo,
  });
}

// 支付宝
interface getAliProgramUserByAliUserIdProps {
  aliUserId: string;
  subject?: string;
}

export function getAliProgramUserByAliUserIdApi(
  data: getAliProgramUserByAliUserIdProps,
) {
  return GET<Res<any>>('/api/wp/v2/aliProgram/getAliProgramUserByAliUserId', {
    subject: 'ART_APP',
    ...data,
  });
}

// 转介绍判断用户显示36
export function getExpUserApi(uid: string) {
  return GET<Res<any>>('/api/a/v1/referral/productManager/expUser', {
    uid,
  });
}

// Unionid关联公众号获取uid
export function getWeixinByUnionid(unionid, subject = 'ART_APP') {
  return GET<Res<any>>('/api/w/v1/weixin/getWeixinByUnionid', {
    unionid,
    subject,
  });
}

export function getBenefitActiveInit(data: { subject: string }) {
  return GET<Res<unionSwitchRes>>('/api/a/v1/referral/sp/pi/progress', data);
}

export function getSharePosterTaskApi(data: { uid: string }) {
  return GET<Res<any>>('/api/a/v1/task/getSharePosterTask', data);
}

export function getClaimTask(data: { taskId: string; userId: string }) {
  return GET<Res<any>>('/api/a/v1/task/claimTask', data);
}

// 获取用户信息 、小熊币V2
export function getAccountV2(data: {
  accountType: string;
  userId: string;
  subject: string;
}) {
  return GET<Res<any>>('/api/a/v2/account/getAccount', data);
}

// 周周分享本月任务参与次数
export function getMonthlyCount(data: { uid: string; subject: string }) {
  return GET<Res<any>>('/api/a/v1/referral/sp/count/monthly', data);
}

export function findByKey(data: any) {
  return POST<Res<any>>(
    `/1v1k8s/${API1}/api/recommend/api/paramConfig/findByKey`,
    data,
  );
}

export function getPosterListAll(data: {
  id: string;
  qrCode: string;
  uid: string;
}) {
  return POST<Res<any>>(
    `/1v1k8s/${API1}/api/recommend/api/recommend/posterList/createBearPosterAll`,
    data,
  );
}

export function getInviteInfo(data: { uid: string }) {
  return GET<Res<any>>(`/api/c/v1/invite/getInviteInfo`, data);
}

export function getAccountInfo(data: { userId: string; accountType: string }) {
  return GET<Res<any>>(`/api/a/v1/account/getAccountInfo`, data);
}

interface orderExtendReportProps {
  uid: string;
  oids: string;
  tabType: string;
  tabValue: string;
  tabJson?: string;
  posterId?: string | number;
  come?: string;
}

export function orderExtendReport(data: orderExtendReportProps) {
  return POST_JOSN<Res<packagesCrossCreateRes>>(
    '/api/o/v1/order/orderExtendReport',
    data,
  );
}

// 抖店
export function getExchangeCodeConfigApi(code) {
  return GET<Res<packagesCrossCreateRes>>(
    '/api/s/v1/exchange/code/getExchangeCodeConfig',
    { code },
  );
}

// 获取渠道信息列表
interface getChannelListProps {
  appsubject: string;
  channel_class_ids: string;
  channel_ids?: string;
  channel_level?: string;
  page: number;
  size: number;
}

export function getChannelListApi(data: getChannelListProps) {
  return GET<Res<any>>('/api/c/v1/channel/getChannelList', { ...data });
}

// 获取商品禁发区域
export function getforbiddenlist(data) {
  return GET<any>('/msb1v1k8s/logistics/forbidden/area/list', { ...data });
}

// 短链查长链(只能用线上接口解析)
export function getLongUrl(url) {
  return GETONLINE<any>('/api/f/v1/shorturl/getOriginalUrl', { url });
}

// 用户埋点上报
interface userEventReportProps {
  subject: string;
  uid: string;
  channelId: string;
  eventValue: string;
  eventName: string;
  eventTitle: string;
}

export function userEventReport(data: userEventReportProps) {
  const { subject, uid, channelId, ...rest } = data;
  return POST_JOSN<any>(
    `/api/r/v1/userBehaviorRecord/userReport?subject=${subject}&uid=${uid}&channelId=${channelId}`,
    { ...rest },
  );
}

// 获取转介绍来的人体验课优惠券是否已使用
export function getUserCouponByPackageId(uid, packageId) {
  return GET<any>('/api/s/v1/coupon/getUserCouponByPackageId', {
    uid,
    packageId,
  });
}

// 获取绘本课排期
export function getManagementByTypeAndCategory(channelId) {
  return GET<any>('/api/s/v1/management/sup/getManagementByTypeAndCategory', {
    sup: 'S1',
    type: 'CATEGORYTESTCOURSE',
    channelId,
    category: 20,
    subject: 'PICTURE_BOOK',
  });
}

// 获取转介绍来的人体验课优惠券是否已锁定
export function queryCouponLock(userId, sendId, packageId) {
  return GET<any>('/api/o/v1/cache/queryCouponLock', {
    userId,
    sendId,
    packageId,
  });
}

// APP 内查询用户拼团状态
export function getGrouponDetailByUserId(userId) {
  return GET<any>('/api/o/v1/groupon/getGrouponDetailByUserId', {
    userId,
  });
}
// 通过userid和套餐查询拼团信息
export function getGrouponDetailByPackagesId(userId, packagesId) {
  return GET<any>('/api/o/v1/groupon/getGrouponDetailByPackagesId', {
    userId,
    packagesId,
  });
}
// APP 分享查询团购详情
export function getGrouponDetailById(grouponId) {
  return GET<any>('/api/o/v1/groupon/getGrouponDetailById', {
    grouponId,
  });
}
// 获取用户是否买过绘本课
export function getBuyOrderByUserId(data) {
  return GET<Res<any>>('/api/o/v2/order/getBuyOrderByUserId', data);
}
// 领券
export function getFriendCoupon(data) {
  return GET<Res<any>>('/api/s/v1/coupon/get/friend/coupon', data);
}
// 领0元券
export function getFriendCoupon1888(data) {
  return GET<Res<any>>('/api/s/v1/coupon/get/friend/coupon/1888', data);
}
// 查询特价课订单买过吗
export function querySpecialOrderByUserId(data) {
  return GET<Res<any>>('/api/o/v1/order/querySpecialOrderByUserId', data);
}
// 绘本系统课 (订阅)
// type 值 HALFYEAR, YEAR, QUARTER, MONTHLY;
export function queryPackagesIdBySubscribe(data) {
  return GET<Res<any>>(
    '/api/o/v1/orderSubscribe/queryPackagesIdBySubscribe',
    data,
  );
}
// 绘本系统课(正常购买)
// type 值 HALFYEAR, YEAR, QUARTER, MONTHLY;
export function queryPackagesIdBySubject(data) {
  return GET<Res<any>>('/api/o/v2/order/queryPackagesIdBySubject', data);
}

// 获取排期
export function getManagementsByStatus(data) {
  return GET<Res<any>>('/api/s/v2/management/getManagementsByStatus', data);
}
// 获取套餐详情
export function getPackages(data) {
  return GET<Res<any>>('/api/p/v1/product/getPackages', data);
}

// 获取用户地址信息
export function getAddressById(data) {
  return GET<Res<any>>('/api/ex/v1/express/getAddressById', data);
}
// 获取用户地址列表
export function getAddressList(data) {
  return GET<Res<any>>('/api/ex/v1/express/getAddressList', data);
}
// 删除地址
export function deleteAddress(data) {
  return GET<Res<any>>('/api/o/v1/express/deleteAddress', data);
}
// 发送验证码
export function sendCodeV2(data) {
  return POST<Res<any>>('/api/m/v1/sms/sendCodeV2', data);
}
// 登录
export function getLoginV2(data) {
  return POST<Res<any>>('/api/u/v2/user/login', data);
}
// 签约
export function eduContractsSign(data) {
  return GET<Res<any>>('/api/o/v2/pay/eduContractsSign', data);
}
// 查询订单支付状态
export function getOrderStaus(data) {
  return GET<Res<any>>('/api/o/v1/order/getOrder', data);
}
// 查询签约状态
export function orderSubscribeById(data) {
  return GET<Res<any>>('/api/o/v1/orderSubscribe/orderSubscribeById', data);
}
// 企微进入0元美术小程序回调
export function tagAndRemark(data) {
  return GET<Res<any>>(
    '/api/zq/v1/qw/buy/zero/art/order/build/tagAndRemark',
    data,
  );
}
// 获取周周分享分享时间
export function shareReportHandler(data) {
  return GET<any>('/api/a/v1/referral/weekly/shareReport', data);
}

// 获取用户优惠券
export function getUserCouponByPackageIdList(data) {
  return GET<any>('/api/s/v1/coupon/getUserCouponByPackageIdList', data);
}

// 根据user短id获取长id
export function getUserByUserNum(data) {
  return GET<any>('/api/u/v1/user/getUserByUserNum', data);
}

// 查询2023小画家视频
export function selVideo(data) {
  return GET<Res<any>>('/api/a/v1/referral/yearlook/2023/selVideo', data);
}

// 周周分享改版 对接有赞 商品列表
export function getWeeklyShareGoods(params) {
  return POST<Res<any>>('/api/a/v1/yzpoint/goodsByGroupId', params);
}

// 邀请有礼判断是否点过立即邀请
export function checkActiveStatusInfo(params) {
  return GET<Res<any>>('/api/a/v1/account/checkActiveStatusInfo', params);
}
// 复购接口 ?userId=612377970775400448&channels=1&subjects=ART_APP&packageId=62
export function queryOrderByUserIdNew(data) {
  return GET<Res<any>>('/api/o/v1/order/queryOrderByUserIdNew', data);
}

// 从url链接上取三级渠道channelId/msChannelId，再通过三级渠道id调接口换二级渠道信息
export function getChannelById(data) {
  return GET<Res<any>>('/api/c/v1/channel/getChannelById', data);
}

// 查询已购买美术体验课&未购买美术系统课的用户
export function getOrderByRegtypesAndStatus(data) {
  return GET<Res<any>>('/api/o/v1/order/getOrderByRegtypesAndStatus', data);
}
