// 好礼天天领
import qs from 'qs';
import { POST_JOSN, Res, UMSPOST, GET, POST } from './api.type';

//礼物列表
export function getPrezzieListApi(uid) {
  return POST_JOSN<Res<any>>(`/api/a/v1/prezzie/getPrezzieList?uid=${uid}`, {});
}
// 参加任务
interface joinPrezzieProps {
  uid: string;
  goodsId: string;
  epc: string;
  img: string;
  title: string;
}
export function joinPrezzieApi(data: joinPrezzieProps) {
  const params = qs.stringify(data);
  return POST_JOSN<Res<any>>(`/api/a/v1/prezzie/joinPrezzie?${params}`, {});
}
// 获取任务列表
export function getMyListApi(uid) {
  return POST_JOSN<Res<any>>(`/api/a/v1/prezzie/getMyList?uid=${uid}`, {});
}

// 钻石商城2.0 接中台 - 商品详情
export interface getGoodsInfoProps {
  businessType: string;
  goodsId: string;
  currencyType: string;
}
export function getGoodsInfoApi(data: getGoodsInfoProps) {
  return UMSPOST<Res<any>>(
    `/api/im/g/v1/h5/goods/goodsInfo`,
    qs.stringify(data),
  );
}

// 可更换商品列表
export function getReplaceGoodsApi({ price, subject = 'ART_APP', epc }) {
  return POST_JOSN<Res<any>>(
    `/api/a/v1/prezzie/getReplaceGoods?subject=${subject}&price=${price}&epc=${epc}`,
    {},
  );
}

export function getReplaceGoodsNewApi({ prezzieId }) {
  return POST_JOSN<Res<any>>(
    `/api/a/v1/prezzie/getReplaceGoodsNew?prezzieId=${prezzieId}`,
    {},
  );
}

// 更换商品接口
interface replaceGoodsProps {
  uid: string;
  prezzieId: string;
  goodsId: string;
  epc: string;
  img: string;
  title: string;
}
export function replaceGoodsApi(data: replaceGoodsProps) {
  const params = qs.stringify(data);
  return POST_JOSN<Res<any>>(`/api/a/v1/prezzie/replaceGoods?${params}`, {});
}
/* 新版天天领 */
// 初始化
export function prezzieinit(uid) {
  return GET<Res<any>>(`/api/a/v2/prezzie/init`, {
    uid,
    subject: 'ART_APP',
  });
}
// 新版获取商品
export function getPrezzieListV2(uid) {
  return POST<Res<any>>(`/api/a/v2/prezzie/getPrezzieList`, {
    uid,
    subject: 'ART_APP',
  });
}
// 新版兑换列表
export function rewardList(uid, managementId) {
  return GET<Res<any>>(`/api/a/v2/prezzie/reward/list`, {
    uid,
    managementId,
  });
}
