import { OSSGET, POST_JOSN, Res } from './api.type';

// 动态获取上传oss配置信息
export function getOssSigned() {
  return OSSGET<Res<boolean>>(
    'api/backend/api/v1/ossconfig/getPubWriteSigned',
    {},
  );
}

// 提交截图
export function submitScreenShot(data: {
  userId: string;
  businessLogId: string;
  type: string;
  uploadUrl: string;
}) {
  return POST_JOSN<Res<any>>(
    `/api/b/v1/backend/userflow/sharereward/apply`,
    data,
  );
}
