import request from './request';

let serverConfig;
serverConfig = {
  development: 'https://ums-test.xiaoxiongmeishu.com',
  dev: 'https://ums-test.xiaoxiongmeishu.com',
  test: 'https://ums-test.xiaoxiongmeishu.com',
  gray: 'https://ums-gray.xiaoxiongmeishu.com',
  online: 'https://ums.xiaoxiongmeishu.com',
};

const server = serverConfig[process.env.NODE_ENV];
const config = {
  header: { 'content-type': 'application/x-www-form-urlencoded' },
};

function POST<T>(path, data): Promise<T> {
  return request<T>(`${server}${path}`, 'POST', data, config);
}

function POST_JOSN<T>(path, data): Promise<T> {
  return request<T>(`${server}${path}`, 'POST', data, {
    header: { 'content-type': 'application/json' },
  });
}

function GET<T>(path, data): Promise<T> {
  return request<T>(`${server}${path}`, 'GET', data);
}

interface Res<T> {
  errors: string;
  status: string;
  code: number;
  payload: T;
}

// tt 获取openid
export function getOpenidApi(code: string, appid: string) {
  return POST<Res<any>>('/api/market/mp/zijie/getOpenid', { code, appid });
}

// 解密
export function decryptDataApi(sessionKey, encryptedData, iv) {
  return POST<Res<any>>('/api/market/mp/zijie/decryptData', {
    sessionKey,
    encryptedData,
    iv,
  });
}

// 保存手机号
interface saveMobileApiProps {
  openid: string;
  mobile: string;
  appid: string;
}

export function saveMobileApi(data: saveMobileApiProps) {
  return POST<Res<any>>('/api/market/mp/zijie/saveMobile', {
    line: 1,
    ...data,
  });
}

// 实物奖品0元领
export function getZeroCoinShopGoods(data: { ecommendationManageId: number }) {
  return POST<Res<any>>('/api/im/g/v1/recommend/query/recommendGoodsList', {
    ecommendationManageId: data.ecommendationManageId || 990,
    businessType: 'BEAR',
    page: 1,
    pageSize: 1000,
  });
}

// 小熊币商城 接中台 - 新列表
export function getNewCoinGoodsList(data: {
  recommendationTag: string;
  userId?: string;
}) {
  return POST<Res<any>>('/api/im/g/v1/recommend/query/recommendGoodhsList', {
    recommendationTag: data.recommendationTag,
    businessType: 'BEAR',
    page: 1,
    pageSize: 1000,
    userId: data.userId || '',
  });
}
