import request from './request';

const serverConfig = {
  development: 'https://test121.meishubao.com',
  dev: 'https://test121.meishubao.com',
  test: 'https://test121.meishubao.com',
  gray: 'https://vip121.meishubao.com',
  online: 'https://vip.meishubao.com',
};

const server = serverConfig[process.env.NODE_ENV];
const config = {
  header: { 'content-type': 'application/x-www-form-urlencoded' },
  // header: { 'content-type': 'application/json' }
};

// function POST<T>(path, data?): Promise<T> {
//   return request<T>(`${server}${path}`, 'POST', data, config);
// }
function GET<T>(path, data?): Promise<T> {
  return request<T>(`${server}${path}`, 'GET', data);
}
// function POST_JOSN<T>(path, data): Promise<T> {
//   return request<T>(`${server}${path}`, 'POST', data, {
//     header: {
//       'content-type': 'application/json',
//       Authorization:
//         'Bearer eyJhbGciOiJIUzUxMiJ9.eyJqdGkiOiJ1c2VyIiwic3ViIjoidXNlciIsImlhdCI6MTYwMDkzMDkzNSwiYXVkIjoidXNlciIsImV4cCI6MTY4NzMzMDkzNX0.1SYzikspkmMpHjGPo0EXwkzI_ifQVA82OKv8DUm71-XSHufLTZN3sDGXh8njO1ylJnwCMX0pNoo7OY4bjAMUYw',
//     },
//   });
// }
interface Res<T> {
  status: string;
  code: number;
  payload: T;
  errors: string;
  result: string | number;
}

// 导流开关
export function getSwitchFlag(flag) {
  return GET<Res<any>>('/api/ad/switch', { flag, action: 'get_status' });
}
