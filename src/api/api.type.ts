import request from './request';

const ENV = process.env.NODE_ENV;
let serverConfig;
serverConfig = {
  development: 'https://test.meixiu.mobi',
  dev: 'https://dev.meixiu.mobi',
  test: 'https://test.meixiu.mobi',
  gray: 'https://prod.xiaoxiongmeishu.com',
  online: 'https://www.xiaoxiongmeishu.com',
};

const server = serverConfig[ENV] || 'dev';
const config = {
  header: { 'content-type': 'application/x-www-form-urlencoded' },
};

const server1 = 'https://msi.meishubao.com/vip-app-live/';
const ossConfig: any = {
  header: {
    authorization:
      'Bearer eyJhbGciOiJIUzUxMiJ9.eyJqdGkiOiJhZG1pbiIsInN1YiI6ImFkbWluIiwiaWF0IjoxNjg3MzM1MTY1LCJhdWQiOiJhZG1pbiIsImV4cCI6MjU1MTMzNTE2NX0.iIx8DUIQ9ryi9e4SfkwLIWx2Fp-_PG9iFO-N4gQIHCJLp0Z667jNUcvqajhbwWgjqoMwJbLxeaz199l23wC9UQ',
    noNeedToken: true,
  },
};

const umsServer =
  process.env.NODE_ENV === 'online'
    ? 'https://ums.xiaoxiongmeishu.com'
    : 'https://ums-test.xiaoxiongmeishu.com';

export function POST<T>(path, data, header?): Promise<T> {
  if (header) {
    return request<T>(`${server}${path}`, 'POST', data, { header });
  }
  return request<T>(`${server}${path}`, 'POST', data, config);
}
export function POST_JOSN<T>(path, data): Promise<T> {
  return request<T>(`${server}${path}`, 'POST', data, {
    header: { 'content-type': 'application/json' },
  });
}
export function GET<T>(path, data): Promise<T> {
  return request<T>(`${server}${path}`, 'GET', data);
}
export interface Res<T> {
  errors: string;
  status: string;
  code: number;
  payload: T;
}

export function OSSGET<T>(path, data): Promise<T> {
  return request<T>(`${server1}${path}`, 'GET', data, ossConfig);
}

export function UMSPOST<T>(path, data, header?): Promise<T> {
  if (header) {
    return request<T>(`${umsServer}${path}`, 'POST', data, { header });
  }
  return request<T>(`${umsServer}${path}`, 'POST', data, {
    header: {
      authorization:
        'Bearer eyJhbGciOiJIUzUxMiJ9.eyJqdGkiOiJhZG1pbiIsInN1YiI6ImFkbWluIiwiaWF0IjoxNjAwOTMwOTM1LCJhdWQiOiJhZG1pbiIsImV4cCI6MTY4NzMzMDkzNX0.xP7oyOQmCtKtteisDtptZnY0U8E1VAbpGc3lOcQpcljQWjL1DOKOG07ncgEBpHKryy-vxiFHc3szx2gJnArQAA',
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
}

// 小熊音乐
const xxyyServerConfig = {
  development: 'https://ai-xxyy-dev.xiaoxiongyinyue.com',
  dev: 'https://ai-xxyy-dev.xiaoxiongyinyue.com',
  test: 'https://ai-xxyy-test.xiaoxiongyinyue.com',
  gray: 'https://ai-xxyy-prod.xiaoxiongyinyue.com',
  online: 'https://ai-xxyy-live.xiaoxiongyinyue.com',
};
const xxyyServer = xxyyServerConfig[ENV];
export function XXYYPOST<T>(path, data, header?): Promise<T> {
  if (header) {
    return request<T>(`${xxyyServer}${path}`, 'POST', data, { header });
  }
  return request<T>(`${xxyyServer}${path}`, 'POST', data, config);
}
export function XXYYPOST_JOSN<T>(path, data): Promise<T> {
  return request<T>(`${xxyyServer}${path}`, 'POST', data, {
    header: { 'content-type': 'application/json' },
  });
}
export function XXYYGET<T>(path, data): Promise<T> {
  return request<T>(`${xxyyServer}${path}`, 'GET', data);
}
