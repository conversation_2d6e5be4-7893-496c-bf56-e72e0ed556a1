import request from './request';

const serverConfig = {
  development: 'https://test121.meishubao.com/1v1k8s',
  dev: 'https://test121.meishubao.com/1v1k8s',
  test: 'https://test121.meishubao.com/1v1k8s',
  gray: 'https://vip121.meishubao.com/1v1k8s',
  online: 'https://vip.meishubao.com/1v1k8s',
};

const server = serverConfig[process.env.NODE_ENV];
const config = {
  header: { 'content-type': 'application/x-www-form-urlencoded' },
  // header: { 'content-type': 'application/json' }
};

function POST<T>(path, data?): Promise<T> {
  return request<T>(`${server}${path}`, 'POST', data, config);
}

function GET<T>(path, data?): Promise<T> {
  return request<T>(`${server}${path}`, 'GET', data);
}

function POST_JOSN<T>(path, data): Promise<T> {
  return request<T>(`${server}${path}`, 'POST', data, {
    header: {
      'content-type': 'application/json',
      Authorization:
        'Bearer eyJhbGciOiJIUzUxMiJ9.eyJqdGkiOiJ1c2VyIiwic3ViIjoidXNlciIsImlhdCI6MTY4NzMzMTk3NCwiYXVkIjoidXNlciIsImV4cCI6MTc3MzczMTk3NH0.pKKxe_f7iwPJIT_LcdGI0CIZZWtq8qvWzXZqpQBJ13PoajKkfVmBuddwJxR698C5bivDbze4t80wxA4HTKVz8w',
    },
  });
}

interface Res<T> {
  status: string;
  code: number;
  payload: T;
  errors: string;
}

interface getPosterListParams {
  id: number;
  qrCode: string;
  uid: string;
}

export interface getPosterListResult {
  code: string;
}

export function getPosterListApi(data: getPosterListParams) {
  return POST<Res<getPosterListResult>>(
    '/poster/api/recommend/posterList/createBearPosterAll',
    data,
  );
}

export function getPosterListByIdApi(id: string) {
  return POST_JOSN<Res<any>>(
    '/poster/api/v1/recommend/poster/getPosterList?id=' + id,
    {},
  );
}

// 导流开关
export function getSwitchFlag(key) {
  return POST<Res<any>>('/recommend/api/paramConfig/findByKey', { key });
}

// 校验兑换码
export function checkExchangeCode({
  userId,
  code,
  stage,
  thirdOid,
  subject = 'ART_APP',
}) {
  return GET<Res<any>>('/market/api/v2/market/douyin/checkExchangeCode', {
    userId,
    code,
    stage,
    thirdOid,
    subject,
  });
}

// 校验兑换码（直接选级别）
export function checkExchangeCodeWish({
  userId,
  code,
  stage,
  level,
  thirdOid,
  subject = 'ART_APP',
}) {
  return GET<Res<any>>('/market/api/code/manager/checkExchangeCode', {
    userId,
    code,
    stage,
    level,
    thirdOid,
    subject,
  });
}
