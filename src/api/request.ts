import Taro from '@tarojs/taro';

type HTTPMedthods =
  | 'POST'
  | 'GET'
  | 'OPTIONS'
  | 'PUT'
  | 'DELETE'
  | 'HEAD'
  | 'TRACE'
  | 'CONNECT';

interface TaroHTTPConfig {
  header?: {
    [ket: string]: string;
  };
  dataType?: 'json';
  responseType?: 'text' | 'arraybuffer';
}

export default function<T>(
  url: string,
  method: HTTPMedthods,
  data: any,
  config: TaroHTTPConfig = {},
): Promise<T> {
  return new Promise((resolve, reject) => {
    if (!config) config = {};
    if (!config.header) config.header = {};
    if (Taro.getStorageSync('appHash'))
      config.header['hash'] = Taro.getStorageSync('appHash');
    if (Taro.getStorageSync('appToken')) {
      if (!config.header.noNeedToken)
        config.header['Authorization'] = Taro.getStorageSync('appToken');
    }

    // config.header['Authorization'] = "Bearer eyJhbGciOiJIUzUxMiJ9.eyJqdGkiOiI3OTUwMjE2ODA4MzM4MzA5MTIiLCJzdWIiOiI3OTUwMjE2ODA4MzM4MzA5MTIiLCJpYXQiOjE2OTg5MjI3MTEsImF1ZCI6InVzZXIiLCJleHAiOjE3MDc1NjI3MTF9.Sl0avTZtZt6MTDrTrLkylGJYgZPVzQ2KXLEmTbR8K2E2t5eVxPFTQHxMUO8sILmeigzVjVNHkNpZAjjzBbMI3g"

    Taro.request({
      url,
      method,
      data,
      ...config,
      success(res) {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(res);
          Taro.showToast({
            title: `网络请求出错：${res.statusCode}`,
            icon: 'none',
          });
        }
      },
      fail(err) {
        reject(err);
        Taro.showToast({
          title: `网络请求出错`,
          icon: 'none',
        });
      },
    });
  });
}
