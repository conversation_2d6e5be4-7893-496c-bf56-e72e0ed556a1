/**
 * 仅用做打开微信开发者工具
 * open.js
 * !/usr/bin/env
 * author: <PERSON><PERSON><PERSON><PERSON>
 * time: 2023-2-16
 * */

/* eslint-disable import/no-commonjs */
const runDev = require('./runDev');
const child = require('child_process');

const exec = child.execSync;

const path = '/Applications/wechatwebdevtools.app/Contents/MacOS/cli';
const projectPath = `${process.cwd()}/dist`;

child.exec('git branch --show-current', (err, res) => {
  console.log('当前分支：', res);
});

child.exec(
  `${path} open --project ${projectPath}`,
  // { timeout: 1000 },
  { stdio: 'inherit' },
  async (err, res) => {
    console.log(res, '===res===');
    if (res) {
      // exec(`${path} login --qr-size small`, { stdio: 'inherit' });
      exec(`${path} open --project ${projectPath}`, { stdio: 'inherit' });
      // exec('yarn dev', { stdio: 'inherit' });
      runDev();
    } else {
      // exec('yarn dev', { stdio: 'inherit' });
      runDev();
    }
  },
);

// exec('tsc --ignore-error', { timeout: 1000 }, (error, stdout, stderr) => {

//     console.log(chalk.blue('****************** build-ts log start ******************'));

//     if (error !== null) console.log(`exec error: ${error}`);

//     console.log(`${stdout}`);

//     // console.log(`${stderr}`);

//     console.log(chalk.blue('****************** build-ts log start ******************'));

// });
