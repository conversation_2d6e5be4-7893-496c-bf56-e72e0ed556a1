/**
 * 用作打包小程序并上传微信公众平台
 * upload.js
 * !/usr/bin/env
 * author: <PERSON><PERSON><PERSON><PERSON>
 * time: 2023-2-16
 * ps:  上传需打开微信开发者工具的代理设置。  路径：设置 -> 代理设置 -> 使用系统代理
 * version.js 添加小程序 appid等信息
 * */

/* eslint-disable import/no-commonjs */
const path = require('path');
const child = require('child_process');
const {
  replaceConfig,
  replaceVersion,
  getCurrentApp,
  writeVersion,
} = require('./utils');
const fs = require('fs');

const exec = child.execSync;

function getDays() {
  const date = new Date();

  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const strDate = date.getDate();
  const hours = date.getHours();
  const minutes = date.getMinutes();

  return `在${year}年${month}月${strDate}日${hours}点${minutes}分提交上传`;
}

// const branch = child.execSync('git name-rev --name-only HEAD', {
//   encoding: 'utf8',
// });

const getconfig = (NODE_ENV, version, desc) => {
  return {
    path: `/Applications/wechatwebdevtools.app/Contents/MacOS/cli`,
    projectPath: `${process.cwd()}/dist`,
    version,
    desc: `${desc}，环境${NODE_ENV},${getDays()}`,
  };
};

const uploadHandler = ({
  env_number,
  NODE_ENV,
  version,
  desc,
  APP_ENV,
  appletType,
}) => {
  try {
    exec(
      `APP_ENV=weapp${env_number} NODE_ENV=${NODE_ENV} APPLETTYPE=${appletType} taro build --type weapp && rm -rf dist/assets `,
      { stdio: 'inherit' },
    );
    const timer = setInterval(() => {
      const isExists = fs.existsSync(
        path.join(__dirname, '../../dist', '/app.json'),
      );
      if (isExists) {
        clearInterval(timer);
        writeVersion(APP_ENV, version, NODE_ENV);
        const config = getconfig(NODE_ENV, version, desc);
        console.log(
          '\x1B[32m%s\x1B[39m',
          '上传配置:' + JSON.stringify(config, null, 2),
        );
        console.log('\x1B[32m%s\x1B[39m', '上传中...');

        if (NODE_ENV === 'online') {
          exec('git pull origin master', {
            stdio: 'inherit',
          });

          exec(`git add . && git commit -m 'feat: ${desc}' && git push`, {
            stdio: 'inherit',
          });

          exec('npm run mr', {
            stdio: 'inherit',
          });
        }

        child.exec(
          `${config.path} upload --project ${config.projectPath} -v ${config.version} -d ${config.desc}`,
          { stdio: 'inherit' },
          (err, res) => {
            if (res) {
              exec(
                `${config.path} upload --project ${config.projectPath} -v ${config.version} -d ${config.desc}`,
                { stdio: 'inherit' },
              );
            }
          },
        );
      }
    }, 500);
  } catch (error) {
    process.exit(1);
  }
};

getCurrentApp()
  .then(res => {
    const { APP_ENV, NODE_ENV, envNumberUpperCase, appletType } = res;
    if (!APP_ENV) {
      console.log('\x1B[31m%s\x1B[39m', '请先设置需要打包小程序');
      return;
    }
    if (!NODE_ENV) {
      console.log('\x1B[31m%s\x1B[39m', '请先设置打包环境');
      return;
    }
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
    rl.question('请输入版本描述\n', function(prompt) {
      replaceConfig(APP_ENV);
      const version = replaceVersion(APP_ENV);
      uploadHandler({
        APP_ENV,
        NODE_ENV,
        version,
        env_number: envNumberUpperCase,
        desc: prompt,
        appletType,
      });
      rl.close();
    });
  })
  .catch(() => {
    process.exit(1);
  });
