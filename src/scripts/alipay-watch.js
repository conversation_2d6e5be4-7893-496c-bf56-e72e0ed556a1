const { execSync } = require('child_process');
const { replaceConfig, getCurrentApp, MINI_TYPES } = require('./utils');

// 获取当前支付宝小程序信息
getCurrentApp(MINI_TYPES.ALIPAY).then((res) => {
  const {
    APP_ENV,
    NODE_ENV,
    envNumberUpperCase,
    isBuild,
    appletType,
    miniType,
  } = res;

  console.log(
    '🚀 ~ alipay-watch.js:16 ~ getCurrentApp ~ APP_ENV:',
    APP_ENV,
    'NODE_ENV:',
    NODE_ENV,
    'envNumberUpperCase:',
    envNumberUpperCase,
    'isBuild:',
    isBuild,
    'appletType:',
    appletType,
    'miniType:',
    miniType,
  );

  console.log(`使用支付宝小程序: ${APP_ENV}, 环境: ${NODE_ENV}`);

  // 替换配置文件
  replaceConfig(APP_ENV, MINI_TYPES.ALIPAY);

  // 设置环境变量
  process.env.APP_ENV = `alipay${envNumberUpperCase}`;
  process.env.NODE_ENV = NODE_ENV;

  // 执行构建命令
  try {
    if (isBuild) {
      console.log('开始构建支付宝小程序...');
      execSync(`taro build --type alipay && rm -rf dist/assets`, {
        stdio: 'inherit',
      });
      console.log('支付宝小程序构建成功！');
    } else {
      console.log('开始开发支付宝小程序...');
      execSync(`RUNLOCAL=runlocal taro build --type alipay --watch`, {
        stdio: 'inherit',
      });
    }
  } catch (error) {
    console.error('支付宝小程序构建失败:', error);
    process.exit(1);
  }
});
