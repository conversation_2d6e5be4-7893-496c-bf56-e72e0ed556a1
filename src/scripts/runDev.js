/**
 * 用作本地运行小程序
 * runDev.js
 * !/usr/bin/env
 * author: <PERSON><PERSON><PERSON><PERSON>
 * time: 2023-2-16
 * */

/* eslint-disable import/no-commonjs */
const child = require('child_process');
const { replaceConfig, getCurrentApp } = require('./utils');

const exec = child.execSync;

function runDev() {
  getCurrentApp()
    .then((res) => {
      const {
        APP_ENV,
        NODE_ENV,
        envNumberUpperCase,
        isBuild,
        appletType,
        miniType,
      } = res;
      console.log(
        '🚀 ~ runDev.js:27 ~ getCurrentApp ~ APP_ENV:',
        APP_ENV,
        'NODE_ENV:',
        NODE_ENV,
        'envNumberUpperCase:',
        envNumberUpperCase,
        'isBuild:',
        isBuild,
        'appletType:',
        appletType,
        'miniType:',
        miniType,
      );
      if (!APP_ENV) {
        console.log('\x1B[31m%s\x1B[39m', '请先设置需要运行小程序');
        return;
      }
      if (isBuild && !NODE_ENV) {
        console.log('\x1B[31m%s\x1B[39m', '请先设置打包环境');
        return;
      }
      replaceConfig(APP_ENV);
      exec(
        `APP_ENV=weapp${envNumberUpperCase} ${
          NODE_ENV ? `NODE_ENV=${NODE_ENV}` : ''
        } APPLETTYPE=${appletType} ${
          !isBuild ? 'RUNLOCAL=runlocal' : ''
        } taro build --type weapp ${
          !isBuild ? '--watch' : '&& rm -rf dist/assets'
        }`,
        {
          stdio: 'inherit',
        },
      );
    })
    .catch(() => {
      process.exit(1);
    });
}

module.exports = runDev;
