const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const alipayVersions = require('./alipay-version');

// 解析命令行参数
const args = process.argv.slice(2);
const versionArg = args.find((arg) => arg.startsWith('v'));
const version = versionArg ? versionArg.substring(1) : null;
const envArg = args.find((arg) =>
  ['dev', 'test', 'gray', 'online'].includes(arg),
);
const env = envArg || 'dev';

// 根据版本选择对应的小程序
let appid = '';
let appName = '';

if (version) {
  // 查找对应版本的小程序
  for (const [name, info] of Object.entries(alipayVersions)) {
    if (info.env_number === `V${version}`) {
      appName = name;
      appid = info.appid;
      break;
    }
  }
}

// 如果未找到对应版本的小程序，使用第一个
if (!appName) {
  appName = Object.keys(alipayVersions)[0];
  appid = alipayVersions[appName].appid;
}

console.log(`使用支付宝小程序: ${appName}, appid: ${appid}, 环境: ${env}`);

// 生成支付宝小程序配置文件
const config = {
  compileType: 'miniprogram',
  miniprogramRoot: 'dist/',
  enableAppxNg: true,
  appid: appid,
};

// 写入配置文件
const configPath = path.resolve(__dirname, '../../project.alipay.json');
fs.writeFileSync(configPath, JSON.stringify(config, null, 2), 'utf8');

console.log(`支付宝小程序配置文件已生成: ${configPath}`);

// 设置环境变量
process.env.APP_ENV = `alipayV${version || '0'}`;
process.env.NODE_ENV = env;

// 执行构建命令
try {
  console.log('开始构建支付宝小程序...');
  execSync(`taro build --type alipay && rm -rf dist/assets`, {
    stdio: 'inherit',
  });
  console.log('支付宝小程序构建成功！');

  // 上传支付宝小程序
  console.log('开始上传支付宝小程序...');
  // 这里需要根据实际情况修改上传命令
  // 例如：execSync(`支付宝小程序上传命令 ${appid}`, { stdio: 'inherit' });
  console.log('支付宝小程序上传成功！');
} catch (error) {
  console.error('支付宝小程序上传失败:', error);
  process.exit(1);
}
