/**
 * 测试 weappV19 环境配置
 * 用于验证视频号直播测试页面的环境配置是否正确
 */

// 模拟环境变量
process.env.APP_ENV = 'weappV19';
process.env.TARO_ENV = 'weapp';

// 导入配置
const config = require('../app.config.ts');

console.log('🚀 测试 weappV19 环境配置');
console.log('环境变量 APP_ENV:', process.env.APP_ENV);
console.log('环境变量 TARO_ENV:', process.env.TARO_ENV);

// 检查页面配置
if (config.default && config.default.pages) {
  console.log('\n📄 页面配置:');
  config.default.pages.forEach((page, index) => {
    console.log(`  ${index + 1}. ${page}`);
  });
  
  // 检查是否包含视频号直播测试页面
  const hasChannelLiveTest = config.default.pages.includes('pages/channelLiveTest/index');
  console.log('\n✅ 视频号直播测试页面:', hasChannelLiveTest ? '已配置' : '未配置');
  
  // 检查页面数量
  console.log('📊 总页面数量:', config.default.pages.length);
} else {
  console.log('❌ 无法读取页面配置');
}

// 检查权限配置
if (config.default && config.default.requiredPrivateInfos) {
  console.log('\n🔐 权限配置:');
  config.default.requiredPrivateInfos.forEach((permission, index) => {
    console.log(`  ${index + 1}. ${permission}`);
  });
  
  // 检查是否包含视频号直播权限
  const hasChannelLivePermission = config.default.requiredPrivateInfos.includes('getChannelsLiveInfo');
  console.log('\n✅ 视频号直播权限:', hasChannelLivePermission ? '已配置' : '未配置');
}

console.log('\n🎉 weappV19 环境配置测试完成');
