/* eslint-disable no-undef */
/* eslint-disable import/no-commonjs */
const path = require('path');
const fs = require('fs');
const projectConfig = require('../../project.config.json');
const projectAlipayConfig = require('../../project.alipay.json');
const _version = require('./version');
const _alipayVersion = require('./alipay-version');
const inquirer = require('inquirer');

// 小程序类型
const MINI_TYPES = {
  WECHAT: 'wechat',
  ALIPAY: 'alipay',
};

function replaceConfig(_selApp, miniType = MINI_TYPES.WECHAT) {
  let config;
  let configPath;
  let versionInfo;

  if (miniType === MINI_TYPES.WECHAT) {
    config = projectConfig;
    configPath = path.join(__dirname, '../../project.config.json');
    versionInfo = _version[_selApp];
  } else {
    config = projectAlipayConfig;
    configPath = path.join(__dirname, '../../project.alipay.json');
    versionInfo = _alipayVersion[_selApp];
  }

  const appid = versionInfo.appid;
  const configReplacedStr = JSON.stringify(
    config,
    (key, value) => {
      return key === 'description'
        ? _selApp || ''
        : value && key === 'appid'
        ? appid || ''
        : value;
    },
    3,
  );

  fs.writeFileSync(configPath, configReplacedStr, (err) => {
    if (err) {
      console.log('\x1B[31m%s\x1B[39m', `配置${miniType}小程序appid失败...`);
      throw new Error(err);
    }
  });
  console.log('\x1B[32m%s\x1B[39m', `配置${miniType}小程序appid成功...`);
}

// 获取当前执行的小程序
function getCurrentMini(miniType = MINI_TYPES.WECHAT) {
  let APP_ENV = '';
  let NODE_ENV = '';
  // 获取node命令行参数
  const argvs = require('minimist')(process.argv.slice(2));
  const argv = argvs['_'];
  const envNumber = argv[0] || '';
  let envNumberUpperCase = envNumber.toUpperCase();
  // 可执行的环境变量数组
  const envs = ['dev', 'Test', 'gray', 'online'];
  // 获取命令行参数的最后一个参数
  const env = argv[1];

  // 根据小程序类型选择版本文件
  const version = miniType === MINI_TYPES.WECHAT ? _version : _alipayVersion;

  // 获取_version中的env_number的数组集合
  const env_numbers = Object.keys(version).map(
    (item) => version[item].env_number,
  );
  // 判断命令行参数是否在env_number中
  if (env_numbers.includes(envNumberUpperCase)) {
    APP_ENV = Object.keys(version).filter(
      (item) => version[item].env_number === envNumberUpperCase,
    )[0];
    console.log('\x1B[32m%s\x1B[39m', `本地执行${miniType}小程序: ${APP_ENV}`);
    // 判断命令行参数env是否在envs中,存在就赋值不存在默认为dev
    NODE_ENV = envs.includes(env) ? env : '';
    console.log('\x1B[32m%s\x1B[39m', `本地执行环境: ${NODE_ENV}`);
  }
  return {
    APP_ENV,
    NODE_ENV,
    envNumberUpperCase,
    envs,
    isBuild: argvs['build'] === 'build',
    miniType,
  };
}

// Object转字符串并格式化
function formatObj(obj) {
  const objJson = JSON.stringify(obj, null, '\t');
  const strArr = objJson.split(/\r\n|\n|\r/gm).map((item) => {
    return item.replace(/"/, '').replace(/\"\:/, ':');
  });
  const objStr = strArr.join('\r\n');
  return objStr;
}

// 版本号递增 1.0.0 => 1.0.1 逢20进一 1.0.19 => 1.1.0 逢20进一 1.19.19 => 2.0.0
function versionIncrease(version) {
  const versionArr = version.split('.');
  const lastVersion = versionArr[versionArr.length - 1];
  if (lastVersion < 19) {
    versionArr[versionArr.length - 1] = Number(lastVersion) + 1;
  } else {
    versionArr[versionArr.length - 1] = 0;
    versionArr[versionArr.length - 2] =
      Number(versionArr[versionArr.length - 2]) + 1;
  }
  return versionArr.join('.');
}

// 修改version.js文件中的版本号
function replaceVersion(_selApp, miniType = MINI_TYPES.WECHAT) {
  const version =
    miniType === MINI_TYPES.WECHAT
      ? _version[_selApp].release_number || '1.0.0'
      : _alipayVersion[_selApp].release_number || '1.0.0';

  const currentVersion = versionIncrease(version);
  console.log('\x1B[32m%s\x1B[39m', '当前版本号: ' + currentVersion);
  return currentVersion;
}

// 修改版本号写入文件
function writeVersion(
  _selApp,
  currentVersion,
  NODE_ENV,
  miniType = MINI_TYPES.WECHAT,
) {
  // 线上环境才修改版本号
  if (NODE_ENV === 'online') {
    if (miniType === MINI_TYPES.WECHAT) {
      _version[_selApp].release_number = currentVersion;
      const _versionStr = `module.exports=${formatObj(_version)}`;
      const routerFile = path.join(__dirname, './version.js');
      // fs 修改version.js文件中的版本号
      fs.writeFileSync(routerFile, _versionStr, (err) => {
        if (err) {
          console.log('\x1B[31m%s\x1B[39m', '修改微信小程序版本号失败...');
          throw new Error(err);
        }
      });
    } else {
      _alipayVersion[_selApp].release_number = currentVersion;
      const _versionStr = `module.exports=${formatObj(_alipayVersion)}`;
      const routerFile = path.join(__dirname, './alipay-version.js');
      // fs 修改alipay-version.js文件中的版本号
      fs.writeFileSync(routerFile, _versionStr, (err) => {
        if (err) {
          console.log('\x1B[31m%s\x1B[39m', '修改支付宝小程序版本号失败...');
          throw new Error(err);
        }
      });
    }
  }
}

function getCurrentApp(miniType = MINI_TYPES.WECHAT) {
  return new Promise((resolve) => {
    const { APP_ENV, NODE_ENV, envNumberUpperCase, envs, isBuild } =
      getCurrentMini(miniType);

    // 根据小程序类型选择版本文件
    const version = miniType === MINI_TYPES.WECHAT ? _version : _alipayVersion;

    if (APP_ENV) {
      const appletType = version[APP_ENV].appletType;
      return resolve({
        APP_ENV,
        NODE_ENV,
        envNumberUpperCase,
        appletType,
        isBuild,
        miniType,
      });
    }
    const questions = [
      {
        name: 'APP_ENV',
        type: 'list',
        message: `请选择${miniType}小程序`,
        choices: Object.keys(version),
      },
      {
        name: 'NODE_ENV',
        type: 'list',
        message: '请选择小程序环境',
        choices: envs,
      },
    ];
    inquirer.prompt(questions).then((answers) => {
      const env_number = version[answers.APP_ENV].env_number;
      const appletType = version[answers.APP_ENV].appletType;
      return resolve({
        APP_ENV: answers.APP_ENV,
        NODE_ENV: answers.NODE_ENV,
        envNumberUpperCase: env_number,
        appletType,
        isBuild,
        miniType,
      });
    });
  });
}

module.exports = {
  replaceConfig,
  getCurrentMini,
  versionIncrease,
  replaceVersion,
  getCurrentApp,
  writeVersion,
  MINI_TYPES,
};
