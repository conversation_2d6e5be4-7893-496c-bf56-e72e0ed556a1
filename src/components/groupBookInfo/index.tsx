import Taro from '@tarojs/taro';
import { useEffect, useMemo, useState } from 'react';
import bg from '@/assets/groupBuying/booking-bg.png';
import Img from '@/assets/groupBuying/booking-img.png';
import Psuccess from '@/assets/groupBuying/group-success.png';
import Pfail from '@/assets/groupBuying/group-fail.png';
import Lackuser from '@/assets/groupBuying/lack-user.png';
import { Image, View, Text, Button } from '@tarojs/components';
import { useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import CountDown from '../groupbuy/countdown';
import './index.scss';

export default (props) => {
  const userId = useSelector((state: UserStateType) => state.userid);
  const {
    groupInfo,
    groupParentFrom = 'newIndex',
    joinGroupHandler = null,
    teacherPage = false,
    rebuildHadnler = null,
    directHadnler = null,
  } = props;
  const [countDownNum, setCountDown] = useState(0);
  useEffect(() => {
    if (!groupInfo) return;
    let remainingTime = groupInfo.endtime - new Date().getTime();
    if (remainingTime < 0) remainingTime = 0;
    setCountDown(remainingTime);
  }, [groupInfo]);

  return (
    <View
      className='content-group'
      style={!teacherPage ? 'margin-bottom:-60px;' : ''}
    >
      <Image className='bg' src={bg} />
      <View className='package-info'>
        <Image className='package-info-img' src={Img} />
        <View className='package-info-details'>
          <View className='package-info-details-title'>小熊美术体验版</View>
          <View className='package-info-details-nums'>3人团</View>
          <View className='package-info-details-price'>
            <View className='package-info-details-price-info'>
              {/* ¥{groupParentFrom == 'index9' ? 9 : 14}.9 <Text>¥36</Text> */}
              ¥{groupParentFrom == 'index9' ? 9 : 10}.9 <Text>¥36</Text>
            </View>
            <View className='package-info-details-price-buy-nums'>
              200w+人购买
            </View>
          </View>
        </View>
        {groupInfo && (
          <>
            {groupInfo.status !== 'DEFAULT' && (
              <Image
                className='state-img'
                src={groupInfo.status == 'SUCCESS' ? Psuccess : Pfail}
              ></Image>
            )}
          </>
        )}
      </View>
      <View className='group-team'>
        <View className='group-team-title'>
          {groupInfo && (
            <>
              {groupInfo.status == 'SUCCESS' && (
                <>
                  拼团成功，
                  <Text>
                    {teacherPage ? '添加老师激活课程服务' : '请重新开团'}
                  </Text>
                </>
              )}
              {groupInfo.status == 'FAIL' && (
                <>
                  拼团失败，
                  <Text>
                    {teacherPage ? '报名费用将原路退回' : '请重新开团'}
                  </Text>
                </>
              )}
              {groupInfo.status == 'DEFAULT' && (
                <>
                  距离拼团成功还差 <Text>{3 - groupInfo.joinNumber}</Text> 人
                </>
              )}
            </>
          )}
        </View>
        <View className='group-team-crew'>
          {groupInfo &&
            groupInfo.joinHeads.map((o, i) => {
              return teacherPage ? (
                o ? (
                  <View className='group-team-crew-item' key={i}>
                    <Image className='' src={o} />
                    {i == 0 && <Text>团长</Text>}
                  </View>
                ) : (
                  <Button
                    openType='share'
                    className='group-team-crew-item'
                    key={i}
                  >
                    <Image className='' src={Lackuser} />
                  </Button>
                )
              ) : (
                <View className='group-team-crew-item' key={i}>
                  <Image className='' src={o ? o : Lackuser} />
                  {i == 0 && <Text>团长</Text>}
                </View>
              );
            })}
        </View>
        {groupInfo && (
          <>
            {(groupInfo.status == 'DEFAULT' || groupInfo.status == 'FAIL') && (
              <>
                <View className='count-down'>
                  剩余&nbsp;
                  <CountDown isShowHouers payCountDown={countDownNum} />
                  &nbsp;结束
                </View>
              </>
            )}
            {/* 首页 */}
            {(userId != groupInfo.uid || !userId) &&
              // (groupInfo.status == 'DEFAULT' || groupInfo.status == 'FAIL') &&
              !teacherPage && (
                <>
                  <View className='starting-btn' onClick={joinGroupHandler}>
                    {/* ¥{groupParentFrom == 'index9' ? 9 : 14}.9 一键 */}¥
                    {groupParentFrom == 'index9' ? 9 : 10}.9 一键
                    {groupInfo.status == 'DEFAULT' ? '参' : '开'}团
                  </View>
                </>
              )}
            {/* 老师页 */}
            {groupInfo.status == 'FAIL' && teacherPage && (
              <>
                <View className='starting-btn reset' onClick={rebuildHadnler}>
                  重新发起
                </View>
                <View className='starting-btn to-buy' onClick={directHadnler}>
                  直接购买
                </View>
              </>
            )}
          </>
        )}
      </View>
    </View>
  );
};
