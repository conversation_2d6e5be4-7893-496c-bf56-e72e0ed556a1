.content-group {
  position: relative;
  padding-bottom: 60px;
  padding-top: 176px;
  .bg {
    position: absolute;
    width: 750px;
    height: 560px;
    top: 0;
  }
  .package-info {
    width: 690px;
    height: 224px;
    background: #fff;
    border-radius: 32px;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    position: relative;
    z-index: 2;
    margin: 0 auto;
    &-img {
      width: 176px;
      height: 176px;
      border-radius: 16px;
      margin-right: 24px;
    }
    &-details {
      flex: 1;
      &-title {
        font-size: 32px;
        margin-bottom: 8px;
      }
      &-nums {
        width: 64px;
        height: 34px;
        line-height: 34px;
        color: #ffa700;
        font-size: 24px;
        text-align: center;
        background-color: #fff4e0;
        margin-bottom: 42px;
        padding: 0 8px;
      }
      &-price {
        font-size: 40px;
        color: #fa6c3a;
        font-weight: 600;
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: baseline;
        Text {
          color: #999999;
          font-size: 28px;
          font-weight: 400;
          margin-right: 8px;
          text-decoration: line-through;
        }
        &-buy-nums {
          font-weight: 400;
          color: #999999;
          font-size: 28px;
        }
      }
    }
    .state-img {
      position: absolute;
      width: 136px;
      height: 128px;
      right: 0;
      top: 0;
    }
  }
  .group-team {
    width: 690px;
    padding: 40px 76px 48px;
    box-sizing: border-box;
    background-color: #fff;
    margin: 30px auto;
    position: relative;
    border-radius: 32px;
    z-index: 2;
    text-align: center;
    &-title {
      font-size: 36px;
      margin-bottom: 50px;
      Text {
        color: #fa6c3a;
      }
    }
    &-crew {
      display: flex;
      justify-content: space-between;
      overflow: visible;
      margin-bottom: 30px;
      &-item {
        position: relative;
        overflow: visible;
        padding: 2px;
        &.lack image {
          border: none;
        }
        image {
          border: 2px solid #ffa700;
          width: 128px;
          height: 128px;
          border-radius: 50%;
        }
        Text {
          position: absolute;
          width: 96px;
          height: 40px;
          line-height: 40px;
          border-radius: 40px;
          text-align: center;
          color: #fff;
          font-size: 28px;
          left: 0;
          right: 0;
          bottom: -20px;
          margin: 0 auto;
          background: #ffa700;
        }
      }
    }
    .starting-btn {
      width: 536px;
      height: 88px;
      line-height: 88px;
      font-size: 32px;
      color: #fff;
      background: linear-gradient(to right, #ff6a00, #ffa300);
      border-radius: 88px;
      margin-top: 74px;
      &.to-buy {
        color: #fa6c3a;
        background: #fff;
        border: 1px solid #ff6b00;
        margin-top: 30px;
      }
    }
    .count-down {
      font-size: 28px;
      margin: 58px 0 48px;
    }
  }
  .custom-time-main.new {
    .at-countdown__item {
      align-items: baseline;
    }
    .at-countdown__time {
      font-size: 28px;
      width: 40px;
      padding: 4px;
      height: 40px;
      color: #fa6c3a;
      background-color: #feede7;
    }
    .at-countdown__separator {
      font-size: 28px;
    }
  }
}

.custom-time-main .at-countdown__item:last-child {
  display: inline-flex;
}
.timer {
  display: inline-flex;
  &.normal {
    .time-num,
    .colon {
      font-size: 28px;
      color: #fa6c3a;
    }
    .time-num {
      width: 40px;
      height: 40px;
      color: #fa6c3a;
      line-height: 40px;
      background-color: #feede7;
    }
  }
}
button {
  background-color: transparent;
  border: none;
  margin: 0;
  &::after {
    border: none;
  }
}
