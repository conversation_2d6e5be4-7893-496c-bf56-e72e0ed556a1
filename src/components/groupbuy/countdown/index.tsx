import { useEffect, useState } from 'react';
import { View } from '@tarojs/components';
import './index.scss';

export default function Index(props) {
  const {
    propClassName,
    payCountDown = 0,
    timeReachZero,
    isShowHouers = false,
    isShowMin = true,
  } = props;
  const [timeRest, setTimeRest] = useState<number>(payCountDown);
  const [hour, setHour] = useState<number | string>(0);
  const [min, setMin] = useState<number | string>(0);
  const [sec, setSec] = useState<number | string>(0);
  const [ms, setMs] = useState<number | string>(0);
  const [mountedTime, setMountedTime] = useState<number>(Date.now());
  useEffect(() => {
    setTimeRest(payCountDown);
    const startTimer = () => {
      if (!payCountDown) {
        return;
      }
      const timer = setInterval(() => {
        const timeNow = Date.now();
        const range = timeNow - mountedTime;
        const rest = payCountDown - range;
        // console.log(rest);
        if (rest <= 0) {
          setTimeRest(0);
          clearInterval(timer);
          timeReachZero && timeReachZero();
        } else {
          setTimeRest(rest);
        }
      }, 100);
      return timer;
    };
    const timer = startTimer();
    return () => {
      timer && clearInterval(timer);
    };
  }, [payCountDown, timeReachZero, mountedTime]);

  useEffect(() => {
    setMountedTime(Date.now());
  }, [payCountDown]);
  useEffect(() => {
    let $time: number | string = timeRest;
    let $hour: number | string = Math.floor($time / 3600000);
    let $min: number | string = Math.floor(($time - $hour * 3600000) / 60000);
    let $sec: number | string = Math.floor(
      ($time - $hour * 3600000 - $min * 60000) / 1000,
    );
    let $ms: number | string = Math.floor(
      // (time - hour * 3600000 - min * 60000 - sec * 1000) / 10
      // ($time - $hour * 3600000 - $min * 60000 - $sec * 1000) / 100,
      ($time - $hour * 3600000 - $min * 60000 - $sec * 1000) / 15,
    );
    $hour = String($hour).length === 1 ? `0${$hour}` : $hour;
    $min = String($min).length === 1 ? `0${$min}` : $min;
    $sec = String($sec).length === 1 ? `0${$sec}` : $sec;
    // $ms = String($ms).length === 1 ? `0${$ms}` : $ms
    // $ms = $ms;
    setHour($hour);
    setMin($min);
    setSec($sec);
    setMs($ms);
  }, [timeRest]);

  return (
    <View className={`timer ${propClassName || 'normal'}`}>
      {isShowHouers && (
        <>
          <View className='time-num hour'>{hour}</View>
          <View className='colon'>:</View>
        </>
      )}
      {isShowMin && (
        <>
          <View className='time-num min'>{min}</View>
          <View className='colon'>:</View>
        </>
      )}
      <View className='time-num sec'>{sec}</View>
      <View className='colon'>:</View>
      <View className='time-num ms'>{+ms > 10 ? ms : `0${ms}`}</View>
    </View>
  );
}
