import { View } from '@tarojs/components';
import './tag.scss';

export default function Tag(props) {
  const {
    bgColor = '#ffffff',
    textColor = '#000000',
    textSize = '22rpx',
    borderRadius = '0rpx',
    text = '',
    type = 0,
    newStyle = {},
  } = props;
  const customStyle = Object.assign(
    {
      backgroundColor: bgColor,
      color: textColor,
      fontSize: textSize,
      borderRadius,
    },
    newStyle,
  );
  const typeComponent = {
    1: 'c-label-white',
    2: 'c-label-green',
    3: 'c-label-red',
  };
  return type > 0 ? (
    <View className={typeComponent[type]}>{text}</View>
  ) : (
    <View style={customStyle}>{text}</View>
  );
}
