.conversion_teacher_wrap {
  width: 100%;
  padding-bottom: 128px;
  min-height: 100vh;
  background-color: #ffc500;

  image {
    width: 100%;
    height: 100%;
  }

  .header {
    height: 64px;
    background: #efffe8;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 0;

    .tc_suc {
      width: 40px;
      height: 40px;
      background-image: url('../../assets/conversion/tc_suc.png');
      background-position: 0 0;
      background-repeat: no-repeat;
      background-size: cover;
    }

    .tc_word {
      font-size: 30px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #45b249;
      margin-left: 8px;
    }
  }

  .top_title {
    text-align: center;
    height: 60px;
    font-size: 50px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    line-height: 60px;
    margin-top: 54px;

    &_1 {
      color: #191919;
    }

    &_2 {
      color: #ff6d00;
      margin-top: 10px;
    }
  }

  .top-wrap {
    width: 637px;
    height: 155px;
    margin: 43px auto 0;
  }

  .section {
    width: 670px;
    background: #ffffff;
    border-radius: 40px;
    margin: 98px auto 0;
    position: relative;
    padding-bottom: 20px;

    .header_pic {
      width: 150px;
      height: 150px;
      border-radius: 50%;
      position: absolute;
      margin-left: -75px;
      top: -75px;
      left: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #ffffff;

      .header_box {
        width: 130px;
        height: 130px;
        border-radius: 50%;
        overflow: hidden;

        image {
          width: 130px;
          height: 130px;
        }
      }
    }

    .section_box {
      padding-top: 87px;

      .name {
        text-align: center;
        height: 40px;
        font-size: 28px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #666666;
        line-height: 40px;
      }

      .certification {
        margin-top: 4px;
        display: flex;
        align-items: center;
        justify-content: center;

        .certification_l {
          width: 110px;
          height: 34px;
        }

        .certification_r {
          width: 110px;
          height: 34px;
          margin-left: 10px;
        }
      }

      .qrcode_tips_box {
        width: 100%;
        text-align: center;
        height: 152px;
        margin-top: 57px;
        margin-bottom: 52px;

        .qrcode_tips_f {
          font-size: 56px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #333333;
          line-height: 76px;
        }
      }

      .qrCode {
        width: 260px;
        height: 260px;
        border-radius: 10px;
        margin: 40px auto 0;
        overflow: hidden;
        border: 1px solid #cccccc;

        image {
          width: 100%;
          height: 100%;
        }
      }

      .qrcode_tips {
        margin: 40px auto 75px;
        text-align: center;

        & > text {
          display: inline-block;
          font-size: 40px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #ff7300;
          background: url('../../assets/conversion/arrow-up-icon.png') left
              center no-repeat,
            url('../../assets/conversion/arrow-up-icon.png') right center
              no-repeat;
          background-size: 20px auto;
          padding: 0 36px;

          &.ali-txt {
            font-size: 32px;
            background: #fff;
          }
        }
        .long-press {
        }
      }

      .teacher_tips {
        width: 100%;
        margin-top: 35px;
        text-align: center;

        .w-80-percent {
          width: 80%;
        }
        .tt {
          font-size: 34px;
          display: flex;
          align-items: center;
          font-weight: 600;
          justify-content: center;
          margin: 20px 0;
          Text {
            display: inline-block;
            width: 83px;
            height: 2px;
            background: linear-gradient(to right, #fff, #989898);
            &:nth-child(2) {
              background: linear-gradient(to left, #fff, #989898);
            }
            margin: 0px 16px;
          }
        }
      }

      .footer_tips {
        height: 33px;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        line-height: 33px;
        text-align: center;
        margin-top: 10px;
      }
    }
  }

  .foot_pic {
    width: 686px;
    height: 444px;
    margin: 42px auto 0;

    image {
      width: 100%;
      height: 100%;
    }
  }

  &_v4 {
    background-color: #f7f7f7;
    padding-bottom: 0;

    .section {
      margin: 200px auto 0;

      .section_box {
        padding-bottom: 0;
      }
    }
  }
}
