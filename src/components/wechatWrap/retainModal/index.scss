.conver_retain_wrap {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100vh;
  .conver_retain_wrap_layout {
    background: #000000;
    opacity: 0.7;
    width: 100%;
    height: 100%;
  }
  .conver_retain_wrap_section {
    width: 686rpx;
    height: 799rpx;
    background: #ffffff;
    border-radius: 40rpx;
    padding-top: 47px;
    box-sizing: border-box;
    position: absolute;
    left: 50%;
    top: 50%;
    margin: -400px 0 0 -343px;
    .section_icon {
      width: 246px;
      height: 210px;
      margin: 0 auto 21px;
      image {
        width: 100%;
        height: 100%;
      }
    }
    .section_word {
      text-align: center;
      height: 80rpx;
      font-size: 48rpx;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #333333;
      line-height: 80rpx;
      margin-bottom: 19px;
    }
    .section_icon_bottom {
      width: 664px;
      height: 258px;
      margin: 0 auto 19px;
      image {
        width: 100%;
        height: 100%;
      }
    }
    .section_btn_wrap {
      display: flex;
      padding: 0 48px;
      align-items: center;
      justify-content: space-between;
    }
    .section_btn_left {
      width: 230rpx;
      height: 90rpx;
      border-radius: 45rpx;
      border: 2rpx solid #ffcc1d;
      font-size: 32rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #905d1f;
      line-height: 90px;
      text-align: center;
    }
    .section_btn_right {
      width: 340rpx;
      height: 90rpx;
      background: linear-gradient(270deg, #ffa300 0%, #ff6a00 100%);
      border-radius: 45rpx;
      font-size: 32rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 90px;
      text-align: center;
    }
  }
}
.hide {
  display: none;
}
