import { Image, Text, View } from '@tarojs/components';
import { useEffect, useRef, useState } from 'react';
import Taro, { useRouter } from '@tarojs/taro';
import { getTeacherInfo } from '@/api/groupbuy';
import { AtToast } from 'taro-ui';
import { UserStateType } from '@/store/groupbuy/state';
import { useSelector } from 'react-redux';
import sensors from '@/utils/sensors_data';
import ImgLongPress from '@/components/ImgLongPress';
import tctop from '@/assets/conversion/tc_top.png';
import tcqy from '@/assets/conversion/tc_qy.png';
import tcsm from '@/assets/conversion/tc_sm.png';
import DefaultHead from '@/assets/conversion/default-head.jpg';
import tcteactip from '@/assets/conversion/tc_teacher_tips.png';
import tcteactipV2 from '@/assets/conversion/tc_teacher_tips_v2.png';
import tcteactipV4 from '@/assets/conversion/tc_teacher_v4.png';
// import teacherTX from '@/assets/conversion/teacher.png';
import PageContainerWrap from './pageContainerWrap';
import RetainModal from './retainModal';

import './index.scss';

// showType => 1/2/3/4 是否制定ui 或者随机

const abtest = ['无介绍', '四个介绍', '三个介绍', '无介绍', '老师团队'];
// const randomNum = Math.floor(Math.random() * 4);
const randomNum = 3;

const isAlipay = process.env.TARO_ENV == 'alipay';

export default function WechatWrap({ showType = '' }) {
  const type = showType || randomNum;
  const isShow = val => val == type;

  const wrapClassFn = () => {
    if (Number(type) == 4) {
      return 'conversion_teacher_wrap conversion_teacher_wrap_v4';
    }
    return 'conversion_teacher_wrap';
  };
  const channelId = useSelector((state: UserStateType) => state.channelId);
  const wrapClass = wrapClassFn();
  const router = useRouter();
  let params = router.params;
  const [teacherName, setteacherName] = useState('');
  const [teacherTX, setteacherTX] = useState('');
  const [pageCVisible, setPageCVisible] = useState(false);
  const [retainVisible, setRetainVisible] = useState(true);
  const [isLongPressHandle, setIsLongPressHandle] = useState(false);

  // 小学艺术乐园专有埋点
  const [isFromH5AndNeedPoint] = useState(() => {
    if (Taro.getEnv() === 'WEAPP') {
      return (
        Taro.getAccountInfoSync().miniProgram.appId === 'wx34831c7cbd4406e5'
      );
    }
    return false;
  });

  const [isOpen, setIsOpen] = useState(true);
  const qrcodeNo = useRef('');
  const orderid = useSelector((state: UserStateType) => state.orderId);
  const orderId = params.outTradeNo || params.orderId || orderid;
  const getArtTeacher = () => {
    orderId &&
      getTeacherInfo({
        orderNo: orderId,
        addSource: params.addSource || '1',
      }).then(res => {
        if (res.code === 0) {
          qrcodeNo.current = res.payload.teacherWeChatQrCode;
          setteacherName(res.payload.teacherName);
          setteacherTX(res.payload.teacherHead);
          if (res.payload.teacherWeChatQrCode) {
            setIsOpen(false);
          }
        }
      });
  };

  const onLongPresshandle = () => {
    if (Taro.getEnv() === 'ALIPAY') {
      sensors.track('ai_marketing_AlipayminiAPP_teacherpageclick', {
        channel_id: params.channelId,
        urlType: params.urlType,
      });
      Taro.saveImageToPhotosAlbum({
        filePath: qrcodeNo.current,
        success: () => {
          Taro.showToast({
            title: '保存成功',
            icon: 'success',
            duration: 2000,
          });
        },
      });
    } else {
      sensors.track('xxms_testcourse_appletaddteacherpage_longpress', {
        diversion_subject: '',
        buy_model: 'model_4',
        abtest: abtest[type],
      });
      if (isFromH5AndNeedPoint) {
        sensors.track('ai_ArtSystemCourse_appletaddateacherclick', {
          open_source: router.params.openSource === 'H5' ? 2 : 1,
          channel_id: router.params.channelId || channelId,
          clickID: router.params.clickid,
          userType: 1,
        });
      }
    }
    setTimeout(() => {
      setIsLongPressHandle(true);
    }, 500);
  };

  const onBeforeLeave = () => {
    setPageCVisible(true);
  };

  const retainClose = () => {
    setRetainVisible(false);
  };

  const initEl = () => (
    <View className={wrapClass}>
      <View className='header'>
        <View className='tc_suc'></View>
        <View className='tc_word'>此二维码已经通过安全验证，请放心扫码</View>
      </View>
      {isShow(4) ? (
        <View className='top_title'>
          <View className='top_title_1'>添加专属指导老师</View>
          <View className='top_title_2'>激活课程</View>
        </View>
      ) : (
        <View className='top-wrap'>
          <Image src={tctop} mode='widthFix' />
        </View>
      )}

      <View
        className='section'
        style={{ paddingBottom: isAlipay ? '0.2rem' : '0.4rem' }}
      >
        <View className='header_pic'>
          <View className='header_box'>
            <Image src={teacherTX || DefaultHead} mode='widthFix' />
          </View>
        </View>
        <View className='section_box'>
          <View className='name'>{teacherName}老师</View>
          <View className='certification'>
            <View className='certification_l'>
              <Image src={tcqy} mode='widthFix' />
            </View>
            <View className='certification_r'>
              <Image src={tcsm} mode='widthFix' />
            </View>
          </View>
          {isShow(3) && (
            <View className='qrcode_tips_box'>
              <View className='qrcode_tips_f'>请务必添加二维码</View>
              <View className='qrcode_tips_f'>否则无法激活课程</View>
            </View>
          )}
          <View className='qrCode'>
            {Taro.getEnv() === 'WEAPP' ? (
              <Image
                src={qrcodeNo.current}
                mode='widthFix'
                showMenuByLongpress
                onLongPress={() => onLongPresshandle()}
              />
            ) : (
              <ImgLongPress
                currentUrl={qrcodeNo.current}
                showMenuByLongpress
                onLongPress={onLongPresshandle}
              />
            )}
          </View>
          <View className='qrcode_tips'>
            <Text className={`${isAlipay ? 'ali-txt' : ''}`}>
              {isAlipay
                ? '长按或截图保存二维码 微信内扫码添加'
                : '长按识别二维码添加'}
            </Text>
          </View>
          {(isShow(1) || isShow(2)) && (
            <View
              className='teacher_tips'
              style={{ paddingBottom: isShow(2) ? '20rpx' : 'inherit' }}
            >
              {isShow(1) && (
                <View className='tt'>
                  <Text />
                  添加老师 获取特权
                  <Text />
                </View>
              )}
              <Image
                src={isShow(1) ? tcteactip : tcteactipV2}
                mode='widthFix'
                className={isShow(1) ? '' : 'w-80-percent'}
              />
            </View>
          )}

          {!isAlipay && (
            <View className='footer_tips'>
              *若添加失败，截图保存二维码在微信中识别打开
            </View>
          )}
        </View>
      </View>
      {isShow(4) && (
        <View className='foot_pic'>
          <Image src={tcteactipV4} mode='widthFix' />
        </View>
      )}
      <AtToast
        isOpened={isOpen}
        text='正在为您匹配老师， 请稍等片刻...'
        status='loading'
        duration={0}
      ></AtToast>
    </View>
  );

  useEffect(() => {
    sensors.track('xxys_experienceCoursePage_addv_view', {
      buy_model: 'model_4',
      abtest: abtest[type],
    });

    if (isFromH5AndNeedPoint) {
      sensors.track('ai_ArtSystemCourse_appletaddteacherbrowse', {
        open_source: router.params.openSource === 'H5' ? 2 : 1,
        channel_id: router.params.channelId || channelId,
        clickID: router.params.clickid,
        userType: 1,
      });
    }
    for (let i = 1; i <= 6; i++) {
      setTimeout(() => {
        !qrcodeNo.current && getArtTeacher();
        if (i === 6 && isOpen) {
          setIsOpen(false);
        }
      }, 1500 * i);
    }
  }, []);

  return (
    <>
      {isAlipay || pageCVisible ? (
        <>
          {initEl()}
          {!isAlipay && (
            <RetainModal
              show={retainVisible && !isLongPressHandle}
              close={retainClose}
            />
          )}
        </>
      ) : (
        <PageContainerWrap onBeforeLeave={onBeforeLeave}>
          {initEl()}
        </PageContainerWrap>
      )}
    </>
  );
}
