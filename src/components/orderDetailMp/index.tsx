import { View, Image } from '@tarojs/components';
import Taro from '@tarojs/taro';
import leftIcon from '@/assets/orderdetailmp/left-icon.png';
import styles from './index.module.scss';

export default function Orderdetailmp() {
  const toPage = () => {
    Taro.navigateTo({
      url: `/pages/orderh5/index`,
    });
  };

  return (
    <View className={styles['orderdetail-mp']}>
      <View className={styles['orderdetail-mp-box']} onClick={toPage}>
        <View className={styles['orderdetail-mp-box-left']}>我的订单</View>
        <View className={styles['orderdetail-mp-box-right']}>
          <Image
            src={leftIcon}
            mode='widthFix'
            className={styles['orderdetail-mp-box-img']}
          />
        </View>
      </View>
    </View>
  );
}
