import Taro from '@tarojs/taro';
import { View } from '@tarojs/components';
import './index.scss';

function Rule() {
  return (
    <View className='content-box'>
      <View className='p'>
        参与活动前，请务必阅读本活动规则。凡参加活动，则代表您已阅读、理解并统一遵守本活动规则全部内容
      </View>
      <View className='content'>
        <View className='sub-title'>参与用户</View>
        <View className='desc'>小熊美术的注册用户可成为推荐人</View>
      </View>
      <View className='content'>
        <View className='sub-title'>推荐规则</View>
        <View className='desc'>
          推荐人通过个人邀请海报每推荐1位好友首次购买29元小熊美术双周体验版，可得25元现金奖励；推荐好友成功购买系统课未退费且上课1个月后，可再得50元现金奖励。现金奖励上不封顶，多邀多得。
        </View>
        {/* {meetRenew200 && <View className='desc'>
            1月限定活动：除以上现金奖励外，活动期内推荐1位好友首次购买29元小熊美术双周体验版，即可额外获得200元续费代金券1张。代金券不支持转赠，领取后有效期为7天，过期失效！
          </View>} */}
        <View className='desc'>
          推荐成功后，现金奖励将发放至邀请收入页面可随时提现（推荐人微信需大陆实名认证并绑定大陆的银行卡），代金券可用于续费抵扣。
        </View>
        <View className='desc'>亲友券邀请好友无现金、金币邀请奖励。</View>
      </View>
      <View className='content'>
        <View className='sub-title'>其他说明</View>
        <View className='desc'>
          作弊、刷单、邀请无实际操作用户行为包括但不限于：
        </View>
        <View className='sub-desc'>
          1、被邀请学员无真实上课数据（无实际参课、完课，无上传作品）
        </View>
        <View className='sub-desc'>
          2、多个被邀请学员个人信息雷同（如头像、昵称，收货地址）
        </View>
        <View className='desc'>
          如发现邀请人存在以上几类情况，小熊美术将视情节严重情况予以判定，判定结果包括但不限于:
        </View>
        <View className='sub-desc'>1、减少活动奖励小熊币数量</View>
        <View className='sub-desc'>2、取消领取奖励资格</View>
        <View className='sub-desc'>3、禁止参加任何小熊美术官方活动</View>
        <View className='desc'>活动最终解释权归小熊美术所有。</View>
      </View>
    </View>
  );
}

export default Rule;
