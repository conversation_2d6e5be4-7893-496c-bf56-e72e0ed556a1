import { getProgramUserSubject } from '@/api/groupbuy';
import { UserStateType } from '@/store/groupbuy/state';
import { Button, View } from '@tarojs/components';
import Taro, { hideLoading, setStorageSync } from '@tarojs/taro';
import { useDispatch, useSelector } from 'react-redux';
import styles from './index.module.scss';

const AuthPhoneBtn = props => {
  const { authSuccess, authError = null } = props;
  const userid = useSelector((store: UserStateType) => store.userid);
  const openid = useSelector((store: UserStateType) => store.openid);
  const dispatch = useDispatch();
  // 手机号授权
  const getUserPhoneNumber = res => {
    if (res.detail.errMsg === 'getPhoneNumber:fail user deny') {
      hideLoading();
      console.log(res.detail.errMsg);
      authError && authError();
    } else {
      const { encryptedData, iv } = res.detail;
      getProgramUserSubject({
        openId: openid,
        encryptedData,
        iv,
      }).then(phone => {
        if (phone.payload.token)
          Taro.setStorageSync('appToken', phone.payload.token);
        phone.payload.uid &&
          setStorageSync('__msb_user_id__', phone.payload.uid);
        phone.payload.uid &&
          dispatch({
            type: 'CHANGE_USERID',
            userid: phone.payload.uid,
          });
        phone.payload.mobile &&
          dispatch({
            type: 'CHANGE_MOBILE',
            mobile: phone.payload.mobile,
          });
        authSuccess && authSuccess(phone.payload.uid);
      });
    }
  };

  return (
    <>
      {userid ? (
        <View
          className={styles['btn-box']}
          onClick={() => {
            authSuccess && authSuccess(userid);
          }}
        >
          {props.children}
        </View>
      ) : (
        <Button
          className={styles['base-btn']}
          openType='getPhoneNumber'
          onGetPhoneNumber={getUserPhoneNumber}
        >
          {props.children}
        </Button>
      )}
    </>
  );
};

export default AuthPhoneBtn;
