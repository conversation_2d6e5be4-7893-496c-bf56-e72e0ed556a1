@import '../../theme/groupbuy/common.scss';

.order {
  padding: 20px 15px 10px;
  box-sizing: border-box;

  .title {
    display: flex;
    justify-content: center;
    margin-bottom: 48px;
    padding: 0 120px;
    position: relative;

    .close {
      width: 100px;
      height: 100px;
      position: absolute;
      top: -32px;
      right: -32px;

      &::before,
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        display: inline-block;
        width: 36px;
        height: 4px;
        border-radius: 1px;
        background: #ccc;
      }

      &::before {
        transform: translate3d(-50%, -50%, 0) rotate(45deg);
      }

      &::after {
        transform: translate3d(-50%, -50%, 0) rotate(-45deg);
      }
    }

    .quota,
    .time {
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;

      .highlight {
        color: $red;
        font-size: $font-44;
        font-weight: 600;
      }

      .text {
        color: $silver;
        font-size: $font-28;
      }
    }
  }

  .subTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 18px;
    border-bottom: 1px solid #e6e6e6;
    margin-bottom: 24px;

    .level {
      color: $dim-gray;
      font-size: $font-32;
    }

    .start-time {
      color: $grey;
      font-size: $font-24;
    }
  }

  .exist-level {
    .send-title {
      font-size: 32px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #727272;
      margin-bottom: 18px;

      text {
        background: #ff7000;
        color: #fff;
        font-size: 28px;
        border-radius: 10px;
        padding: 0 14px;
        margin-right: 10px;
      }
    }

    .class-img {
      margin-bottom: 38px;
      border-radius: 16px;
      overflow: hidden;

      image {
        width: 100%;
        height: 422px;
      }
    }
  }

  .no-exist-level {
    .class-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 35px;
      position: relative;

      image {
        border-radius: 20px;
        width: 302px;
        height: 194px;
      }

      .tag {
        width: 76px;
        height: 76px;
        position: absolute;
        left: -10px;
        top: -10px;
      }

      .info-main {
        flex: 1;
        padding-left: 22px;
        box-sizing: border-box;
        text-align: left;

        text {
          font-size: 36px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #333333;
          line-height: 60px;
        }

        .start-time {
          font-size: 24px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #a9a9a9;
        }

        .send-title {
          margin-top: 25px;
          font-size: 28px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #727272;
          margin-bottom: 18px;

          text {
            background: #ff7000;
            color: #fff;
            font-size: 24px;
            border-radius: 8px;
            padding: 0 14px;
            margin-right: 10px;
          }
        }
      }
    }

    .age-main {
      margin: 30px 0 50px;

      .age-title {
        font-size: 32px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #222222;
      }

      .age-list {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        column-gap: 30px;
        row-gap: 18px;
        margin-top: 24px;

        .list-item {
          border-radius: 15px;
          border: 1px solid #cccccc;
          position: relative;
          overflow: hidden;
          text-align: center;
          padding: 10px 0;

          &.selected {
            box-shadow: 0 0 13px 0 rgba(177, 177, 177, 0.26);

            text {
              background-color: #b1b1b1 !important;
            }

            .age-title,
            .des {
              color: #b1b1b1 !important;
            }
            &::after {
              content: ' ';
              width: 80px;
              height: 64px;
              position: absolute;
              top: 0;
              right: 0;
              background-image: url('../../assets/thirtySix/chooseLevel/hasbuy.png');
              background-repeat: no-repeat;
              background-position: 0 0;
              background-size: cover;
            }
          }

          .age-title {
            font-size: 32px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #222222;
          }

          .des {
            font-size: 24px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
          }

          text {
            position: absolute;
            left: -2px;
            top: 0;
            color: #fff;
            font-size: 24px;
            padding: 0 14px;
            border-bottom-right-radius: 12px;
          }
        }
      }
    }
  }

  .tips {
    font-size: 28px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #727272;
    margin-bottom: 15px;
    text-align: center;
  }

  .pay {
    padding-bottom: 50px;

    .button {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 96px;
      border-radius: 48px;
      background: linear-gradient(270deg, #ffa300 0%, #ff6a00 100%);
      color: $white;
      font-weight: bold;
      position: relative;
      overflow: visible;

      text {
        font-size: 50px;

        &:first-child {
          line-height: 0;

          &:first-letter {
            font-size: 30px;
          }
        }

        &:last-child {
          font-size: $font-34;
          margin-left: 15px;
        }
      }

      &::after {
        border: none;
      }
    }
  }
}
.order_ui2 {
  // padding: 20px 15px 10px;
  box-sizing: border-box;
  background: #f8f8f8;
  .title {
    padding: 32px 32px 0 32px;
    .timeTitle {
      height: 56px;
      font-size: 40px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333333;
      line-height: 56px;
      text-align: center;
      margin-bottom: 26px;
    }
    .time {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 43px;
      text {
        font-size: 32px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
      }
      .time-num {
        width: 66px;
        height: 66px;
        background: #ffa700;
        border-radius: 8px;
        line-height: 66px;
        text-align: center;
        font-weight: 500;
        color: #ffffff;
        margin: 0 16px;
      }
    }
  }
  .exist-level {
    padding: 0 32px;
    .exist-level-view {
      height: 563px;
      padding: 24px 32px 0 32px;
      border-radius: 30px;
      background: #ffffff;
    }
    .subTitle {
      .level {
        height: 48px;
        font-size: 34px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333333;
        line-height: 48px;
        text-align: left;
        margin-bottom: 8px;
        padding-left: 0;
      }
      .start-time {
        height: 40px;
        font-size: 28px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
        line-height: 40px;
        margin-bottom: 16px;
        text {
          color: #ffa700;
        }
      }
    }
    .class-img {
      width: 622px;
      height: 403px;
      border-radius: 30px;
      overflow: hidden;
      image {
        width: 100%;
      }
    }
  }
  .tips {
    margin-top: 24px;
    height: 66px;
    background: #fff4de;
    line-height: 66px;
    font-size: 28px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffa700;
    margin-bottom: 20px;
    image {
      width: 32px;
      height: 32px;
      padding-top: 17px;
      display: block;
      float: left;
      margin-left: 31px;
    }
    .tips-word {
      float: left;
      margin-left: 12px;
    }
  }
  .pay {
    padding: 20px 32px 50px 32px;
    background-color: #ffffff;
    .button {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 96px;
      border-radius: 48px;
      background: linear-gradient(270deg, #ffa300 0%, #ff6a00 100%);
      color: $white;
      font-weight: bold;
      position: relative;
      overflow: visible;

      text {
        font-size: 50px;

        &:first-child {
          line-height: 0;

          &:first-letter {
            font-size: 30px;
          }
        }

        &:last-child {
          font-size: $font-34;
          margin-left: 15px;
        }
      }

      &::after {
        border: none;
      }
    }
  }
}
