import Taro, { useRouter } from '@tarojs/taro';
import { useEffect, useState, useImperativeHandle, forwardRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Button, Image, Text, View } from '@tarojs/components';
import { subscribeMsgHandle } from '@/utils';
import S1Img from '@/assets/groupbuy/s1-icon.png';
import S2Img from '@/assets/groupbuy/s2-icon.png';
import S3Img from '@/assets/groupbuy/s3-icon.png';
import S4Img from '@/assets/groupbuy/s4-icon.png';
import selIcon4V1 from '@/assets/groupbuy/index/gift-new4-v1.jpg';
import selIcon4 from '@/assets/groupbuy/index/gift-new4.jpg';
import warningIcon from '@/assets/groupbuy/index/icon_warning.png';
import { dateFormat } from '@/utils/index';
import { ILevel, levelV3 } from '@/common/data.config';
import { UserStateType } from '@/store/groupbuy/state';
import sensors from '@/utils/sensors_data';

import {
  getPackagesCreate,
  getPackagesCreateParams,
  getProgramUserSubject,
  getSupManagements,
  getSupManagementsParams,
  getSupManagementsV1,
  getWeixinProgramPay,
  orderExtendReport,
  packagesCrossCreate,
  postReportReady,
  queryOrderByUserId,
  wxCombine,
  zeroPackagesCreate,
  packagesZeroCreate, // 书法
  dyReport,
} from '@/api/groupbuy';
import CheckLevel from '@/utils/checkLevel';
// 倒计时
import CountDown from '../groupbuy/countdown';
// import LevelTips from '../levelTips';
import './index.scss';

/**
 *  @param levelType 级别类型
 *  0、选择级别（年龄）
 *  1、选择级别（写死描述）
 *  2、支付和级别选择一起
 * */

// S级别角标
const levelTags = {
  S1: S1Img,
  S2: S2Img,
  S3: S3Img,
  S4: S4Img,
};

// export default function Index(props, ref) {
const ChildComponent = forwardRef((props: any, ref) => {
  const {
    watchCloseOrder,
    payPageData,
    orderType,
    giveaway,
    pType,
    packagesId,
    topicId,
    pName,
    subject,
    isIntroduce = true,
    levelType,
    regtype,
    timing = 600000,
    isUseByType = false,
    payui = 1,
    disCoupon = null,
    isReceive = false,
    isJoinGroup = false, // 是否参与拼团
    groupId = '', // 团id
    ownOrderInfo = null, // 拼团重新时的上一笔购买信息
    teacherPage = false, //拼团老师页
    // 绘本加购
    selectPicBook = false,
    attachSubject = null, // 加购课程信息
    // pciswitch = false, // 绘本加购开关是否开启
    addPicShwoLevel, // 加购情况下
    afterCreateZeroOrder = null, // 0元订单接口成功执行
    payConfirmHandler,
    groupParentFrom = 'index', // 拼团来源,
    sourcePageType = '', // 页面来源
  } = props;
  console.log('????????!!!!', packagesId);
  useImperativeHandle(ref, () => ({
    payConfirm,
    getPhoneNumber,
  }));

  const router = useRouter();
  const teacherId = router.params.tid || router.params.teacherId || '';
  const platform = router.params.from == '1' ? 2 : 0;
  // 定义倒计时初始值
  const [countDownNum, setCountDown] = useState(0);
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  //openid
  const openId = useSelector((state: UserStateType) => state.openid);
  //channelId
  let channelId = useSelector((state: UserStateType) => state.channelId);
  //clickId
  const clickId = useSelector((state: UserStateType) => state.clickId);
  //wxAccountNu
  const wxAccountNu = useSelector((state: UserStateType) => state.wxAccountNu);
  //routePath
  const routePath = useSelector((state: UserStateType) => state.routePath);
  //adPlatform
  const adPlatform =
    useSelector((state: UserStateType) => state.adPlatform) ||
    router.params.adPlatform;
  //sendId
  const sendId = useSelector((state: UserStateType) => state.sendId);
  //sendId
  const spreadId =
    useSelector((state: UserStateType) => state.spreadId) ||
    router.params.spreadId ||
    '';
  //posterId
  const posterId =
    useSelector((state: UserStateType) => state.posterId) ||
    router.params.poster_id ||
    router.params.posterId ||
    '';

  const [spreadCode] = useState(router.params.spreadCode || '');

  const userRole = useSelector((state: UserStateType) => state.userRole);
  const { orderId } = router.params;
  const orderid = useSelector((state: UserStateType) => state.orderId);
  //支付flag
  const [payFlag, setPayFlag] = useState<boolean>(true);
  const [courseday, setCourseday] = useState<string>();
  const [endCourseday, setEndCourseday] = useState<string>();
  const [period, setPeriod] = useState<any>(0);
  // const [musicPeriod, setMusicPeriod] = useState<number>(0);

  const [selectedMusic, setSelectedMusic] = useState<boolean>(false);
  const [canBuyArt, setCanBuyArt] = useState<boolean>(true);
  const [canBuyMusic, setCanBuyMusic] = useState<boolean>(true);

  // 是否已经购买过级别
  const [hasBuyLevel, setHasBuyLevel] = useState<any[]>([]);
  // 当前选择级别 相关数据
  const [curLevelInfo, setCurLevelInfo] = useState<ILevel | null>(null);
  const [levelTypeImg, setLevelTypeImg] = useState<string>('');
  // 合购订单
  const [unionCombineOrders, setunionCombineOrders] = useState('');
  const dispatch = useDispatch();

  // 跳转成功页
  const goAddAddress = () => {
    if (selectedMusic) {
      // form=1代表是从艺术宝app跳转过来到购买页的
      Taro.navigateTo({
        url: `/pages/groupbuy/addAddress/index?type=106${
          router.params.from == '1' ? '&from=1' : ''
        }&vType=2&sup=${curLevelInfo?.sup}&orderType=${orderType}`,
      });
    } else {
      const path = router.path;
      // soul区分是否是拼团
      let soul = '';
      if (path.includes('/pages/normalGroup/art/index')) {
        soul = `&soul=artindex`;
      }
      //   ownOrderInfo有就代表重新拼团或直接购买  用之前的addressId 所以不用填地址
      if (!ownOrderInfo) {
        let _url = `/pages/groupbuy/addAddress/index?vType=2${soul}&sup=${curLevelInfo?.sup}&orderType=${orderType}`;
        // 如果是转介绍下单
        if (isIntroduce) _url = _url + '&type=new';
        // sourcePageType 是否是从小程序跳转过来的
        if (sourcePageType === 'H5') _url = _url + '&openSource=H5';
        // 如果是参与拼团的订单
        if (isJoinGroup) {
          _url = _url + `&orderGroup=true&from=${groupParentFrom}`;
        }
        if (router.params.clickid) {
          _url = _url + `&clickid=${router.params.clickid}`;
        }
        // 是否加购了绘本课
        if (
          selectPicBook &&
          !(
            attachSubject.subject == 'PICTURE_BOOK' && curLevelInfo?.sup == 'S3'
          )
        )
          _url =
            _url +
            `&isAddPic=true&unionCombineOrders=${unionCombineOrders || ''}`;
        Taro.navigateTo({
          url: _url,
        });
      } else {
        if (!isJoinGroup)
          // 拼团老师页直接购买因为有addressid 直接跳添加老师页
          Taro.navigateTo({
            url: `/pages/launch/follow/index?sup=${
              ownOrderInfo.sup
            }&uid=${userId}${`&source=H5&channel=${channelId}`}&soul=artindex&orderId=${orderId}`,
          });
        else
          Taro.reLaunch({
            url: `/pages/groupBuying/${groupParentFrom}/index`,
          });
      }
    }
  };

  async function pay(uid = userId) {
    if (!curLevelInfo?.sup && !ownOrderInfo) {
      Taro.showToast({
        title: '请选择级别',
        icon: 'none',
        duration: 2000,
      });
      return;
    }
    /** 神策埋点
     * 用户点击支付时触发 **/
    if (isIntroduce) {
      sensors.track('xxys_experienceCoursePage_pay_click', {
        channel_id: channelId,
        user_role: userRole,
        user_id: uid,
        poster_id: posterId,
        buy_model: 'model_4',
        abtest: levelType == 2 ? '级别&支付' : '单支付',
        sendId: sendId || '',
      });
    }
    /** 神策埋点 **/
    setPayFlag(() => false);
    // setCanBuyArt(true)
    if (selectedMusic && canBuyArt && canBuyMusic) {
      const packagesSplitList: any[] = [
        {
          packagesId: packagesId,
          stage: period,
          sup: curLevelInfo?.sup,
          topicId,
          channel: channelId,
        },
        {
          packagesId: 522,
          stage: 0,
          sup: 'S3',
          topicId: 4,
          channel: '8119',
        },
      ];
      packagesCrossCreate({
        type: 'ALONE',
        userId: uid,
        addressId: 0,
        sendId,
        spreadId,
        posterId,
        spreadCode,
        packagesSplitList,
        model: '4',
      }).then((res) => {
        if (res.status === 'EXCEPTION') {
          setPayFlag(() => true);
          if (res.code == 80000053 || res.code == 8000176) {
            Taro.showToast({
              title: '您已购买体验课，不支持再次购买',
              icon: 'none',
              duration: 2000,
            });
          } else {
            Taro.showToast({
              title: res.errors || '下单失败！',
              icon: 'none',
              duration: 2000,
            });
          }
          return false;
        }
        if (res.code === 0) {
          const { orderSimpleList } = res.payload;
          const orderIdList = [];
          let unionCombineOrders1 = '';
          let unionAmount = 0;
          orderSimpleList.forEach((v: never) => {
            unionCombineOrders1 = unionCombineOrders1 + v['orderId'] + ',';
            unionAmount = unionAmount + +v['amount'];
            orderIdList.push(v['outTradeNo']);
            if (v['subject'] === 'ART_APP') {
              dispatch({
                type: 'CHANGE_ORDERID',
                orderId: v['orderId'],
              });
            }
          });
          reportOrder(unionCombineOrders1, unionAmount);
          setTimeout(() => {
            Taro.setStorageSync('outTradeNo', orderIdList.join(','));
          }, 200);
          wxCombine({
            userId: uid,
            openId,
            payType: 'AI_WXPROGRAM',
            mergeOutTradeNo: res.payload['unionOutTradeNo'],
            notifyUrl: '',
          })
            .then((result) => {
              const {
                timeStamp,
                nonceStr,
                package: packageId,
                paySign,
              } = result.payload;
              Taro.requestPayment({
                timeStamp,
                nonceStr,
                package: packageId,
                // @ts-ignore
                signType: 'RSA',
                paySign,
                success: function () {
                  setPayFlag(() => true);
                  // 29拼单控制分享页面状态 =>重新下单 转态重置
                  Taro.removeStorageSync('spellactive');
                  sensors.track('xxys_experienceCoursePage_payresult_view', {
                    buy_model: 'model_4',
                    status: '支付成功',
                  });
                  goAddAddress();
                },
                fail: function () {
                  setPayFlag(() => true);
                  sensors.track('xxys_experienceCoursePage_payresult_view', {
                    buy_model: 'model_4',
                    status: '支付失败',
                  });
                  Taro.showToast({
                    title: '下单失败！',
                    icon: 'none',
                    duration: 2000,
                  });
                },
              });
            })
            .catch(() => {
              setPayFlag(() => true);
            });
        }
      });
    }
    // else if (selectPicBook && curLevelInfo?.sup != 'S3') {
    //   const packagesSplitList: any[] = [
    //     {
    //       packagesId: packagesId,
    //       stage: period,
    //       sup: curLevelInfo?.sup,
    //       topicId,
    //       channel: channelId,
    //     },
    //     attachSubject,
    //   ];
    //   packagesCrossCreate({
    //     type: 'ALONE',
    //     userId: uid,
    //     addressId: 0,
    //     sendId,
    //     spreadId,
    //     posterId,
    //     packagesSplitList,
    //     model: '4',
    //   }).then(res => {
    //     if (res.status === 'EXCEPTION') {
    //       setPayFlag(true);
    //       if (res.code == 80000053) {
    //         Taro.showToast({
    //           title: '您已购买体验课，不支持再次购买',
    //           icon: 'none',
    //           duration: 2000,
    //         });
    //       } else {
    //         Taro.showToast({
    //           title: res.errors || '下单失败！',
    //           icon: 'none',
    //           duration: 2000,
    //         });
    //       }
    //       return false;
    //     }
    //     if (res.code === 0) {
    //       const { orderSimpleList } = res.payload;
    //       const orderIdList = [];
    //       orderSimpleList.forEach((v: never) => {
    //         orderIdList.push(v['orderId']);
    //         if (v['subject'] === 'ART_APP') {
    //           dispatch({
    //             type: 'CHANGE_ORDERID',
    //             orderId: v['orderId'],
    //           });
    //         }
    //       });
    //       setTimeout(() => {
    //         setunionCombineOrders(orderIdList.join(','));
    //         Taro.setStorageSync('unionCombineOrders', orderIdList.join(','));
    //       }, 200);
    //       wxCombine({
    //         userId: uid,
    //         openId,
    //         payType: 'AI_WXPROGRAM',
    //         mergeOutTradeNo: res.payload['unionOutTradeNo'],
    //         notifyUrl: '',
    //       })
    //         .then(result => {
    //           const {
    //             timeStamp,
    //             nonceStr,
    //             package: packageId,
    //             paySign,
    //           } = result.payload;
    //           Taro.requestPayment({
    //             timeStamp,
    //             nonceStr,
    //             package: packageId,
    //             // @ts-ignore
    //             signType: 'RSA',
    //             paySign,
    //             success: function () {
    //               setPayFlag(true);
    //               // 29拼单控制分享页面状态 =>重新下单 转态重置
    //               Taro.removeStorageSync('spellactive');
    //               sensors.track('xxys_experienceCoursePage_payresult_view', {
    //                 buy_model: 'model_4',
    //                 status: '支付成功',
    //               });
    //               goAddAddress();
    //             },
    //             fail: function () {
    //               setPayFlag(true);
    //               sensors.track('xxys_experienceCoursePage_payresult_view', {
    //                 buy_model: 'model_4',
    //                 status: '支付失败',
    //               });
    //               Taro.showToast({
    //                 title: '下单失败！',
    //                 icon: 'none',
    //                 duration: 2000,
    //               });
    //             },
    //           });
    //         })
    //         .catch(() => {
    //           setPayFlag(true);
    //         });
    //     }
    //   });
    // }
    else {
      let params: getPackagesCreateParams = {
        type: 'ALONE',
        userId: uid,
        packagesId,
        stage: period,
        sup: curLevelInfo?.sup || '',
        channel: channelId,
        sendId,
        spreadId,
        topicId,
        posterId,
        teacherId,
        platform,
        spreadCode,
        model: '4',
      };
      if (ownOrderInfo) {
        delete ownOrderInfo.orderId;
        delete ownOrderInfo.outTradeNo;
        delete ownOrderInfo.couldBuy;
        delete ownOrderInfo.currency_code;
        delete ownOrderInfo.packagesName;
        let _stage = await periodHandler(false, ownOrderInfo.sup);
        ownOrderInfo.stage = _stage;
        ownOrderInfo.channel = channelId;
        params = { ...params, ...ownOrderInfo };
      }
      if (isUseByType) {
        params = { ...params, category: 19 };
      }
      if (!isReceive && !isJoinGroup && Number(orderType) < 10) {
        delete params.model;
      }
      if (isReceive && disCoupon) {
        let _result = await disCoupon(sendId, uid, true);
        if (!_result) return;
      }
      //   是否参加拼团
      if (isJoinGroup) {
        if (!ownOrderInfo) params['grouponId'] = groupId;
        params.type = 'GROUPON';
      }
      if (teacherPage && !isJoinGroup) {
        params['grouponId'] = '';
        params.type = 'ALONE';
      }
      // 逻辑太多了，捋不明白啊。。。。
      // 0元赠课，36元优惠订单
      if (packagesId === '1888') {
        zeroPackagesCreate({
          userId,
          packageId: packagesId,
          sup: curLevelInfo?.sup || '',
          channelId: channelId,
          stage: period,
          sendId: router.params['sendId'] || '',
          addressId: 0,
        }).then((res) => {
          if (res.code === 0) {
            dispatch({
              type: 'CHANGE_ORDERID',
              orderId: res.payload.id,
            });
            reportOrder(res.payload.id, res.payload.amount);
            goAddAddress();
          } else {
            Taro.showToast({
              title: res.errors,
              icon: 'none',
            });
          }
        });
        return;
      }
      const payFun = function (zeroId: any = null) {
        return (
          params.sup &&
          getPackagesCreate(params).then((res) => {
            if (res.status === 'EXCEPTION') {
              setPayFlag(() => true);
              if (res.code == 80000053 || res.code == 8000176) {
                Taro.showToast({
                  title: '您已购买体验课，不支持再次购买',
                  icon: 'none',
                  duration: 2000,
                });
              } else {
                Taro.showToast({
                  title: res.errors || '下单失败！',
                  icon: 'none',
                  duration: 2000,
                });
              }
              return false;
            }
            if (res.code === 0) {
              clickId &&
                postReportReady({
                  orderId: res.payload.order.id,
                  platform: adPlatform ? adPlatform.toLocaleUpperCase() : 'WX',
                  type:
                    adPlatform?.toLocaleUpperCase() === 'TX_WP'
                      ? 'PURCHASE'
                      : 'RESERVATION',
                  clickId: clickId,
                  url: routePath,
                  params: openId,
                  wxAccountNu: wxAccountNu,
                });
              // 订单拓展信息上报 转介绍762 按需增加上报
              if (router.params.pzd) {
                orderExtendReport({
                  uid,
                  oids: res.payload.order.id,
                  tabType: 'SEND',
                  tabValue: sendId,
                  tabJson: JSON.stringify({ prezzieId: router.params.pzd }),
                  posterId: posterId,
                  come: '',
                });
              }

              dispatch({
                type: 'CHANGE_ORDERID',
                orderId: res.payload.order.id,
              });

              if (zeroId != null) {
                setunionCombineOrders(
                  `${res.payload.order.id},${zeroId.orderid}`,
                );
                Taro.setStorageSync('unionCombineOrders', [
                  { type: 'ART_APP', orderid: res.payload.order.id },
                  zeroId,
                ]);
              }
              reportOrder(
                zeroId != null
                  ? `${res.payload.order.id},${zeroId.orderid}`
                  : res.payload.order.id,
                res.payload.order.amount,
              );
              // 不同的产品payType不同
              let _url = getWeixinProgramPay,
                _data = {
                  openId,
                  orderId: res.payload.order.id,
                  userId: uid,
                  payType: 'AI_WXPROGRAM',
                  notifyUrl: '',
                };
              if (pType === 'calligraphy') {
                _data['payType'] = 'WRITE_WXPROGRAM';
              }
              _url(_data)
                .then((data) => {
                  if (data.code === 0) {
                    const {
                      timeStamp,
                      nonceStr,
                      package: packageId,
                      paySign,
                    } = data.payload;
                    // 不同的支付接口的返回不同
                    Taro.requestPayment({
                      timeStamp,
                      nonceStr,
                      package: packageId,
                      signType: 'HMAC-SHA256',
                      paySign,
                      success: function (payRes) {
                        setPayFlag(() => true);
                        console.log(payRes);
                        // 29拼单控制分享页面状态 =>重新下单 转态重置
                        Taro.removeStorageSync('spellactive');
                        sensors.track(
                          'xxys_experienceCoursePage_payresult_view',
                          {
                            buy_model: 'model_4',
                            status: '支付成功',
                          },
                        );
                        if (isJoinGroup) {
                          subscribeMsgHandle([
                            'WqIike19XxQDgq0RJ4CR2mxM2KTgC8cPUvaajzR0cCk',
                            'FmCLb9ig-FU0h9RobYqVNOSvN8LSh_k3f_qLbzvsh20',
                            '6lGxqUREkrDEK_8rOxfFAmzzsd7gN0NnkYEX9gbgbiE',
                          ]).then((res1) => {
                            console.log(res1, '订阅');
                            goAddAddress();
                          });
                        } else goAddAddress();
                      },
                      fail: function (failRes) {
                        setPayFlag(() => true);
                        console.log(failRes);
                        // `${failRes.errMsg}` ||
                        sensors.track(
                          'xxys_experienceCoursePage_payresult_view',
                          {
                            buy_model: 'model_4',
                            status: '支付失败',
                          },
                        );
                        Taro.showToast({
                          title: '下单失败！',
                          icon: 'none',
                          duration: 2000,
                        });
                      },
                    });
                  } else setPayFlag(() => true);
                })
                .catch((err) => {
                  setPayFlag(() => true);
                  console.log(err);
                });
            }
          })
        );
      };
      if (
        selectPicBook &&
        !(attachSubject.subject == 'PICTURE_BOOK' && curLevelInfo?.sup == 'S3')
      ) {
        let _url = zeroPackagesCreate;
        let _data: any = {
          userId: params.userId,
          packageId: attachSubject.packagesId,
          packagesId: attachSubject.packagesId,
          sup: attachSubject.sup,
          channelId: attachSubject.channel,
          channel: attachSubject.channel,
          stage: attachSubject.stage,
          topicId: attachSubject.topicId,
          model: '6',
          sendId,
        };
        if (attachSubject.subject == 'WRITE_APP') {
          _url = packagesZeroCreate;
          _data.sup =
            curLevelInfo?.sup == 'S1'
              ? 'S1'
              : curLevelInfo?.sup == 'S2'
              ? 'S3'
              : 'S2';
          delete _data.packageId;
          delete _data.channelId;
          _data.type = 'ALONE';
        }
        _url(_data).then((resZero) => {
          if (!resZero.code) {
            const zeroId = {
              type: attachSubject.subject,
              orderid:
                attachSubject.subject == 'WRITE_APP'
                  ? resZero.payload.orderId
                  : resZero.payload.id,
            };
            payFun(zeroId);
            afterCreateZeroOrder();
          } else {
            // if (resZero.errors == '此套餐已购买，无法再次购买') payFun();
            if (resZero.code == 8000178) payFun();
            else
              Taro.showToast({
                title: `${attachSubject.name}课领取失败，请联系管理员！`,
                icon: 'none',
                duration: 2000,
              });
          }
        });
      } else payFun();
    }
  }

  // 校验是否能买音乐课
  const checkBuyMusic = () => {
    Taro.showLoading();
    queryOrderByUserId({
      userId: userId,
      channels: channelId,
      subjects: 'ART_APP,MUSIC_APP',
      packageId: 522,
    })
      .then((res) => {
        const {
          channelCheck,
          systemCheckMap,
          pluralCheckMap,
          subjectOrderMap,
          experienceCheckMap,
        } = res.payload;
        // experienceCheckMap.ART_APP为true 表示之前已经买过体验课了
        if (experienceCheckMap.ART_APP) {
          if (
            subjectOrderMap &&
            subjectOrderMap.ART_APP &&
            subjectOrderMap.ART_APP.EXPERIENCE &&
            subjectOrderMap.ART_APP.EXPERIENCE.length > 0
          ) {
            const experience: never[] =
              subjectOrderMap['ART_APP']['EXPERIENCE'];
            let supList = [];
            for (let i = 0; i < experience.length; i++) {
              supList.push(experience[i]['sup']);
            }
            setTimeout(() => {
              if (supList.some((i) => i == curLevelInfo?.sup)) {
                setCanBuyArt(false);
              } else {
                setCanBuyArt(true);
              }
            }, 200);
          }
          if (
            channelCheck &&
            !systemCheckMap.ART_APP &&
            pluralCheckMap.ART_APP
          ) {
            // 可以重复购买
            setCanBuyArt(true);
          } else {
            // 不可以重复购买
            setCanBuyArt(false);
          }
        } else {
          // experienceCheckMap.ART_APP为false 表示之前没有买过体验课了，是首单
          setCanBuyArt(true);
        }
        if (experienceCheckMap.MUSIC_APP) {
          if (
            subjectOrderMap &&
            subjectOrderMap.MUSIC_APP &&
            subjectOrderMap.MUSIC_APP.EXPERIENCE &&
            subjectOrderMap.MUSIC_APP.EXPERIENCE.length > 0
          ) {
            const experience: never[] =
              subjectOrderMap['MUSIC_APP']['EXPERIENCE'];
            let supList = [];
            for (let i = 0; i < experience.length; i++) {
              supList.push(experience[i]['sup']);
            }
            if (supList.some((i) => i == 'S3')) {
              setCanBuyMusic(false);
              setSelectedMusic(false);
            } else {
              setCanBuyMusic(true);
              // setSelectedMusic(true);
            }
          }
          if (
            channelCheck &&
            !systemCheckMap.MUSIC_APP &&
            pluralCheckMap.MUSIC_APP
          ) {
            // 可以重复购买
            setCanBuyMusic(true);
            // setSelectedMusic(true);
          } else {
            // 不可以重复购买
            setCanBuyMusic(false);
            setSelectedMusic(false);
          }
        } else {
          // experienceCheckMap.ART_APP为false 表示之前没有买过体验课了，是首单
          setCanBuyMusic(true);
          // setSelectedMusic(true);
        }
        Taro.hideLoading();
      })
      .catch(() => {
        Taro.hideLoading();
      });
  };
  // 获取用户手机号
  const getPhoneNumber = (res) => {
    if (res.detail.errMsg === 'getPhoneNumber:fail user deny') {
      console.log(res.detail.errMsg);
    } else {
      const { encryptedData, iv } = res.detail;
      // getProgramUserSubject兼容getProgramUser
      getProgramUserSubject({
        openId,
        encryptedData,
        iv,
        subject,
      }).then((phone) => {
        if (phone.code !== 0) {
          Taro.showToast({
            title: '授权失败，请更换网络或删除小程序后重试',
            icon: 'none',
          });
          return;
        }
        if (phone.payload.uid) {
          dispatch({
            type: 'CHANGE_USERID',
            userid: phone.payload.uid,
          });
        }
        if (phone.payload.token)
          Taro.setStorageSync('appToken', phone.payload.token);
        phone.payload.mobile &&
          dispatch({
            type: 'CHANGE_MOBILE',
            mobile: phone.payload.mobile,
          });
        // if (!(pciswitch && curLevelInfo?.sup != 'S3'))
        //   payFlag && pay(phone.payload.uid);
        // else
        if (addPicShwoLevel) addPicShwoLevel();
        // 为什么获取了手机号后，直接就走支付流程呢？？
        if (payConfirmHandler) payConfirmHandler();
      });
    }
  };
  const payConfirm = () => {
    payFlag && pay();
  };

  const filterBuyLevel = (sup) => {
    return (
      hasBuyLevel &&
      hasBuyLevel.length &&
      hasBuyLevel.findIndex((v) => v == sup) > -1
    );
  };

  // 选择级别
  const toggleLevel = (data: ILevel) => {
    if (filterBuyLevel(data.label)) {
      return;
    }
    setCurLevelInfo(data);
    sensors.track('xxys_experienceCoursePage_courseSup_click', {
      course_sup: data.sup,
      channel_id: channelId,
      user_role: userRole,
      buy_model: 'model_4',
      abtest: '年龄&介绍',
      sendId: sendId || '',
    });
  };

  // 获取是否已经购买过级别
  useEffect(() => {
    if (levelType != 2) {
      return;
    }
    new CheckLevel({
      userId,
      channelId,
      orderId: orderid || orderId,
      regtype: regtype || '',
      subjects: 'ART_APP',
    })
      .initCheck()
      .then((res: any[]) => {
        setHasBuyLevel(res);
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId, channelId, levelType]);

  useEffect(() => {
    if (countDownNum === 0 && props.isShowOrder) {
      setCountDown(timing);
    }
  }, [props.isShowOrder, countDownNum]);

  useEffect(() => {
    payPageData &&
      Object.keys(payPageData).length > 0 &&
      setCurLevelInfo(payPageData);
  }, [payPageData]);

  useEffect(() => {
    if (curLevelInfo) {
      let supImg = '';
      switch (curLevelInfo.sup) {
        case 'S1':
        case 'S2':
        case 'S3':
          supImg = require(`@/assets/groupbuy/index/gift-level-new-${curLevelInfo.sup}.png`);
          break;
        default:
          supImg = require('@/assets/groupbuy/index/gift-level-new1.png');
      }
      setLevelTypeImg(supImg);
    }
  }, [curLevelInfo]);

  useEffect(() => {
    periodHandler();
  }, [payPageData, subject, curLevelInfo?.sup, isUseByType]);
  //   请求排期
  const periodHandler = (needLevel = true, sup = '') => {
    const levelInfo = curLevelInfo || payPageData;
    if (needLevel)
      if (!(levelInfo && Object.keys(levelInfo).length > 0)) {
        return;
      }
    let tempParams: getSupManagementsParams = {
      type: ['9.9'].includes(orderType)
        ? isReceive || isJoinGroup
          ? 'TESTCOURSE'
          : 'CATEGORYTESTCOURSE'
        : 'TESTCOURSE',
      sup: levelInfo.sup || sup,
      channelId: router.params.channelId || channelId,
    };
    const params = Object.assign(
      tempParams,
      isUseByType ? { category: 19 } : { subject },
    );
    const ApiHandle = isUseByType ? getSupManagementsV1 : getSupManagements;
    let _r = ApiHandle(params).then((res) => {
      const result = res.payload && res.payload;
      if (result) {
        const openCourseDate = dateFormat(
          result.courseDay * 1,
          'MM' + '月' + 'dd' + '日',
        );
        const endCourseDate = dateFormat(
          result.courseDay * 1 + 86400000 * 14,
          'MM' + '月' + 'dd' + '日',
        );
        setEndCourseday(endCourseDate);
        setCourseday(openCourseDate);
        setPeriod(result.period);
        return result.period;
      }
    });
    return _r || false;
  };

  useEffect(() => {
    userId && checkBuyMusic();
  }, [userId]);

  const reportOrder = (orderIds, amounts) => {
    const routerParams = router.params;
    const WXClick = routerParams.gdt_vid;
    const GDTClick = routerParams.qz_gdt;
    const TTCLick = routerParams.clickid;
    const BDClick = routerParams.bd_vid;
    const WYClick =
      routerParams.adPlatform === 'wy' ? routerParams.callback : '';
    let KSClick = routerParams.adPlatform !== 'wy' ? routerParams.callback : '';
    let WxApp = routerParams.wxAccountNu || '';
    const WBClick = routerParams.from === 'wb' ? routerParams.mark_id : '';
    const ZtUid = routerParams.ztuid;
    const UC = routerParams.adPlatform === 'uc' ? 'uc' : '';
    const qz_xhs = routerParams.click_id;
    const callBackParam = urlParamsToObject(routerParams.fullPath);
    // eslint-disable-next-line @typescript-eslint/no-shadow
    let platform = '';
    let actionType = '';
    let ALLClickId = '';
    let otherQuery = {};
    if (WXClick) {
      platform = 'WX';
      actionType = 'RESERVATION';
      ALLClickId = WXClick;
      // 腾讯额外参数
      otherQuery = {
        platformAccountId: routerParams.account_id,
        platformPlanId: routerParams.adgroup_id,
        platformAdvertiseId: routerParams.ad_id,
      };
    } else if (GDTClick) {
      platform = 'GDT';
      actionType = 'RESERVATION';
      ALLClickId = GDTClick;
    } else if (TTCLick) {
      platform = 'TT';
      actionType = '2';
      ALLClickId = TTCLick;
      otherQuery = {
        platformAccountId: routerParams.advertiser_id,
        platformPlanId: routerParams.projectid,
        platformAdvertiseId: routerParams.promotionid,
        platformSourceId: routerParams.mid3,
        platformKeywordId: routerParams.kwid,
        platformKeyword: routerParams.sckw,
      };
    } else if (BDClick) {
      // 增加百度携带的key
      platform = 'BD';
      actionType = '10';
      ALLClickId = BDClick;
      otherQuery = {
        platformAccountId: routerParams.uid,
        platformPlanId: routerParams.unit,
        platformAdvertiseId: routerParams.e_creative,
      };
    } else if (WYClick) {
      platform = 'WY';
      actionType = 'buy';
      ALLClickId = WYClick;
    } else if (KSClick) {
      platform = 'KS';
      actionType = '9';
      ALLClickId = KSClick;
    } else if (WBClick) {
      platform = 'WB';
      actionType = '1007';
      ALLClickId = WBClick;
    } else if (ZtUid) {
      platform = 'ZTJY';
      actionType = channelId;
      ALLClickId = ZtUid;
    } else if (UC) {
      platform = 'UC';
      actionType = '2';
    } else if (qz_xhs) {
      platform = 'XHS';
      actionType = '102';
      ALLClickId = qz_xhs;
    }
    if (routerParams.cbf === 'hw') {
      platform = 'HW';
      actionType = '';
      ALLClickId = KSClick || '';
    }
    if (callBackParam) {
      platform = 'QBB';
      actionType = '105';
      ALLClickId = callBackParam;
    }
    if (!platform) return;
    let url = router.path;
    KSClick = KSClick ? amounts : '';
    let uctrackid = routerParams.uctrackid;
    dyReport({
      orderId: orderIds,
      platform: platform,
      type: actionType,
      clickId: ALLClickId,
      url: url,
      params: KSClick,
      wxAccountNu: WxApp,
      uctrackid: uctrackid,
      ...otherQuery,
    });
  };
  const urlParamsToObject = (url) => {
    if (!url) return '';
    if (!url.split('callBackParam=')[1]) return '';
    let _url = url.split('callBackParam=')[1].split('&')[0];
    return _url ? _url : '';
  };

  const payui1 = () => {
    return (
      <View className='order'>
        <View className='title'>
          <View className='time'>
            <CountDown payCountDown={countDownNum} />
            <View className='text'>剩余支付时间</View>
          </View>
          <View className='close' onClick={() => watchCloseOrder(false)}></View>
        </View>
        {levelType !== 2 && (
          <View className='subTitle'>
            <View className='level'>
              小熊{pName}体验课-{curLevelInfo?.label}
            </View>
            {curLevelInfo?.courseday && (
              <View className='start-time'>{courseday}开课</View>
            )}
          </View>
        )}
        {levelType == 2 ? (
          <View className='no-exist-level'>
            <View className='class-info'>
              <Image
                src={
                  curLevelInfo?.sup == 'S4' && orderType != '9.9'
                    ? selIcon4
                    : giveaway.sImg
                }
                mode='aspectFill'
              ></Image>
              {curLevelInfo && levelTags[curLevelInfo.label] && (
                <Image
                  src={levelTags[curLevelInfo.label]}
                  className='tag'
                  mode='aspectFill'
                ></Image>
              )}
              <View className='info-main'>
                <Text>小熊{pName}体验课</Text>
                {curLevelInfo?.courseday && (
                  <View className='start-time'>{courseday}开课</View>
                )}
                <View className='send-title'>
                  <Text>赠送</Text>购买即赠画材
                  {orderType == '9.9' ? '礼包' : '大礼盒'}
                </View>
              </View>
            </View>
            <View className='age-main'>
              <Text className='age-title'>请选择合适年龄</Text>
              <View className='age-list'>
                {levelV3.map((item: ILevel, index) => {
                  return (
                    index != 3 && (
                      <View
                        className={`list-item ${
                          filterBuyLevel(item.label) ? `selected` : ``
                        }`}
                        style={{
                          backgroundColor: `${
                            item.label == curLevelInfo?.label
                              ? item.bgcolorOpacity
                              : '#fff'
                          }`,
                        }}
                        onClick={() => toggleLevel(item)}
                        key={item.sup}
                      >
                        <Text style={{ backgroundColor: item.bgcolor }}>
                          {item.sup}
                        </Text>
                        <View className='age-title'>{item.fit}</View>
                        <View className='des' style={{ color: item.bgcolor }}>
                          {item.range}
                        </View>
                      </View>
                    )
                  );
                })}
              </View>
              {/* {orderType != '9.9' && <LevelTips mTop={1} />} */}
            </View>
          </View>
        ) : (
          <View className='exist-level'>
            <View className='send-title'>
              <Text>赠送</Text>购买即赠画材
              {orderType == '9.9' ? '礼包' : '大礼盒'}
            </View>
            <View className='class-img'>
              <Image
                src={curLevelInfo?.sup == 'S4' ? selIcon4V1 : giveaway.img}
                mode='aspectFill'
              ></Image>
            </View>
          </View>
        )}
        <View className='tips'>支付后填写收货地址</View>
        <View className='pay'>
          {userId ? (
            <Button className='button' onClick={payConfirm}>
              <Text>¥{selectedMusic ? +orderType + 0.1 : orderType}</Text>
              <Text>立即支付</Text>
            </Button>
          ) : (
            <Button
              className='button'
              open-type='getPhoneNumber'
              onGetPhoneNumber={getPhoneNumber}
            >
              <Text>¥{selectedMusic ? +orderType + 0.1 : orderType}</Text>
              <Text>立即支付</Text>
            </Button>
          )}
        </View>
      </View>
    );
  };

  const payui2 = () => {
    return (
      <View className='order_ui2'>
        <View className='title'>
          <View className='timeTitle'>确认订单</View>
          <View className='time'>
            <Text>请在</Text>
            <CountDown payCountDown={countDownNum} />
            <Text>内支付</Text>
          </View>
          <View className='close' onClick={() => watchCloseOrder(false)}></View>
        </View>

        <View className='exist-level'>
          <View className='exist-level-view'>
            <View className='subTitle'>
              <View className='level'>
                小熊{pName}体验课 第{period}期
              </View>
              {curLevelInfo?.courseday && (
                <View className='start-time'>
                  体验时间：
                  <Text>
                    {courseday}-{endCourseday}
                  </Text>
                </View>
              )}
            </View>
            <View className='class-img'>
              <Image src={levelTypeImg} mode='widthFix'></Image>
            </View>
          </View>
        </View>
        <View className='tips'>
          <Image src={warningIcon} mode='widthFix'></Image>
          <View className='tips-word'>
            支付完成后请务必填写地址，否则影响您的学习体验
          </View>
        </View>
        <View className='pay'>
          {userId ? (
            <Button className='button' onClick={payConfirm}>
              <Text>¥{selectedMusic ? +orderType + 0.1 : orderType}</Text>
              <Text>立即支付</Text>
            </Button>
          ) : (
            <Button
              className='button'
              open-type='getPhoneNumber'
              onGetPhoneNumber={getPhoneNumber}
            >
              <Text>¥{selectedMusic ? +orderType + 0.1 : orderType}</Text>
              <Text>立即支付</Text>
            </Button>
          )}
        </View>
      </View>
    );
  };
  return payui == 1 ? payui1() : payui2();
});

export default ChildComponent;
