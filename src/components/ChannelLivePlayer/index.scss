@import '../../theme/groupbuy/common.scss';

.channel-live-player {
  width: 100%;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  margin: 20rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.06);

  // 错误提示卡片样式
  &__error-card {
    padding: 40rpx 32rpx;
    text-align: center;
    background: #fff2f0;
    border: 1rpx solid #ffccc7;
    border-radius: 12rpx;
    margin: 20rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  &__error-title {
    display: block;
    color: #f5222d;
    font-size: $font-28; // 使用项目字体变量
    font-weight: bold;
    margin: 16rpx 0 8rpx 0;
    line-height: 1.4;
  }

  &__error-desc {
    display: block;
    color: #f5222d;
    font-size: $font-26; // 使用项目字体变量
    margin-bottom: 8rpx;
    line-height: 1.5;
  }

  &__error-note {
    display: block;
    color: #d48806;
    font-size: $font-22; // 使用项目字体变量
    line-height: 1.4;
  }

  // 兼容旧版错误样式
  &__error {
    padding: 32rpx;
    text-align: center;
    background: #fff2f0;
    border: 1rpx solid #ffccc7;
    border-radius: 12rpx;
    margin: 20rpx;

    &-text {
      display: block;
      color: #f5222d;
      font-size: $font-26; // 使用项目字体变量
      margin-bottom: 16rpx;
      line-height: 1.5;
    }
  }

  &__loading {
    padding: 80rpx 40rpx;
    text-align: center;
    background: #f8f9fa;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    &-text {
      color: #666;
      font-size: $font-24; // 使用项目字体变量
      margin-top: 16rpx;
      line-height: 1.4;
    }
  }

  &__info {
    padding: 30rpx;
    background: #f0f7ff;
    border-bottom: 1rpx solid #e6f7ff;

    text {
      display: block;
      margin-bottom: 16rpx;
      font-size: $font-26; // 使用项目字体变量
      color: #1890ff;
      line-height: 1.5;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  &__status {
    font-weight: 600;
    font-size: $font-26; // 使用项目字体变量
  }

  &__replay {
    color: #52c41a !important;
    font-size: $font-26 !important; // 使用项目字体变量
  }

  &__feed-id {
    color: #666 !important;
    font-size: $font-22 !important; // 使用项目字体变量
  }

  &__container {
    position: relative;
    width: 100%;
    background: #000;
  }

  &__component {
    width: 100%;
    height: 400rpx;
    display: block;
  }

  &__placeholder {
    width: 100%;
    height: 400rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #f5f5f5;
    border: 2rpx dashed #d9d9d9;
    padding: 40rpx;
    box-sizing: border-box;

    &-text {
      font-size: $font-26; // 使用项目字体变量
      font-weight: bold;
      color: #666;
      margin-bottom: 16rpx;
    }

    &-note {
      font-size: $font-22; // 使用项目字体变量
      color: #999;
      text-align: center;
      line-height: 1.4;
      margin-bottom: 24rpx;
    }

    &-info {
      font-size: $font-20; // 使用项目字体变量
      color: #1890ff;
      margin-bottom: 8rpx;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  &__jump-info {
    padding: 24rpx 30rpx;
    background: #f6ffed;
    border-top: 1rpx solid #b7eb8f;
  }

  &__jump-title {
    display: block;
    font-weight: 600;
    color: #52c41a;
    font-size: $font-24; // 使用项目字体变量
    margin-bottom: 8rpx;
  }

  &__jump-content {
    display: block;
    color: #237804;
    font-size: $font-26; // 使用项目字体变量
    line-height: 1.4;
  }

  &__empty {
    padding: 80rpx 40rpx;
    text-align: center;
    color: #999;
    font-size: $font-26; // 使用项目字体变量
    background: #fafafa;
  }

  // 小屏幕适配（iPhone SE等）
  @media (max-width: 375px) {
    margin: 16rpx;

    &__info {
      padding: 24rpx;

      text {
        font-size: $font-24; // 使用项目字体变量
      }
    }

    &__component,
    &__placeholder {
      height: 360rpx;
    }

    &__jump-info {
      padding: 20rpx 24rpx;
    }

    &__placeholder {
      padding: 32rpx;

      &-text {
        font-size: $font-24; // 使用项目字体变量
      }

      &-note {
        font-size: $font-20; // 使用项目字体变量
      }
    }

    &__error-title {
      font-size: $font-26; // 使用项目字体变量
    }

    &__error-desc {
      font-size: $font-24; // 使用项目字体变量
    }

    &__loading-text {
      font-size: $font-22; // 使用项目字体变量
    }
  }

  // 加载动画
  &__loading {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 40rpx;
      height: 40rpx;
      margin: -20rpx 0 0 -20rpx;
      border: 4rpx solid #e6f7ff;
      border-top-color: #1890ff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  // 触摸反馈优化
  &__container {
    -webkit-tap-highlight-color: transparent;
  }

  // 文字选择禁用（提升移动端体验）
  &__placeholder-text,
  &__placeholder-note,
  &__jump-title,
  &__jump-content {
    -webkit-user-select: none;
    user-select: none;
  }
}
