.channel-live-player {
  width: 100%;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

  &__error {
    padding: 20px;
    text-align: center;
    background: #fff2f0;
    border: 1px solid #ffccc7;
    border-radius: 8px;
    margin: 10px;

    &-text {
      display: block;
      color: #f5222d;
      font-size: 14px;
      margin-bottom: 8px;
      line-height: 1.5;
    }

    &-tip {
      display: block;
      color: #d48806;
      font-size: 12px;
      line-height: 1.4;
    }
  }

  &__loading {
    padding: 40px 20px;
    text-align: center;
    color: #666;
    font-size: 14px;
    background: #f8f9fa;
  }

  &__info {
    padding: 15px;
    background: #f0f7ff;
    border-bottom: 1px solid #e6f7ff;

    text {
      display: block;
      margin-bottom: 8px;
      font-size: 14px;
      color: #1890ff;
      line-height: 1.5;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  &__status {
    font-weight: 600;
  }

  &__replay {
    color: #52c41a !important;
  }

  &__feed-id {
    color: #666 !important;
    font-size: 12px !important;
  }

  &__container {
    position: relative;
    width: 100%;
    background: #000;
  }

  &__component {
    width: 100%;
    height: 200px;
    display: block;
  }

  &__placeholder {
    width: 100%;
    height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #f5f5f5;
    border: 2px dashed #d9d9d9;
    padding: 20px;
    box-sizing: border-box;

    &-text {
      font-size: 16px;
      font-weight: bold;
      color: #666;
      margin-bottom: 8px;
    }

    &-note {
      font-size: 12px;
      color: #999;
      text-align: center;
      line-height: 1.4;
      margin-bottom: 12px;
    }

    &-info {
      font-size: 11px;
      color: #1890ff;
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  &__jump-info {
    padding: 12px 15px;
    background: #f6ffed;
    border-top: 1px solid #b7eb8f;
  }

  &__jump-title {
    display: block;
    font-weight: 600;
    color: #52c41a;
    font-size: 13px;
    margin-bottom: 4px;
  }

  &__jump-content {
    display: block;
    color: #237804;
    font-size: 14px;
    line-height: 1.4;
  }

  &__empty {
    padding: 40px 20px;
    text-align: center;
    color: #999;
    font-size: 14px;
    background: #fafafa;
  }

  // 响应式设计
  @media (max-width: 375px) {
    &__info {
      padding: 12px;
      
      text {
        font-size: 13px;
      }
    }

    &__component {
      height: 180px;
    }

    &__jump-info {
      padding: 10px 12px;
    }
  }

  // 深色模式适配
  @media (prefers-color-scheme: dark) {
    background: #1f1f1f;
    color: #fff;

    &__info {
      background: #2a2a2a;
      border-bottom-color: #3a3a3a;
    }

    &__jump-info {
      background: #2a3a2a;
      border-top-color: #4a5a4a;
    }

    &__empty {
      background: #2a2a2a;
      color: #ccc;
    }

    &__loading {
      background: #2a2a2a;
      color: #ccc;
    }
  }

  // 动画效果
  &__container {
    transition: all 0.3s ease;
  }

  &__info,
  &__jump-info {
    transition: background-color 0.3s ease;
  }

  // 加载动画
  &__loading {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 20px;
      height: 20px;
      margin: -10px 0 0 -10px;
      border: 2px solid #e6f7ff;
      border-top-color: #1890ff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}
