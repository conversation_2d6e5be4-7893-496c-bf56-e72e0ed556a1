import React, { useEffect, useState } from 'react';
import { View, Text } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { ChannelLivePlayerProps, ChannelLiveInfo } from '@/types/types';
import {
  checkChannelLiveSupport,
  getChannelsLiveInfo,
  generateTimeRange,
  getLiveStatusText,
  getReplayStatusText,
  getJumpPageText,
} from '@/utils/channelLive';
import './index.scss';

const ChannelLivePlayer: React.FC<ChannelLivePlayerProps> = ({
  finderUserName,
  feedId,
  className = '',
  onError,
  onStatusChange,
}) => {
  const [liveInfo, setLiveInfo] = useState<ChannelLiveInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [compatible, setCompatible] = useState(true);

  // 检测兼容性
  useEffect(() => {
    const isSupported = checkChannelLiveSupport();
    setCompatible(isSupported);

    if (!isSupported) {
      const errorMsg = '当前微信版本过低，请升级至最新版本（基础库需≥2.29.0）';
      setError(errorMsg);
      onError?.(errorMsg);
    }
  }, [onError]);

  // 获取直播信息
  const fetchLiveInfo = async () => {
    if (!compatible) return;

    setLoading(true);
    setError('');

    try {
      const { startTime, endTime } = generateTimeRange();
      const info = await getChannelsLiveInfo(
        finderUserName,
        startTime,
        endTime,
      );

      setLiveInfo(info);
      onStatusChange?.(info.status);
    } catch (err) {
      const errorMsg = err.message || '获取直播信息失败';
      setError(errorMsg);
      onError?.(errorMsg);

      Taro.showToast({
        title: errorMsg,
        icon: 'none',
        duration: 2000,
      });
    } finally {
      setLoading(false);
    }
  };

  // 自动获取直播信息（如果提供了feedId）
  useEffect(() => {
    if (compatible && feedId) {
      fetchLiveInfo();
    }
  }, [compatible, feedId]);

  // 渲染不兼容提示
  if (!compatible) {
    return (
      <View className={`channel-live-player ${className}`}>
        <View className='channel-live-player__error'>
          <Text className='channel-live-player__error-text'>{error}</Text>
          <Text className='channel-live-player__error-tip'>
            请升级微信客户端至最新版本
          </Text>
        </View>
      </View>
    );
  }

  // 渲染加载状态
  if (loading) {
    return (
      <View className={`channel-live-player ${className}`}>
        <View className='channel-live-player__loading'>
          <Text>获取直播信息中...</Text>
        </View>
      </View>
    );
  }

  // 渲染错误状态
  if (error && !liveInfo) {
    return (
      <View className={`channel-live-player ${className}`}>
        <View className='channel-live-player__error'>
          <Text className='channel-live-player__error-text'>{error}</Text>
        </View>
      </View>
    );
  }

  // 渲染直播组件
  return (
    <View className={`channel-live-player ${className}`}>
      {liveInfo && (
        <>
          <View className='channel-live-player__info'>
            <Text className='channel-live-player__status'>
              直播状态: {getLiveStatusText(liveInfo.status)}
            </Text>
            {liveInfo.status === 3 && (
              <Text className='channel-live-player__replay'>
                回放状态: {getReplayStatusText(liveInfo.replayStatus)}
              </Text>
            )}
            <Text className='channel-live-player__feed-id'>
              Feed ID: {liveInfo.feedId || '无'}
            </Text>
          </View>

          <View className='channel-live-player__container'>
            {/* 注意：ChannelLive组件在Taro 3.3.9中可能不可用，需要升级到更新版本 */}
            <View className='channel-live-player__placeholder'>
              <Text className='channel-live-player__placeholder-text'>
                视频号直播组件占位符
              </Text>
              <Text className='channel-live-player__placeholder-note'>
                实际使用时需要升级Taro版本或使用原生小程序组件
              </Text>
              <Text className='channel-live-player__placeholder-info'>
                Feed ID: {liveInfo.feedId}
              </Text>
              <Text className='channel-live-player__placeholder-info'>
                视频号: {finderUserName}
              </Text>
            </View>
          </View>

          <View className='channel-live-player__jump-info'>
            <Text className='channel-live-player__jump-title'>
              点击封面将跳转至:
            </Text>
            <Text className='channel-live-player__jump-content'>
              {getJumpPageText(liveInfo.status, liveInfo.replayStatus)}
            </Text>
          </View>
        </>
      )}

      {!liveInfo && !loading && (
        <View className='channel-live-player__empty'>
          <Text>暂无直播信息</Text>
        </View>
      )}
    </View>
  );
};

export default ChannelLivePlayer;
