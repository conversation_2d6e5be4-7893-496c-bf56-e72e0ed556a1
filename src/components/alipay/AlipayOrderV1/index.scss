@import '../../../theme/groupbuy/common.scss';

.order {
  font-family: PingFangSC-Medium, PingFang SC;
  min-height: 850px;
  padding: 20px 14px 0 14px;
  box-sizing: border-box;

  .count-down {
    display: flex;
    flex-direction: column;
    align-items: center;

    & > text {
      font-size: 28px;
      font-weight: 400;
      color: #666666;
      padding-top: 10px;
    }
  }

  .age-main {
    margin-top: 30px;

    text {
      font-size: 32px;
      font-weight: 500;
      color: #1a1a1a;
    }

    .age-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-auto-rows: 70px;
      gap: 12px 20px;
      justify-content: space-between;
      margin: 20px 0 40px;

      .grid-item {
        background: #f7f7f7;
        border-radius: 16px;
        text-align: center;
        line-height: 70px;
        font-weight: 400;
        color: #333333;
        font-size: 26px;
        border: 2px solid transparent;

        &.active {
          background: #fff9f2;
          border-radius: 16px;
          border-color: #ff6a31;
          font-weight: 500;
          color: #ff6a31;
        }
      }
    }
  }

  .title {
    display: flex;
    justify-content: center;
    margin-bottom: 28px;
    padding: 0 120px;
    position: relative;

    .close {
      width: 100px;
      height: 100px;
      position: absolute;
      top: -32px;
      right: -32px;

      &::before,
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        display: inline-block;
        width: 36px;
        height: 4px;
        border-radius: 2px;
        background: #bbb;
      }

      &::before {
        transform: translate3d(-50%, -50%, 0) rotate(45deg);
      }

      &::after {
        transform: translate3d(-50%, -50%, 0) rotate(-45deg);
      }
    }

    .quota,
    .time {
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;

      .highlight {
        color: $red;
        font-size: $font-44;
        font-weight: 600;
      }

      .text {
        color: $silver;
        font-size: $font-28;
      }
    }
  }

  .subTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 18px;
    border-bottom: 1px solid #e6e6e6;
    margin-bottom: 24px;

    .level {
      color: $dim-gray;
      font-size: $font-32;
    }

    .start-time {
      color: $grey;
      font-size: $font-24;
    }
  }

  .union-sub-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .left {
      .level {
        color: $dim-gray;
        font-size: $font-32;
      }

      .start-time {
        color: $grey;
        font-size: $font-24;
        padding: 0 14px;
        margin-top: 10px;
      }
    }

    .right {
      height: 56px;
      font-size: 40px;
      font-weight: bold;
      color: #ff4100;
      line-height: 56px;
    }
  }

  .packages {
    margin: 24px 0 32px 0;
    display: flex;
    flex-direction: column;

    .row {
      width: 100%;
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .label {
        align-self: flex-start;
        color: $orange;
        font-size: $font-24;
      }

      .desc {
        flex: 1;
        color: $silver;
        font-size: $font-24;

        .text {
          margin-bottom: 12px;
        }
      }

      .desc-art-index {
        .span {
          color: #ff9c00;
        }
      }
    }
  }

  .gift-box-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 20px;
    border: 3px dashed $light-grey;
    padding: 20px 0 20px 15px;
    margin-bottom: 40px;

    .gift-box {
      width: 202px;
      height: 137px;
      box-sizing: border-box;

      &.art {
        margin-left: 22px;
      }

      .img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .label {
      width: 420px;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;

      &.calligraphy {
        width: 465px;
      }

      .label-item {
        width: 48%;
        font-size: $font-26;
        color: $light-grey;
        position: relative;
        overflow: hidden;

        &.calligraphy {
          width: 54%;
        }

        &:nth-child(2n-1).calligraphy {
          width: 45%;
        }

        &::before {
          display: inline-block;
          content: '·';
          font-size: $font-38;
          vertical-align: middle;
          margin-right: 8px;
        }
      }
    }
  }

  .music-box {
    background: #fff3db;
    border-radius: 21px;
    padding: 22px;
    box-sizing: border-box;
    margin-bottom: 24px;

    .music-sub-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      .left {
        .level {
          color: $dim-gray;
          font-size: $font-32;
        }

        .desc {
          color: $grey;
          font-size: $font-24;
          padding: 0 14px;
          margin-top: 10px;
        }
      }

      .right {
        height: 56px;
        font-size: 40px;
        color: #ff4100;
        line-height: 56px;
        display: flex;
        align-items: center;

        .img {
          width: 42px;
          height: 42px;
          margin-left: 10px;
        }
      }
    }

    .gift-img {
      width: 100%;
      height: 180px;
    }
  }

  .pay {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: calc(28px + env(safe-area-inset-bottom));

    .button {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 96px;
      border-radius: 48px;
      background: linear-gradient(270deg, #ffa300 0%, #ff6a00 100%);
      color: $white;
      font-size: $font-32;
      font-weight: bold;
      margin-right: 0;
      position: relative;
      overflow: visible;

      text {
        font-size: 44px;
        margin-right: 16px;

        &:first-letter {
          font-size: 30px;
          font-weight: 400;
        }
        &.original-cost {
          font-size: 26px;
          color: #f0f0f0;
          text-decoration: line-through;

          &:first-letter {
            font-size: 26px;
            font-weight: 400;
          }
        }
      }

      &::after {
        border: none;
      }
    }
  }
}

.original-tips {
  text-align: center;
  font-size: 26px;
  border-radius: 10px;
  color: #e59e75;
  background: #fef9ef;
  padding: 10px 20px;
}
