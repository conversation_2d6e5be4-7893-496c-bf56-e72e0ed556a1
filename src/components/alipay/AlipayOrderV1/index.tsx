import { useEffect, useState } from 'react';
import { showToast, useRouter } from '@tarojs/taro';
import { useSelector } from 'react-redux';
import { Button, Image, Text, View } from '@tarojs/components';
import { UserStateType } from '@/store/groupbuy/state';
import { useThirtySixPay } from '@/hooks/alipay/useThirtySixPay';
import sensors from '@/utils/sensors_data';

import { getSupManagements } from '@/api/groupbuy';
import './index.scss';

interface IAgeSup {
  name: string;
  sup: string;
}

// 年龄
const ageSup: IAgeSup[] = [
  {
    name: '3岁',
    sup: 'S1',
  },
  {
    name: '4岁',
    sup: 'S1',
  },
  {
    name: '5岁',
    sup: 'S2',
  },
  {
    name: '6岁',
    sup: 'S2',
  },
  {
    name: '7岁',
    sup: 'S3',
  },
  {
    name: '8岁+',
    sup: 'S3',
  },
];

export default function Index(props) {
  const { params } = useRouter();
  const {
    watchCloseOrder,
    payPageData,
    orderType,
    giveaway,
    pType,
    packagesId,
    topicId,
    pName,
    subject,
    urlType,
    isIntroduce = true,
    canReport = false,
    model = '',
    originalCost = '',
    activityConsultId,
  } = props;

  //userid
  const userId = useSelector((state: UserStateType) => state.userid);

  // 级别 索引
  const [supIdx, setSupIdx] = useState<number | null>(null);
  // loading 标记是否可支付
  const [loading, setLoading] = useState<boolean>(false);
  // const [countdown] = useState<number>(600000);

  const { authSuccess, authError, setPeriod, payConfirm } = useThirtySixPay({
    topicId: topicId || 3,
    packagesId: packagesId || 62,
    sup: '',
    canReport,
    model,
    originalCost,
    urlType,
    activityConsultId,
  });

  useEffect(() => {
    const supSta = payPageData.sup && payPageData.sup != 'default';
    if (!supSta) {
      return;
    }
    setLoading(false);
    getSupManagements({
      type: 'TESTCOURSE',
      sup: payPageData.sup,
      subject,
    }).then((res) => {
      const result = res.code == 0 && res.payload;
      if (result) {
        setLoading(true);
        setPeriod(result.period as number);
      }
    });
  }, [payPageData, subject]);

  // 支付
  const payHandle = (authorizeSta: boolean) => {
    if (supIdx == null) {
      showToast({ title: '请选择年龄', icon: 'none' });
      return;
    }
    if (!loading) {
      return;
    }
    const sup = ageSup[supIdx].sup;
    authorizeSta ? payConfirm(sup) : authSuccess({ sup });
  };
  const ageSupClick = (idx) => {
    if (idx == supIdx) return;
    setSupIdx(idx);
    watchCloseOrder(true, { sup: ageSup[idx].sup });
    // 小程序选择级别浏览
    sensors.track('ai_marketing_AlipayminiAPP_selectionlevelclick', {
      channel_id: params.channelId,
      urlType: urlType,
    });
  };
  return (
    <View className='order'>
      <View className='title'>
        <View className='close' onClick={() => watchCloseOrder(false)}></View>
      </View>
      {/*<View className='count-down'>
       <CountDown payCountDown={countdown} />
       <Text>剩余支付时间</Text>
       </View>*/}
      <View className='age-main'>
        <Text>选择年龄</Text>
        <View className='age-grid'>
          {ageSup.map((item, idx) => {
            return (
              <View
                className={`grid-item ${idx == supIdx ? 'active' : ''}`}
                key={idx}
                onClick={() => ageSupClick(idx)}
              >
                {item.name}
              </View>
            );
          })}
        </View>
      </View>
      <View className='subTitle'>
        <View className='level'>{pName}</View>
      </View>
      <View className='packages'>
        <View className='row'>
          <View className='label'>【优惠】</View>
          <View className='desc'>限购1次，新人专享特惠价</View>
        </View>
        <View className='row'>
          <View className='label'>【赠品】</View>
          <View className='desc'>
            创意绘画手工礼盒
            <Text className='label'>（收货信息将在付款后填写）</Text>
          </View>
        </View>
        <View className='row'>
          <View className='label'>【提醒】</View>
          <View className='desc'>
            支付成功后请您
            <Text className='label'>务必返回该页面进行下一步操作</Text>
          </View>
        </View>
      </View>
      <View className='gift-box-row'>
        <View className={pType + ' gift-box'}>
          <Image src={giveaway.img} mode='widthFix' className='img' />
        </View>
        <View className={pType + ' label'}>
          {giveaway.detail.map((item, index) => {
            return (
              <View className={pType + ' label-item'} key={`label-${index}`}>
                {item}
              </View>
            );
          })}
        </View>
      </View>
      {originalCost && (
        <View className='original-tips'>
          提示：支付宝代金券将在订单支付时抵扣
        </View>
      )}
      <View className='pay'>
        {userId ? (
          <Button className='button' onClick={() => payHandle(true)}>
            <Text>￥{orderType}</Text>{' '}
            {originalCost && (
              <Text className='original-cost'>￥{originalCost}</Text>
            )}
            立即支付
          </Button>
        ) : (
          <Button
            className='button'
            openType='getAuthorize'
            scope='phoneNumber'
            onError={authError}
            onGetAuthorize={() => payHandle(false)}
          >
            <Text>￥{orderType}</Text>{' '}
            {originalCost && (
              <Text className='original-cost'>￥{originalCost} </Text>
            )}
            立即支付
          </Button>
        )}
      </View>
    </View>
  );
}
