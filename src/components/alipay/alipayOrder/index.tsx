import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { View, Text, Image, Button } from '@tarojs/components';
import { UserStateType } from '@/store/groupbuy/state';
import { useThirtySixPay } from '@/hooks/alipay/useThirtySixPay';

import { getSupManagements } from '@/api/groupbuy';
import './index.scss';

export default function Index(props) {
  const {
    watchCloseOrder,
    payPageData,
    orderType,
    giveaway,
    pType,
    packagesId,
    topicId,
    pName,
    classNum,
    subject,
    isIntroduce = true,
  } = props;

  //userid
  const userId = useSelector((state: UserStateType) => state.userid);

  const { authSuccess, authError, setPeriod, payConfirm } = useThirtySixPay({
    topicId: topicId || 3,
    packagesId: packagesId || 62,
    sup: 'DEFAULT',
  });

  useEffect(() => {
    payPageData &&
      getSupManagements({
        type: 'TESTCOURSE',
        sup: payPageData.sup,
        subject,
      }).then(res => {
        const result = res.payload && res.payload;
        if (result) {
          setPeriod(+result.period);
        }
      });
  }, [payPageData, subject]);

  return (
    <View className='order'>
      <View className='title'>
        <View className='close' onClick={() => watchCloseOrder(false)}></View>
      </View>
      <View className='subTitle'>
        <View className='level'>小熊{pName}体验课</View>
      </View>
      <View className='packages'>
        <View className='row'>
          <View className='label'>【优惠】</View>
          <View className='desc'>
            {`新人专享${orderType}元${classNum}节课`}
          </View>
        </View>
        <View className='row'>
          <View className='label'>【赠品】</View>
          <View className='desc'>
            配套随材礼包
            <Text className='label'>（收货信息将在付款后填写）</Text>
          </View>
        </View>
        <View className='row'>
          <View className='label'>【提醒】</View>
          <View className='desc'>
            随材礼盒为课程配套物品，不同级别的礼盒略有差异
          </View>
        </View>
      </View>
      <View className='gift-box-row'>
        <View className={pType + ' gift-box'}>
          <Image src={giveaway.img} mode='widthFix' className='img' />
        </View>
        <View className={pType + ' label'}>
          {giveaway.detail.map((item, index) => {
            return (
              <View className={pType + ' label-item'} key={`label-${index}`}>
                {item}
              </View>
            );
          })}
        </View>
      </View>
      <View className='pay'>
        <View className='price-box'>
          <View className='symbol'>¥</View>
          <View className='price'>{orderType}</View>
        </View>
        {userId ? (
          <Button className='button' onClick={() => payConfirm()}>
            确认支付
          </Button>
        ) : (
          <Button
            className='button'
            openType='getAuthorize'
            scope='phoneNumber'
            onError={authError}
            onGetAuthorize={authSuccess}
          >
            确认支付
          </Button>
        )}
      </View>
    </View>
  );
}
