import { useRouter } from '@tarojs/taro';
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';

import qs from 'qs';
import './index.scss';

const Index = () => {
  const dispatch = useDispatch();
  //路由参数
  const router = useRouter();
  /** 头部导航栏 **/

  useEffect(() => {
    let path = router.path || '';
    if (path.substr(0, 1) === '/') {
      path = path.substr(1);
    }
    router.path &&
      dispatch({
        type: 'CHANGE_ROUTEPATH',
        routePath: `http://www.${path}/?${qs.stringify(router.params)}`,
      });
    router.params.sendId &&
      dispatch({
        type: 'CHANGE_SENDID',
        sendId: router.params.sendId,
      });
    router.params.spreadId &&
      dispatch({
        type: 'CHANGE_SPREADID',
        sendId: router.params.spreadId,
      });
    router.params.adPlatform &&
      dispatch({
        type: 'CHANGE_ADPLATFORM',
        adPlatform: router.params.adPlatform,
      });
    dispatch({
      type: 'CHANGE_CHANNELID',
      channelId: router.params.channelId || 7732,
    });
    router.params.gdt_vid &&
      dispatch({
        type: 'CHANGE_CLICKID',
        clickId: router.params.gdt_vid,
      });
    router.params.qz_gdt &&
      dispatch({
        type: 'CHANGE_CLICKID',
        clickId: router.params.qz_gdt,
      });
    router.params.wxAccountNu &&
      dispatch({
        type: 'CHANGE_WXACCOUNTNU',
        wxAccountNu: router.params.wxAccountNu,
      });
  }, [dispatch, router, router.params]);

  return null;
};

export default Index;
