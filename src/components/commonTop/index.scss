.index-header {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  background-color: #fff;
  display: flex;
  .index-header-title {
    position: absolute;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .title {
      font-size: 32px;
      font-weight: 500;
    }
    .head-btn {
      position: absolute;
      font-size: 24px;
      left: 12px;
      width: 200px;
      height: 56px;
      background: rgba(255, 255, 255, 0.6);
      border-radius: 28px;
      border: 1px solid rgba(0, 0, 0, 0.08);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-left: 12px;
      padding-right: 16px;
      box-sizing: border-box;
      &::after {
        border: none;
      }
      .btn-left {
        display: flex;
        align-items: center;
      }
      .arrow-left {
        width: 24px;
        height: 24px;
      }
      .btn-right {
        height: 24px;
        border-left: 1px solid #f3f3f3;
        padding-left: 8px;
        display: flex;
        align-items: center;
      }
      .icon-close {
        width: 24px;
        height: 24px;
      }
    }
  }
}
