import Taro, { useRouter } from '@tarojs/taro';
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import { View } from '@tarojs/components';
// 图片
import qs from 'qs';

import './index.scss';

const Index = (props: any) => {
  const dispatch = useDispatch();
  //channelId
  const channelId = useSelector((state: UserStateType) => state.channelId);
  const { currentName, isIntroduce = true, defaultChannel = '' } = props;
  //路由参数
  const router = useRouter();
  /** 头部导航栏 **/
  // 胶囊信息
  const [menuBtnTop, setMenuBtnTop] = useState<number>(0);
  const [menuBtnHeight, setMenuBtnHeight] = useState<number>(0);

  // 设置导航栏高度
  useEffect(() => {
    // const options = Taro.getLaunchOptionsSync();
    // setScene(options.scene);
    setMenuBtnTop(Taro.getMenuButtonBoundingClientRect().top);
    setMenuBtnHeight(Taro.getMenuButtonBoundingClientRect().height);
  }, []);

  useEffect(() => {
    let path = router.path || '';
    if (path.substr(0, 1) === '/') {
      path = path.substr(1);
    }
    // router.params.adapp && setAdapp(parseInt(router.params.adapp));
    // 目前只有非转介绍页（36元落地页）接收从app跳转参数
    if (!isIntroduce) {
      router.path &&
        dispatch({
          type: 'CHANGE_ROUTEPATH',
          routePath: `http://www.${path}/?${qs.stringify(router.params)}`,
        });
      router.params.sendId &&
        dispatch({
          type: 'CHANGE_SENDID',
          sendId: router.params.sendId,
        });
      router.params.spreadId &&
        dispatch({
          type: 'CHANGE_SPREADID',
          spreadId: router.params.spreadId,
        });
      router.params.adPlatform &&
        dispatch({
          type: 'CHANGE_ADPLATFORM',
          adPlatform: router.params.adPlatform,
        });
      // 增加默认渠道
      const needChanneIdRoutePath = [
        '/pages/groupbuy/index',
        '/pages/normalGroup/art/index',
      ];
      let defaultChannelId = '1983';
      if (needChanneIdRoutePath.includes(router.path)) {
        defaultChannelId = '11077';
      }
      if (
        channelId == '1983' ||
        !channelId ||
        router.params.channelId ||
        router.params.channel
      ) {
        dispatch({
          type: 'CHANGE_CHANNELID',
          channelId:
            router.params.channelId ||
            router.params.channel ||
            defaultChannel ||
            defaultChannelId,
        });
      }

      router.params.gdt_vid &&
        dispatch({
          type: 'CHANGE_CLICKID',
          clickId: router.params.gdt_vid,
        });
      router.params.qz_gdt &&
        dispatch({
          type: 'CHANGE_CLICKID',
          clickId: router.params.qz_gdt,
        });
      router.params.wxAccountNu &&
        dispatch({
          type: 'CHANGE_WXACCOUNTNU',
          wxAccountNu: router.params.wxAccountNu,
        });

      if (
        [3487, 8, 7908, 7900, 5977, 2058, 2015, 4].includes(
          Number(router.params.channelId),
        ) &&
        router.path === '/pages/groupbuy/index'
      )
        Taro.redirectTo({
          url: `/pages/normalGroup/art/index?channelId=${router.params.channelId}`,
        });
    }
  }, [dispatch, isIntroduce, router, router.params]);

  return (
    <View
      className='top-parcel'
      style={`padding-top: ${menuBtnTop + menuBtnHeight + 10}px;`}
    >
      <View
        className='index-header'
        style={`padding-top: ${menuBtnTop + menuBtnHeight + 10}px;`}
      >
        <View
          className='index-header-title'
          style={`top: ${menuBtnTop}px;height:${menuBtnHeight}px`}
        >
          <View className='title'>{currentName}</View>
        </View>
      </View>
    </View>
  );
};

export default Index;
