@import '../../theme/groupbuy/common.scss';

.level {
  padding: 0 14px;

  .card {
    display: flex;
    align-items: center;
    padding: 32px 10px 32px 0;
    border: 1px solid #cccccc;
    border-radius: 10px;
    margin-bottom: 20px;
    text-align: left;
    line-height: 1;
    background-color: #fff;

    &.selected {
      border: 1px solid #eaeaea;
      box-shadow: 0px 0px 13px 0px rgba(177, 177, 177, 0.26);
      position: relative;

      .selected-mark {
        width: 109px;
        height: 92px;
        position: absolute;
        top: -2px;
        right: 0;
        background-image: url('../../assets/thirtySix/chooseLevel/hasbuy.png');
        background-repeat: no-repeat;
        background-position: left top;
        background-size: contain;
      }

      .label {
        background: #eaeaea;
        color: #ffffff;
      }

      .info {
        position: relative;

        .title {
          .fit {
            color: #eaeaea;
          }
        }

        .desc {
          color: #eaeaea;
        }

        .tips {
          position: absolute;
          right: 10px;
          bottom: -14px;
          font-size: 24px;
          color: #b4b4b4;
        }
      }
    }

    .label {
      width: 80px;
      height: 80px;
      line-height: 80px;
      color: $white;
      font-size: $font-40;
      font-weight: bold;
      border-radius: 0 100px 100px 0;
      padding-left: 11px;
      margin-right: 15px;
      box-sizing: border-box;

      &.calligraphy {
        width: 100px;
        font-size: 36px;
        margin-right: 20px;
      }
    }

    .info {
      flex: 1;
      position: relative;

      .title {
        display: flex;
        align-items: center;
        height: 50px;
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .fit {
          font-size: $font-36;
          color: #222222;
        }

        .range {
          font-size: $font-24;
          color: #222222;
          font-weight: bold;
        }
      }

      .desc {
        font-size: $font-28;
        color: $silver;
      }

      .tips {
        position: absolute;
        right: 10px;
        bottom: -14px;
        font-size: 24px;
        color: #b4b4b4;
      }
    }
  }

  .active {
    border: 1px solid #ff9c00;
    background: #fffbf4;
  }

  .level-tip {
    font-size: 26px;
    color: #ffa415;
    text-align: center;
    margin-bottom: 30px;
  }
}

.layout-header {
  background: $white !important;
  padding: 40px 0;

  .layout-header__title {
    text-align: center;
    font-size: $font-30;
    padding-left: 80px;
  }
}

.order-layout {
  .layout {
    min-height: 850px;
    height: fit-content;
    max-height: 1850px;

    .layout-body {
      min-height: 850px;
      max-height: 1850px;
    }

    .layout-body__content {
      max-height: 1850px;
    }
  }
}
.selPicBook {
  padding-bottom: 100px;
}
.buy-more {
  padding: 30px 0 220px 0;
  &-title {
    display: flex;
    font-size: 30px;
    justify-content: space-between;
    color: #999999;
    &-left,
    &-right Text {
      color: #ff800f;
    }
    &-right {
      display: flex;
      align-items: center;
      image {
        width: 42px;
        height: 42px;
        margin-left: 8px;
      }
      .inline {
        display: inline-block;
        position: relative;
        overflow: hidden;
        width: 40px;
        height: 40px;
        .up {
          top: 0;
          position: absolute;
          transition: 1s;
          left: 20%;
          &.act {
            top: -40px;
          }
        }
        .down {
          top: 40px;
          position: absolute;
          transition: 1s;
          left: 35%;
          &.act {
            top: 0px;
          }
        }
      }
    }
  }
  .picture-book-info {
    width: 690px;
    height: 334px;
    border-radius: 21px;
    background: #e5f4ff;
    padding: 22px 24px;
    box-sizing: border-box;
    margin-top: 24px;
    &-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      &-left {
        font-size: 32px;
        color: #461818;
        .p {
          color: #936161;
          font-size: 24px;
        }
      }
      &-right {
        color: #ff4100;
        font-size: 40px;
      }
    }
    &.MUSIC_APP {
      background: #7d35f8;
      .picture-book-info-title {
        &-left {
          color: #fff;
          .p {
            color: #fad41a;
          }
        }
        &-right {
          color: #fad41a;
        }
      }
    }
    &.WRITE_APP {
      background: #3dc593;
      .picture-book-info-title {
        &-left {
          color: #fff;
          .p {
            color: #fdfe01;
          }
        }
        &-right {
          color: #fdfe01;
        }
      }
    }
    &-Image {
      width: 100%;
      height: 179px;
      margin-top: 24px;
    }
  }
}
.bottom {
  width: 720px;
  display: flex;
  justify-content: space-between;
  padding: 40px 0;
  position: fixed;
  box-sizing: border-box;
  z-index: 999;
  bottom: 0px;
  background: #fff;
  left: 0;
  margin: 0 auto;
  right: 0;
  .price {
    color: #ff7000;
    font-size: 66px;
    Text {
      font-size: 34px;
    }
  }
  .pay-btn {
    width: 226px;
    height: 96px;
    line-height: 96px;
    text-align: center;
    border-radius: 96px;
    color: #ffffff;
    font-size: 34rpx;
    margin: 0;
    background: linear-gradient(to right, #ff6a00, #ffa300);
  }
}
