import { useEffect, useState } from 'react';
import { useRouter } from '@tarojs/taro';
import { Text, View, Button, Image } from '@tarojs/components';
import { supAgeInterval, supSwitch } from '@/api/groupbuy';
import { useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import CheckLevel from '@/utils/checkLevel';
import selImg from '@/assets/normalGroup/art/select-icon.png';
import seledImg from '@/assets/normalGroup/art/selected-icon.png';
import picturebookImg from '@/assets/normalGroup/art/picturebook-img.png';
import sensors from '@/utils/sensors_data';

import './index.scss';

/**
 *  @param levelType 级别类型
 *  0、选择级别（年龄）
 *  1、选择级别（写死描述）
 *  2、支付和级别选择一起（不在此弹窗处理）
 * */

export default function Index(props) {
  // 选择卡片后通知父组件显示订单浮窗
  const {
    watchShowOrder,
    oldLevelArray,
    pType,
    levelType = 0,
    orderType = '',
    isOpenWind = false,
    payConfirmHandler,
    getPhoneNumberHandler,
    newLevelType = false, // 是否是点击级别不弹直接购买
    selectPicBookType = false,
    selPicBook, // 选择
    attachSubject = null,
    payOrderData, // 美术套餐
  } = props; //new_array,
  // 获取路由挂参
  const router = useRouter();
  let params = router.params;
  // 选择激活的卡片
  const [tabActive, setTabActive] = useState<number>(-1);

  const [level, setLevel] = useState<any>([]);
  const [hasBuyLevel, setHasBuyLevel] = useState<any[]>([]);
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  const { orderId } = params;
  const orderid = useSelector((state: UserStateType) => state.orderId);
  //channelId
  const channelId = useSelector((state: UserStateType) => state.channelId);
  // const [title, setTitle] = useState<string>(
  //   '为保证孩子的学习体验，请正确选择孩子的年龄',
  // );

  const title =
    levelType == 1
      ? '为保证孩子的学习体验，请正确选择对应的级别'
      : '为保证孩子的学习体验，请正确选择孩子的年龄';

  // const [supSwitchType, setSupSwitchType] = useState<any>(false);

  const [upup, setupup] = useState(false);
  useEffect(() => {
    isOpenWind &&
      setTimeout(() => {
        setupup(true);
      }, 3000);
  }, [isOpenWind]);

  useEffect(() => {
    supSwitch()
      .then(res => {
        // setSupSwitchType(res.payload);
        if (res.payload) {
          oldLevel();
        } else {
          oldLevel();
        }
      })
      .catch(() => {
        oldLevel();
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [levelType]);
  const oldLevel = () => {
    const levelArray = oldLevelArray;
    if (levelType == 1) {
      setLevel(levelArray);
      return;
    }
    supAgeInterval().then(res => {
      levelArray.map((item, index) => {
        item.range = res.payload['S' + (index + 1)];
      });
      setLevel(levelArray);
    });
  };

  const filterBuyLevel = sup => {
    return (
      hasBuyLevel &&
      hasBuyLevel.length &&
      hasBuyLevel.findIndex(v => v == sup) > -1
    );
  };

  useEffect(() => {
    if (userId) {
      sensors.track('market_adverts_enterpage_wp_user', {
        userId: userId,
        clickId: params.clickid,
        channel: channelId,
        ua: navigator.userAgent,
      });
    }
    new CheckLevel({
      userId,
      channelId,
      orderId: orderid || orderId,
      regtype: props.regtype,
      subjects: 'ART_APP',
    })
      .initCheck()
      .then((res: any[]) => {
        setHasBuyLevel(res);
      });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId, channelId]);

  return (
    <View className='level'>
      <View className='level-tip'>*{title}</View>
      {level.map((item, index) => {
        if (index > 2) return;
        if (!newLevelType) {
          return (
            <View
              className={`card ${
                filterBuyLevel(item.label) ? `selected` : ``
              } ${tabActive === index ? 'active' : ''}`}
              key={`card-${index}`}
              onClick={() => {
                if (filterBuyLevel(item.label)) {
                  return;
                }
                setTabActive(index);
                watchShowOrder(true, level[index]);
              }}
            >
              <View className='selected-mark'></View>
              <View
                className={pType + ' label'}
                style={`${
                  filterBuyLevel(item.label)
                    ? ``
                    : `background: ${item.bgcolor}`
                }`}
              >
                {item.label}
              </View>
              <View className='info'>
                <View className='title'>
                  <Text className='fit'>{item.fit}</Text>
                  {/* <View className="range">{item.range}</View> */}
                </View>
                {/* {(!supSwitchType || pType === 'calligraphy') && ( */}
                <View
                  className='desc'
                  style={`${
                    filterBuyLevel(item.label) ? `` : `color: ${item.bgcolor}`
                  }`}
                >
                  {item.range}
                </View>
                {item.tips && <Text className='tips'>{item.tips}</Text>}
              </View>
            </View>
          );
        } else {
          if (userId) {
            return (
              <View
                className={`card ${
                  filterBuyLevel(item.label) ? `selected` : ``
                } ${tabActive === index ? 'active' : ''}`}
                key={`card-${index}`}
                onClick={() => {
                  if (filterBuyLevel(item.label)) {
                    return;
                  }
                  setTabActive(index);
                  //   美术选择S3不能加购
                  if (
                    !(
                      (index == 2 &&
                        attachSubject &&
                        attachSubject.subject == 'PICTURE_BOOK') ||
                      attachSubject == null
                    )
                  )
                    return;
                  else selPicBook != null && selPicBook(false);
                  watchShowOrder(true, level[index]);
                  payConfirmHandler();
                }}
              >
                <View className='selected-mark'></View>
                <View
                  className={pType + ' label'}
                  style={`${
                    filterBuyLevel(item.label)
                      ? ``
                      : `background: ${item.bgcolor}`
                  }`}
                >
                  {item.label}
                </View>
                <View className='info'>
                  <View className='title'>
                    <Text className='fit'>{item.fit}</Text>
                    {/* <View className="range">{item.range}</View> */}
                  </View>
                  {/* {(!supSwitchType || pType === 'calligraphy') && ( */}
                  <View
                    className='desc'
                    style={`${
                      filterBuyLevel(item.label) ? `` : `color: ${item.bgcolor}`
                    }`}
                  >
                    {item.range}
                  </View>
                  {item.tips && <Text className='tips'>{item.tips}</Text>}
                </View>
              </View>
            );
          } else {
            return (
              <Button
                className={`card ${
                  filterBuyLevel(item.label) ? `selected` : ``
                } ${tabActive === index ? 'active' : ''}`}
                open-type='getPhoneNumber'
                onGetPhoneNumber={getPhoneNumberHandler}
                key={`card-${index}`}
                onClick={() => {
                  if (filterBuyLevel(item.label)) {
                    return;
                  }
                  setTabActive(index);
                  // 美术选择S3不能加购
                  if (
                    !(
                      (index == 2 &&
                        attachSubject &&
                        attachSubject.subject == 'PICTURE_BOOK') ||
                      attachSubject == null
                    )
                  )
                    return;
                  else selPicBook != null && selPicBook(false);
                  watchShowOrder(true, level[index]);
                }}
              >
                <View className='selected-mark'></View>
                <View
                  className={pType + ' label'}
                  style={`${
                    filterBuyLevel(item.label)
                      ? ``
                      : `background: ${item.bgcolor}`
                  }`}
                >
                  {item.label}
                </View>
                <View className='info'>
                  <View className='title'>
                    <Text className='fit'>{item.fit}</Text>
                    {/* <View className="range">{item.range}</View> */}
                  </View>
                  {/* {(!supSwitchType || pType === 'calligraphy') && ( */}
                  <View
                    className='desc'
                    style={`${
                      filterBuyLevel(item.label) ? `` : `color: ${item.bgcolor}`
                    }`}
                  >
                    {item.range}
                  </View>
                  {item.tips && <Text className='tips'>{item.tips}</Text>}
                </View>
              </Button>
            );
          }
        }
      })}
      {/*  美术选择S3不能加购 */}
      {!(
        attachSubject == null ||
        ((tabActive == 2 || tabActive == -1) &&
          attachSubject.subject == 'PICTURE_BOOK')
      ) && (
        <View className='selPicBook'>
          <View className='buy-more'>
            <View className='buy-more-title'>
              <View className='buy-more-title-left'>【限时福利】</View>
              <View
                className='buy-more-title-right'
                onClick={() => {
                  selPicBook != null && selPicBook(!selectPicBookType);
                }}
              >
                名额仅剩
                <View className='inline'>
                  <Text className={`up ${upup ? 'act' : ''}`}>10</Text>
                  <Text className={`down ${upup ? 'act' : ''}`}>7</Text>
                </View>
                个
                <Image src={!selectPicBookType ? selImg : seledImg} />
              </View>
            </View>
            <View
              className={`picture-book-info ${
                attachSubject != null ? attachSubject.subject : ''
              }`}
            >
              <View className='picture-book-info-title'>
                <View className='picture-book-info-title-left'>
                  {attachSubject.title}
                  <View className='p'>{attachSubject.desc}</View>
                </View>
                <View className='picture-book-info-title-right'>免费</View>
              </View>
              <Image src={picturebookImg} className='picture-book-info-Image' />
            </View>
            <View className='bottom'>
              <View className='price'>
                <Text>￥</Text>
                {+payOrderData.orderType + (selectPicBookType ? 0 : 0)}
              </View>
              {userId ? (
                <View
                  className='pay-btn'
                  onClick={() => {
                    watchShowOrder(true, level[tabActive]);
                    payConfirmHandler();
                  }}
                >
                  立即支付
                </View>
              ) : (
                <Button
                  className='pay-btn'
                  open-type='getPhoneNumber'
                  onGetPhoneNumber={getPhoneNumberHandler}
                  onClick={() => {
                    watchShowOrder(true, level[tabActive]);
                  }}
                >
                  立即支付
                </Button>
              )}
            </View>
          </View>
        </View>
      )}
    </View>
  );
}
