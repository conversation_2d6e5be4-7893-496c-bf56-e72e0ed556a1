import Taro from '@tarojs/taro';

import React, { useState, useEffect } from 'react';
import { View, Image, Text } from '@tarojs/components';
import { AddressTypes } from '@/types/types';
import { useSelector, useDispatch } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';

import { getAddressById, getAddressList } from '@/api/groupbuy';

import arrowRight from '@/assets/pay/arrow-right.png';
import addressIcon from '@/assets/pay/address-icon.png';
import './index.scss';

interface Props {
  addressCallback: Function;
  title?: string;
}
const AddressSelect: React.FunctionComponent<Props> = (props: Props) => {
  const dispatch = useDispatch();
  const userId = useSelector((state: UserStateType) => state.userid);
  // const userId = '795021680833830912'
  const chooseAddressId = useSelector(
    (state: UserStateType) => state.chooseAddressId,
  );

  const { addressCallback, title } = props;
  const [address, setAddress] = useState<AddressTypes | any>();

  useEffect(() => {
    getChooseAddress();
  }, [chooseAddressId]);

  const getChooseAddress = () => {
    if (chooseAddressId) {
      getAddressById({ addressId: chooseAddressId, userId })
        .then(res => {
          if (res.code === 0) {
            setAddress(res.payload);
          } else {
            Taro.showToast({
              title: res.errors,
              icon: 'none',
            });
          }
        })
        .catch(err => {
          Taro.showToast({
            title: '网络异常~' + err,
            icon: 'none',
          });
        });
    } else if (userId) {
      getAddressList({ userId, subject: 'ART_APP' })
        .then(res => {
          const { payload } = res;
          if (payload?.length > 0) {
            // 如果选择的默认地址为国外地址,则取第一条国内地址作为当前订单的默认地址
            let defaultAddress = payload.find(
              item => +item.isDefault === 1 && item.countryCode === 'cn',
            );
            if (!defaultAddress) {
              defaultAddress = payload.find(item => item.countryCode === 'cn');
            }
            if (defaultAddress) {
              setAddress(defaultAddress);
            }
          }
        })
        .catch(err => {
          Taro.showToast({
            title: '网络异常~' + err,
            icon: 'none',
          });
        });
    }
  };

  useEffect(() => {
    if (address) {
      dispatch({
        type: 'CHOOSEADDRESSID',
        chooseAddressId: address.id,
      });
      addressCallback(address);
    }
  }, [address]);

  const handleAddressClick = () => {
    let href = `pages/address/list/index?enterPage=chooseAddress`;
    Taro.navigateTo({
      url: `/${href}`,
    });
  };
  return (
    <View className='address'>
      {title ? (
        <View className='addresstitle'>{title}</View>
      ) : (
        <View className='height40'></View>
      )}
      <View className='content' onClick={handleAddressClick}>
        {!address ? (
          <>
            <Image className='address-icon' src={addressIcon} />
            <View className='text'>请填写收货地址</View>
          </>
        ) : (
          <>
            <View className='address-info'>
              <View className='name'>
                {address.receiptName}
                <Text className='namePhone'>
                  {address.receiptTel.replace(/^(.{3})(.*)(.{4})$/, '$1 $2 $3')}
                </Text>
              </View>
              <View className='detail'>
                {(address.province === address.city
                  ? address.province
                  : address.province + address.city) +
                  address.area +
                  address.street +
                  address.addressDetail}
              </View>
            </View>
          </>
        )}

        <Image className='arrow-icon' src={arrowRight} />
      </View>
    </View>
  );
};

export default AddressSelect;
