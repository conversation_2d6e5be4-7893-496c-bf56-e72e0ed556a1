.address {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: #fff;
  .content {
    display: flex;
    align-items: center;
    padding: 32px;
    .address-info {
      flex: 1;
      .name {
        font-size: 32px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
        margin-bottom: 16px;
        .namePhone {
          margin-left: 20px;
        }
      }
      .detail {
        font-size: 28px;
        color: #999999;
        line-height: 40px;
      }
    }
    .address-icon {
      width: 23px;
      height: 27px;
    }
    .text {
      color: #cccccc;
      font-size: 32px;
      flex: 1;
      margin-left: 10px;
    }
  }
}
.arrow-icon {
  width: 30px;
  height: 30px;
}
.addresstitle {
  color: #888888;
  background: #f8f8f8;
  font-size: 28px;
  font-family: PingFangSC-Regular, PingFang SC;
  padding: 24px 32px;
}
.height40 {
  height: 40px;
}
