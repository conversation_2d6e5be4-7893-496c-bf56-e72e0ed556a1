import { View, Image } from '@tarojs/components';
import tips from '@/assets/assist/level-tips.png';
import styles from './index.module.scss';

export default function LevelTips({ mTop = 0 }) {
  return (
    <View
      className={`${styles['level-tips']}  ${
        mTop ? styles['level-tips-30'] : ''
      }`}
    >
      <View className={styles['level-tips-left']}>
        <Image src={tips} mode='widthFix' />
      </View>
      <View className={styles['level-tips-right']}>
        <View className={styles['tips-word']}>
          · S1，S2，S3每天体验1次，10天体验完成
        </View>
        {/* <View className={styles['tips-word']}>
          · S4每天可体验2次，5-10天体验完成
        </View> */}
        <View className={styles['tips-word']}>· 体验结束后仍可反复观看</View>
      </View>
    </View>
  );
}
