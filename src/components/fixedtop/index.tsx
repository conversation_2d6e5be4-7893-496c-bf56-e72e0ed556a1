import { useState, useCallback } from 'react';
import { View } from '@tarojs/components';
import './index.scss';

export default function Index(props) {
  const [containerHeigt, setContainerHeight] = useState<number>(0);
  const sticky = useCallback(dom => {
    if (dom != null) {
      dom
        .boundingClientRect(rect => {
          setContainerHeight(rect.height);
        })
        .exec();
    }
  }, []);
  return (
    // @ts-ignore
    <View className='container' style={{ height: `${containerHeigt}px` }}>
      <View ref={sticky}>{props.children}</View>
    </View>
  );
}
