.drainage-address-status-wrap {
  .at-modal__container {
    width: 640px;
    border-radius: 30px;
    padding: 50px 26px;
    box-sizing: border-box;
  }
  .at-modal__header {
    padding: 0;
    font-size: 40px;
    font-weight: 600;
    color: #333333;
    line-height: 56px;
    text-align: center;
    &::after {
      border: 0;
    }
  }
  .at-modal__content {
    padding: 0;
  }
  .at-modal__footer {
    &::before {
      border: 0;
    }
  }

  .at-modal__action {
    display: flex;
    justify-content: space-between;
    .left {
      width: 270px;
      height: 88px;
      border-radius: 48px;
      border: 1px solid #bdbdbd;
      font-size: 32px;
      font-weight: 500;
      color: #999999;
      line-height: 88px;
      text-align: center;
      box-sizing: border-box;
    }
    .right {
      width: 270px;
      height: 88px;
      border-radius: 48px;
      line-height: 88px;
      text-align: center;
      background: linear-gradient(270deg, #ffa300 0%, #ff6a00 100%);
      font-size: 32px;
      font-weight: 500;
      color: #ffffff;
    }
  }
}
.dialogBox {
  .title {
    font-size: 30px;
    font-weight: 400;
    color: #666666;
    margin-top: 10px;
    text-align: center;
    .titleText {
      line-height: 42px;
      text-align: center;
    }
  }
  .addressInfo {
    min-height: 312px;
    background: #f8f8f8;
    border-radius: 30px;
    margin: 40px auto 50px;
    box-sizing: border-box;
    padding: 32px 10px 40px 40px;
    .addressInfoItem {
      font-size: 26px;
      font-weight: 400;
      color: #999999;
      display: flex;
      margin-bottom: 20px;
      .itemLeft {
        width: 140px;
        text-align: left;
      }
      .itemRight {
        width: 1px;
        flex: 1;
        text-align: left;
        word-break: break-all;
      }
    }
  }
  .tips {
    font-size: 30px;
    font-weight: 400;
    color: #666666;
    padding: 20px 50px 50px 50px;
  }
}
