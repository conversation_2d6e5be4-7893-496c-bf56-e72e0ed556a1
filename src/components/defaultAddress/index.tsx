import { getDefaultAddress } from '@/api/groupbuy';
import { UserStateType } from '@/store/groupbuy/state';
import { View } from '@tarojs/components';
import { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import Taro from '@tarojs/taro';
import { AtModal, AtModalAction, AtModalContent, AtModalHeader } from 'taro-ui';
import './index.scss';

interface DefaultAddressProps {
  variableAssignment: (onlysetId: boolean, data: any, type?: number) => void;
  defaultAddressModal: any;
  setDefaultAddressModal: any;
}

export default function DefaultAddress(props: DefaultAddressProps) {
  const {
    variableAssignment,
    defaultAddressModal,
    setDefaultAddressModal,
  } = props;
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);

  const [defaultAddressInfo, setDefaultAddressInfo] = useState({
    addressId: '',
    telAreaCode: '',
    province: '',
    city: '',
    area: '',
    street: '',
    addressData: '',
    addressDetail: '',
    name: '',
    phone: '',
  });
  const modalOptions = useMemo(() => {
    if (defaultAddressModal.type === '2') {
      return {
        show: defaultAddressModal.show,
        title: '地址填写',
        confirBtn: '确认提交',
        cancelBtn: '取消修改',
        type: '2',
      };
    }
    return {
      show: defaultAddressModal.show,
      title: '地址填写',
      confirBtn: '直接使用',
      cancelBtn: '更换信息',
      type: '1',
    };
  }, [defaultAddressModal.type, defaultAddressModal.show]);

  const initDefaultAddress = data => {
    const {
      telAreaCode,
      province,
      city,
      area,
      street,
      addressDetail,
      receiptName,
      receiptTel,
      id,
    } = data;
    let municipalDistricts = province === city;
    let addressJoin = municipalDistricts
      ? province + '/' + area
      : province + '/' + city + '/' + area;
    const addressData = `${addressJoin}/${street}`;

    setDefaultAddressInfo({
      telAreaCode,
      province,
      city,
      area,
      street,
      addressData,
      addressDetail,
      name: receiptName,
      phone: receiptTel,
      addressId: id,
    });
  };
  const handleConfirm = () => {
    let isCheck = true;
    let isChange = false;
    let newAddress = false;
    if (modalOptions.type === '1') {
      variableAssignment(false, defaultAddressInfo);
    } else {
      isCheck = false;
      isChange = true;
    }

    setDefaultAddressModal({
      show: false,
      type: modalOptions.type,
      isCheck,
      isChange,
      newAddress,
    });
  };
  const handleCancel = () => {
    let isCheck = true;
    let isChange = false;
    let newAddress = false;
    if (modalOptions.type === '2') {
      variableAssignment(false, defaultAddressInfo);
    } else {
      isCheck = false;
      newAddress = true;
      variableAssignment(true, defaultAddressInfo);
    }
    setDefaultAddressModal({
      show: false,
      type: modalOptions.type,
      isCheck,
      isChange,
      newAddress,
    });
  };

  const getUserDefaultAddUser = () => {
    getDefaultAddress(userId).then(res => {
      if (res.code === 0 && res.payload) {
        initDefaultAddress(res.payload);
        setDefaultAddressModal({
          show: true,
          type: modalOptions.type,
          isCheck: true,
          isChange: false,
          newAddress: false,
        });
      } else {
        const chooseAddressTime = Taro.getStorageSync('chooseAddressTime');
        const nowTime = new Date().valueOf();
        if (nowTime - chooseAddressTime / 1 < 60 * 60 * 1000) return;
        Taro.setStorageSync('chooseAddressTime', nowTime);
        let ENV_TYPE = Taro.getEnv();
        if (ENV_TYPE === 'WEAPP') {
          const appId = Taro.getAccountInfoSync().miniProgram.appId;
          // 小熊艺术乐园移除选择微信地址
          if (appId === 'wx34831c7cbd4406e5') {
            return false;
          }
          // @ts-ignore taro没有集成的api可以直接使用wx.api
          wx.requirePrivacyAuthorize({
            success: (Authorizeres: any) => {
              console.error(Authorizeres);
              // 用户同意授权
              // 继续小程序逻辑
              wxchooseAddressFunc();
            },
          });
        } else if (ENV_TYPE === 'ALIPAY') {
          alichooseAddressFunc();
        }
      }
    });
  };
  const wxchooseAddressFunc = () => {
    Taro.chooseAddress({
      success: function(addressRes: any) {
        let addressJoin =
          addressRes.provinceName === addressRes.cityName
            ? addressRes.provinceName + '/' + addressRes.countyName
            : addressRes.provinceName +
              '/' +
              addressRes.countyName +
              '/' +
              addressRes.countyName;
        const addressData = `${addressJoin}/${addressRes.streetName}`;

        let selectAddressInfo = {
          telAreaCode: '+86',
          province: addressRes.provinceName,
          city: addressRes.cityName,
          area: addressRes.countyName,
          street: addressRes.streetName,
          name: addressRes.userName,
          phone: addressRes.telNumber,
          addressDetail: addressRes.detailInfoNew,
          addressData,
          addressId: '',
        };
        variableAssignment(false, selectAddressInfo, 2);
        setDefaultAddressModal({
          show: false,
          type: modalOptions.type,
          isCheck: false,
          isChange: false,
          newAddress: true,
        });
      },
    });
  };
  const alichooseAddressFunc = () => {
    my.getAddress({
      success: function(res: any) {
        if (res.resultStatus === '9000' && typeof res.result === 'object') {
          let addressRes = res.result;
          let addressJoin =
            addressRes.prov === addressRes.city
              ? addressRes.prov + '/' + addressRes.city
              : addressRes.prov + '/' + addressRes.city + '/' + addressRes.area;
          const addressData = `${addressJoin}/${addressRes.street}`;

          let selectAddressInfo = {
            telAreaCode: '+86',
            province: addressRes.prov,
            city: addressRes.city,
            area: addressRes.area,
            street: addressRes.street,
            name: addressRes.fullname,
            phone: addressRes.mobilePhone,
            addressDetail: addressRes.address,
            addressData,
            addressId: '',
          };
          variableAssignment(false, selectAddressInfo, 2);
          setDefaultAddressModal({
            show: false,
            type: modalOptions.type,
            isCheck: false,
            isChange: false,
            newAddress: true,
          });
        }
      },
    });
  };
  useEffect(() => {
    userId && getUserDefaultAddUser();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId]);

  const typeOneEl = type => {
    if (type === '1') {
      return (
        <>
          <View className='title'>
            <View className='titleText'>发现您已是我们的用户</View>
            <View className='titleText'>是否填入默认信息</View>
          </View>
          <View className='addressInfo'>
            <View className='addressInfoItem'>
              <View className='itemLeft'>收货人：</View>
              <View className='itemRight'>{defaultAddressInfo.name}</View>
            </View>
            <View className='addressInfoItem'>
              <View className='itemLeft'>联系电话：</View>
              <View className='itemRight'>{defaultAddressInfo.phone}</View>
            </View>
            <View className='addressInfoItem'>
              <View className='itemLeft'>所在地区：</View>
              <View className='itemRight'>
                {defaultAddressInfo.addressData}
              </View>
            </View>
            <View className='addressInfoItem'>
              <View className='itemLeft'>详细地址：</View>
              <View className='itemRight'>
                {defaultAddressInfo.addressDetail}
              </View>
            </View>
          </View>
        </>
      );
    } else {
      return (
        <View className='tips'>
          家长您好，此次修改涉及到可能已购买的小熊音乐、小熊书法等其他产品的随材礼包邮寄地址，确认继续修改？
        </View>
      );
    }
  };

  return (
    <View>
      {modalOptions.show && (
        <AtModal
          isOpened={modalOptions.show}
          closeOnClickOverlay={false}
          className='drainage-address-status-wrap'
        >
          <AtModalHeader>{modalOptions.title}</AtModalHeader>
          <AtModalContent>
            <View className='dialogBox'>{typeOneEl(modalOptions.type)}</View>
          </AtModalContent>
          <AtModalAction>
            <View className='left' onClick={handleCancel}>
              {modalOptions.cancelBtn}
            </View>
            <View className='right' onClick={handleConfirm}>
              {modalOptions.confirBtn}
            </View>
          </AtModalAction>
        </AtModal>
      )}
    </View>
  );
}
