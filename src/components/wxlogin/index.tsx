import Taro, { useRouter } from '@tarojs/taro';
import { useDispatch } from 'react-redux';
import { useEffect } from 'react';
import {
  getOrderId,
  getOrdersId,
  getUserOpenIdSubject,
  getWeixinByUnid,
} from '@/api/groupbuy'; // getUserOpenId,

export default function Index(props) {
  // isIntroduce是否是转介绍页，非转介绍页调用接口一样
  const {
    subject = 'ART_APP',
    isIntroduce = true,
    isFollow = true,
    noAskNoaddress = false,
  } = props;
  const router = useRouter();
  const dispatch = useDispatch();
  useEffect(() => {
    Taro.login({
      success: function(res) {
        if (res.code) {
          // getUserOpenIdSubject,兼容getUserOpenId
          getUserOpenIdSubject({
            code: res.code,
            channel: router.params.channelId || props.channelId || '',
            subject,
          }).then(data => {
            if (data.code === 0) {
              //  data.payload.uid = '876106691757166592';
              data.payload.mobile &&
                dispatch({
                  type: 'CHANGE_MOBILE',
                  mobile: data.payload.mobile,
                });
              dispatch({
                type: 'CHANGE_OPENID',
                openid: data.payload.openid,
              });
              dispatch({
                type: 'CHANGE_UNIONID',
                unionId: data.payload.unionid,
              });
              if (data.payload.token)
                Taro.setStorageSync('appToken', data.payload.token);
              // 获取用户状态
              getWeixinByUnid({ unionid: data.payload.unionid }).then(item => {
                if (item.code === 0) {
                  dispatch({
                    type: 'CHANGE_USERROLE',
                    userRole: item.payload.user_role,
                  });
                }
              });
              // 很多接口、逻辑需要监听uid，把更新uid的操作放在后面以提高以上更新状态的优先级、降低报错率
              // 实测：部分接口监听到uid更新后调用接口获取数据，而且需要有token，但是token是在uid更新后才更新的，所以有时候接口会取不到token而报错
              if (data.payload.uid) {
                dispatch({
                  type: 'CHANGE_USERID',
                  userid: data.payload.uid,
                });
              } else {
                dispatch({ type: 'CHANGE_USERID', userid: '' });
              }
              if (noAskNoaddress) return;
              //   获取未填写地址订单
              // 美术写字的接口不同，其第三个参数不同
              let $url = subject === 'ART_APP' ? getOrdersId : getOrderId,
                $obj =
                  subject === 'ART_APP'
                    ? { subjects: subject }
                    : { status: 'COMPLETED' };
              data.payload.uid &&
                $url({
                  userId: data.payload.uid,
                  addressId: 0,
                  ...$obj,
                }).then(item => {
                  let _payload =
                    subject === 'ART_APP' ? item.payload[0] : item.payload;
                  if (item.code === 0 && _payload) {
                    dispatch({
                      type: 'CHANGE_ORDERID',
                      orderId: _payload.id,
                    });
                    if (!isFollow) return;
                    // 用户订单是否已退费 REFUNDEND=>已退费
                    if (_payload.isRefund === 'REFUNDEND') {
                      return;
                    }
                    if (
                      subject !== 'WRITE_APP' &&
                      _payload.packagesName !== '小熊书法体验版'
                    ) {
                      // form=1代表是从艺术宝app跳转过来到购买页的，model=4  模式4
                      let _url = `/pages/groupbuy/addAddress/index?sup=${
                        _payload.sup
                      }${_payload.model == 4 ? '&vType=2' : ''}`;
                      let unionCombineOrders = Taro.getStorageSync(
                          'unionCombineOrders',
                        ),
                        unionCombineOrdersIndex;
                      if (unionCombineOrders)
                        unionCombineOrdersIndex = unionCombineOrders.findIndex(
                          o => o.orderid == _payload.id,
                        );
                      if (unionCombineOrdersIndex > -1)
                        _url =
                          _url +
                          `&isAddPic=true&unionCombineOrders=${unionCombineOrders[0]
                            .orderid +
                            ',' +
                            unionCombineOrders[1].orderid}`;
                      Taro.navigateTo({
                        url: _url,
                      });
                    } else {
                      // Taro.navigateTo({
                      //   url: `/pages/groupbuy/addTeacher/index?uid=${data.payload.uid}&type=105&subject=WRITE_APP`,
                      // });
                      //   if (
                      //     !['910', '8915', '7659', '668', '911', '8916'].includes(
                      //       _payload.packagesId,
                      //     )
                      //   )
                      //     //   Taro.navigateTo({
                      //     //     url: `/pages/writeExperience/addTeacher/index`,
                      //     //   });
                      //     // else
                      //     Taro.navigateTo({
                      //       url: `/pages/launch/follow/index?uid=${data.payload.uid}&type=105&subject=WRITE_APP`,
                      //     });
                    }
                  }
                });
            }
          });
        } else {
          console.log('登录失败！' + res.errMsg);
        }
      },
    });
    !isIntroduce &&
      Taro.showShareMenu({
        withShareTicket: true,
      });
  }, [dispatch, isIntroduce, router, router.params, subject]);
  return null;
}
