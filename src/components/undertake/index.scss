page {
  background: #ffc500;
}
.box {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 50px 0;
  min-height: 100vh;
  box-sizing: border-box;
}
.topBanner {
  width: 100%;
  height: 360px;
  position: absolute;
  top: 0;
  left: 0;
  image {
    width: 100%;
    height: 100%;
  }
}
.section {
  width: 670px;
  background: #ffffff;
  border-radius: 40px;
  padding-bottom: 80px;
}
.qrcodeWrap {
  width: 100%;
  margin: 80px auto 0;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  .qrcodeTitle {
    text-align: center;
    font-size: 36px;
    font-weight: 400;
    color: #4e4e4e;
    height: 50px;
    line-height: 50px;
    margin-bottom: 28px;
    &.linkTypeTitle {
      color: #333;
    }
    .qrcodeText {
      color: #ff8228;
    }
  }
  image {
    width: 100%;
    height: 100%;
  }
  .qrcode {
    width: 380px;
    min-height: 380px;
    overflow: hidden;
  }
  .tipsWord {
    margin-top: 20px;
    height: 32px;
    font-size: 24px;
    color: #888888;
    line-height: 32px;
  }
  .tips {
    width: 90%;
    margin-top: 36px;
    display: flex;
    &.linkType {
      margin-top: 0;
    }
    &.tips-center {
      justify-content: center;
      .tipsText {
        flex: none;
      }
    }
  }
  .tipsIcon {
    width: 50px;
    height: 70px;
    margin-top: 12px;
  }
  .tipsText {
    flex: 1;
    margin-left: 12px;
    .tipsTit {
      margin-bottom: 4px;
    }
    .tipsSub {
      font-size: 28px;
      font-weight: 500;
      color: #777777;
    }
  }
}
.btn {
  width: 540px;
  height: 80px;
  margin: 68px auto 0;
  image {
    width: 100%;
    height: 100%;
  }
}
.writeFollow {
  position: relative;
  padding-top: 236px;
  height: 100%;
  background: #f7f7f7;
  box-sizing: border-box;
  .scan-body {
    width: 648px;
    min-height: 704px;
    background-color: #fff;
    border-radius: 20px;
    margin: 0 auto;
    text-align: center;
    position: relative;
    z-index: 2;
    .scan-title {
      font-size: 40px;
      color: #0ab7da;
      padding: 60px 0 40px;
    }
    .scan-derc {
      width: 488px;
      height: 88px;
      background: #f7f7f7;
      line-height: 88px;
      font-size: 24px;
      margin: 0 auto;
      border-radius: 8px;
      color: #333;
      margin-bottom: 38px;
    }
    .scan-qrcode {
      background: url('../../assets/follow/scan-border.png') no-repeat;
      background-size: 100% 100%;
      width: 312px;
      height: 312px;
      margin: 0 auto;
      padding: 18px;
      box-sizing: border-box;
      padding-bottom: 148px;
      margin-top: 40px;
      .scan-qrcode-img {
        width: 276px;
        height: 276px;
        border-radius: 8px;
        background: #efefef;
      }
    }
    .scan-qrcode-desc {
      font-size: 28px;
      margin-top: 30px;
    }
    .scan-pointer {
      position: absolute;
      width: 190px;
      height: 304px;
      bottom: -52px;
      right: -12px;
      animation: fingerHandle 2s ease infinite both;
    }
    .subjectLevel {
      font-size: 28px;
      margin-top: 80px;
      color: #999999;
      padding-bottom: 64px;
    }
    @keyframes fingerHandle {
      0% {
        transform: none;
      }
      70% {
        transform: translate3d(-50px, -50px, 0);
      }
      100% {
        transform: none;
      }
    }
  }
}
