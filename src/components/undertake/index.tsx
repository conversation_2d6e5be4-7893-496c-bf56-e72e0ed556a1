import { Image, Text, View } from '@tarojs/components';
import { useEffect, useState } from 'react';
import { useLogin } from '@/hooks/loginhook/useLogin';
import icon from '@/assets/undertake/icon.png';
// import addbtnimg from '@/assets/undertake/addbtn.png';
import followBanner from '@/assets/follow/follow-banner.png';
import pointer from '@/assets/follow/pointer.png';
import {
  getTeacherInfoByOrderIdsAndSubject,
  getUserFollowStatusApi,
  getWechatTeacher,
  getWeixinParamQrcodeImageUrlApi,
  getWriteTeacherInfo,
} from '@/api/groupbuy';
import { useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import { useRouter } from '@tarojs/taro';
import sensors from '@/utils/sensors_data';
import typeKeyof from '@/utils/typeKeyof';
import './index.scss';

/**
 * 【typeKeyof】 type description
 * */

export default function Undertask(props) {
  const { isWriteFollow = false } = props;
  // 二维码
  const [qrcodeNo, setQrcodeNo] = useState('');
  const [qrcodeMusicNo, setQrcodeMusicNo] = useState('');

  const [linkTypeTitle, setLinkTypeTitle] = useState('');
  const [cueWords, setCueWords] = useState('');

  const router = useRouter();
  let params: any = router.params;
  /*
   * 客户端学习频道跳转小程序，添加老师微信
   */
  let fromAppAddTeacher = {
    source: '',
    type: '',
    planCourseId: '',
  };

  if (params.source && params.source === 'appWechat') {
    fromAppAddTeacher = {
      source: params.source || '',
      type: params.type || '',
      planCourseId: params.planCourseId || '',
    };
  }

  const isWrite =
    (params.subject && params.subject === 'WRITE_APP') ||
    (params.type && params.type == '105');

  const { iLoading } = useLogin({
    subject: isWrite ? 'WRITE_APP' : 'ART_APP',
    isIntroduce: false,
    isFollow: false,
  });

  const isCrafts = params.type && params.type == '601';

  const isLinkType: boolean =
    params.type && ['1', '3', '107', '108'].includes(params.type);

  //redux
  const orderId = useSelector((state: UserStateType) => state.orderId);
  let orderid = params.outTradeNo || params.orderId || orderId;
  if (isWriteFollow) orderid = params.orderIds;
  //uid
  const userId =
    useSelector((state: UserStateType) => state.userid) || params.uid || '';
  const wxOpenId = useSelector((state: UserStateType) => state.openid) || '';
  const sensorsSta =
    useSelector((state: UserStateType) => state.sensorsSta) || '';
  const orderIds = params.orderIds || ''; // orderIds 逗号隔开 美术orderId,音乐orderId

  // 是否有音乐体验课订单
  const musicOrder = orderIds ? orderIds.split(',')[1] : '';

  const hasMusicOrder = !!musicOrder && !isWrite;

  useEffect(() => {
    setCueWords(
      isWrite ? '长按二维码添加老师' : '长按识别二维码 公众号内添加老师',
    );

    if (!isWrite && params['type'] == '2')
      setCueWords('长按识别二维码关注公众号');

    if (isCrafts) {
      setCueWords('长按识别关注公众号');
    }
    if (!!fromAppAddTeacher.source) {
      setCueWords('长按识别二维码，添加老师微信');
    }
    if (params.type && params.type == '3') {
      setLinkTypeTitle('查看物流信息，立领200币');
      setCueWords('长按识别二维码关注公众号');
    }
    if (params.type && params.type == '107') {
      setLinkTypeTitle('请完善学习信息');
      setCueWords('长按识别 公众号内完善信息');
    }
    if (params.type && params.type == '108') {
      setLinkTypeTitle('您的邮寄地址待填写');
      setCueWords('长按识别 公众号内完善信息');
    }
    if (params.type && params.type == '1') {
      setLinkTypeTitle('已成功匹配您的专属老师');
      setCueWords('长按识别 公众号内添加老师');
    }
  }, []);

  const qrTitle = () => {
    if (isWrite) {
      return '书法';
    }
    if (isCrafts) {
      return '手工';
    }
    return '美术';
  };

  const tipsSub = () => {
    if (!isWrite && params['type'] == '2') {
      return '享受更多服务';
    } else if (isCrafts) {
      return '了解更多趣味手工艺创作';
    } else if (isLinkType) {
      return '';
    } else {
      return '享受专属服务';
    }
  };

  // 获取美术公众号 5 已添加老师未关注公众号 1 未添加老师未关注公众号
  const getWeixinParamQrcodeImageUrl = (type = 1) => {
    getWeixinParamQrcodeImageUrlApi({
      uid: userId,
      type: params.type ? +params.type : type,
      appsubject: 'ART_APP',
      channel: params.channel || '',
      outTradeNo: params.packagesId || '',
      orderIds: orderid || '',
      source: params.source || '',
    }).then(res => {
      if (res.code === 0) {
        setQrcodeNo(res.payload);
      }
    });
  };

  const getWeixinParamQrcodeImageUrlForCrafts = () => {
    // getWeixinParamQrcodeImageUrlApi({
    //   uid: params.uid || '',
    //   type: params.type ? +params.type : 1,
    //   appsubject: 'HANDWORK_APP',
    //   channel: params.channel || '',
    //   source: params.source || '',
    // }).then(res => {
    //   if (res.code === 0) {
    //     setQrcodeNo(res.payload);
    //   }
    // });
    setQrcodeNo('https://s1.xiaoxiongmeishu.com/app/handwork-app-qrcode.jpg');
  };

  const getArtTeacher = () => {
    orderid &&
      getWriteTeacherInfo({
        orderNo: orderid,
        subject: 'ART_APP',
      }).then(res => {
        if (res.code === 0) {
          setQrcodeNo(res.payload.teacherWeChatQrCode);
        }
      });
  };

  const getWriteTeacher = () => {
    const subject = isWrite ? 'WRITE_APP' : params.subject || 'WRITE_APP';
    orderid &&
      getWriteTeacherInfo({
        orderNo: orderid,
        subject,
      }).then(res => {
        if (res.code === 0) {
          setQrcodeNo(res.payload.teacherWeChatQrCode);
        }
      });
  };

  const getMusicQr = () => {
    hasMusicOrder &&
      getTeacherInfoByOrderIdsAndSubject({
        orderNo: musicOrder,
        subject: 'MUSIC_APP',
      }).then(result => {
        if (result.code === 0) {
          setQrcodeMusicNo(result.payload.teacherWeChatQrCode);
        }
      });
  };

  const getQrFromApp = () => {
    !!fromAppAddTeacher.source &&
      getWechatTeacher({
        planCourseId: fromAppAddTeacher.planCourseId,
        type: fromAppAddTeacher.type,
      }).then(res => {
        setQrcodeNo(res.payload);
      });
  };

  // 获取用户添加老师和公众号情况 小熊用户 未添加公众号二维码显示公众号二维码
  const getQrByUserWxStatus = () => {
    if (orderid) {
      //   getUserFollowStatusApi({
      //     appsubject: 'ART_APP',
      //     orderNo: orderid,
      //     userId: userId,
      //   }).then(res => {
      //     if (res.code == 0) {
      //       // 已关注公众号或者未添加老师
      //       if (res.payload.followWeixin) {
      setCueWords('长按识别二维码添加老师微信');
      getArtTeacher();
      //       } else {
      //         getWeixinParamQrcodeImageUrl(res.payload.addTeacher ? 5 : 1);
      //       }
      //     }
      //   });
    }
  };

  // 长按关注公众号二维码
  const onLongPresshandle = () => {
    if (wxOpenId && isLinkType && sensorsSta) {
      sensors.track('Followpublicaccountspage_longpresscode', {
        userId,
      });
    }
  };

  useEffect(() => {
    // console.log(iLoading);
    if (wxOpenId && isLinkType && sensorsSta) {
      sensors.track('Followpublicaccountspage_veiw', {
        userId,
        scene: typeKeyof[params.type],
        channel_id: params.channel,
      });
    }
  }, [wxOpenId, sensorsSta]);

  useEffect(() => {
    console.log('params', params);
    if (isWrite || isWriteFollow) {
      getWriteTeacher();
    } else {
      if (isCrafts) {
        getWeixinParamQrcodeImageUrlForCrafts();
      } else {
        // getWeixinParamQrcodeImageUrl();
        const tempType: boolean =
          params.type && ['3', '107', '108'].includes(params.type);
        tempType ? getWeixinParamQrcodeImageUrl() : getQrByUserWxStatus();
      }
      getMusicQr();
    }
    getQrFromApp();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params]);

  return (
    <>
      {(isWriteFollow && (
        <View className='writeFollow'>
          <View className='topBanner'>
            <Image src={followBanner} mode='widthFix' />
          </View>
          <View className='scan-body'>
            <View className='scan-title'>
              {!!params.levelName
                ? '因年级变更，已为您分配新老师'
                : '添加老师微信咨询'}
            </View>
            {!!params.levelName && (
              <View className='scan-derc'>
                为了不影响宝贝的学习，请马上添加哦！
              </View>
            )}
            <View className='scan-qrcode'>
              <Image
                className='scan-qrcode-img'
                src={qrcodeNo}
                showMenuByLongpress
              />
            </View>
            <View className='scan-qrcode-desc'>长按识别二维码咨询老师</View>
            <Image className='scan-pointer' src={pointer} />
            {!!params.levelName && (
              <View className='subjectLevel'>
                {decodeURI(params.levelName)}
              </View>
            )}
          </View>
        </View>
      )) || (
        <View className='box'>
          {/* <View className='topBanner'>
           <Image src={topBanner} mode='widthFix' />
           </View> */}
          <View className='section'>
            <View className='qrcodeWrap'>
              {hasMusicOrder && (
                <View className='qrcodeTitle'>
                  添加
                  <Text className='qrcodeText'>小熊{qrTitle()}</Text>
                  班主任，激活服务
                </View>
              )}
              {isLinkType && (
                <View className='qrcodeTitle linkTypeTitle'>
                  {linkTypeTitle}
                </View>
              )}
              <View className='qrcode'>
                <Image
                  src={qrcodeNo}
                  mode='widthFix'
                  showMenuByLongpress
                  onLongPress={() => onLongPresshandle()}
                  // onLongPress={() => {
                  //   if (
                  //     cueWords != '长按识别二维码关注公众号' &&
                  //     !hasMusicOrder &&
                  //     !isCrafts
                  //   )
                  //     setShowJumpBtn(true);
                  // }}
                />
              </View>
              {hasMusicOrder ? (
                <View className='tipsWord'>
                  长按二维码识别关注，公众号内添加老师微信
                </View>
              ) : (
                <View
                  className={`tips ${isLinkType ? 'linkType' : ''} ${
                    isWrite ? 'tips-center' : ''
                  }`}
                >
                  {!isLinkType && (
                    <View className='tipsIcon'>
                      <Image src={icon} mode='widthFix' />
                    </View>
                  )}
                  <View className='tipsText'>
                    <View
                      className='tipsTit'
                      style={{ textAlign: isLinkType ? 'center' : 'left' }}
                    >
                      {cueWords}
                    </View>
                    <View className='tipsSub'>{tipsSub()}</View>
                  </View>
                </View>
              )}
            </View>
            {hasMusicOrder ? (
              <View className='qrcodeWrap'>
                <View className='qrcodeTitle'>
                  添加<Text className='qrcodeText'>小熊音乐</Text>
                  班主任，激活服务
                </View>
                <View className='qrcode'>
                  <Image
                    src={qrcodeMusicNo}
                    mode='widthFix'
                    showMenuByLongpress
                  />
                </View>
                <View className='tipsWord'>长按识别二维码添加老师微信</View>
              </View>
            ) : null}
            {/* {showJumpBtn && (
             <View className='btn' onClick={handleShow}>
             <Image src={addbtnimg} mode='widthFix' />
             </View>
             )} */}
          </View>
        </View>
      )}
    </>
  );
}
