import Taro, { useRouter } from '@tarojs/taro';
import { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { View, Text, Image, Button } from '@tarojs/components';
import { dateFormat } from '@/utils/index';
import { UserStateType } from '@/store/groupbuy/state';
import sensors from '@/utils/sensors_data';
import musicGiftImg from '@/assets/groupbuy/thirtySix/music-gift.png';
import selected from '@/assets/groupbuy/thirtySix/selected.png';
import unselect from '@/assets/groupbuy/thirtySix/unselect.png';

import {
  getProgramUserSubject,
  getPackagesCreate,
  getWeixinProgramPay,
  postReportReady,
  getSupManagements,
  queryOrderByUserId,
  packagesCrossCreate,
  wxCombine,
  orderExtendReport,
} from '@/api/groupbuy';
// 倒计时
import CountDown from '../groupbuy/countdown';
import './index.scss';

export default function Index(props) {
  const {
    watchCloseOrder,
    payPageData,
    orderType,
    giveaway,
    pType,
    packagesId,
    topicId,
    pName,
    classNum,
    subject,
    isIntroduce = true,
    unionClass,
  } = props;

  const router = useRouter();
  const teacherId = router.params.tid || router.params.teacherId || '';
  const platform = router.params.from == '1' ? 2 : 0;
  // 定义倒计时初始值
  const [countDownNum, setCountDown] = useState(0);
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  //openid
  const openId = useSelector((state: UserStateType) => state.openid);
  //channelId
  let channelId = useSelector((state: UserStateType) => state.channelId);

  //clickId
  const clickId = useSelector((state: UserStateType) => state.clickId);
  //wxAccountNu
  const wxAccountNu = useSelector((state: UserStateType) => state.wxAccountNu);
  //routePath
  const routePath = useSelector((state: UserStateType) => state.routePath);
  //adPlatform
  const adPlatform = useSelector((state: UserStateType) => state.adPlatform);
  //sendId
  const sendId = useSelector((state: UserStateType) => state.sendId);
  //sendId
  const spreadId = useSelector((state: UserStateType) => state.spreadId);
  //posterId
  const posterId =
    useSelector((state: UserStateType) => state.posterId) ||
    router.params.poster_id ||
    '';
  const userRole = useSelector((state: UserStateType) => state.userRole);
  //支付flag
  const [payFlag, setPayFlag] = useState<boolean>(true);
  const [courseday, setCourseday] = useState<string>();
  const [period, setPeriod] = useState<any>(0);
  // const [musicPeriod, setMusicPeriod] = useState<number>(0);

  const [selectedMusic, setSelectedMusic] = useState<boolean>(false);
  const [canBuyArt, setCanBuyArt] = useState<boolean>(true);
  const [canBuyMusic, setCanBuyMusic] = useState<boolean>(true);

  const dispatch = useDispatch();
  // 跳转成功页
  const goAddAddress = () => {
    if (selectedMusic) {
      // form=1代表是从艺术宝app跳转过来到购买页的
      Taro.navigateTo({
        url: `/pages/groupbuy/addAddress/index?type=106${
          router.params.from == '1' ? '&from=1' : ''
        }`,
      });
    } else {
      const path = router.path;
      // soul区分是否是拼团
      let soul = '';
      if (path.includes('/pages/normalGroup/art/index')) {
        soul = `&soul=artindex`;
      }
      // form=1代表是从艺术宝app跳转过来到购买页的
      Taro.navigateTo({
        url: `/pages/groupbuy/addAddress/index${
          isIntroduce ? '?type=new' : ''
        }${soul}${router.params.from == '1' ? '&from=1' : ''}`,
      });
    }
  };
  function pay(uid = userId) {
    /** 神策埋点
     * 用户点击支付时触发 **/
    if (isIntroduce) {
      sensors.track('xxys_experienceCoursePage_pay_click', {
        channel_id: channelId,
        user_role: userRole,
        user_id: uid,
        poster_id: posterId,
        sendId: sendId || '',
      });
    }
    /** 神策埋点 **/
    setPayFlag(false);
    // setCanBuyArt(true)
    if (selectedMusic && canBuyArt && canBuyMusic) {
      const packagesSplitList: any[] = [
        {
          packagesId: packagesId,
          stage: period,
          sup: payPageData.sup,
          topicId,
          channel: channelId,
        },
        {
          packagesId: 522,
          stage: 0,
          sup: 'S3',
          topicId: 4,
          channel: '8119',
        },
      ];
      packagesCrossCreate({
        type: 'ALONE',
        userId: uid,
        addressId: 0,
        sendId,
        spreadId,
        posterId,
        packagesSplitList,
      }).then(res => {
        if (res.status === 'EXCEPTION') {
          setPayFlag(true);
          if (res.code == 80000053) {
            Taro.showToast({
              title: '您已购买体验课，不支持再次购买',
              icon: 'none',
              duration: 2000,
            });
          } else {
            Taro.showToast({
              title: res.errors || '下单失败！',
              icon: 'none',
              duration: 2000,
            });
          }
          return false;
        }
        if (res.code === 0) {
          const { orderSimpleList } = res.payload;
          const orderIdList = [];
          orderSimpleList.forEach((v: never) => {
            orderIdList.push(v['outTradeNo']);
            if (v['subject'] === 'ART_APP') {
              dispatch({
                type: 'CHANGE_ORDERID',
                orderId: v['orderId'],
              });
            }
          });
          setTimeout(() => {
            Taro.setStorageSync('outTradeNo', orderIdList.join(','));
          }, 200);
          wxCombine({
            userId: uid,
            openId,
            payType: 'AI_WXPROGRAM',
            mergeOutTradeNo: res.payload['unionOutTradeNo'],
            notifyUrl: '',
          })
            .then(result => {
              const {
                timeStamp,
                nonceStr,
                package: packageId,
                paySign,
              } = result.payload;
              Taro.requestPayment({
                timeStamp,
                nonceStr,
                package: packageId,
                signType: 'RSA',
                paySign,
                success: function() {
                  setPayFlag(true);
                  // 29拼单控制分享页面状态 =>重新下单 转态重置
                  Taro.removeStorageSync('spellactive');
                  goAddAddress();
                },
                fail: function() {
                  setPayFlag(true);
                  Taro.showToast({
                    title: '下单失败！',
                    icon: 'none',
                    duration: 2000,
                  });
                },
              });
            })
            .catch(() => {
              setPayFlag(true);
            });
        }
      });
    } else {
      getPackagesCreate({
        type: 'ALONE',
        userId: uid,
        packagesId,
        stage: period,
        sup: payPageData.sup,
        channel: channelId,
        sendId,
        spreadId,
        topicId,
        posterId,
        teacherId,
        platform,
      }).then(res => {
        if (res.status === 'EXCEPTION') {
          setPayFlag(true);
          if (res.code == 80000053) {
            Taro.showToast({
              title: '您已购买体验课，不支持再次购买',
              icon: 'none',
              duration: 2000,
            });
          } else {
            Taro.showToast({
              title: res.errors || '下单失败！',
              icon: 'none',
              duration: 2000,
            });
          }
          return false;
        }
        if (res.code === 0) {
          clickId &&
            postReportReady({
              orderId: res.payload.order.id,
              platform: adPlatform ? adPlatform.toLocaleUpperCase() : 'WX',
              type: 'RESERVATION',
              clickId: clickId,
              url: routePath,
              params: '',
              wxAccountNu: wxAccountNu,
            });
          // 订单拓展信息上报 转介绍762 按需增加上报
          if (router.params.pzd) {
            orderExtendReport({
              uid,
              oids: res.payload.order.id,
              tabType: 'SEND',
              tabValue: sendId,
              tabJson: JSON.stringify({ prezzieId: router.params.pzd }),
              posterId: posterId,
              come: '',
            });
          }

          dispatch({
            type: 'CHANGE_ORDERID',
            orderId: res.payload.order.id,
          });

          // 不同的产品payType不同
          let _url = getWeixinProgramPay,
            _data = {
              openId,
              orderId: res.payload.order.id,
              userId: uid,
              payType: 'AI_WXPROGRAM',
              notifyUrl: '',
            };
          if (pType === 'calligraphy') {
            _data['payType'] = 'WRITE_WXPROGRAM';
          }
          _url(_data)
            .then(data => {
              if (data.code === 0) {
                const {
                  timeStamp,
                  nonceStr,
                  package: packageId,
                  paySign,
                } = data.payload;
                // 不同的支付接口的返回不同
                Taro.requestPayment({
                  timeStamp,
                  nonceStr,
                  package: packageId,
                  signType: 'HMAC-SHA256',
                  paySign,
                  success: function(payRes) {
                    setPayFlag(true);
                    console.log(payRes);
                    // 29拼单控制分享页面状态 =>重新下单 转态重置
                    Taro.removeStorageSync('spellactive');
                    goAddAddress();
                  },
                  fail: function(failRes) {
                    setPayFlag(true);
                    console.log(failRes);
                    // `${failRes.errMsg}` ||
                    Taro.showToast({
                      title: '下单失败！',
                      icon: 'none',
                      duration: 2000,
                    });
                  },
                });
              }
            })
            .catch(err => {
              setPayFlag(true);
              console.log(err);
            });
        }
      });
    }
  }
  // 校验是否能买音乐课
  const checkBuyMusic = () => {
    Taro.showLoading();
    queryOrderByUserId({
      userId: userId,
      channels: channelId,
      subjects: 'ART_APP,MUSIC_APP',
      packageId: 522,
    })
      .then(res => {
        const {
          channelCheck,
          systemCheckMap,
          pluralCheckMap,
          subjectOrderMap,
          experienceCheckMap,
        } = res.payload;
        // experienceCheckMap.ART_APP为true 表示之前已经买过体验课了
        if (experienceCheckMap.ART_APP) {
          if (
            subjectOrderMap &&
            subjectOrderMap.ART_APP &&
            subjectOrderMap.ART_APP.EXPERIENCE &&
            subjectOrderMap.ART_APP.EXPERIENCE.length > 0
          ) {
            const experience: never[] =
              subjectOrderMap['ART_APP']['EXPERIENCE'];
            let supList = [];
            for (let i = 0; i < experience.length; i++) {
              supList.push(experience[i]['sup']);
            }
            setTimeout(() => {
              if (supList.some(i => i == payPageData.sup)) {
                setCanBuyArt(false);
              } else {
                setCanBuyArt(true);
              }
            }, 200);
          }
          if (
            channelCheck &&
            !systemCheckMap.ART_APP &&
            pluralCheckMap.ART_APP
          ) {
            // 可以重复购买
            setCanBuyArt(true);
          } else {
            // 不可以重复购买
            setCanBuyArt(false);
          }
        } else {
          // experienceCheckMap.ART_APP为false 表示之前没有买过体验课了，是首单
          setCanBuyArt(true);
        }
        if (experienceCheckMap.MUSIC_APP) {
          if (
            subjectOrderMap &&
            subjectOrderMap.MUSIC_APP &&
            subjectOrderMap.MUSIC_APP.EXPERIENCE &&
            subjectOrderMap.MUSIC_APP.EXPERIENCE.length > 0
          ) {
            const experience: never[] =
              subjectOrderMap['MUSIC_APP']['EXPERIENCE'];
            let supList = [];
            for (let i = 0; i < experience.length; i++) {
              supList.push(experience[i]['sup']);
            }
            if (supList.some(i => i == 'S3')) {
              setCanBuyMusic(false);
              setSelectedMusic(false);
            } else {
              setCanBuyMusic(true);
              // setSelectedMusic(true);
            }
          }
          if (
            channelCheck &&
            !systemCheckMap.MUSIC_APP &&
            pluralCheckMap.MUSIC_APP
          ) {
            // 可以重复购买
            setCanBuyMusic(true);
            // setSelectedMusic(true);
          } else {
            // 不可以重复购买
            setCanBuyMusic(false);
            setSelectedMusic(false);
          }
        } else {
          // experienceCheckMap.ART_APP为false 表示之前没有买过体验课了，是首单
          setCanBuyMusic(true);
          // setSelectedMusic(true);
        }
        Taro.hideLoading();
      })
      .catch(() => {
        Taro.hideLoading();
      });
  };
  // 获取用户手机号
  const getPhoneNumber = res => {
    if (res.detail.errMsg === 'getPhoneNumber:fail user deny') {
      console.log(res.detail.errMsg);
    } else {
      const { encryptedData, iv } = res.detail;
      // getProgramUserSubject兼容getProgramUser
      getProgramUserSubject({
        openId,
        encryptedData,
        iv,
        subject,
      }).then(phone => {
        if (phone.payload.uid) {
          dispatch({
            type: 'CHANGE_USERID',
            userid: phone.payload.uid,
          });
        }
        if (phone.payload.token)
          Taro.setStorageSync('appToken', phone.payload.token);
        phone.payload.mobile &&
          dispatch({
            type: 'CHANGE_MOBILE',
            mobile: phone.payload.mobile,
          });

        payFlag && pay(phone.payload.uid);
      });
    }
  };
  const payConfirm = () => {
    payFlag && pay();
  };

  // 增加按钮文字区分逻辑
  const btnName = () => {
    if (isIntroduce && pType === 'art') {
      if (packagesId == 771 || packagesId == 770 || packagesId == 7613) {
        return <Text className='span'>邀友直降7元</Text>;
      }
      return <Text className='span'>学课返7元</Text>;
    }
    return null;
  };
  // 增加价格tips区分
  const priceWord = () => {
    if (isIntroduce && pType === 'art') {
      if (packagesId == 771 || packagesId == 770 || packagesId == 7613) {
        return (
          <Text className='feedback'>
            邀友<Text className='back'>直降7元</Text>
          </Text>
        );
      }
      return (
        <Text className='feedback'>
          学课<Text className='back'>返7元</Text>
        </Text>
      );
    }
    return null;
  };
  // 增加随材文案区分
  const descWord = () => {
    const path = router.path;
    if (path.includes('/pages/normalGroup/art/index')) {
      return (
        <View className='desc-art-index'>
          新人专享36元10节课，
          <Text className='span'>邀友直降7元</Text>
        </View>
      );
    }
    return `新人专享${orderType}元${classNum}节课`;
  };

  useEffect(() => {
    if (countDownNum === 0 && props.isShowOrder) {
      setCountDown(600000);
    }
  }, [props.isShowOrder, countDownNum]);
  useEffect(() => {
    payPageData &&
      getSupManagements({
        type: 'TESTCOURSE',
        sup: payPageData.sup,
        subject,
      }).then(res => {
        const result = res.payload && res.payload;
        if (result) {
          const openCourseDate = dateFormat(
            result.courseDay * 1,
            'MM' + '月' + 'dd' + '日',
          );
          setCourseday(openCourseDate);
          setPeriod(result.period);
        }
      });
    // getSupManagements({
    //   type: '',
    //   sup: 's1',
    //   subject: 'MUSIC_APP',
    // }).then(res => {
    //   const result = res.payload && res.payload;
    //   if (result) {
    //     setMusicPeriod(result.period);
    //   }
    // });
  }, [payPageData, subject]);

  useEffect(() => {
    userId && checkBuyMusic();
  }, [userId]);

  return (
    <View className='order'>
      <View className='title'>
        <View className='time'>
          <CountDown payCountDown={countDownNum} />
          <View className='text'>剩余支付时间</View>
        </View>
        <View className='close' onClick={() => watchCloseOrder(false)}></View>
      </View>
      {unionClass ? (
        <View className='union-sub-title'>
          <View className='left'>
            <View className='level'>
              小熊{pName}体验课-{payPageData.label}
            </View>
            {payPageData.courseday && (
              <View className='start-time'>{courseday}开课</View>
            )}
          </View>
          <View className='right'>¥{orderType}</View>
        </View>
      ) : (
        <View className='subTitle'>
          <View className='level'>
            小熊{pName}体验课-{payPageData.label}
          </View>
          {payPageData.courseday && (
            <View className='start-time'>{courseday}开课</View>
          )}
        </View>
      )}
      {!unionClass ? (
        <View className='packages'>
          <View className='row'>
            <View className='label'>【优惠】</View>
            <View className='desc'>
              {/* 新人专享{orderType}元{classNum}节课 */}
              {descWord()}
            </View>
          </View>
          <View className='row'>
            <View className='label'>【赠品】</View>
            <View className='desc'>
              配套随材礼包
              <Text className='label'>（收货信息将在付款后填写）</Text>
            </View>
          </View>
          <View className='row'>
            <View className='label'>【提醒】</View>
            <View className='desc'>
              随材礼盒为课程配套物品，不同级别的礼盒略有差异
            </View>
          </View>
        </View>
      ) : null}
      <View className='gift-box-row'>
        <View className={pType + ' gift-box'}>
          <Image src={giveaway.img} mode='widthFix' className='img' />
        </View>
        <View className={pType + ' label'}>
          {giveaway.detail.map((item, index) => {
            return (
              <View className={pType + ' label-item'} key={`label-${index}`}>
                {item}
              </View>
            );
          })}
        </View>
      </View>
      {unionClass ? (
        <View
          className='music-box'
          onClick={() => {
            if (canBuyMusic) {
              if (selectedMusic) {
                sensors.track('xxys_experienceCoursePage_deselectCourse_click');
              }
              setSelectedMusic(!selectedMusic);
            } else {
              Taro.showToast({
                title: '您已报名音乐',
                icon: 'none',
                duration: 2000,
              });
            }
          }}
        >
          <View className='music-sub-title'>
            <View className='left'>
              <View className='level'>小熊音乐体验课</View>
              <View className='desc'>限时特惠4节课</View>
            </View>
            <View className='right'>
              <View>¥0.1</View>
              {selectedMusic ? (
                <Image src={selected} mode='widthFix' className='img' />
              ) : (
                <Image src={unselect} mode='widthFix' className='img' />
              )}
            </View>
          </View>
          <Image src={musicGiftImg} mode='widthFix' className='gift-img' />
        </View>
      ) : null}
      <View className='pay'>
        <View className='price-box'>
          <View className='symbol'>¥</View>
          <View className='price'>
            {selectedMusic ? +orderType + 0.1 : orderType}
            {priceWord()}
          </View>
        </View>
        {userId ? (
          <Button className='button' onClick={payConfirm}>
            确认支付
            {btnName()}
          </Button>
        ) : (
          <Button
            className='button'
            open-type='getPhoneNumber'
            onGetPhoneNumber={getPhoneNumber}
          >
            确认支付
            {btnName()}
          </Button>
        )}
      </View>
    </View>
  );
}
