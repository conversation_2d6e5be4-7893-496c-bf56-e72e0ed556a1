@import '../../theme/groupbuy/common.scss';
.order {
  min-height: 850rpx;
  padding: 48rpx 14rpx 0 14rpx;
  box-sizing: border-box;
  .title {
    display: flex;
    justify-content: center;
    margin-bottom: 48rpx;
    padding: 0 120rpx;
    position: relative;
    .close {
      width: 100rpx;
      height: 100rpx;
      position: absolute;
      top: -32rpx;
      right: -32rpx;
      &::before,
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        display: inline-block;
        width: 36rpx;
        height: 2px;
        border-radius: 1px;
        background: #ccc;
      }
      &::before {
        transform: translate3d(-50%, -50%, 0) rotate(45deg);
      }
      &::after {
        transform: translate3d(-50%, -50%, 0) rotate(-45deg);
      }
    }
    .quota,
    .time {
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      .highlight {
        color: $red;
        font-size: $font-44;
        font-weight: 600;
      }
      .text {
        color: $silver;
        font-size: $font-28;
      }
    }
  }
  .subTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 18rpx;
    border-bottom: 1rpx solid #e6e6e6;
    margin-bottom: 24rpx;
    .level {
      color: $dim-gray;
      font-size: $font-32;
    }
    .start-time {
      color: $grey;
      font-size: $font-24;
    }
  }
  .union-sub-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;
    .left {
      .level {
        color: $dim-gray;
        font-size: $font-32;
      }
      .start-time {
        color: $grey;
        font-size: $font-24;
        padding: 0 14rpx;
        margin-top: 10rpx;
      }
    }
    .right {
      height: 56px;
      font-size: 40px;
      font-weight: bold;
      color: #ff4100;
      line-height: 56px;
    }
  }
  .packages {
    margin: 24rpx 0 32rpx 0;
    display: flex;
    flex-direction: column;
    .row {
      width: 100%;
      display: flex;
      align-items: center;
      margin-bottom: 12rpx;
      .label {
        align-self: flex-start;
        color: $orange;
        font-size: $font-24;
      }
      .desc {
        flex: 1;
        color: $silver;
        font-size: $font-24;
        .text {
          margin-bottom: 12rpx;
        }
      }
      .desc-art-index {
        .span {
          color: #ff9c00;
        }
      }
    }
  }
  .gift-box-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 20rpx;
    border: 3rpx dashed $light-grey;
    padding: 20rpx 0 20rpx 15rpx;
    margin-bottom: 40rpx;
    .gift-box {
      width: 202rpx;
      height: 137rpx;
      box-sizing: border-box;
      &.art {
        margin-left: 22rpx;
      }
      .img {
        max-width: 100%;
        max-height: 100%;
      }
    }
    .label {
      width: 420rpx;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      &.calligraphy {
        width: 465rpx;
      }
      .label-item {
        width: 48%;
        font-size: $font-26;
        color: $light-grey;
        position: relative;
        overflow: hidden;
        &.calligraphy {
          width: 54%;
        }
        &:nth-child(2n-1).calligraphy {
          width: 45%;
        }
        &::before {
          display: inline-block;
          content: '·';
          font-size: $font-38;
          vertical-align: middle;
          margin-right: 8rpx;
        }
      }
    }
  }
  .music-box {
    background: #fff3db;
    border-radius: 21rpx;
    padding: 22rpx;
    box-sizing: border-box;
    margin-bottom: 24rpx;
    .music-sub-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24rpx;
      .left {
        .level {
          color: $dim-gray;
          font-size: $font-32;
        }
        .desc {
          color: $grey;
          font-size: $font-24;
          padding: 0 14rpx;
          margin-top: 10rpx;
        }
      }
      .right {
        height: 56rpx;
        font-size: 40rpx;
        color: #ff4100;
        line-height: 56rpx;
        display: flex;
        align-items: center;
        .img {
          width: 42rpx;
          height: 42rpx;
          margin-left: 10rpx;
        }
      }
    }
    .gift-img {
      width: 100%;
      height: 180px;
    }
  }
  .pay {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 28rpx;
    .price-box {
      display: flex;
      align-items: baseline;
      color: #ff7000;
      font-weight: bold;
      .symbol {
        font-size: $font-40;
        margin-right: 15rpx;
      }
      .price {
        font-size: $font-68;
        .feedback {
          color: $silver;
          font-size: $font-24;
          font-weight: normal;
          margin-left: 4rpx;
          .back {
            color: #ff7000;
          }
        }
      }
      .music-price {
        height: 32rpx;
        font-size: 24rpx;
        font-weight: normal;
        color: #727272;
        line-height: 32rpx;
        margin-left: 14rpx;
        margin-bottom: 6rpx;
        text {
          color: #ff7103;
        }
      }
    }
    .button {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 216px;
      height: 80rpx;
      border-radius: 40rpx;
      background: $orange-to-yellow;
      color: $white;
      font-size: $font-32;
      font-weight: bold;
      margin-right: 0;
      position: relative;
      overflow: visible;
      &::after {
        border: none;
      }
      .span {
        position: absolute;
        font-size: 22px;
        height: 40px;
        padding: 0 10px;
        text-align: center;
        line-height: 40px;
        border-radius: 40px 40px 40px 0;
        color: #a44e0b;
        background: linear-gradient(to right, #fff5db, #ffe798);
        top: -25px;
        right: 0;
      }
    }
  }
}
