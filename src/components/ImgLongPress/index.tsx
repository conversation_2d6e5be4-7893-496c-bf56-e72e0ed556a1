import { Image } from '@tarojs/components';
import { useEffect } from 'react';

interface ImgLongPressProps {
  currentUrl: string;
  onLongPress: Function;
  showMenuByLongpress: Boolean;
  styleName?: string;
}

export default function ImgLongPress(props: ImgLongPressProps) {
  const {
    currentUrl,
    onLongPress,
    showMenuByLongpress = false,
    styleName = 'a',
  } = props;
  let onLongPresstimeOutEvent: any = null;
  useEffect(() => {
    return clearTimeout(onLongPresstimeOutEvent);
  });
  //长按事件（起始）
  const gtouchstart = () => {
    onLongPresstimeOutEvent = setTimeout(function() {
      longPress();
    }, 500); //这里设置定时器，定义长按500毫秒触发长按事件
    return false;
  };
  //手释放，如果在500毫秒内就释放，则取消长按事件，此时可以执行onclick应该执行的事件
  const showDeleteButton = () => {
    clearTimeout(onLongPresstimeOutEvent); //清除定时器
    if (onLongPresstimeOutEvent) {
      console.log('点击但未长按');
    }
    return false;
  };
  //如果手指有移动，则取消所有事件，此时说明用户只是要移动而不是长按
  const gtouchmove = () => {
    clearTimeout(onLongPresstimeOutEvent); //清除定时器
    onLongPresstimeOutEvent = null;
  };
  //真正长按后应该执行的内容
  const longPress = () => {
    //执行长按要执行的内容，如上报埋点
    onLongPress();
    onLongPresstimeOutEvent = null;
  };

  return (
    <Image
      src={currentUrl}
      mode='widthFix'
      className={styleName}
      showMenuByLongpress={showMenuByLongpress}
      onTouchStart={gtouchstart}
      onTouchMove={gtouchmove}
      onTouchEnd={showDeleteButton}
    />
  );
}
