import Taro, { useRouter } from '@tarojs/taro';
import { useDispatch, useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import sensors from '@/utils/sensors_data';
import { getPackagesCreate, postReportReady } from '@/api/groupbuy';
import { useState } from 'react';

export function useAlipay({ topicId, packagesId, model, originalCost = '', address_id= '',aliUserId,
  path,
  out_sku_id,
  out_item_id,
  activityConsultId,
  goods_id }: any) {
  const { params } = useRouter();
  const dispatch = useDispatch();
  //openid
  //channelId
  const channelId = useSelector((state: UserStateType) => state.channelId);
  //clickId
  const clickId = useSelector((state: UserStateType) => state.clickId);
  //adPlatform
  const adPlatform = useSelector((state: UserStateType) => state.adPlatform);
  //routePath
  const routePath = useSelector((state: UserStateType) => state.routePath);
  //wxAccountNu
  const wxAccountNu = useSelector((state: UserStateType) => state.wxAccountNu);
  //sendId
  const sendId = useSelector((state: UserStateType) => state.sendId);
  //sendId
  const spreadId = useSelector((state: UserStateType) => state.spreadId);
  //posterId
  const posterId = useSelector((state: UserStateType) => state.posterId);
  //支付flag
  const [payFlag, setPayFlag] = useState<boolean>(true);
  const [period, setPeriod] = useState<number>(0);
  // 挽留弹窗flag
  const [redeemFlag, setRedeemFlag] = useState(true);

  function packageCreate(uid, sup, sourceId) {
    return new Promise((resolve, reject) => {
      setPayFlag(false);
      let data: any = {
        type: !originalCost ? 'ALONE' : 'MODULE_ALI',
        userId: uid,
        packagesId,
        stage: period,
        sup: sup || 'DEFAULT',
        channel: channelId,
        sendId,
        spreadId,
        topicId,
        posterId,
        come: params.come,
        model,
        addressId: address_id
      }
      if (address_id) {
        data = {
          ...data,
          type: 'ALI_PROGRAM',
          thirdId: aliUserId,
          path,
          sourceId,
          outSkuId: out_sku_id,
          outItemId: out_item_id,
          goodsId: goods_id,
          activityConsultId
        }
      }
      getPackagesCreate(data)
        .then(res => {
          if (res.status === 'EXCEPTION') {
            if (packagesId == '617') {
              sensors.track('xxms_testcourse_home_buybuttonclick', {
                is_buy: '否',
              });
            }
            setPayFlag(true);
            if (res.code == 80000053) {
              Taro.showToast({
                title: '您已购买体验课，不支持再次购买',
                icon: 'none',
                duration: 2000,
              });
            } else {
              Taro.showToast({
                title: res.errors || '下单失败！',
                icon: 'none',
                duration: 2000,
              });
            }
            // 已购买过 不展示挽留弹窗
            setRedeemFlag(false);

            reject(res);
          }
          if (res.code === 0) {
            if (packagesId == '617') {
              sensors.track('xxms_testcourse_home_buybuttonclick', {
                is_buy: '是',
              });
            }
            clickId &&
              postReportReady({
                orderId: res.payload.order.id,
                platform: adPlatform
                  ? adPlatform.toLocaleUpperCase()
                  : 'ALIPAY',
                type: 'RESERVATION',
                clickId: clickId,
                url: routePath,
                params: '',
                wxAccountNu: wxAccountNu,
              });
            dispatch({
              type: 'CHANGE_ORDERID',
              orderId: res.payload.order.id,
            });
            resolve(res);
          }
        })
        .catch(err => {
          setPayFlag(true);
          reject(err);
        });
    });
  }

  return {
    payFlag,
    setPayFlag,
    period,
    setPeriod,
    packageCreate,
    redeemFlag,
  };
}
