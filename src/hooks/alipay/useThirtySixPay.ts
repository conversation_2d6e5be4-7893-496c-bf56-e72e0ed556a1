import { useDispatch, useSelector } from 'react-redux';
import Taro, { useRouter } from '@tarojs/taro';
import { UserStateType } from '@/store/groupbuy/state';
import sensors from '@/utils/sensors_data';
import { decryptAliUserApi, getProgramPay, dyReport } from '@/api/groupbuy';
import { useAlipay } from './useAlipay';

export function useThirtySixPay({
  activityConsultId,
  canReport = false,
  originalCost = '',
  packagesId,
  model = '',
  urlType,
  topicId,
  sup,
  goods_id = '',
  out_item_id = '',
  out_sku_id = '',
  address_id = '',
}) {
  const { params } = useRouter();
  const curPages = Taro.getCurrentPages();
  const path = `/${curPages[curPages.length - 1].$taroPath}`;

  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  const authCode = useSelector((state: UserStateType) => state.authCode);
  const aliUserId = useSelector((state: UserStateType) => state.aliUserId);
  //channelId
  let channelId =
    params.channelId || useSelector((state: UserStateType) => state.channelId);

  const { packageCreate, payFlag, setPayFlag, setPeriod, redeemFlag } =
    useAlipay({
      topicId,
      packagesId,
      model,
      originalCost,
      address_id,
      aliUserId,
      path,
      out_item_id,
      out_sku_id,
      goods_id,
      activityConsultId
    });
  const dispatch = useDispatch();
  // 跳转成功页
  const sucLink = (orderId: string, realSup: string) => {
    const tempSup = realSup == 'DEFAULT' ? '' : realSup;
    // 如果是新增模式 5
    if (Number(model) === 5) {
      Taro.redirectTo({
        url: `/pages/thirtySix/addAddress/index?vType=3&sup=${tempSup}&orderId=${orderId}&packagesId=${packagesId}&channelId=${channelId}&urlType=${urlType}`,
      });
    }else if (address_id) {
      Taro.redirectTo({
        url: `/pages/launch/follow/index?sup=${
          realSup
        }&uid=${userId}${`&channel=${channelId}`}&orderId=${orderId}`})
    } else if (packagesId == '618') {
      Taro.redirectTo({
        url: `/pages/thirtySix/addAddress/index?urlType=${urlType}`,
      });
    } else {
      let _url = `/pages/thirtySix/${
        tempSup ? 'addAddress' : 'chooseLevel'
      }/index?packagesId=${packagesId}&come=${params.come}&orderId=${orderId}`;
      if (tempSup) _url = _url + `&sup=${tempSup}&urlType=${urlType}`;
      if (originalCost) _url = _url + `&originalCost=true`;
      console.error('sucLink ---' + _url);
      Taro.navigateTo({
        url: _url,
      });
    }
  };

  function initpay(uid = userId, supLevel?) {
    if (my.canIUse('checkBeforeAddOrder')) {
      my.checkBeforeAddOrder({
        success({ requireOrder, sceneId, sourceId }) {
          console.log(
            '🚀 ~ file: index.tsx:121 ~ success ~ requireOrder, sceneId, sourceId:',
            requireOrder,
            sceneId,
            sourceId,
          );
          // 是否需要创建交易组件订单, 1 表示需要，0 表示不需要；
          // if (requireOrder === 1) {
            // success 为异步回调，请在回调里完成创建订单操作（注意执行顺序，避免在回调执行前进行订单创建），否则有可能造成结果未返回导致使用条件判断错误
            packageCreate(uid, sup || supLevel, sourceId)
              .then((res: any) => {
                if (canReport) {
                  dyReport({
                    orderId: res.payload.order.id,
                    platform: 'MSYQ',
                    clickId: aliUserId,
                    type: '',
                    subject: 'ART_APP',
                  });
                }
                if (packagesId == '617') {
                  Taro.setStorageSync(
                    'zeroDotOneOrderId',
                    res.payload.order.id,
                  );
                }
                let _data: any = {
                  type: 'ali',
                  orderId: res.payload.order.id,
                  userId: uid,
                  payType: 'AI_PRM_ALI',
                  notifyUrl: '',
                  returnType: 'GET',
                  code: authCode,
                  payAppId: '2021002179659167',
                  thirdId: aliUserId,

                };
                if (address_id) {
                  _data = {
                    ..._data,
                    activityConsultId,
                    sourceId,
                    appId: '2021002179659167',
                    payType: 'AI_PRM_ALI',
                    path,
                    outItemId: out_item_id,
                    outSkuId: out_sku_id,
                    goodsId: goods_id,
                    addressId: address_id,
                  }
                }
                if (originalCost) {
                  // @ts-ignore
                  const pages = getCurrentPages();
                  const currentPage = pages[pages.length - 1];
                  const currentPath = `/${currentPage.route}`;
                  _data.appId = '2021002179659167';
                  _data.pageLink = currentPath;
                }
                console.log(
                  '🚀 ~ file: useThirtySixPay.ts:119 ~ .then ~ _data:',
                  _data,
                );
                getProgramPay(_data)
                  .then((r) => {
                    if (r.code === 0) {
                      my.tradePay({
                        // 调用统一收单交易创建接口（alipay.trade.create），获得返回字段支付宝交易号trade_no
                        tradeNO: r.payload || '',
                        success: (resPay) => {
                          setPayFlag(true);
                          console.error('下单完成');
                          Taro.hideLoading();
                          if (resPay.resultCode === '9000') {
                            sucLink(res.payload.order.id, sup || supLevel);
                          } else {
                            Taro.showToast({
                              title: '下单失败！',
                              icon: 'none',
                              duration: 2000,
                            });
                          }
                        },
                        fail: () => {
                          showError('下单失败！');
                        },
                      });
                    } else {
                      showError(r.errors);
                    }
                  })
                  .catch(() => {
                    Taro.hideLoading();
                    setPayFlag(true);
                  });
              })
              .catch(() => {
                setTimeout(() => {
                  Taro.hideLoading();
                }, 3000);
              });
          // }
        },
        fail({ error, errorMessage }) {
          showError(errorMessage);
        },
        complete() {},
      });
    }
  }

  function pay(uid = userId, supLevel?: string) {
    initpay(uid, sup || supLevel);
  }

  const payConfirm = (supLevel = 'DEFAULT') => {
    if (payFlag) {
      Taro.showLoading();
      pay(userId, sup || supLevel);
    }
  };
  const authError = () => {};
  const authSuccess = (authData?) => {
    if (payFlag) {
      Taro.showLoading();
      my.getPhoneNumber({
        success: (res) => {
          const { sign, response } = JSON.parse(res.response);
          decryptAliUserApi({
            sign,
            response,
            subject: 'ART_APP',
            aliUserId: aliUserId,
          }).then((result) => {
            if (result.code == 0) {
              const payload = result.payload;
              payload.uid &&
                dispatch({
                  type: 'CHANGE_USERID',
                  userid: payload.uid,
                });
              payload.mobile &&
                dispatch({
                  type: 'CHANGE_MOBILE',
                  mobile: payload.mobile,
                });
              pay(payload.uid, sup || authData.sup || 'DEFAULT');

              if (result.payload.token)
                Taro.setStorageSync('appToken', result.payload.token);
              sensors.track('xxms_testcourse_loginsignupresult', {
                is_success: '是',
              });
            } else {
              sensors.track('xxms_testcourse_loginsignupresult', {
                is_success: '否',
              });
            }
          });
          if (packagesId == 617) {
            sensors.track(
              'xxms_testcourse_registrationpaylayer_Getauthorization',
              {
                is_authorization: '允许',
              },
            );
          }
        },
        fail: (res) => {
          if (packagesId == 617) {
            sensors.track(
              'xxms_testcourse_registrationpaylayer_Getauthorization',
              {
                is_authorization: '拒绝',
              },
            );
          }
          Taro.hideLoading();
          Taro.showModal({
            content: res.errorMessage,
            showCancel: false,
          });
        },
      });
    }
  };

  // 封装的显示错误信息的函数
  async function showError(message: string) {
    Taro.hideLoading();
    Taro.showToast({
      title: message,
      icon: 'none',
      duration: 2000,
    });
    setPayFlag(true);
  }

  return {
    authError,
    authSuccess,
    setPeriod,
    payConfirm,
    redeemFlag,
  };
}
