import Taro from '@tarojs/taro';
import { useEffect, useState } from 'react';
import { getOpenidApi } from '@/api/umsapi';
import { useDispatch } from 'react-redux';
import { ttGetUidByOpenid } from '@/api/groupbuy';

export function useLogin(props) {
  // const { subject, isIntroduce = true } = props;
  const dispatch = useDispatch();
  const [iLoading, setiLoading] = useState(true);
  useEffect(() => {
    Taro.login({
      success(res) {
        if (res.code) {
          Taro.showLoading({ title: '加载中...' });
          getOpenidApi(res.code, 'tt979ce12108a4d5f701')
            .then((resData: any) => {
              dispatch({
                type: 'CHANGE_OPENID',
                openid: resData.payload.openid,
              });
              dispatch({
                type: 'CHANGE_SESSIONKEY',
                sessionkey: resData.payload.session_key,
              });

              ttGetUidByOpenid({
                openid: resData.payload.openid,
                appsubject: 'ART_APP',
              })
                .then((result: any) => {
                  setiLoading(false);
                  Taro.hideLoading();
                  if (result.payload && result.payload.mobile) {
                    dispatch({
                      type: 'CHANGE_USERID',
                      userid: result.payload.uid,
                    });
                    dispatch({
                      type: 'CHANGE_MOBILE',
                      mobile: result.payload.mobile,
                    });
                  }
                })
                .catch(() => {
                  setiLoading(false);
                  Taro.hideLoading();
                });
            })
            .catch(() => {
              setiLoading(false);
              Taro.hideLoading();
            });
        }
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return { iLoading };
}
