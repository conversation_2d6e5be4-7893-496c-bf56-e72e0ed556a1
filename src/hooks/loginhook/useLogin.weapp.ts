import Taro, { useRouter } from '@tarojs/taro';
import { useDispatch } from 'react-redux';
import { useEffect, useState } from 'react';
import sensors from '@/utils/sensors_data';
import {
  getUserOpenIdSubject,
  getOrderId,
  getOrdersId,
  getWeixinByUnid,
} from '@/api/groupbuy'; // getUserOpenId,

export function useLogin(props) {
  // isIntroduce是否是转介绍页，非转介绍页调用接口一样
  const { subject, isIntroduce = true, isFollow = true } = props;
  const [iLoading, setiLoading] = useState(true);
  const router = useRouter();
  const dispatch = useDispatch();
  useEffect(() => {
    Taro.login({
      success: function(res) {
        if (res.code) {
          // getUserOpenIdSubject,兼容getUserOpenId
          getUserOpenIdSubject({
            code: res.code,
            channel: router.params.channelId || '',
            subject,
          }).then(data => {
            if (data.code === 0) {
              if (data.payload.uid) {
                dispatch({
                  type: 'CHANGE_USERID',
                  userid: data.payload.uid,
                });
              } else {
                dispatch({ type: 'CHANGE_USERID', userid: '' });
              }
              data.payload.mobile &&
                dispatch({
                  type: 'CHANGE_MOBILE',
                  mobile: data.payload.mobile,
                });
              dispatch({
                type: 'CHANGE_OPENID',
                openid: data.payload.openid,
              });
              if (data.payload.token)
                Taro.setStorageSync('appToken', data.payload.token);
              // 获取用户状态
              getWeixinByUnid({ unionid: data.payload.unionid }).then(item => {
                if (item.code === 0) {
                  dispatch({
                    type: 'CHANGE_USERROLE',
                    userRole: item.payload.user_role,
                  });
                }
              });
              // 美术写字的接口不同，其第三个参数不同
              let $url = subject === 'ART_APP' ? getOrdersId : getOrderId,
                $obj =
                  subject === 'ART_APP'
                    ? { subjects: subject }
                    : { status: 'COMPLETED' };
              data.payload.uid &&
                $url({
                  userId: data.payload.uid,
                  addressId: 0,
                  ...$obj,
                }).then(item => {
                  let _payload =
                    subject === 'ART_APP' ? item.payload[0] : item.payload;
                  if (item.code === 0 && _payload) {
                    dispatch({
                      type: 'CHANGE_ORDERID',
                      orderId: _payload.id,
                    });
                    if (!isFollow) return;
                    if (
                      subject !== 'WRITE_APP' &&
                      _payload.packagesName !== '小熊书法体验版'
                    ) {
                      // form=1代表是从艺术宝app跳转过来到购买页的
                      Taro.navigateTo({
                        url: `/pages/groupbuy/addAddress/index${
                          router.params.from == '1' ? '?from=1' : ''
                        }`,
                      });
                    } else {
                      Taro.navigateTo({
                        url: `/pages/groupbuy/addTeacher/index?uid=${data.payload.uid}&type=105&subject=WRITE_APP`,
                      });
                    }
                  }
                });
            }
          });
        } else {
          console.log('登录失败！' + res.errMsg);
        }
      },
    });
    !isIntroduce &&
      Taro.showShareMenu({
        withShareTicket: true,
      });
  }, [dispatch, isIntroduce, router, router.params, subject]);

  return { iLoading };
}
