import Taro from '@tarojs/taro';
import {
  initAliprogram,
  initAliprogramOneToOne,
  getAliProgramUserByAliUserIdApi,
} from '@/api/groupbuy';
import { useDispatch } from 'react-redux';
import { ALIUSERID, AUTHCODE } from '@/store/groupbuy/constants';
import { useState, useEffect } from 'react';
import { getOrderId } from '@/common/order';

export function useLogin(props) {
  const { subject, mobile = '', urlType } = props;
  const dispatch = useDispatch();
  const [iLoading, setiLoading] = useState(true);
  useEffect(() => {
    my.getAuthCode({
      scopes: ['auth_base'],
      success: (resCode) => {
        dispatch({
          type: AUTHCODE,
          authCode: resCode.authCode,
        });
        if (urlType === 'msbSub') {
          initAliprogramOneToOne(resCode.authCode, mobile)
            .then((resInit) => {
              console.log(
                '🚀 ~ useLogin.alipay.ts:28 ~ initAliprogramOneToOne ~ resInit:',
                resInit,
              );
              if (resInit.code == 0) {
                dispatch({
                  type: ALIUSERID,
                  aliUserId: resInit.payload.aliUserId,
                });
                if (resInit.payload.token)
                  Taro.setStorageSync('appToken', resInit.payload.token);

                const payload = resInit.payload;
                console.log(
                  '🚀 ~ useLogin.alipay.ts:41 ~ useEffect ~ payload:',
                  payload,
                );
                payload.uid &&
                  dispatch({
                    type: 'CHANGE_USERID',
                    userid: payload.uid,
                  });
                payload.mobile &&
                  dispatch({
                    type: 'CHANGE_MOBILE',
                    mobile: payload.mobile,
                  });
              } else {
                Taro.showToast({
                  title: resInit.errors,
                  icon: 'none',
                  duration: 2000,
                });
              }
              setiLoading(false);
            })
            .catch(() => {
              setiLoading(false);
            });
          return;
        }
        initAliprogram(resCode.authCode)
          .then((resInit) => {
            dispatch({
              type: ALIUSERID,
              aliUserId: resInit.payload.aliUserId,
            });
            if (resInit.payload.token)
              Taro.setStorageSync('appToken', resInit.payload.token);
            getAliProgramUserByAliUserIdApi({
              aliUserId: resInit.payload.aliUserId,
              subject,
            })
              .then((result) => {
                if (result.code == 0) {
                  const payload = result.payload;
                  console.log(result, '==loginresult');
                  payload.uid &&
                    dispatch({
                      type: 'CHANGE_USERID',
                      userid: payload.uid,
                    });
                  payload.mobile &&
                    dispatch({
                      type: 'CHANGE_MOBILE',
                      mobile: payload.mobile,
                    });
                  getOrderId(payload.uid);
                }
                setiLoading(false);
              })
              .catch(() => {
                setiLoading(false);
              });
          })
          .catch(() => {
            setiLoading(false);
          });
      },
    });
  }, []);
  return { iLoading };
}
