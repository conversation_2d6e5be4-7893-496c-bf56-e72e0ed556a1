import { useDispatch, useSelector } from 'react-redux';
import Taro from '@tarojs/taro';
import { UserStateType } from '@/store/groupbuy/state';
import { getProgramPay } from '@/api/groupbuy';
import { decryptDataApi } from '@/api/umsapi';
import { usePay } from './usePay';

export function useThirtySixPay({
  topicId,
  packagesId,
  isIntroduce,
  subject,
  pType,
  payPageData,
}) {
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  const openid = useSelector((state: UserStateType) => state.openid);
  const sessionkey = useSelector((state: UserStateType) => state.sessionkey);
  const { payFn, payFlag, setPayFlag, setPeriod, redeemFlag } = usePay({
    topicId,
    packagesId,
    isIntroduce,
  });
  const dispatch = useDispatch();
  // 跳转成功页
  const goChooseLevel = () => {
    Taro.navigateTo({
      url: `/pages/thirtySix/chooseLevel/index`,
    });
  };

  function initpay(uid = userId, sup?) {
    payFn(uid, 'TT', sup)
      .then((res: any) => {
        getProgramPay({
          type: 'bd',
          orderId: res.payload.order.id,
          userId: uid,
          payType: 'ART_BYTE_DANCE_PRM',
          notifyUrl: '',
          openId: openid,
        })
          .then((r: any) => {
            console.log(r, '===r===r');

            if (r.code === 0) {
              tt.pay({
                servce: 5,
                orderInfo: {
                  order_id: r.payload.order_id,
                  order_token: r.payload.order_token,
                },
                success: (payRes: any) => {
                  setPayFlag(true);
                  Taro.hideLoading();
                  if (payRes.code === 0) {
                    goChooseLevel();
                  } else {
                    Taro.showToast({ title: '下单失败', icon: 'none' });
                  }
                },
                fail: () => {
                  setPayFlag(true);
                  Taro.hideLoading();
                  Taro.showToast({ title: '下单失败', icon: 'none' });
                },
              });
            } else {
              Taro.hideLoading();
              Taro.showToast({
                title: r.errors,
                icon: 'none',
                duration: 2000,
              });
              setPayFlag(true);
            }
          })
          .catch(() => {
            Taro.hideLoading();
            setPayFlag(true);
          });
      })
      .catch(() => {
        Taro.hideLoading();
      });
  }

  function pay(uid = userId, sup?: string) {
    initpay(uid, sup);
  }

  const payConfirm = (sup = 'DEFAULT') => {
    if (payFlag) {
      pay(userId, sup);
    }
  };

  const authSuccess = authData => {
    if (payFlag) {
      const { encryptedData, iv } = authData;
      decryptDataApi(sessionkey, encryptedData, iv).then(result => {
        const payload = result.payload;
        payload.uid &&
          dispatch({
            type: 'CHANGE_USERID',
            userid: payload.uid,
          });
        pay(payload.uid, authData.sup || 'DEFAULT');
      });
      // dispatch({
      //   type: 'CHANGE_USERID',
      //   userid: '606902568945684480',
      // });
      // pay('606902568945684480', authData.sup || 'DEFAULT');
    }
  };

  return {
    authSuccess,
    setPeriod,
    payConfirm,
    redeemFlag,
  };
}
