import Taro, { useRouter } from '@tarojs/taro';
import { useDispatch, useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import sensors from '@/utils/sensors_data';
import {
  getPackagesCreate,
  getPackagesCreateV2,
  orderExtendReport,
  postReportReady,
} from '@/api/groupbuy';
import { getOrderId } from '@/common/order';
import { useState } from 'react';

export function usePay({
  topicId,
  packagesId,
  isIntroduce,
  subject = 'ART_APP',
  ispictureBook = false,
}) {
  const { params } = useRouter();
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  //openid
  const openId = useSelector((state: UserStateType) => state.openid);
  //channelId
  const channelId = useSelector((state: UserStateType) => state.channelId);
  //clickId
  const clickId = useSelector((state: UserStateType) => state.clickId);
  //wxAccountNu
  const wxAccountNu = useSelector((state: UserStateType) => state.wxAccountNu);
  //routePath
  const routePath = useSelector((state: UserStateType) => state.routePath);
  //adPlatform
  const adPlatform = useSelector((state: UserStateType) => state.adPlatform);
  //sendId
  const sendId =
    useSelector((state: UserStateType) => state.sendId) || params.sendId || '';
  //spreadId
  const spreadId = useSelector((state: UserStateType) => state.spreadId);
  //posterId
  const posterId =
    useSelector((state: UserStateType) => state.posterId) ||
    params.poster_id ||
    '';
  const userRole = useSelector((state: UserStateType) => state.userRole);
  //支付flag
  const [payFlag, setPayFlag] = useState<boolean>(true);
  const [period, setPeriod] = useState<number>(0);
  // 挽留弹窗flag
  const [redeemFlag, setRedeemFlag] = useState(true);
  const dispatch = useDispatch();

  function packageCreate(uid, sup) {
    return new Promise((resolve, reject) => {
      let _url = getPackagesCreate;
      let _data = {
        type: 'ALONE',
        userId: uid,
        packagesId,
        stage: period,
        sup: sup || 'DEFAULT',
        channel: channelId,
        sendId,
        spreadId,
        topicId,
        posterId,
        come: params.come || '',
      };
      //   绘本课创建订单
      if (ispictureBook) {
        _url = getPackagesCreateV2;
        _data['model'] = '6';
      }
      _url(_data)
        .then(res => {
          if (res.status === 'EXCEPTION') {
            if (packagesId == '617') {
              sensors.track('xxms_testcourse_home_buybuttonclick', {
                is_buy: '否',
              });
            }
            setPayFlag(true);
            if (res.code == 80000053) {
              Taro.showToast({
                title: '您已购买体验课，不支持再次购买',
                icon: 'none',
                duration: 2000,
              });
            } else {
              Taro.showToast({
                title: res.errors || '下单失败！',
                icon: 'none',
                duration: 2000,
              });
            }
            // 已购买过 不展示挽留弹窗
            setRedeemFlag(false);
            reject(res);
          }
          if (res.code === 0) {
            if (packagesId == '617') {
              sensors.track('xxms_testcourse_home_buybuttonclick', {
                is_buy: '是',
              });
            }
            if (params.pzd)
              orderExtendReport({
                uid,
                oids: res.payload.order.id,
                tabType: 'SEND',
                tabValue: sendId,
                tabJson: JSON.stringify({ prezzieId: params.pzd }),
                posterId: posterId,
                come: '',
              });
            resolve(res);
          }
        })
        .catch(err => {
          setPayFlag(true);
          reject(err);
        });
    });
  }

  /**
   * @description 提取支付单套餐下单接口
   * @param uid
   * @param platform
   * @returns
   */
  function payFn(uid = userId, platform: 'WX' | 'ALIPAY' | 'TT', sup?) {
    return new Promise((resolve, reject) => {
      /** 神策埋点
       * 用户点击支付时触发 **/
      if (isIntroduce) {
        sensors.login(openId);
        sensors.track('xxys_experienceCoursePage_pay_click', {
          channel_id: channelId,
          user_role: userRole,
          user_id: uid,
          poster_id: posterId,
          sendId: sendId || '',
        });
      }
      /** 神策埋点 **/
      setPayFlag(false);

      getOrderId(uid, subject).then(() => {
        packageCreate(uid, sup)
          .then((res: any) => {
            clickId &&
              postReportReady({
                orderId: res.payload.order.id,
                platform: adPlatform
                  ? adPlatform.toLocaleUpperCase()
                  : platform,
                type: 'RESERVATION',
                clickId: clickId,
                url: routePath,
                params: '',
                wxAccountNu: wxAccountNu,
              });

            dispatch({
              type: 'CHANGE_ORDERID',
              orderId: res.payload.order.id,
            });
            resolve(res);
          })
          .catch(err => [reject(err)]);
      });
    });
  }

  return {
    payFlag,
    setPayFlag,
    period,
    setPeriod,
    payFn,
    packageCreate,
    redeemFlag,
  };
}
