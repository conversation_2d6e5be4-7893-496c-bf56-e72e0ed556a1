import { useDispatch, useSelector } from 'react-redux';
import { useState, useEffect } from 'react';
import { UserStateType } from '@/store/groupbuy/state';
import Taro, { useRouter } from '@tarojs/taro';
import {
  initAliprogram,
  decryptAliUserApi,
  getProgramPay,
  getPackagesCreateV3,
  getAliProgramUserByAliUserIdApi,
  // queryOrderByUserId,
} from '@/api/groupbuy';
import Checkinfo from '@/utils/checkorder';
import { ALIUSERID, AUTHCODE } from '@/store/groupbuy/constants';
import qs from 'qs';

interface useMusicPayProps {
  packageId?: string | number;
  isGoAddress: boolean;
  topicId?: string;
}

export function useMusicPay({
  packageId = '521',
  isGoAddress = false,
  topicId = '18',
}: useMusicPayProps) {
  const { params } = useRouter();
  const dispatch = useDispatch();
  const [payFlag, setPayFlag] = useState<boolean>(true);
  const channelId = useSelector((state: UserStateType) => state.channelId);
  const musicOrderId = useSelector((state: UserStateType) => state.orderId);
  const [iLoading, setiLoading] = useState(true);

  const userId = useSelector((state: UserStateType) => state.userid);
  const aliUserId = useSelector((state: UserStateType) => state.aliUserId);
  const authCode = useSelector((state: UserStateType) => state.authCode);

  useEffect(() => {
    my.getAuthCode({
      scopes: ['auth_base'],
      success: resCode => {
        dispatch({
          type: AUTHCODE,
          authCode: resCode.authCode,
        });
        initAliprogram(resCode.authCode, 'MUSIC_APP')
          .then(resInit => {
            dispatch({
              type: ALIUSERID,
              aliUserId: resInit.payload.aliUserId,
            });
            if (resInit.payload.token)
              Taro.setStorageSync('appToken', resInit.payload.token);
            getAliProgramUserByAliUserIdApi({
              aliUserId: resInit.payload.aliUserId,
              subject: 'MUSIC_APP',
            })
              .then(result => {
                if (result.code == 0) {
                  const payload = result.payload;
                  console.log(result, '==loginresult');
                  payload.uid &&
                    dispatch({
                      type: 'CHANGE_USERID',
                      userid: payload.uid,
                    });
                  payload.mobile &&
                    dispatch({
                      type: 'CHANGE_MOBILE',
                      mobile: payload.mobile,
                    });
                }
                setiLoading(false);
              })
              .catch(() => {
                setiLoading(false);
              });
          })
          .catch(() => {
            setiLoading(false);
          });
      },
    });
  }, []);

  /**
   * 失败
   */
  const authError = error => {
    console.log(error);
    Taro.showToast({
      title: '用户取消授权',
      icon: 'none',
      duration: 2000,
    });
  };
  /**
   * 成功
   */
  const authSuccess = async () => {
    try {
      if (payFlag && !iLoading) {
        Taro.showLoading();
        setPayFlag(false);
        if (userId) {
          await pay(userId, packageId, authCode, aliUserId);
        } else {
          const { sign, response } = await getPhoneNumber();
          const { uid, mobile } = await decryptAliUser(
            sign,
            response,
            'MUSIC_APP',
          );
          dispatch({
            type: 'CHANGE_USERID',
            userid: uid,
          });
          dispatch({
            type: 'CHANGE_MOBILE',
            mobile: mobile,
          });
          await pay(uid, packageId, authCode, aliUserId);
        }
      }
    } catch (error) {
      setPayFlag(true);
      Taro.hideLoading();
      console.log('=======', error);
      if (error) {
        Taro.showToast({
          title: error.errorMessage || '系统出现未知错误，请联系管理员',
          icon: 'none',
          duration: 2000,
        });
      }
    }
  };

  // interface authCode {
  //   authCode: string;
  //   authSuccessScopes: string[];
  //   authErrorScopes: string | undefined;
  // }
  // const getAuthCode = () => {
  //   return new Promise<authCode>((resolve, reject) => {
  //     my.getAuthCode({
  //       scopes: ['auth_base'],
  //       success: res => {
  //         resolve(res);
  //       },
  //       fail: error => {
  //         reject(error);
  //       },
  //     });
  //   });
  // };
  interface phoneNumber {
    sign: string;
    response: string;
  }
  const getPhoneNumber = () => {
    return new Promise<phoneNumber>((resolve, reject) => {
      my.getPhoneNumber({
        success: res => {
          resolve(JSON.parse(res.response));
        },
        fail: error => {
          reject(error);
        },
      });
    });
  };
  interface decryptAliUserRes {
    aliUserId: string;
    uid: string;
    mobile: string;
  }
  const decryptAliUser = (sign: string, response: string, subject: string) => {
    return new Promise<decryptAliUserRes>((resolve, reject) => {
      decryptAliUserApi({
        sign,
        response,
        subject,
        aliUserId,
      })
        .then(res => {
          if (res.code == 0) {
            if (res.payload.token)
              Taro.setStorageSync('appToken', res.payload.token);
            resolve(res.payload);
          } else {
            reject(res);
          }
        })
        .catch(reject);
    });
  };

  const pay = async (
    uid: string,
    packagesId: number | string,
    code: string,
    thirdId: string,
  ) => {
    try {
      if (await getOrderByUserId(uid, 'MUSIC_APP')) {
        const order: any = await packageCreate(uid, packagesId);
        const payload = await programPay(uid, order.id, thirdId, code);
        tradePay(payload);
      }
    } catch (error) {
      setPayFlag(true);
      Taro.hideLoading();
    }
  };

  const getOrderByUserId = (uid: string, subjects: string) => {
    return new Promise((resolve, reject) => {
      const checkinfo = new Checkinfo({ subjects, uid });
      checkinfo.checkOrder(packageId).then((orderlist: any[]) => {
        if (orderlist.length) {
          if (isGoAddress) {
            orderlist.forEach(item => {
              const { addressId, id, status } = item;
              if (addressId == '0') {
                goAddress(id);
              }
              if (addressId != '0' && status == 'COMPLETED') {
                Taro.showToast({
                  title: '您已购买过音乐体验课，请勿重复购买',
                  icon: 'none',
                  duration: 2000,
                });
                reject();
              }
            });
          } else {
            Taro.showToast({
              title: '您已购买过音乐体验课，请勿重复购买',
              icon: 'none',
              duration: 2000,
            });
            reject();
          }
        } else {
          resolve(true);
        }
      });
      // queryOrderByUserId({
      //   userId,
      //   channels: channelId,
      //   subjects,
      //   packageId,
      // }).then(res => {
      //   console.log(res);
      //   if (res.code === 0) {
      //     const { experienceCheckMap } = res.payload;
      //     if (experienceCheckMap.MUSIC_APP) {
      //       Taro.showToast({
      //         title: '您已购买过音乐体验课，请勿重复购买',
      //         icon: 'none',
      //         duration: 2000,
      //       });
      //       reject();
      //     }
      //     resolve(true);
      //   }
      // });
    });
  };
  /**
   * 创建订单
   * @param uid 用户id
   * @param sup 级别
   */
  const packageCreate = (uid: string, packagesId: number | string) => {
    return new Promise((resolve, reject) => {
      let data = {
        type: 'ALONE',
        packagesId,
        stage: 0,
        sup: 'S3',
        channel: channelId,
        topicId,
        come: params.come,
      };
      let header = {
        'content-type': 'application/json',
        'app-current-user-id': uid,
        subject: 'MUSIC_APP',
      };

      getPackagesCreateV3(data, header)
        .then(res => {
          if (res.status === 'EXCEPTION') {
            if (res.code == 8000178) {
              Taro.showToast({
                title: '您已购买过音乐体验课，请勿重复购买',
                icon: 'none',
                duration: 2000,
              });
            } else {
              Taro.showToast({
                title: res.errors || '下单失败！',
                icon: 'none',
                duration: 2000,
              });
            }
            reject();
          } else if (res.code === 0) {
            dispatch({
              type: 'CHANGE_ORDERID',
              orderId: res.payload.order.id,
            });
            resolve(res.payload.order);
          }
        })
        .catch(reject);
    });
  };

  const programPay = (
    uid: string,
    orderId: string,
    thirdId: string,
    code: string,
  ) => {
    return new Promise((resolve, reject) => {
      getProgramPay({
        type: 'ali',
        payType: 'MUSIC_PRM_ALI',
        userId: uid,
        orderId,
        thirdId,
        returnType: 'GET',
        notifyUrl: '',
        code,
      })
        .then(res => {
          if (res.code === 0) {
            resolve(res.payload);
          } else {
            Taro.showToast({
              title: res.errors || '下单失败！',
              icon: 'none',
              duration: 2000,
            });
            reject();
          }
        })
        .catch(reject);
    });
  };
  /**
   * 唤起支付
   * @param payload 交易单号
   */
  const tradePay = payload => {
    my.tradePay({
      // 调用统一收单交易创建接口（alipay.trade.create），获得返回字段支付宝交易号trade_no
      tradeNO: payload || '',
      success: resPay => {
        Taro.hideLoading();
        setPayFlag(true);
        if (resPay.resultCode === '9000') {
          jumpPage(musicOrderId);
        } else {
          Taro.showToast({
            title: '下单失败！',
            icon: 'none',
            duration: 2000,
          });
        }
      },
      fail: () => {
        Taro.hideLoading();
        Taro.showToast({
          title: '下单失败！',
          icon: 'none',
          duration: 2000,
        });
      },
    });
  };

  /**
   * 跳转添加老师页
   */
  const goAddTeacher = () => {
    Taro.hideLoading();
    Taro.navigateTo({
      url: `/pages/music/addTeacher/index`,
    });
  };
  // 添加地址
  const goAddress = orderId => {
    Taro.hideLoading();
    const levels = {
      level: 'S3',
      packagesId: packageId,
      orderId: orderId,
      subject: 'MUSIC_APP',
    };
    Taro.redirectTo({
      url: `/pages/thirtySix/addAddress/index?${qs.stringify(levels)}`,
    });
  };

  const jumpPage = (orderId?) => {
    if (isGoAddress) {
      goAddress(orderId);
    } else {
      goAddTeacher();
    }
  };

  return {
    authError,
    authSuccess,
  };
}
