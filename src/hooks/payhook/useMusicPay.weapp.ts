import { useDispatch, useSelector } from 'react-redux';
import { useState } from 'react';
import { UserStateType } from '@/store/groupbuy/state';
import Taro, { useRouter } from '@tarojs/taro';
import {
  getProgramPay,
  getPackagesCreateV3,
  orderExtendReport,
} from '@/api/groupbuy';
import Checkinfo from '@/utils/checkorder';
import qs from 'qs';

interface useMusicPayProps {
  packageId?: string | number;
  isGoAddress?: boolean;
  topicId?: string;
}

export function useMusicPay({
  packageId = '521',
  isGoAddress = false,
  topicId = '18',
}: useMusicPayProps) {
  const { params } = useRouter();
  const dispatch = useDispatch();
  const [payFlag, setPayFlag] = useState<boolean>(true);
  const channelId = useSelector((state: UserStateType) => state.channelId);
  const musicOrderId = useSelector((state: UserStateType) => state.orderId);

  const userId = useSelector((state: UserStateType) => state.userid);
  const openId = useSelector((state: UserStateType) => state.openid);
  //sendId
  const sendId = useSelector((state: UserStateType) => state.sendId);
  //posterId
  const posterId =
    useSelector((state: UserStateType) => state.posterId) ||
    params.poster_id ||
    '';
  /**
   * 失败
   */
  const authError = error => {
    console.log(error);
    Taro.showToast({
      title: '用户取消授权',
      icon: 'none',
      duration: 2000,
    });
  };
  /**
   * 成功
   */
  const authSuccess = async ({
    uid = userId,
    _packageId = packageId,
    period = '',
  }) => {
    try {
      if (payFlag) {
        Taro.showLoading();
        setPayFlag(false);
        await pay(uid, _packageId || packageId, period);
      }
    } catch (error) {
      setPayFlag(true);
      Taro.hideLoading();
      console.log('=======', error);
      if (error) {
        Taro.showToast({
          title: error.errorMessage || '系统出现未知错误，请联系管理员',
          icon: 'none',
          duration: 2000,
        });
      }
    }
  };

  const pay = async (
    uid: string,
    packagesId: number | string,
    period?: string,
  ) => {
    try {
      if (await getOrderByUserId(uid, 'MUSIC_APP')) {
        const order: any = await packageCreate(uid, packagesId, period);
        const payload = await programPay(uid, order.id);
        tradePay(payload);
      }
    } catch (error) {
      setPayFlag(true);
      Taro.hideLoading();
    }
  };

  const getOrderByUserId = (uid: string, subjects: string) => {
    return new Promise((resolve, reject) => {
      const checkinfo = new Checkinfo({ subjects, uid });
      checkinfo.checkOrder(packageId).then((orderlist: any[]) => {
        if (orderlist.length) {
          if (isGoAddress) {
            orderlist.forEach(item => {
              const { addressId, id, status } = item;
              if (addressId == '0') {
                goAddress(id);
              }
              if (addressId != '0' && status == 'COMPLETED') {
                Taro.showToast({
                  title: '您已购买过音乐体验课，请勿重复购买',
                  icon: 'none',
                  duration: 2000,
                });
                reject();
              }
            });
          } else {
            Taro.showToast({
              title: '您已购买过音乐体验课，请勿重复购买',
              icon: 'none',
              duration: 2000,
            });
            reject();
          }
        } else {
          resolve(true);
        }
      });
    });
  };
  /**
   * 创建订单
   * @param uid 用户id
   * @param sup 级别
   */
  const packageCreate = (
    uid: string,
    packagesId: number | string,
    period = '',
  ) => {
    return new Promise((resolve, reject) => {
      let data = {
        type: 'ALONE',
        packagesId,
        stage: period || 0,
        sup: 'S3',
        channel: channelId,
        topicId,
        sendId,
        come: params.come,
      };
      let header = {
        'content-type': 'application/json',
        'app-current-user-id': uid,
        subject: 'MUSIC_APP',
      };

      getPackagesCreateV3(data, header)
        .then(res => {
          if (res.status === 'EXCEPTION') {
            if (res.code == 8000178) {
              Taro.showToast({
                title: '您已购买过音乐体验课，请勿重复购买',
                icon: 'none',
                duration: 2000,
              });
            } else {
              Taro.showToast({
                title: res.errors || '下单失败！',
                icon: 'none',
                duration: 2000,
              });
            }
            reject();
          } else if (res.code === 0) {
            dispatch({
              type: 'CHANGE_ORDERID',
              orderId: res.payload.order.id,
            });
            if (params.pzd) {
              orderExtendReport({
                uid,
                oids: res.payload.order.id,
                tabType: 'SEND',
                tabValue: sendId,
                tabJson: JSON.stringify({ prezzieId: params.pzd }),
                posterId: posterId,
                come: '',
              });
            }
            resolve(res.payload.order);
          }
        })
        .catch(reject);
    });
  };

  const programPay = (uid: string, orderId: string) => {
    return new Promise((resolve, reject) => {
      getProgramPay({
        type: 'wx',
        payType: 'ART_TO_MUSIC_WXPROGRAM',
        userId: uid,
        orderId,
        notifyUrl: '',
        openId,
      })
        .then(res => {
          if (res.code === 0) {
            resolve(res.payload);
          } else {
            Taro.showToast({
              title: res.errors || '下单失败！',
              icon: 'none',
              duration: 2000,
            });
            reject();
          }
        })
        .catch(reject);
    });
  };
  /**
   * 唤起支付
   * @param payload 交易单号
   */
  const tradePay = payload => {
    Taro.requestPayment({
      ...payload,
      success: () => {
        Taro.hideLoading();
        setPayFlag(true);
        jumpPage(musicOrderId);
      },
      fail: () => {
        Taro.hideLoading();
        Taro.showToast({
          title: '下单失败！',
          icon: 'none',
          duration: 2000,
        });
      },
    });
  };

  /**
   * 跳转添加老师页
   */
  const goAddTeacher = () => {
    Taro.hideLoading();
    Taro.navigateTo({
      url: `/pages/music/addTeacher/index`,
    });
  };
  // 添加地址
  const goAddress = orderId => {
    Taro.hideLoading();
    const levels = {
      level: 'S3',
      packagesId: packageId,
      orderId: orderId,
      subject: 'MUSIC_APP',
    };
    Taro.redirectTo({
      url: `/pages/thirtySix/addAddress/index?${qs.stringify(levels)}`,
    });
  };

  const jumpPage = (orderId?) => {
    if (isGoAddress) {
      goAddress(orderId);
    } else {
      goAddTeacher();
    }
  };

  return {
    authError,
    authSuccess,
  };
}
