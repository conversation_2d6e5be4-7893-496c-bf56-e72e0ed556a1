import Taro, { useRouter } from '@tarojs/taro';
import { useEffect } from 'react';
import sensors from '@/utils/sensors_data';
import { useDispatch, useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import {
  getProgramUserSubject,
  dyReport,
  tagAndRemark,
  getWeixinProgramPay,
  packagesZeroCreate,
  zeroPackagesCreate,
  getBuyOrderByUserId,
  querySpecialOrderByUserId,
} from '@/api/groupbuy';
import { usePay } from './usePay';

export function useThirtySixPay({
  topicId,
  packagesId,
  isIntroduce,
  subject,
  pType,
  payPageData,
  isZero = false,
  ispictureBook = false,
  isSimple = false,
  inEnterpriseWeChat,
  showAfterModal = null,
}) {
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  //openid
  const openId = useSelector((state: UserStateType) => state.openid);
  //channelId
  const channelId =
    useSelector((state: UserStateType) => state.channelId) || '15822';
  const { payFn, payFlag, setPayFlag, setPeriod, redeemFlag } = usePay({
    topicId,
    packagesId,
    isIntroduce,
    subject,
    ispictureBook,
  });
  const dispatch = useDispatch();

  const params = useRouter().params;

  useEffect(() => {
    payPageData.period != '' && setPeriod(payPageData.period);
  }, [payPageData]);

  // 跳转成功页
  const goChooseLevel = (oid = '') => {
    if (subject === 'WRITE_APP') {
      if (
        ['910', '8915', '7659', '911', '8916', '909', '8914', '7660'].includes(
          packagesId,
        )
      )
        Taro.navigateTo({
          url: `/pages/writeExperience/addTeacher/index`,
        });
      else
        Taro.navigateTo({
          url: `/pages/calligraphyExperience/addTeacher/index`,
        });
      return;
    }
    if (ispictureBook) {
      if (payPageData.subject == 'ART_APP')
        Taro.navigateTo({
          url: `/pages/launch/follow/index?subject=ART_APP&orderId=${oid}`,
        });
      else
        Taro.navigateTo({
          url: `/pages/conversion/addteacher/index?from=pictureBook&orderId=${oid}&isSimple=${
            isSimple ? 1 : 0
          }`,
        });
      return;
    }
    if (payPageData.addTeacher) {
      // Taro.navigateTo({
      //   url: `/pages/groupbuy/addTeacher/index?type=105&subject=WRITE_APP`,
      // });
      Taro.navigateTo({
        url: `/pages/launch/follow/index?type=105&subject=WRITE_APP`,
      });
    } else {
      Taro.navigateTo({
        url: `/pages/thirtySix/chooseLevel/index`,
      });
    }
  };
  function pay(uid = userId, sup?) {
    payFn(uid, 'WX', sup)
      .then((res: any) => {
        if (subject === 'WRITE_APP') {
          sensors.track('sf_Experiencecourse_paypage_paymentclick', {
            level: sup,
            is_receive: '是',
          });
        }

        // 不同的产品payType不同
        let _url = getWeixinProgramPay,
          _data = {
            openId,
            orderId: res.payload.order.id,
            userId: uid,
            payType: 'AI_WXPROGRAM',
            notifyUrl: '',
          };
        if (pType === 'calligraphy') {
          _data['payType'] = 'WRITE_WXPROGRAM';
        }
        _url(_data)
          .then(data => {
            if (data.code === 0) {
              const {
                timeStamp,
                nonceStr,
                package: packageId,
                paySign,
              } = data.payload;
              // 不同的支付接口的返回不同
              Taro.requestPayment({
                timeStamp,
                nonceStr,
                package: packageId,
                signType: 'HMAC-SHA256',
                paySign,
                success: function() {
                  setPayFlag(true);
                  if (
                    !(
                      ['1798', '7719'].includes(packagesId) &&
                      !payPageData.addTeacher
                    )
                  )
                    goChooseLevel(res.payload.order.id);
                },
                fail: function() {
                  setPayFlag(true);
                  Taro.showToast({
                    title: '下单失败！',
                    icon: 'none',
                    duration: 2000,
                  });
                },
              });
            }
          })
          .catch(err => {
            setPayFlag(true);
          });
      })
      .catch(res => {
        if (subject === 'WRITE_APP') {
          sensors.track('sf_Experiencecourse_paypage_paymentclick', {
            level: sup,
            is_receive: '否',
          });
        }
      });
  }

  const payConfirm = (sup = 'DEFAULT') => {
    payFlag && pay(userId, sup);
  };
  const authError = () => {};
  const authSuccess = res => {
    const { encryptedData, iv } = res.detail;
    getProgramUserSubject({
      openId,
      encryptedData,
      iv,
      subject,
    }).then(async phone => {
      phone.payload.uid &&
        dispatch({
          type: 'CHANGE_USERID',
          userid: phone.payload.uid,
        });
      if (phone.payload.token)
        Taro.setStorageSync('appToken', phone.payload.token);
      phone.payload.mobile &&
        dispatch({
          type: 'CHANGE_MOBILE',
          mobile: phone.payload.mobile,
        });
      if (['1798', '7719'].includes(packagesId)) {
        let buyres = await getHadBuyArtZero(phone.payload.uid);
        if (!buyres.payload.buy) {
          Taro.showToast({
            title: '您好，您之前的报名仍在体验期，暂无需重复领取哦',
            icon: 'none',
            duration: 3000,
          });
          if (inEnterpriseWeChat && params.externalUserId) {
            tagAndRemark({
              externalUserId: params.externalUserId,
              staffUserId: params.staffUserId,
              externalMobile: phone.payload.mobile,
              error: 1,
            });
          }
          return;
        }
      }
      if (ispictureBook) {
        if (payPageData.subject == 'ART_APP')
          pictureBookZero(
            phone.payload.uid,
            phone.payload.mobile,
            res.sup,
            res._period,
          );
        else pay(phone.payload.uid, res.sup || 'DEFAULT');
        return;
      }
      isZero
        ? receiveZero(phone.payload, res.sup)
        : pay(phone.payload.uid, res.sup || 'DEFAULT');
    });
  };
  const getHadBuyArtZero = id => {
    return querySpecialOrderByUserId({
      subject,
      userId: id,
      packageId: packagesId,
      channelId,
    }).then(res => {
      if (!res.payload.buy && payPageData.addTeacher) {
        setTimeout(() => {
          getBuyOrderByUserId({
            userId: id,
            orderRegType: 'SPECIAL',
            subjects: 'ART_APP',
            sortEnum: 'DESC',
          }).then(res1 => {
            let { payload = [] } = res1;
            if (payload.length) {
              Taro.navigateTo({
                url: `/pages/launch/follow/index?subject=ART_APP&orderId=${payload[0].id}`,
              });
            }
          });
        }, 3000);
      }
      return res;
    });
  };
  // 0元书法
  const channel = useSelector((state: UserStateType) => state.channelId);
  //sendId
  const sendId = useSelector((state: UserStateType) => state.sendId) || '0';
  const receiveZero = (val, sup) => {
    const data = {
      channel,
      packagesId: packagesId || '910',
      sendId,
      stage: 0,
      sup,
      topicId: 7,
      type: 'ALONE',
      userId: val.uid,
    };
    packagesZeroCreate(data).then(res => {
      if (res.code === 0) {
        const { orderId } = res.payload;
        dispatch({
          type: 'CHANGE_ORDERID',
          orderId,
        });
        goChooseLevel();
      } else {
        Taro.showToast({
          title: res.errors,
          icon: 'none',
        });
      }
    });
  };
  const pictureBookZero = (_uid, _mobile = '', sup?, _period?) => {
    zeroPackagesCreate({
      userId: _uid,
      packageId: packagesId,
      sup: sup || 'S1',
      channelId: channel,
      stage: _period ? _period : payPageData.period,
      model: '6',
      sendId,
    }).then(res => {
      if (res.code === 0) {
        const { id } = res.payload;
        dispatch({
          type: 'CHANGE_ORDERID',
          orderId: id,
        });
        Taro.showToast({
          title: '领取成功！',
          icon: 'none',
          duration: 2000,
        });
        if (['1798', '7719'].includes(packagesId))
          dyReport({
            orderId: id,
            platform: 'WXMP',
            clickId: openId,
            wxAccountNu: 'wxc7c14f4b2f39252b',
            type: '',
            subject: 'ART_APP',
          });
        if (payPageData.addTeacher) goChooseLevel(id);
        else {
          // @ts-ignore
          if (showAfterModal != null) showAfterModal();
        }
      } else {
        Taro.showToast({
          title: res.errors,
          icon: 'none',
        });
      }
      if (inEnterpriseWeChat && params.externalUserId) {
        tagAndRemark({
          externalUserId: params.externalUserId,
          staffUserId: params.staffUserId,
          externalMobile: _mobile,
          error: res.code == 0 ? 0 : 1,
        });
      }
    });
  };
  return {
    authError,
    authSuccess,
    setPeriod,
    payConfirm,
    redeemFlag,
  };
}
