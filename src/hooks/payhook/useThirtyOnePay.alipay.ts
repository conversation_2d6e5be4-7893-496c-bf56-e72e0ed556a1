import { useDispatch, useSelector } from 'react-redux';
import { useState } from 'react';
import Taro, { useRouter } from '@tarojs/taro';
import { UserStateType } from '@/store/groupbuy/state';
import sensors from '@/utils/sensors_data';
import {
  initAliprogram,
  decryptAliUserApi,
  getProgramPay,
} from '@/api/groupbuy';
import { usePay } from './usePay';

export function useThirtyOnePay({
  topicId,
  packagesId,
  isIntroduce,
  subject,
  pType,
  payPageData,
  sup,
}) {
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  const [orderId, setOrderId] = useState<string>('');
  const { params } = useRouter();
  const { payFn, payFlag, setPayFlag, setPeriod, redeemFlag } = usePay({
    topicId,
    packagesId,
    isIntroduce,
  });
  const dispatch = useDispatch();
  // 跳转成功页
  const goChooseLevel = () => {
    // if (packagesId == '617') {
    //   console.log(orderId, 617)
    //   Taro.redirectTo({
    //     url: `/pages/thirtySix/chooseLevel/index?packagesId=${packagesId}&orderId=${orderId}`,
    //   });
    // } else
    if (packagesId == '618') {
      Taro.redirectTo({
        url: '/pages/thirtyOne/addAddress/index',
      });
    } else {
      Taro.navigateTo({
        url: `/pages/thirtyOne/chooseLevel/index?packagesId=${packagesId}&come=${params.come}`,
      });
    }
  };

  function initpay(uid = userId, authCode, thirdId, supLevel?) {
    payFn(uid, 'ALIPAY', sup || supLevel)
      .then((res: any) => {
        if (packagesId == '617') {
          console.log(617617);
          Taro.setStorageSync('zeroDotOneOrderId', res.payload.order.id);
        }
        getProgramPay({
          type: 'ali',
          orderId: res.payload.order.id,
          userId: uid,
          payType: 'AI_PRM_ALI',
          notifyUrl: '',
          returnType: 'GET',
          code: authCode,
          thirdId: thirdId,
        })
          .then(r => {
            if (r.code === 0) {
              my.tradePay({
                // 调用统一收单交易创建接口（alipay.trade.create），获得返回字段支付宝交易号trade_no
                tradeNO: r.payload || '',
                success: resPay => {
                  setPayFlag(true);
                  Taro.hideLoading();
                  if (resPay.resultCode === '9000') {
                    goChooseLevel();
                  } else {
                    Taro.showToast({
                      title: '下单失败！',
                      icon: 'none',
                      duration: 2000,
                    });
                  }
                },
                fail: resPay => {
                  setPayFlag(true);
                  Taro.hideLoading();
                  Taro.showToast({
                    title: '下单失败！',
                    icon: 'none',
                    duration: 2000,
                  });
                },
              });
            } else {
              Taro.hideLoading();
              Taro.showToast({
                title: r.errors,
                icon: 'none',
                duration: 2000,
              });
              setPayFlag(true);
            }
          })
          .catch(() => {
            Taro.hideLoading();
            setPayFlag(true);
          });
      })
      .catch(() => {
        Taro.hideLoading();
      });
  }

  function pay(
    uid = userId,
    supLevel?: string,
    authCode?: string,
    thirdId?: string,
  ) {
    // 支付宝获取authCode 场景也需要按钮点击
    if (authCode) {
      initpay(uid, authCode, thirdId, sup || supLevel);
    } else {
      my.getAuthCode({
        scopes: ['auth_base'],
        success: resCode => {
          initAliprogram(resCode.authCode).then(resInit => {
            initpay(
              uid,
              resCode.authCode,
              resInit.payload.aliUserId,
              sup || supLevel,
            );
            if (resInit.payload.token)
              Taro.setStorageSync('appToken', resInit.payload.token);
          });
        },
      });
    }
  }

  const payConfirm = (supLevel = 'DEFAULT') => {
    if (payFlag) {
      Taro.showLoading();
      pay(userId, sup || supLevel);
    }
  };
  const authError = () => {};
  const authSuccess = (authData?) => {
    if (payFlag) {
      Taro.showLoading();
      my.getAuthCode({
        scopes: ['auth_base'],
        success: resCode => {
          initAliprogram(resCode.authCode).then(resInit => {
            my.getPhoneNumber({
              success: res => {
                const { sign, response } = JSON.parse(res.response);
                decryptAliUserApi({
                  sign,
                  response,
                  subject: 'ART_APP',
                  aliUserId: resInit.payload.aliUserId,
                }).then(result => {
                  if (result.code == 0) {
                    const payload = result.payload;
                    payload.uid &&
                      dispatch({
                        type: 'CHANGE_USERID',
                        userid: payload.uid,
                      });
                    payload.mobile &&
                      dispatch({
                        type: 'CHANGE_MOBILE',
                        mobile: payload.mobile,
                      });
                    pay(
                      payload.uid,
                      sup || authData.sup || 'DEFAULT',
                      resCode.authCode,
                      resInit.payload.aliUserId,
                    );
                    if (resInit.payload.token)
                      Taro.setStorageSync('appToken', resInit.payload.token);
                    if (result.payload.token)
                      Taro.setStorageSync('appToken', result.payload.token);
                    sensors.track('xxms_testcourse_loginsignupresult', {
                      is_success: '是',
                    });
                  } else {
                    sensors.track('xxms_testcourse_loginsignupresult', {
                      is_success: '否',
                    });
                  }
                });
                if (packagesId == 617) {
                  sensors.track(
                    'xxms_testcourse_registrationpaylayer_Getauthorization',
                    {
                      is_authorization: '允许',
                    },
                  );
                }
              },
              fail: res => {
                if (packagesId == 617) {
                  sensors.track(
                    'xxms_testcourse_registrationpaylayer_Getauthorization',
                    {
                      is_authorization: '拒绝',
                    },
                  );
                }
                Taro.hideLoading();
                Taro.showModal({
                  content: res.errorMessage,
                  showCancel: false,
                });
              },
            });
          });
        },
      });
    }
  };

  return {
    authError,
    authSuccess,
    setPeriod,
    payConfirm,
    redeemFlag,
  };
}
