import { useSelector } from 'react-redux';
import Taro from '@tarojs/taro';
import { UserStateType } from '@/store/groupbuy/state';
import { initAliprogram, getProgramPay } from '@/api/groupbuy';
import { usePay } from './usePay';

export function useAuthPay({ topicId, packagesId }) {
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  const { payFn, payFlag, setPayFlag, setPeriod, redeemFlag } = usePay({
    topicId,
    packagesId,
  });
  // 跳转成功页
  const goChooseLevel = () => {
    Taro.navigateTo({
      url: `/pages/thirtySix/chooseLevel/index`,
    });
  };

  function initpay(uid = userId, authCode, thirdId, sup?) {
    payFn(uid, sup)
      .then((res: any) => {
        getProgramPay({
          type: 'ali',
          orderId: res.payload.order.id,
          userId: uid,
          payType: 'AI_PRM_ALI',
          notifyUrl: '',
          returnType: 'GET',
          code: authCode,
          thirdId: thirdId,
        })
          .then(r => {
            if (r.code === 0) {
              my.tradePay({
                // 调用统一收单交易创建接口（alipay.trade.create），获得返回字段支付宝交易号trade_no
                tradeNO: r.payload || '',
                success: resPay => {
                  setPayFlag(true);
                  Taro.hideLoading();
                  if (resPay.resultCode === '9000') {
                    goChooseLevel();
                  } else {
                    Taro.showToast({
                      title: '下单失败！',
                      icon: 'none',
                      duration: 2000,
                    });
                  }
                },
                fail: () => {
                  setPayFlag(true);
                  Taro.hideLoading();
                  Taro.showToast({
                    title: '下单失败！',
                    icon: 'none',
                    duration: 2000,
                  });
                },
              });
            } else {
              Taro.hideLoading();
              Taro.showToast({
                title: r.errors,
                icon: 'none',
                duration: 2000,
              });
              setPayFlag(true);
            }
          })
          .catch(() => {
            Taro.hideLoading();
            setPayFlag(true);
          });
      })
      .catch(() => {
        Taro.hideLoading();
      });
  }

  function pay(
    uid = userId,
    sup?: string,
    authCode?: string,
    thirdId?: string,
  ) {
    // 支付宝获取authCode 场景也需要按钮点击
    if (authCode) {
      initpay(uid, authCode, thirdId, sup);
    } else {
      my.getAuthCode({
        scopes: ['auth_base'],
        success: resCode => {
          initAliprogram(resCode.authCode).then(resInit => {
            if (resInit.payload.token)
              Taro.setStorageSync('appToken', resInit.payload.token);
            initpay(uid, resCode.authCode, resInit.payload.aliUserId, sup);
          });
        },
      });
    }
  }

  const payConfirm = (uid = userId, sup = 'DEFAULT') => {
    if (payFlag) {
      Taro.showLoading();
      pay(uid, sup);
    }
  };

  const authSuccess = (authData?) => {
    if (payFlag) {
      Taro.showLoading();
      pay(
        authData.payload.uid,
        authData.sup || 'DEFAULT',
        authData.resCode.authCode,
        authData.resInit.payload.aliUserId,
      );
    }
  };

  return {
    authSuccess,
    setPeriod,
    payConfirm,
    redeemFlag,
  };
}
