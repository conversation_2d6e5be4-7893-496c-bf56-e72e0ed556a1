import Taro, { useRouter } from '@tarojs/taro';
import { useDispatch, useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import { getPackagesCreate } from '@/api/groupbuy';
import { getOrderId } from '@/common/order';
import sensors from '@/utils/sensors_data';

import { useState } from 'react';

export function usePay({ topicId, packagesId }) {
  const { params } = useRouter();
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  //channelId
  const channelId = params.channelId || params.channel;
  //sendId
  const sendId = params.sendId || '';
  //sendId
  const spreadId = params.spreadId || '';
  //posterId
  const posterId = params.poster_id || '';
  //支付flag
  const [payFlag, setPayFlag] = useState<boolean>(true);
  const [period, setPeriod] = useState<number>(0);
  // 挽留弹窗flag
  const [redeemFlag, setRedeemFlag] = useState(true);
  const dispatch = useDispatch();

  function packageCreate(uid, sup = 'DEFAULT') {
    return new Promise((resolve, reject) => {
      const defChannel = Taro.getStorageSync('defChannel');
      getPackagesCreate({
        type: 'ALONE',
        userId: uid,
        packagesId,
        stage: period,
        sup: sup,
        channel: channelId || defChannel,
        sendId,
        spreadId,
        topicId,
        posterId,
        come: params.come,
      })
        .then(res => {
          if (res.status === 'EXCEPTION') {
            sensors.track(
              'xxms_testcourse_registrationpaylayer_payButtonClick',
              {
                payment: '',
                is_buy: '否',
              },
            );
            setPayFlag(true);
            if (res.code == 80000053) {
              Taro.showToast({
                title: '您已购买体验课，不支持再次购买',
                icon: 'none',
                duration: 2000,
              });
            } else {
              Taro.showToast({
                title: res.errors || '下单失败！',
                icon: 'none',
                duration: 2000,
              });
            }
            // 已购买过 不展示挽留弹窗
            setRedeemFlag(false);
            reject(res);
          }
          if (res.code === 0) {
            sensors.track(
              'xxms_testcourse_registrationpaylayer_payButtonClick',
              {
                payment: '',
                is_buy: '是',
              },
            );
            resolve(res);
          }
        })
        .catch(err => {
          setPayFlag(true);
          reject(err);
        });
    });
  }

  /**
   * @description 提取支付单套餐下单接口
   * @param uid
   * @param platform
   * @returns
   */
  function payFn(uid = userId, sup?) {
    return new Promise((resolve, reject) => {
      setPayFlag(false);
      getOrderId(uid).then(() => {
        packageCreate(uid, sup)
          .then((res: any) => {
            dispatch({
              type: 'CHANGE_ORDERID',
              orderId: res.payload.order.id,
            });
            resolve(res);
          })
          .catch(err => [reject(err)]);
      });
    });
  }

  return {
    payFlag,
    setPayFlag,
    period,
    setPeriod,
    payFn,
    packageCreate,
    redeemFlag,
  };
}
