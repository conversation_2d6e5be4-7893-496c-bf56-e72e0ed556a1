import Taro from '@tarojs/taro';
import sensors from '@/utils/sensors_data';
import { useSelector } from 'react-redux';
import { UserStateType } from '@/store/groupbuy/state';
import { getWeixinProgramPay } from '@/api/groupbuy';
import { usePay } from './usePay';

export function useAuthPay({ topicId, packagesId, pType, payPageData }) {
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  //openid
  const openId = useSelector((state: UserStateType) => state.openid);

  const { payFn, payFlag, setPayFlag, setPeriod, redeemFlag } = usePay({
    topicId,
    packagesId,
  });

  // 跳转成功页
  const goChooseLevel = () => {
    if (payPageData.addTeacher) {
      Taro.navigateTo({
        url: `/pages/groupbuy/addTeacher/index?type=105&subject=WRITE_APP`,
      });
    } else {
      Taro.navigateTo({
        url: `/pages/thirtySix/chooseLevel/index`,
      });
    }
  };
  function pay(uid = userId, sup?) {
    payFn(uid, sup)
      .then((res: any) => {
        sensors.track('sf_Experiencecourse_paypage_paymentclick', {
          level: sup,
          is_receive: '是',
        });

        // 不同的产品payType不同
        let _url = getWeixinProgramPay,
          _data = {
            openId,
            orderId: res.payload.order.id,
            userId: uid,
            payType: 'AI_WXPROGRAM',
            notifyUrl: '',
          };
        if (pType === 'calligraphy') {
          _data['payType'] = 'WRITE_WXPROGRAM';
        }
        _url(_data)
          .then(data => {
            if (data.code === 0) {
              const {
                timeStamp,
                nonceStr,
                package: packageId,
                paySign,
              } = data.payload;
              // 不同的支付接口的返回不同
              Taro.requestPayment({
                timeStamp,
                nonceStr,
                package: packageId,
                signType: 'HMAC-SHA256',
                paySign,
                success: function() {
                  setPayFlag(true);
                  goChooseLevel();
                },
                fail: function() {
                  setPayFlag(true);
                  Taro.showToast({
                    title: '下单失败！',
                    icon: 'none',
                    duration: 2000,
                  });
                },
              });
            }
          })
          .catch(err => {
            setPayFlag(true);
            console.log(err);
          });
      })
      .catch(() => {
        sensors.track('sf_Experiencecourse_paypage_paymentclick', {
          level: sup,
          is_receive: '否',
        });
      });
  }

  const payConfirm = (uid = userId, sup = 'DEFAULT') => {
    payFlag && pay(uid, sup);
  };

  const authSuccess = res => {
    pay(res.payload.uid, res.sup || 'DEFAULT');
  };
  return {
    authSuccess,
    setPeriod,
    payConfirm,
    redeemFlag,
  };
}
