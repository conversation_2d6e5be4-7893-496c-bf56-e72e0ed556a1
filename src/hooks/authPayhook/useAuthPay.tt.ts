import { useSelector } from 'react-redux';
import Taro from '@tarojs/taro';
import { UserStateType } from '@/store/groupbuy/state';
import { getProgramPay } from '@/api/groupbuy';
import { usePay } from './usePay';

export function useAuthPay({
  topicId,
  packagesId,
  subject,
  pType,
  payPageData,
}) {
  //userid
  const userId = useSelector((state: UserStateType) => state.userid);
  const openid = useSelector((state: UserStateType) => state.openid);
  const { payFn, payFlag, setPayFlag, setPeriod, redeemFlag } = usePay({
    topicId,
    packagesId,
  });

  // 跳转成功页
  const goChooseLevel = () => {
    Taro.reLaunch({
      url: `/pages/thirtySix/chooseLevel/index`,
    });
  };

  function initpay(uid = userId, sup?) {
    payFn(uid, sup).then((res: any) => {
      getProgramPay({
        type: 'bd',
        orderId: res.payload.order.id,
        userId: uid,
        payType: 'ART_BYTE_DANCE_PRM',
        notifyUrl: '',
        openId: openid,
      })
        .then((r: any) => {
          if (r.code === 0) {
            tt.pay({
              service: 5,
              orderInfo: {
                order_id: r.payload.order_id,
                order_token: r.payload.order_token,
              },
              success: (payRes: any) => {
                console.log(payRes, 'pay');

                setPayFlag(true);
                if (payRes.code === 0) {
                  goChooseLevel();
                } else {
                  Taro.showToast({ title: '下单失败', icon: 'none' });
                }
              },
              fail: () => {
                setPayFlag(true);
                Taro.showToast({ title: '下单失败', icon: 'none' });
              },
            });
          } else {
            Taro.showToast({
              title: r.errors,
              icon: 'none',
              duration: 2000,
            });
            setPayFlag(true);
          }
        })
        .catch(() => {
          setPayFlag(true);
        });
    });
  }

  function pay(uid = userId, sup?: string) {
    initpay(uid, sup);
  }

  const payConfirm = (uid = userId, sup = 'DEFAULT') => {
    if (payFlag) {
      pay(uid, sup);
    }
  };

  const authSuccess = authData => {
    if (payFlag) {
      pay(authData.payload.uid, authData.sup || 'DEFAULT');
    }
  };

  return {
    authSuccess,
    setPeriod,
    payConfirm,
    redeemFlag,
  };
}
