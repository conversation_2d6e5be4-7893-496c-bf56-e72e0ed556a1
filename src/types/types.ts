export interface AddressTypes {
  id: string;
  userId?: string;
  addressId?: string;
  receiptName: string;
  receiptTel: string;
  province: string;
  city: string;
  area: string;
  street: string;
  addressDetail: string;
  telAreaCode?: string;
  idCode: string;
}

export interface agreementInfoTypes {
  agreementName: string;
  agreementUrl: string;
}
/**
 * @description: 用户地址payload字段
 */
export interface addressListTypes {
  id: string;
  addressId?: string;
  addressStr?: string;
  isDefault: string;
  receiptName: string;
  receiptTel: string;
  countryCode: string;
  province: string;
  city: string;
  area: string;
  street: string;
  addressDetail: string;
  countryName: string;
  telAreaCode?: string;
  idCode: string;
}
export interface GetAddressByIdRes {
  id: string;
  userId: string;
  receiptName: string;
  receiptTel: string;
  province: string;
  city: string;
  area: string;
  street: string;
  addressDetail: string;
  isDefault: number;
  type: number;
  telAreaCode: string;
  zipCode: string;
  countryCode: string;
  countryName: string;
  email?: string;
  idCode?: string;
}

export interface ToastStausTypes {
  status?: 'error' | 'loading' | 'success' | undefined;
  text: string;
  duration: number;
  isOpened: boolean;
}

export interface GetAddressTownListRes {
  unshift?: Function;
  find?: Function;
  townName?: string;
}

export interface GetCenterCountryItem {
  countryCode: string;
  countryName: string;
  id: string;
  simpleName: string;
  telAreaCode: string;
}

export interface areaAddressType {
  state: boolean;
  content: {
    showProvince: string;
    showTelAreaCode: string;
    showCity: string;
    showArea: string;
    showStreet: string;
    showCountryType: number;
    showZipCode: string;
    showCountryCode: string;
    showCountryName: string;
  };
  isBackShow?: boolean;
}
