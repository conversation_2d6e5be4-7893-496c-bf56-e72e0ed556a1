export interface AddressTypes {
  id: string;
  userId?: string;
  addressId?: string;
  receiptName: string;
  receiptTel: string;
  province: string;
  city: string;
  area: string;
  street: string;
  addressDetail: string;
  telAreaCode?: string;
  idCode: string;
}

export interface agreementInfoTypes {
  agreementName: string;
  agreementUrl: string;
}
/**
 * @description: 用户地址payload字段
 */
export interface addressListTypes {
  id: string;
  addressId?: string;
  addressStr?: string;
  isDefault: string;
  receiptName: string;
  receiptTel: string;
  countryCode: string;
  province: string;
  city: string;
  area: string;
  street: string;
  addressDetail: string;
  countryName: string;
  telAreaCode?: string;
  idCode: string;
}
export interface GetAddressByIdRes {
  id: string;
  userId: string;
  receiptName: string;
  receiptTel: string;
  province: string;
  city: string;
  area: string;
  street: string;
  addressDetail: string;
  isDefault: number;
  type: number;
  telAreaCode: string;
  zipCode: string;
  countryCode: string;
  countryName: string;
  email?: string;
  idCode?: string;
}

export interface ToastStausTypes {
  status?: 'error' | 'loading' | 'success' | undefined;
  text: string;
  duration: number;
  isOpened: boolean;
}

export interface GetAddressTownListRes {
  unshift?: Function;
  find?: Function;
  townName?: string;
}

export interface GetCenterCountryItem {
  countryCode: string;
  countryName: string;
  id: string;
  simpleName: string;
  telAreaCode: string;
}

export interface areaAddressType {
  state: boolean;
  content: {
    showProvince: string;
    showTelAreaCode: string;
    showCity: string;
    showArea: string;
    showStreet: string;
    showCountryType: number;
    showZipCode: string;
    showCountryCode: string;
    showCountryName: string;
  };
  isBackShow?: boolean;
}

// 视频号直播相关类型定义
export interface ChannelLiveInfo {
  errMsg: string;
  status: number; // 直播状态：0-未知，1-直播中，2-未开始，3-已结束，4-禁播，5-暂停，6-异常
  replayStatus: number; // 回放状态：0-未知，1-回放生成中，2-回放生成完成，3-回放生成失败
  feedId: string; // 直播feedId
}

export interface ChannelLivePlayerProps {
  finderUserName: string; // 视频号ID
  feedId?: string; // 直播feedId
  className?: string; // 自定义样式类名
  onError?: (error: string) => void; // 错误回调
  onStatusChange?: (status: number) => void; // 状态变化回调
}

export interface ChannelLiveTestState {
  liveInfo: ChannelLiveInfo | null;
  loading: boolean;
  error: string;
  compatible: boolean;
}
