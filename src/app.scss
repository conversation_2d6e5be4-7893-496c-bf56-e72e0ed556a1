/*周周公益倒计时*/
.custom-time-main {
  .at-countdown__item {
    &:last-child {
      display: none;
    }

    .at-countdown__time-box {
      background: #fff0d6;
      border-radius: 6px;

      .at-countdown__time {
        font-size: 26px;
        color: #ff6736;
        font-family: <PERSON><PERSON>-<PERSON><PERSON>, <PERSON><PERSON>;
        line-height: 32px;
      }
    }

    .at-countdown__separator {
      color: #666666;
      font-size: 24px;
    }
  }
}

/*周周公益弹窗自定义*/
.custom-modal {
  .at-modal__overlay {
    background: rgba(0, 0, 0, 0.8) !important;
  }

  .at-modal__container {
    width: 80%;
    background-color: transparent;
    overflow: visible;

    .at-modal__content {
      padding: 0;
      margin: 0;
    }
  }
}
