import { getOrdersIdApi } from '@/api/groupbuy';
import Taro from '@tarojs/taro';
import qs from 'qs';

/**
 * @description 根据用户id 获取订单状态
 * @param userId 用户id
 * @returns
 */
export function getOrderId(
  userId,
  subjects = 'ART_APP',
  nowSta = false,
  urlType = '',
) {
  return new Promise((resolve, reject) => {
    getOrdersIdApi({ userId, addressId: 0, subjects })
      .then(data => {
        if (data.payload && data.payload.length) {
          Taro.hideLoading();
          // 直接返回 自定义处理
          if (nowSta) {
            return resolve(data);
          }
          // 如果有未选择级别订单 先取选级别
          let needChooseLevel = false;
          // 用户订单是否已退费 REFUNDEND=>已退费
          let isrefund = '';
          for (let item of data.payload) {
            if (item.status === 'WAIT_COMPLETED') {
              isrefund = item.isRefund;
              needChooseLevel = true;
              const levels = {
                level: '',
                packagesId: '',
                orderId: item.id,
                urlType,
              };
              isrefund != 'REFUNDEND' &&
                Taro.redirectTo({
                  url: `/pages/thirtySix/chooseLevel/index?${qs.stringify(
                    levels,
                  )}`,
                });
            }
          }
          // 如果没有未选择级别的，就跳转填写地址
          if (!needChooseLevel) {
            const levels = {
              level: data.payload[0].sup,
              packagesId: data.payload[0].packagesId,
              orderId: data.payload[0].id,
              urlType,
            };
            isrefund != 'REFUNDEND' &&
              Taro.redirectTo({
                url: `/pages/thirtySix/addAddress/index?${qs.stringify(
                  levels,
                )}`,
              });
          }
          resolve(data);
        } else {
          resolve(data);
        }
      })
      .catch(error => {
        reject(error);
      });
  });
}
