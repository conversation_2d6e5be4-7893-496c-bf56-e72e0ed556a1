const TARO_ENV = process.env.TARO_ENV;

/*
 * 兼容 wx/alipay的内置api 提供统一接口 方便调用
 */

const platformApi = {
  weapp: {},
  alipay: {
    showToast: props => {
      const { title, icon, duration, ...rest } = props;
      my.showToast({
        type: icon || 'none',
        content: title,
        duration: duration || 2000,
        ...rest,
      });
    },
    hideLoading: () => my.hideLoading(),
    showLoading: () => my.showLoading(),
    setClipboardData: props => {
      const { data, ...rest } = props;
      my.setClipboard({
        text: data,
        ...rest,
      });
    },
  },
};

export default { ...platformApi[TARO_ENV] };
