import Taro from '@tarojs/taro';
import qs from 'qs';
import store from '@/store/groupbuy/index';

/**
 * 处理支付宝小程序scheme链接参数
 * 在App的componentDidShow生命周期中调用
 * @param props 启动参数
 */
export function initRouter(props: any) {
  console.log('支付宝小程序启动参数:', props);

  // 处理scheme链接参数
  if (props && props.query) {
    // 保存原始query参数
    const originalQuery = { ...props.query };
    console.log('原始query参数:', originalQuery);

    // 处理可能的URL编码参数
    let decodedParams = {};

    // 如果有query.q参数(支付宝扫码场景)，尝试解码
    if (props.query.q) {
      try {
        const decodedUrl = decodeURIComponent(props.query.q);
        console.log('解码后的URL:', decodedUrl);

        // 提取URL中的查询参数
        const queryIndex = decodedUrl.indexOf('?');
        if (queryIndex > -1) {
          const queryString = decodedUrl.substring(queryIndex + 1);
          decodedParams = qs.parse(queryString);
          console.log('从q参数解析的查询参数:', decodedParams);
        }
      } catch (error) {
        console.error('解析q参数失败:', error);
      }
    }

    // 合并所有参数
    const allParams = { ...originalQuery, ...decodedParams };
    console.log('合并后的所有参数:', allParams);

    // 存储关键参数到全局状态
    if (allParams.channelId) {
      store.dispatch({
        type: 'CHANGE_CHANNELID',
        channelId: allParams.channelId,
      });
    }

    if (allParams.sendId) {
      store.dispatch({ type: 'CHANGE_SENDID', sendId: allParams.sendId });
    }

    if (allParams.poster_id) {
      store.dispatch({
        type: 'CHANGE_POSTERID',
        posterId: allParams.poster_id,
      });
    }

    // 将参数保存到本地存储，以便其他页面使用
    Taro.setStorageSync('schemeParams', JSON.stringify(allParams));
  }
}
