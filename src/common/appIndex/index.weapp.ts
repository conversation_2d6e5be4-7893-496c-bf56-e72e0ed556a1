import Taro from '@tarojs/taro';
import { decodeUrl } from '@/utils';
import sensors from '@/utils/sensors_data';
import store from '@/store/groupbuy/index';
import { getLongUrl } from '@/api/groupbuy';
import qs from 'qs';

let oldSubject = null;
let fromWeekly = false;
interface trackParams {
  course_subject: string;
  poster_id: string;
  channel_id: string;
  xz_channel_id: string;
  yy_channel_id: string;
  user_role: any;
  Use_equipment: string;
  poster_type?: string;
  share_from?: string;
  buy_model?: string;
  sendId?: string;
  sharetype?: string;
  entrance_page?: string;
  scene?: string;
}
// 处理传参
function setStoreStatus(depar: object, props?: object, isShort = false) {
  let _mschannelid = depar['msChannelId'] || '';
  let _xzchannelid = depar['xzChannelId'] || '';
  let _yychannelid = depar['yyChannelId'] || '';
  if (_mschannelid)
    store.dispatch({ type: 'CHANGE_CHANNELID', channelId: _mschannelid });
  else if (_xzchannelid && depar['subject'] == 'xiezi')
    store.dispatch({ type: 'CHANGE_CHANNELID', channelId: _xzchannelid });
  else if (_yychannelid)
    store.dispatch({ type: 'CHANGE_CHANNELID', channelId: _yychannelid });
  depar['sendId'] &&
    store.dispatch({ type: 'CHANGE_SENDID', sendId: depar['sendId'] });
  depar['spreadId'] &&
    store.dispatch({ type: 'CHANGE_SPREADID', spreadId: depar['spreadId'] });
  depar['poster_id'] &&
    store.dispatch({ type: 'CHANGE_POSTERID', posterId: depar['poster_id'] });
  // 神策：页面浏览事件
  setTimeout(() => {
    let track: trackParams = {
      course_subject: 'ART_APP-小熊美术',
      poster_id: depar['poster_id'] || '',
      channel_id: _mschannelid,
      xz_channel_id: _xzchannelid,
      yy_channel_id: _yychannelid,
      user_role: store.getState().userRole,
      Use_equipment: depar['equipment'] == 1 ? 'ipad' : '非ipad',
      buy_model: 'model_4',
      sendId: depar['sendId'] || '',
      entrance_page: depar['entrance_page'] || '',
      scene: props ? props['scene'] : '',
    };

    const posterMap = {
      aha1: 'aha完课1',
      aha2: 'aha完课2',
      aha3: 'aha完课3',
      lr1: '学习报告1',
      lr2: '学习报告2',
    };
    if (depar['p']) {
      track.poster_type = posterMap[depar['p']];
    }
    // 瓜分有奖的海报分享
    if (depar['s']) {
      track.share_from = depar['s'] == 'gf1' ? '瓜分获取次数' : '瓜分上传截图';
    }
    if (depar['isToWf']) {
      let _json = {
        '1': '好友',
        '2': '朋友圈',
        '3': '保存本地',
        '4': '微信分享',
        '5': '中台生成长',
        '6': '中台生成短',
      };
      track.share_from = _json[depar['isToWf']];
    }
    if (depar['sharetype']) track.sharetype = depar['sharetype'];
    if (depar['msChannelId'] == '2079')
      track['abtest'] = !fromWeekly ? '老版' : '新版';
    if (isShort && props)
      track['hostname'] = decodeURIComponent(
        props['query'].q.split('.com')[0] + '.com',
      );
    sensors.track('xxys_experienceCoursePage_view', track);
  }, 3000);

  // 线上隐藏log
  if (process.env.NODE_ENV !== 'online')
    console.log(
      `props=`,
      props,
      `
      传参depar=`,
      depar,
      `
      subject=${depar['subject']}, poster_id=${depar['poster_id']}, spreadId=${depar['spreadId']}, msChannelId=${_mschannelid}, xzChannelId=${_xzchannelid}`,
    );
}

export function initRouter(props) {
  // 获取参数中是否有hashToken
  if (props.query.hash) Taro.setStorageSync('appHash', props.query.hash);
  // 京东支付不处理
  let donotredirectToPath = [
    'pages/art/jdpay/index',
    'pages/normalGroup/art/formh5twentynine/index',
  ];
  if (donotredirectToPath.includes(props.path)) {
    // 处理通过小程序跳转 携带参数
  } else if (props.referrerInfo && props.referrerInfo.appId) {
    const extraData = props.referrerInfo.extraData || {};
    const query = props.query || {};
    const path = props.path;
    console.log({ ...extraData, ...query }, 'referrerInfo');
    Taro.redirectTo({
      url: `/${path}?${qs.stringify({ ...extraData, ...query })}`,
    });
    setStoreStatus({ ...extraData, ...query });
    return;
  }
  // 针对短链处理
  if (props.query.q && props.query.q.indexOf('xiaoxiong.com') > -1) {
    let q = decodeURIComponent(props.query.q) as any;
    getLongUrl(q).then(res => {
      console.log(res);
      const originalUrl = res.payload.originalUrl;
      let depar = decodeUrl(decodeURIComponent(originalUrl)) as any;
      console.log(depar, '===depar===');
      let a = Math.floor(Math.random() * 2);
      if (depar['msChannelId'] == '2079' && a == 1) {
        fromWeekly = true;
        depar['fromWeekly'] = fromWeekly ? 1 : 0;
      }
      const _path =
        props.path == 'pages/normalGroup/calligraphy/index'
          ? 'pages/normalGroup/art/index'
          : props.path;
      Taro.redirectTo({
        url: `/${_path}?${qs.stringify(depar)}`,
      });
      setStoreStatus(depar, props, true);
    });

    return;
  }
  // 针对小程序的普通二维码跳转。因为后台设置路径为 小熊美术：'pages/normalGroup/art/index'，而网址二维码可能是小熊美术或者小熊书法，根据参数subject为meishu或者xiez进行判断，所以 下方有地址判断和字段判断
  // 小程序模拟参数 query.q = "https%3A%2F%2Fwww.xiaoxiongmeishu.com%2Factivity%2Fbear%3FsendId%3D562343115848159232%26msChannelId%3D2439%26subject%3Dmeishu%26poster_id%3D121%26from%3D1"
  if (props.path == 'pages/normalGroup/calligraphy/index') {
    var depar = decodeUrl(decodeURIComponent(props.query.q)) as any;

    // 如果不是普通二维码挂参进来的就不往下执行（比如微信开发者工具进来的）
    if (!Object.keys(depar)[0]) return;
    // 避免切出小程序再切入的自跳转问题
    if (oldSubject && oldSubject == depar['subject']) {
      return;
    }
    oldSubject = depar['subject'];
    // 如果网址为写字网址，跳转到小程序的写字页
    let t = depar['t'],
      from = depar['from'] || '';
    let a = Math.floor(Math.random() * 2);
    if (depar['msChannelId'] == '2079' && a == 1) {
      fromWeekly = true;
      depar['fromWeekly'] = fromWeekly ? 1 : 0;
    }
    Taro.redirectTo({
      url: `/pages/normalGroup/art/index?t=${t}&from=${from}&${qs.stringify(
        depar,
      )}}`,
    });
    oldSubject = null; // 重复扫美术二维码的时候会因为32行判断不跳转，故将oldSubject清空
    setStoreStatus(depar, props);
  } else {
    /**
     * 目前的路径有
     * pages/writeExperience/zeroNine 写字
     * pages/groupBuying/index/index 美术拼团
     * pages/normalGroup/art/discount9_9/index 亲友券
     */
    var depar = decodeUrl(decodeURIComponent(props.query.q)) as any;
    // 如果不是普通二维码挂参进来的就不往下执行（比如微信开发者工具进来的）
    if (!Object.keys(depar)[0]) return;
    let t = depar['t'];
    depar['channel'] &&
      store.dispatch({ type: 'CHANGE_CHANNELID', channelId: depar['channel'] });
    depar['msChannelId'] &&
      store.dispatch({
        type: 'CHANGE_CHANNELID',
        channelId: depar['msChannelId'],
      });
    depar['sendId'] &&
      store.dispatch({ type: 'CHANGE_SENDID', sendId: depar['sendId'] });
    depar['poster_id'] &&
      store.dispatch({ type: 'CHANGE_POSTERID', posterId: depar['poster_id'] });
    let _params = `t=${t}`;
    if (depar['pzd']) _params = _params + `&pzd=${depar['pzd']}`;
    if (depar['groupId']) _params = _params + `&groupId=${depar['groupId']}`;
    _params = _params + `&${qs.stringify(depar)}`;
    Taro.redirectTo({
      url: `/${props.path}?${_params}`,
    });
  }
  // h5点击开放按钮、移动应用app跳转进入小程序美术转介绍页
  if ([1167, 1069, 1037].includes(props.scene)) {
    setStoreStatus(props.query, props);
  }
}
