import Taro, { requirePlugin } from '@tarojs/taro';
import platformApi from '@/common/platformApi';
import { Component } from 'react';
import { Provider } from 'react-redux';
import groupbuyStore from '@/store/groupbuy/index';
import { checkUpdate } from '@/utils/update';
import { sensorsFun } from '@/utils/sensors_data';
// 兼容wx/alipay小程序
import { initRouter } from '@/common/appIndex/index';
import 'taro-ui/dist/style/index.scss';
import qs from 'qs';

import './app.scss';

// 自定义api,覆盖taro
for (const key in platformApi) {
  if (Object.prototype.hasOwnProperty.call(Taro, key)) {
    Taro[key] = platformApi[key];
  }
}

class App extends Component {
  componentWillMount() {
    // my.navigateTo({
    //   url:'plugin://goodsDetailPlugin/goodsDetail?outItemId=17143' //outItemId：必填，商品编码
    // })
    if (process.env.TARO_ENV == 'alipay') {
      if (process.env.APP_ENV === 'alipayV0') {
        const plugin = requirePlugin('goodsDetailPlugin');
        plugin.setPlaceOrderCallback((arg) => {
          console.log('arg', arg.query, arg.outItemId); // 可获取用户点击「立即购买」的信息
          my.redirectTo({
            url: `/pages/payOrder/index?${qs.stringify({ ...arg.query })}`, // 跳转到小程序的下单页面地址
          });
        });
      }
    }
  }

  componentDidMount() {
    checkUpdate();
    sensorsFun();
  }

  componentDidShow(props) {
    console.log(props, 'componentDidShow');
    initRouter(props);
  }

  componentDidHide() {}

  componentDidCatchError() {}

  render() {
    return <Provider store={groupbuyStore}>{this.props.children}</Provider>;
  }
}

export default App;
