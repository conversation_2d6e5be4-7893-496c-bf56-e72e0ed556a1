# weappV19 环境配置说明

## 概述

`weappV19` 是专门为微信小程序视频号直播功能测试而创建的新环境配置。该环境主要用于开发、测试和演示视频号直播相关功能。

## 环境特性

### 主要页面配置

`weappV19` 环境包含以下页面：

1. **`pages/channelLiveTest/index`** - 视频号直播测试页面（主要功能）
2. **`pages/groupbuy/index`** - 基础购买页面
3. **`pages/launch/follow/index`** - 添加老师微信
4. **`pages/groupbuy/addTeacher/index`** - 添加老师微信
5. **`pages/groupbuy/addAddress/index`** - 添加地址
6. **`pages/webview/index`** - 网页视图

### 权限配置

该环境自动包含以下权限：
- `chooseAddress` - 地址选择权限
- `getChannelsLiveInfo` - 视频号直播信息获取权限

### 插件配置

`weappV19` 环境使用空插件配置（`plugins = {}`），确保环境的纯净性。

## 使用方法

### 1. 开发环境启动

```bash
# 设置环境变量并启动开发服务器
APP_ENV=weappV19 npm run dev:weapp

# 或者使用脚本参数
npm run dev:weapp weappV19
```

### 2. 构建生产版本

```bash
# 构建 weappV19 环境的生产版本
APP_ENV=weappV19 npm run build:weapp

# 或者使用脚本参数
npm run build:weapp weappV19
```

### 3. 上传小程序

```bash
# 上传 weappV19 环境版本
APP_ENV=weappV19 npm run upload:weapp

# 或者使用脚本参数
npm run upload:weapp weappV19
```

## 配置详情

### 在 `src/app.config.ts` 中的实现

```typescript
// 视频号直播测试
if (isEnv('weappV19')) {
  pages = [
    'pages/channelLiveTest/index', // 视频号直播测试页面
    'pages/groupbuy/index', // 基础购买页面
    'pages/launch/follow/index', // 添加老师微信
    'pages/groupbuy/addTeacher/index', // 添加老师微信
    'pages/groupbuy/addAddress/index', // 添加地址
    'pages/webview/index', // 网页视图
  ];
}
```

### 插件重置配置

```typescript
// 特定环境重置插件
if (
  isEnvIn([
    'weappV13',
    'weappV14',
    'weappV15',
    'weappV17',
    'weappV9',
    'weappV12',
    'weappV18',
    'weappV19', // 新增
  ])
) {
  plugins = {};
}
```

## 版本管理

### 在 `src/scripts/version.js` 中添加配置

建议在版本配置文件中添加 `weappV19` 的相关信息：

```javascript
视频号直播测试: {
  appid: 'wx34831c7cbd4406e5', // 使用主小程序ID或创建新的测试ID
  env_number: 'V19',
  appletType: 'CHANNEL_LIVE_TEST',
  release_number: '1.0.0',
}
```

## 测试验证

### 功能测试清单

- [ ] 页面正常加载
- [ ] 视频号直播测试页面可访问
- [ ] 基础库版本检测正常
- [ ] 直播信息获取API正常
- [ ] 组件状态展示正确
- [ ] 错误处理机制有效
- [ ] 页面跳转功能正常

### 测试命令

```bash
# 运行环境配置测试
node src/scripts/test-weappV19.js
```

## 注意事项

### 开发注意事项

1. **基础库版本**: 确保微信开发者工具的基础库版本设置为 2.29.0 或更高
2. **权限配置**: 该环境已自动配置必要权限，无需手动添加
3. **页面路由**: 首页为视频号直播测试页面，便于直接测试功能

### 部署注意事项

1. **环境隔离**: 该环境与其他环境完全隔离，不会影响现有功能
2. **版本控制**: 建议为该环境创建独立的版本号管理
3. **测试数据**: 使用测试用的视频号ID，避免影响生产数据

## 故障排除

### 常见问题

1. **页面无法访问**
   - 检查环境变量 `APP_ENV=weappV19` 是否正确设置
   - 确认页面路径配置是否正确

2. **权限错误**
   - 验证 `getChannelsLiveInfo` 权限是否已配置
   - 检查微信开发者工具的权限设置

3. **组件不显示**
   - 确认 Taro 版本是否支持 ChannelLive 组件
   - 查看控制台是否有相关错误信息

### 调试建议

1. 使用微信开发者工具的调试功能
2. 查看网络请求是否正常
3. 检查控制台错误信息
4. 验证环境变量设置

## 后续扩展

### 可能的扩展功能

1. 添加更多视频号相关测试页面
2. 集成直播数据统计功能
3. 添加多视频号支持
4. 增加直播回放功能测试

### 版本升级路径

当 Taro 版本升级支持真正的 ChannelLive 组件后：
1. 更新组件导入方式
2. 替换占位符组件
3. 进行完整功能测试
4. 更新文档和示例

## 联系支持

如有问题或建议，请联系开发团队或查看相关文档。
