/* eslint-disable */

declare module '*.png';
declare module '*.gif';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.svg';
declare module '*.css';
declare module '*.less';
declare module '*.scss';
declare module '*.sass';
declare module '*.styl';

// @ts-ignore
declare const process: {
  env: {
    TARO_ENV:
      | 'weapp'
      | 'swan'
      | 'alipay'
      | 'h5'
      | 'rn'
      | 'tt'
      | 'quickapp'
      | 'qq';
    [key: string]: any;
  };
};

declare module 'qs';
declare const my: any;
declare const wx: any;
declare const tt: any;
declare module '@/utils/sensors_data' {
  export const bearSensors: any;
  export const sensorsFun: () => void;
  export const init: (data?: any) => void;
  export const login: (data: any) => void;
  export const track: (name: string, data?: object) => void;
}

declare module '@/common/appIndex/index' {
  export function initRouter(props): (props: any) => void;
}

// hooks
declare module '@/hooks/payhook/useThirtySixPay' {
  export function useThirtySixPay(data: any): any;
}
declare module '@/components/commonTop/index' {
  export const CommonTop: any;
}

declare module '@/pages/thirtySixTenDays/components/payBtn' {
  const Paybtn: any;
  export default Paybtn;
}

declare module '@/hooks/authPayhook/useAuthPay' {
  export function useAuthPay(data: any): any;
}

declare module '@/hooks/loginhook/useLogin' {
  interface useLoginProps {
    subject?: string;
    isIntroduce?: boolean;
    isFollow?: boolean;
    mobile?: string;
    urlType?: string;
  }
  export function useLogin(props: useLoginProps): any;
}
