/* eslint-disable */

const path = require('path');
const isOnline = process.env.NODE_ENV == 'online';
const ossPath = 'https://fe-cdn.xiaoxiongmeishu.com';
let projectPublicPath = '/';

if (process.env.RUNLOCAL !== 'runlocal') {
  const file_name = process.env.APP_ENV
    ? process.env.APP_ENV
    : process.env.TARO_ENV;
  projectPublicPath = `${ossPath}/xiaoxiongmp/${file_name}/${process.env.NODE_ENV.toLowerCase()}`;
}

let config = {
  projectName: 'ai-mp-users',
  date: '2020-04-26',
  designWidth: 750,
  deviceRatio: {
    '640': 2.34 / 2,
    '750': 1,
    '828': 1.81 / 2,
  },
  sourceRoot: 'src',
  outputRoot: 'dist',
  framework: 'react',
  plugins: [],
  alias: {
    '@': path.resolve(__dirname, '..', 'src'),
  },
  defineConstants: {
    APP_ENV: process.env.APP_ENV,
    RUNLOCAL: process.env.RUNLOCAL,
    APPLETTYPE: process.env.APPLETTYPE,
  },
  copy: {
    patterns: [],
    options: {},
  },
  mini: {
    postcss: {
      pxtransform: {
        enable: true,
        config: {},
      },
      url: {
        enable: true,
        config: {
          limit: isOnline ? 5 : 1024, // 设定转换尺寸上限
          url: '',
          basePath: isOnline ? projectPublicPath : '',
        },
      },
      cssModules: {
        enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:base64:5]',
        },
      },
    },
    sass: {
      resource: [
        path.resolve(__dirname, '..', 'src/theme/normalGroup/common.scss'),
        path.resolve(__dirname, '..', 'src/theme/mixins.scss'),
      ],
    },
    imageUrlLoaderOption: {
      limit: 10,
      name: 'assets/[name].[hash].[ext]',
      publicPath: projectPublicPath,
    },
    miniCssExtractPluginOption: {
      ignoreOrder: true,
    },
  },
};

module.exports = function(merge) {
  if (process.env.RUNLOCAL === 'runlocal') {
    return merge({}, config, require('./dev'));
  }
  return merge({}, config, require('./prod'));
};
