/* eslint-disable */

const WebpackAliyunOss = require('webpack-aliyun-oss');
const file_name = process.env.APP_ENV
  ? process.env.APP_ENV
  : process.env.TARO_ENV;
const distPath = `/xiaoxiongmp/${file_name}/${process.env.NODE_ENV.toLowerCase()}`;
module.exports = {
  env: {
    NODE_ENV: `"${process.env.NODE_ENV.toLowerCase()}"`,
    APP_ENV: `"${process.env.APP_ENV}"`,
    RUNLOCAL: `"${process.env.RUNLOCAL}"`,
    APPLETTYPE: `"${process.env.APPLETTYPE}"`,
  },
  mini: {
    webpackChain(chain) {
      chain.plugin('webpack-aliyun-oss').use(WebpackAliyunOss, [
        {
          from: ['./dist/assets/**'], //排除html文件
          dist: distPath,
          region: 'oss-cn-hangzhou',
          accessKeyId: 'LTAI4GDaQcWnC9KqLgvjKwih',
          accessKeySecret: '******************************',
          bucket: 'msb-xiaoxiong-fe',
        },
      ]);
    },
  },
};
