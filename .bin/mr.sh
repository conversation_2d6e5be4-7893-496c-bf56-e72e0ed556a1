#!/bin/bash
projectId="160" # 项目id
gitUrl="newsgitlab.meishubao.com/msb-ai/frontend/ai-mp-users.git" # 项目git地址

gitlabUrl="newsgitlab.meishubao.com" # gitlab url

time=$(date "+%Y%m%d%H%M%S")
privateToken="7n794Qt-zRs_gh8i5RkY" # token
account="zhaosiyuan:VpRxdhatM-24"
newbranch="GitLabCIBuild-${time}"

branch=`git branch | grep "*"`
currBranch=${branch:2}

# 拉取MR
MR_RESULT=$(curl --request GET --header "PRIVATE-TOKEN: ${privateToken}" "https://${gitlabUrl}/api/v4/projects/${projectId}/merge_requests?state=opened")
echo "${MR_RESULT}"
EMPTY="[]"

# 判断是否为空，不为空中断build
if [ "$MR_RESULT" != "$EMPTY" ];then
  echo '\033[31m MR扫描: 扫描到当前项目存在opened的MR，请将MR合并至master，再拉取最新代码重新上线！(ERROR) \033[0m'
  # 中断！
  exit 1;
else
  echo '\033[32m MR扫描: MR检查通过，未找到状态为opened的MR！(SUCCESS) \033[0m'
fi
git merge origin/master

git remote rm private

git remote add private "https://${account}@${gitUrl}"

git checkout -b $newbranch

git push -u private $newbranch:$newbranch

data="{\"id\":\"${projectId}\",\"source_branch\":\"${newbranch}\",\"target_branch\":\"master\",\"private_token\":\"${privateToken}\",\"title\":\"GitLabCI Build Auto Submit Merge Resquest\",\"description\":\"Jenkins构建自动提交merge request\"}"

curlCmd="curl -X POST -d '${data}' -H 'Content-Type: application/json'  https://${gitlabUrl}/api/v4/projects/${projectId}/merge_requests"

eval $curlCmd

git checkout $currBranch

echo "'${newbranch}'分支提交Merge Request 切回'${currBranch}'"